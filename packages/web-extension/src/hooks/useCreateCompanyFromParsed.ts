import { useCallback } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice'
import type {
  LeadMetaSourcePayload,
  LeadSource,
  Company,
} from '@getheroes/shared'
import { useCreateCompany } from '@internals/features/lead/hooks/createLeads/createCompany/useCreateCompany'
import { useCurrentUser } from '@internals/hooks/useCurrentUser'
import { useTypedSelector } from '@internals/store/store'

import { useExtension } from '../providers/extension/extension'
import { OverlayOperation, TargetFrame } from '../types/declaration/cross-frame'
import { useSendCrossFrameMessageAsync } from './useSendCrossFrameMessageAsync'
import { useToast } from '@getheroes/ui';

export type CreateCompanyFromLinkedIn = Pick<
  Company,
  'name' | 'linkedinUrl' | 'website'
> & { assignUsers?: string[] | null | undefined; skipToaster?: boolean }

/**
 * useCreateCompanyFromParsed
 * @param {Object} params
 * @param {LeadSource} params.source  - the source of the lead
 */
export const useCreateCompanyFromParsed = ({
  source,
  sourceMeta,
}: {
  source: LeadSource
  sourceMeta?: LeadMetaSourcePayload
}) => {
  const { t } = useTranslation('lead')
  const { createToast } = useToast()
  const {
    parsedCompanies,
    setFoundCompanies,
    setFullyMatchedCompanies,
    setNewCompanies,
    setExistingCompanies,
  } = useExtension()
  const currentUser = useTypedSelector(selectCurrentUser)
  const { isCurrentUserAdminOrManager } = useCurrentUser()
  const [createCompany, { isLoading }] = useCreateCompany({
    source,
    sourceMeta,
  })
  const sendMessageToOverlay = useSendCrossFrameMessageAsync({
    target: TargetFrame.OVERLAY,
  })

  const createCompanyFromParsed = useCallback(
    async (parsedCompanyId: string) => {
      const company = parsedCompanies.find(c => c.id === parsedCompanyId)

      if (company) {
        const { linkedinUrl, website, name } = company
        const { createResult, assignResult } =
          company && currentUser
            ? await createCompany({
                name,
                linkedinUrl,
                website,
                assignUsers: isCurrentUserAdminOrManager
                  ? []
                  : [currentUser.id],
              })
            : {
                createResult: {
                  error: {
                    message: !currentUser
                      ? t('User ID is missing')
                      : t('Invalid parsed company'),
                  },
                },
                assignResult: {},
              }

        if ('error' in createResult) {
          createToast({
            type: 'error',
            message:
              'message' in createResult.error
                ? (t(createResult.error.message || '') as string)
                : (t('Something went wrong') as string),
          })
        }

        if (company && 'data' in createResult) {
          setFoundCompanies(prev => [...prev, createResult.data])
          setFullyMatchedCompanies(prev => [...prev, createResult.data])
          setNewCompanies(prev => prev.filter(c => c !== company))
          setExistingCompanies(prev => [...prev, company])

          sendMessageToOverlay({
            operation: OverlayOperation.COMPANY_CREATED,
            data: {
              createdId: createResult.data.id,
              parsedId: parsedCompanyId,
            },
          })
        }
        return { createResult, assignResult }
      }
    },
    [
      createToast,
      createCompany,
      currentUser,
      isCurrentUserAdminOrManager,
      parsedCompanies,
      sendMessageToOverlay,
      setExistingCompanies,
      setFoundCompanies,
      setFullyMatchedCompanies,
      setNewCompanies,
      t,
    ]
  )

  return [createCompanyFromParsed, { isLoading }] as const
}
