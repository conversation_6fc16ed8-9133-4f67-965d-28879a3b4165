import { api } from '@getheroes/frontend/config/api'
import type { Company } from '@getheroes/shared'
import type { SearchCompaniesResponseType } from '@internals/features/lead/types/companyType'
import type { SearchLeadsPayloadType } from '@internals/features/lead/types/genericLeadType'

import i18n from '../config/i18n'

export const companyApi = api.injectEndpoints({
  endpoints: builder => {
    return {
      searchCompanies: builder.query<
        SearchCompaniesResponseType,
        SearchLeadsPayloadType
      >({
        query: ({
          organizationId,
          order,
          orderBy,
          page,
          limitPerPage,
          searchText,
          filters,
        }) => {
          const params = new URLSearchParams()
          if (order) {
            params.append('order', order)
          }
          if (orderBy && order) {
            params.append('orderBy', orderBy)
          }
          if (page) {
            params.append('page', page.toString())
          }
          if (limitPerPage) {
            params.append('limitPerPage', limitPerPage.toString())
          }
          return {
            url: `${organizationId}/leads/companies/search${
              params.toString() ? `?${params.toString()}` : ''
            }`,
            method: 'POST',
            body:
              {
                searchText: searchText,
                filters: filters,
              } || {},
          }
        },
        providesTags: () => [
          { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
        ],
        transformResponse: (data: SearchCompaniesResponseType) => {
          return {
            ...data,
            items: data.items.reduce(
              (acc: Record<string, Company>, company: Company) => {
                acc[company.id] = company
                return acc
              },
              {}
            ),
          }
        },
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'An error occurred while searching companies',
            {
              ns: 'lead',
            }
          )
          return { message: errorMsg }
        },
      }),
    }
  },
})

export const { useSearchCompaniesQuery } = companyApi
