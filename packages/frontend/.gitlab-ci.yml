.frontend-changes: &frontend-changes
  - packages/frontend/*
  - packages/frontend/**/*
  - '.gitlab-ci.yml'
  - 'package.json'
  - 'tsconfig.base.json'
  - 'yarn.lock'

.frontend_rules:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
    when: never
  - if: $CI_PIPELINE_SOURCE == "pipeline"
    when: never
  # Do not trigger if we are creating a tag (from master)
  - if: '$CI_COMMIT_TAG != null'
    when: never
  # Do not trigger when merging on develop, release or master
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "master"'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    changes: *frontend-changes
    when: always
  # - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_TAG != null'
  #   changes: *frontend-changes
  #   when: always
  - when: never

.frontend_rules_lint:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
    when: never
  - if: $CI_PIPELINE_SOURCE == "pipeline"
    when: never
  # Do not trigger if we are creating a tag (from master)
  - if: '$CI_COMMIT_TAG != null'
    when: never
  # Do not trigger when merging on develop, release or master
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "master"'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    changes: *frontend-changes
    when: always
  # - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_TAG != null'
  #   changes: *frontend-changes
  #   when: always
  - when: never

.frontend_rules_deploy:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    when: never
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_TAG != null'
    changes: *frontend-changes
    when: on_success
  - when: never

# .chromatic_frontend_rules:
#   - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
#     changes: *frontend-changes
#     when: always
#     allow_failure: true
#   - when: never

# .chromatic_frontend_develop_rules:
#   - if: '$CI_COMMIT_REF_NAME == "develop"'
#     changes: *frontend-changes
#     when: always
#   - when: never

test:front:
  stage: test
  needs: [ 'install' ]
  image: node:22.15.0-alpine3.21
  coverage: '/All files[^\|]*\|[^\|]*\s+([\d\.]+)/'
  cache:
    - !reference [ .cache_app_pull ]
    - !reference [ .cache_coverage ]
  rules:
    - !reference [ .frontend_rules ]
  script:
    - npx nx run frontend:test:production --ci --coverage --skip-nx-cache

test:front-lint:
  stage: test
  needs: [ 'install' ]
  image: node:22.15.0-alpine3.21
  cache:
    - !reference [ .cache_app_pull ]
  rules:
    - !reference [ .frontend_rules_lint ]
  script:
    - npx nx lint frontend # --skip-nx-cache
  interruptible: true

build:frontend:
  stage: build
  needs:
    - job: install
    - job: test:front
      optional: true
    - job: test:front-lint
      optional: true
  image: node:22.15.0-alpine3.21
  rules:
    - !reference [.frontend_rules_deploy]
  cache:
    - !reference [.cache_app_pull]
  before_script:
    - apk add --no-cache jq aws-cli
    - VERSION=$(jq -r .version package.json)
  script:
    - env | grep VITE_ > packages/frontend/.env.production
    - echo "VITE_APP_VERSION=$VERSION" >> packages/frontend/.env.production
    - npx nx build frontend --skip-nx-cache
  artifacts:
    paths:
      - dist/packages/frontend
    expire_in: 1 day
  variables:
    VITE_APP_NAME: webapp
    VITE_APP_VERSION: $VERSION
    VITE_APP_URL: https://$DASHBOARD_FRONTEND_URL
    VITE_API_URL: https://$DASHBOARD_API_URL/api
    VITE_STATIC_API_URL: https://$DASHBOARD_API_STATIC_URL
    VITE_AWS_REGION: $AWS_REGION
    VITE_COGNITO_USER_POOL_ID: $COGNITO_USER_POOL_ID
    VITE_COGNITO_USER_POOL_WEB_CLIENT_ID: $COGNITO_USER_POOL_CLIENT_ID
    VITE_COGNITO_AUTH_DOMAIN: '${COGNITO_POOL_DOMAIN_FQDN}'
    VITE_DEFAULT_LOCALE: 'fr-FR'
    VITE_INTERCOM_APP_ID: $INTERCOM_APP_ID
    VITE_SEGMENT_API_KEY: $SEGMENT_API_KEY
    VITE_ENVIRONMENT: $ENVIRONMENT
    VITE_DATADOG_APPLICATION_ID: $DATADOG_RUM_APPLICATION_ID
    VITE_DATADOG_CLIENT_TOKEN: $DATADOG_RUM_CLIENT_TOKEN
    VITE_WEBSOCKET_URL: 'wss://${DASHBOARD_WEBSOCKET_URL}'
    VITE_S3_PUBLIC_ASSETS_BUCKET_URL: 'https://getheroes-public.s3.eu-west-3.amazonaws.com'
    VITE_GCP_BUCKET_COMPANY_LOGO_URL: 'https://storage.googleapis.com/company-logo-bucket-dev/'
    VITE_PRISMATIC_URL: 'https://app.eu-west-1.prismatic.io'
    VITE_AG_GRID_LICENSE: 'Using_this_{AG_Grid}_Enterprise_key_{AG-076356}_in_excess_of_the_licence_granted_is_not_permitted___Please_report_misuse_to_legal@ag-grid.com___For_help_with_changing_this_key_please_contact_info@ag-grid.com___{Getheroes}_is_granted_a_{Single_Application}_Developer_License_for_the_application_{Zeliq}_only_for_{7}_Front-End_JavaScript_developers___All_Front-End_JavaScript_developers_working_on_{Zeliq}_need_to_be_licensed___{Zeliq}_has_been_granted_a_Deployment_License_Add-on_for_{1}_Production_Environment___This_key_works_with_{AG_Grid}_Enterprise_versions_released_before_{8_April_2026}____[v3]_[01]_MTc3NTYwMjgwMDAwMA==********************************'
    VITE_CHROME_EXTENSION_ID: $VITE_CHROME_EXTENSION_ID
    VITE_FLATFILE_PUBLIC_KEY: $VITE_FLATFILE_PUBLIC_KEY
    VITE_JIMO_PROJECT_ID: $VITE_JIMO_PROJECT_ID
  environment:
    action: prepare
    name: $ENVIRONMENT/dasboard-frontend
    url: $DASHBOARD_FRONTEND_URL
  resource_group: zeliq-$ENVIRONMENT

deploy:frontend:
  stage: deploy
  rules:
    - !reference [.frontend_rules_deploy]
  image:
    name: amazon/aws-cli
    entrypoint: ['']
  needs:
    - job: install
    - job: build:frontend
    - job: deploy:api
      optional: true
  script:
    # Deploy app sources
    - aws s3 sync --delete ./dist/packages/frontend/ s3://$BUCKET
    # Clear the cache using a cloudfront invalidation
    - |
      INVALIDATION_ID=$(aws cloudfront create-invalidation --distribution-id $DASHBOARD_FRONTEND_CLOUDFRONT_DISTRIBUTION_ID --paths /\*  --query 'Invalidation.Id' --output text)
      # Wait for the invalidation to complete
      STATUS="InProgress"
      while [ "$STATUS" == "InProgress" ]; do
        STATUS=$(aws cloudfront get-invalidation --distribution-id $DASHBOARD_FRONTEND_CLOUDFRONT_DISTRIBUTION_ID --id $INVALIDATION_ID --query 'Invalidation.Status' --output text)
        echo "Invalidation status: $STATUS"
        sleep 10
      done
      echo "Invalidation completed with status: $STATUS"
  variables:
    BUCKET: $DASHBOARD_FRONTEND_URL
  environment:
    name: $ENVIRONMENT/dasboard-frontend
    url: $DASHBOARD_FRONTEND_URL
    deployment_tier: other
  resource_group: zeliq-$ENVIRONMENT

# Gitlab Page
##
#pages:
#  stage: deploy
#  needs: ['install']
#  image: node:22.15.0-alpine3.21
#  cache:
#    - !reference [.cache_app_pull]
#  script:
#    - yarn build-storybook:frontend
#    - mv dist/storybook/frontend/ $CI_PROJECT_DIR/public
#  artifacts:
#    paths:
#      - public
#  rules:
#    - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
#      when: never
#    - if: '$CI_COMMIT_REF_NAME == "develop"'
#      changes: *frontend-changes
#      when: always
#    - when: never
#  allow_failure: true
# deploy:chromatic-frontend-develop:
#   needs: ['install']
#   stage: deploy
#   image: node:22.15.0-alpine3.21
#   rules:
#     - !reference [.chromatic_frontend_develop_rules]
#   cache:
#     - !reference [.cache_app_pull]
#   script:
#     - apk add git
#     - yarn chromatic:frontend --auto-accept-changes
#   resource_group: zeliq-$ENVIRONMENT
#   variables:
#     CHROMATIC_PROJECT_TOKEN: $FRONTEND_CHROMATIC_PROJECT_TOKEN

# deploy:chromatic-frontend-feature:
#   needs: ['install']
#   stage: deploy
#   image: node:22.15.0-alpine3.21
#   rules:
#     - !reference [.chromatic_frontend_rules]
#   cache:
#     - !reference [.cache_app_pull]
#   script:
#     - apk add git
#     - yarn chromatic:frontend --only-changed
#   resource_group: zeliq-$ENVIRONMENT]
#   variables:
#     CHROMATIC_PROJECT_TOKEN: $FRONTEND_CHROMATIC_PROJECT_TOKEN
