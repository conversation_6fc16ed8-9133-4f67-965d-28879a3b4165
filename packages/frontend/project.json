{"name": "frontend", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/frontend/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/packages/frontend", "configFile": "packages/frontend/vite.config.mts", "skipTypeCheck": true}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production"}}}, "serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "frontend:build"}, "configurations": {"development": {"buildTarget": "frontend:build:development", "hmr": true}, "production": {"buildTarget": "frontend:build:production", "hmr": false}}}, "preview": {"executor": "@nx/vite:preview-server", "defaultConfiguration": "production", "options": {"buildTarget": "frontend:build"}}, "test": {"executor": "@nx/vite:test", "outputs": ["{workspaceRoot}/coverage/packages/frontend"], "defaultConfiguration": "development", "configurations": {"development": {"mode": "development"}, "production": {"mode": "production"}}}, "lint": {"executor": "@nx/eslint:lint", "options": {"fix": false, "cache": true, "quiet": true, "cacheLocation": "packages/frontend/.eslintcache"}}, "lint-fix": {"executor": "@nx/eslint:lint", "options": {"fix": true}}, "test-storybook": {"executor": "nx:run-commands", "options": {"commands": ["test-storybook --url  http://localhost:4400/ --config-dir packages/frontend/.storybook --browsers chromium --maxWorkers=4"]}}, "storybook": {"executor": "@nx/storybook:storybook", "options": {"port": 4400, "open": true, "configDir": "packages/frontend/.storybook"}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@nx/storybook:build", "outputs": ["{options.outputDir}"], "options": {"outputDir": "dist/storybook/frontend", "configDir": "packages/frontend/.storybook"}, "configurations": {"ci": {"quiet": true}}}, "typecheck": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit -p tsconfig.app.json", "cwd": "packages/frontend"}}}}