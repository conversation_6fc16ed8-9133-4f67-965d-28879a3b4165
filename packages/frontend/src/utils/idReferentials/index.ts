import { subscriptionsDataTestIds } from '@internals/utils/idReferentials/subscriptions'

export const idReferentials = {
  components: {
    selectAssignUser: {
      selectAssignUser: 'id-components-select-assign-user',
    },
    errorPage: {
      reloadButton: 'id-components-error-page-reload-Button',
    },
    navigation: {
      MyAccountMenu: {
        dropdown: 'id-components-navigation-my-account-menu-dropdown',
      },
    },
  },
  leadImport: {
    stepButton: 'lead-import-step-button',
  },
  layout: {
    header: {
      toggleSidebarButton: 'id-layout-toggle-sidebar-button',
      goBackButton: 'id-layout-go-back-button',
    },
  },
  sidebar: {
    new: 'id-sidebar-new',
    buttons: {
      closeButton: 'id-sidebar-categories-closeButton',
      asyncActionEnrichmentHistory:
        'id-sidebar-sublists-asyncAction-enrichment-history',
    },
    categories: {
      home: 'id-sidebar-categories-home',
      whatsNew: 'id-sidebar-categories-whats-new',
      search: 'id-sidebar-categories-search',
      enrichmentHub: 'id-sidebar-categories-enrichment-hub',
      prepare: 'id-sidebar-categories-prepare',
      engage: 'id-sidebar-categories-engage',
      settings: 'id-sidebar-categories-settings',
      asyncActions: 'id-sidebar-categories-async-actions',
      accountSettings: 'id-sidebar-categories-accountSettings',
      organizationSettings: 'id-sidebar-categories-organizationSettings',
      tasks: 'id-sidebar-categories-tasks',
      analytics: 'id-sidebar-categories-analytics',
      dataHealth: 'id-sidebar-categories-dataHealth',
      sequenceTag: 'id-sidebar-categories-sequence-tag',
    },
    asyncActions: {
      progress: 'id-sidebar-items-async-actions-progress',
      tag: 'id-sidebar-items-async-actions-tag',
    },
    sublists: {
      leads: 'id-sidebar-sublists-leads',
      leadsViews: 'id-sidebar-sublists-leads-views',
      leadsViewsCreateButton: 'id-sidebar-sublists-leads-views-create-button',
      tasks: 'id-sidebar-sublists-tasks',
      tasksOpenButton: 'id-sidebar-sublists-tasks-open-button',
    },
    footer: {
      asyncButton: 'id-sidebar-footer-async-button',
      planButton: 'id-sidebar-footer-plan-button',
      asyncButtonTag: 'id-sidebar-footer-async-button-tag',
      creditTag: 'id-sidebar-footer-credit-button-tag',
      user: 'id-sidebar-user',
      loadingCircle: 'id-sidebar-footer-loading-circle',
      noPhoneIntegration: 'id-sidebar-footer-no-phone-integration',
      settingsButton: 'id-sidebar-footer-settings-button',
    },
    items: {
      home: 'id-sidebar-items-home',
      managerCockpit: 'id-sidebar-items-manager-cockpit',
      myCockpit: 'id-sidebar-items-my-cockpit',
      leaderboard: 'id-sidebar-items-leaderboard',
      search: 'id-sidebar-items-search',
      dataHealth: 'id-sidebar-items-data-health',
      sequences: 'id-sidebar-items-sequences',
      allLeads: 'id-sidebar-items-all-leads',
      myLeads: 'id-sidebar-items-my-leads',
      tasks: 'id-sidebar-items-outreach',
      myInbox: 'id-sidebar-items-my-inbox',
      notifications: 'id-sidebar-items-notifications',
      templates: 'id-sidebar-items-templates',
      myAccount: 'id-sidebar-items-my-account',
      settings: 'id-sidebar-items-settings',
      logout: 'id-sidebar-items-logout',
      enrichmentHub: 'id-sidebar-items-enrichment-hub',
      allLeadsCompanyTag: 'id-sidebar-items-all-leads-company-tag',
      myLeadsCompanyTag: 'id-sidebar-items-my-leads-company-tag',
    },
    ContactQuickActions: {
      addLeadButton: 'id-sidebar-ContactQuickActions-add-lead-button',
    },
    settings: {
      profile: 'id-sidebar-settings-profile',
      emailsAndLinkedIn: 'id-sidebar-emails-and-linkedIn',
      upgradePlan: 'id-sidebar-upgrade-plan',
      importHistory: 'id-sidebar-import-history',
      manageMembers: 'id-sidebar-manage-members',
      templates: 'id-sidebar-templates',
      integrations: 'id-sidebar-integrations',
      logOut: 'id-sidebar-log-out',
    },
    dynamicModules: {
      button: 'id-sidebar-dynamicModules-button',
      onboardingModule: 'id-sidebar-dynamicModules-onboardingModule',
      freePlanModule: 'id-sidebar-dynamicModules-freePlanModule',
      freeTrialModule: 'id-sidebar-dynamicModules-freeTrialModule',
    },
  },
  subscription: subscriptionsDataTestIds,
  search: {
    components: {
      filterItem: {
        autocomplete: 'id-search-components-filter-item-autocomplete',
        enum: 'id-search-components-filter-item-enum',
        location: 'id-search-components-filter-item-location',
        switch: 'id-search-components-filter-item-switch',
        select: 'id-search-components-filter-item-select',
        openButton: 'id-search-components-filter-item-open-button',
      },
      CurrentFiltersForm: {
        inputSearch: 'id-search-components-CurrentFiltersForm-input-search',
      },
      SearchFiltersPopover: {
        searchTextTag:
          'id-search-components-SearchFiltersPopover-searchTextTag',
        filterTag: 'id-search-components-SearchFiltersPopover-filterTag',
      },
      FilterItemSeniority: {
        year: 'id-search-components-FilterItemSeniority-seniority-time-in-current-role-year',
        month:
          'id-search-components-FilterItemSeniority-seniority-time-in-current-role-month',
        seniorityTabs:
          'id-search-components-FilterItemSeniority-seniority-tabs',
        seniorityTabTimeInCurrentRole:
          'id-search-components-FilterItemSeniority-seniority-tab-time-in-current-role',
        seniorityTabTimeOfExperience:
          'id-search-components-FilterItemSeniority-seniority-tab-time-of-experience',
      },
      FilterItemFundingRoundAmount: {
        minInput: 'id-search-components-filter-item-funding-round-amount-min',
        maxInput: 'id-search-components-filter-item-funding-round-amount-max',
      },
      FilterItemCompanyFounded: {
        startDate: {
          input:
            'id-search-components-filter-item-company-founded-start-date-input',
          datePicker:
            'id-search-components-filter-item-company-founded-start-date-date-picker',
        },
        endDate: {
          input:
            'id-search-components-filter-item-company-founded-end-date-input',
          datePicker:
            'id-search-components-filter-item-company-founded-end-date-date-picker',
        },
      },
      FilterItemFullName: {
        input: 'id-search-components-filter-item-full-name-input',
        addButton: 'id-search-components-filter-item-full-name-add-button',
      },
      FilterItemContainer: {
        lockedButton: 'id-search-components-filterItemContainer-locked-button',
        numberOfSelectedFilters:
          'id-search-components-filterItemContainer-number-of-selected-filters',
        selections: 'id-search-components-filterItemContainer-selections',
      },
      FilterItemSelect: {
        checkbox: 'id-search-components-filter-item-select-checkbox',
      },
      FilterItemLocation: {
        nbSelectionsTag:
          'id-search-components-filter-item-location-nb-selections-tag',
      },
    },
    searchPage: {
      searchPanel: {
        filters: {
          accordion: 'id-search-result-page-side-panel-filters-accordion',
          input: 'id-search-result-page-side-panel-filters-input',
          searchButton:
            'id-search-result-page-side-panel-filters-search-button',
          saveSearchButton:
            'id-search-result-page-side-panel-filters-save-search-button',
          clearFiltersButton:
            'id-search-result-page-side-panel-filters-clear-filters-button',
        },
        leadCategory: {
          segmentedTabulation:
            'id-search-result-page-side-panel-lead-category-segmented-tabulation',
          contact: 'id-search-result-page-side-panel-lead-category-contact',
          company: 'id-search-result-page-side-panel-lead-category-company',
        },
      },
      resultsTable: {
        enrichEmailBtn: 'id-search-result-page-results-table-enrich-email',
        enrichPhoneBtn: 'id-search-result-page-results-table-enrich-phone',
        table: 'id-search-result-page-results-table',
        assignButton: 'id-search-result-page-results-table-assign-button',
        addLeadButton: 'id-search-result-page-results-table-add-lead-button',
        alreadyButton:
          'id-search-result-page-results-table-already-lead-button',
        clearFiltersButton:
          'id-search-result-page-results-table-clear-filters-button',
        addCompanyButton: 'id-search-result-page-results-table-add-company',
        unarchiveCompanyButton:
          'id-search-result-page-results-table-unarchive-company',
      },
      floatingBar: {
        contacts: {
          addButton: 'id-search-result-page-results-contacts-add-button',
          enrichButton: 'id-search-result-page-results-contacts-enrich-button',
        },
        companies: {
          addButton: 'id-search-result-page-results-companies-add-button',
        },
        addToMyLeadsButton:
          'id-search-result-page-results-add-to-my-leads-button',
      },
      sidePanel: {
        company: {
          companySizeTextInput: 'id-search-result-page-side-panel-company-size',
          locationTextInput: 'id-search-result-page-side-panel-location',
        },
      },
      saveModal: {
        inputNameSavedSearch:
          'id-search-result-page-side-panel-save-modal-input-name-saved-search',
        toggleCreateUpdateSavedSearch:
          'id-search-result-page-side-panel-save-modal-toggle-create-update-saved-search',
        selectSavedSearch:
          'id-search-result-page-side-panel-save-modal-select-saved-search',
      },
    },
  },
  leads: {
    tableHeader: {
      leadsCategoryTabulation:
        'id-leads-table-header-leads-category-tabulation',
      leadsViewButton: 'id-leads-table-header-leads-view-button',
    },
    leadScoring: {
      cell: 'id-leads-lead-scoring-cell',
      button: 'id-leads-lead-scoring-button',
    },
    myLeadsPage: {
      hooks: {
        useTableNavigation: {
          tableOptionContact:
            'id-leads-my-leads-page-hooks-useTableNavigation-tableOptionContact',
          tableOptionCompany:
            'id-leads-my-leads-page-hooks-useTableNavigation-tableOptionCompany',
        },
        useGetTasksNavigationCategory: {
          navigationSublist:
            'id-leads-my-leads-page-hooks-useGetTasksNavigationSubList-navigationSublist',
        },
      },
      header: {
        tasksButton: 'id-leads-my-leads-page-header-tasks-button',
        launchTasksButton: 'id-leads-my-leads-page-header-launch-tasks-button',
        addLeadsButton: 'id-leads-my-leads-page-header-add-leads-button',
        viewsButton: 'id-leads-my-leads-page-header-views-button',
        leadsCategorySegmentedTabulation:
          'id-leads-my-leads-page-header-leads-category-segmented-tabulation',
        errorBoundaryButton:
          'id-leads-my-leads-page-header-error-boundary-button',
        selectLeadView: 'id-leads-my-leads-page-header-select-lead-view',
      },
      contactsTable: 'id-leads-my-leads-page-contacts-table',
      floatingActionBar: {
        container: 'id-leads-my-leads-page-floating-action-bar-container',
        addTaskButton:
          'id-leads-my-leads-page-floating-action-bar-add-task-button',
        unassignButton:
          'id-leads-my-leads-page-floating-action-bar-unassign-button',
        enrichButton:
          'id-leads-my-leads-page-floating-action-bar-enrich-button',
        addToSequenceButton:
          'id-leads-my-leads-page-floating-action-bar-add-to-sequence-button',
        editLeadStatusButton:
          'id-leads-my-leads-page-floating-action-bar-edit-lead-status-bulk-button',
        saveEditLeadStatusButton:
          'id-leads-my-leads-page-floating-action-bar-save-edit-lead-status-bulk-button',
        cancelEditLeadStatusButton:
          'id-leads-my-leads-page-floating-action-bar-cancel-edit-lead-status-bulk-button',
      },
    },
    actionButton: {
      add: {
        phone: 'id-lead-table-action-phone-add-button',
      },
      call: 'id-leads-action-button-call',
      email: 'id-leads-action-button-email',
      task: 'id-leads-action-button-task',
      sequence: 'id-leads-action-button-sequence',
      exportCsv: {
        openModal: 'id-leads-action-button-export-csv-open-modal',
        confirmExport: 'id-leads-action-button-export-csv-confirm-export',
        cancelExport: 'id-leads-action-button-export-csv-cancel-export',
      },
    },
    modals: {
      contact: {
        quickAction: {
          cancelButton: 'id-leads-modals-contact-quick-action-cancel-button',
          scheduleButton:
            'id-leads-modals-contact-quick-action-schedule-button',
        },
        enrich: {
          leadsSelected: 'id-leads-modals-contact-enrich-leads-selected',
          enrichAvailable: 'id-leads-modals-contact-enrich-enrich-available',
          enrichButton: 'id-leads-modals-contact-enrich-enrich-button',
          enrichEmailsOnly: 'id-leads-modals-contact-enrich-enrich-emails-only',
          cancelButton: 'id-leads-modals-contact-enrich-cancel-button',
        },
        createForm: {
          firstNameInput: 'id-leads-modals-contact-create-form-first-name',
          lastNameInput: 'id-leads-modals-contact-create-form-last-name',
          titleInput: 'id-leads-modals-contact-create-form-title',
          companyInput: 'id-leads-modals-contact-create-form-company',
          emailsInput: 'id-leads-modals-contact-create-form-emails',
          phonesInput: 'id-leads-modals-contact-create-form-phones',
          linkedinUrlInput: 'id-leads-modals-contact-create-form-linkedin-url',
          assignUserIdInput: 'id-leads-modals-contact-create-form-assign-user',
          cancelButton: 'id-leads-modals-contact-create-form-cancel-button',
          saveButton: 'id-leads-modals-contact-create-form-save-button',
        },
      },
      company: {
        createForm: {
          nameInput: 'id-leads-modals-company-create-form-name-input',
          phoneInput: 'id-leads-modals-company-create-form-phone-input',
          assignUsersInput:
            'id-leads-modals-company-create-form-assign-users-input',
          saveButton: 'id-leads-modals-company-create-form-save-button',
          cancelButton: 'id-leads-modals-company-create-form-cancel-button',
        },
      },
      views: {
        createLeadView: {
          viewNameInput: 'id-leads-modals-views-createLeadView-viewNameInput',
          submitButton: 'id-leads-modals-views-createLeadView-submitButton',
        },
        deleteLeadView: {
          cancelButton: 'id-leads-modals-views-deleteLeadView-cancelButton',
          submitButton: 'id-leads-modals-views-deleteLeadView-submitButton',
        },
        duplicateLeadView: {
          viewNameInput:
            'id-leads-modals-views-duplicateLeadView-viewNameInput',
          submitButton: 'id-leads-modals-views-duplicateLeadView-submitButton',
        },
        renameLeadView: {
          viewNameInput: 'id-leads-modals-views-renameLeadView-viewNameInput',
          submitButton: 'id-leads-modals-views-renameLeadView-submitButton',
        },
      },
      addToSequence: {
        statuses: 'id-leads-modals-contact-add-to-sequence-statuses',
        selectSequence:
          'id-leads-modals-contact-add-to-sequence-select-sequence',
      },
      assignLeadsCommonModal: {
        cancel: 'id-leads-modals-assignLeadsCommonModal-cancel-button',
        submit: 'id-leads-modals-assignLeadsCommonModal-submit-button',
        enrichmentAvailableSwitchButton:
          'id-leads-modals-assignLeadsCommonModal-enrichmentAvailableSwitchButton-enrichmentSwitchButton',
        enrichmentAfterAssignSwitchButton:
          'id-leads-modals-assignLeadsCommonModal-assignLeadsCommonModal-enrichmentSwitchButton',
        buyMoreCreditsButton:
          'id-leads-modals-assignLeadsCommonModal-buy-more-credits-button',
      },
    },
    sidePanel: {
      archiveButton: 'id-leads-side-panel-archive-button',
      attributes: {
        goToAttributesSidePanel:
          'id-leads-side-panel-attributes-go-to-attributes-side-panel',
        goToPersonalitySidePanel:
          'id-leads-side-panel-attributes-go-to-personality-side-panel',
        prefix: 'id-leads-side-panel-attributes',
      },
      assignCompanyButton: 'id-leads-side-panel-assign-company',
    },
    components: {
      leadSidePanelHeader: {
        leadScoringPopover:
          'id-leads-components-lead-side-panel-header-lead-scoring-popover',
        leadScoringButton:
          'id-leads-components-lead-side-panel-header-lead-scoring-Button',
        previousButton:
          'id-leads-components-lead-side-panel-header-previous-button',
        plgCreditTag: 'id-leads-components-lead-side-panel-header-plgCreditTag',
      },
      SearchExternalLeadsFloatingActionBar: {
        enrichButton:
          'id-leads-components-SearchExternalLeadsFloatingActionBar-enrich-button',
        addToSequenceButton:
          'id-leads-components-SearchExternalLeadsFloatingActionBar-add-to-sequence-button',
        assignButton:
          'id-leads-components-SearchExternalLeadsFloatingActionBar-assign-button',
      },
      LeadAttribute: {
        label: {
          addInputButton:
            'id-leads-components-lead-attribute-label-add-input-button',
          saveButton: 'id-leads-components-lead-attribute-label-save-button',
          enrichButton:
            'id-leads-components-lead-attribute-label-enrich-button',
        },
        selectInput: 'id-leads-components-lead-attribute-select-input',
      },
      searchLeadsForm: {
        inputSearch: 'id-leads-components-search-leads-form-input-search',
      },
      AllLeadsFloatingActionBar: {
        assignButton:
          'id-leads-components-organization-leads-floating-action-bar-assign-button',
        assignAndAddTaskButton:
          'id-leads-components-organization-leads-floating-action-bar-assign-and-add-task-button',
        enrichButton:
          'id-leads-components-organization-leads-floating-action-bar-enrich-button',
        archiveButton: 'id-leads-components-organization-leads-archive-button',
        addToSequenceButton:
          'id-leads-components-organization-leads-add-to-sequence-button',
      },
      AddToSequenceDrawer: {
        closeButton: 'id-leads-components-add-to-sequence-drawer-close-button',
        settingFooterButton:
          'id-leads-components-add-to-sequence-drawer-setting-footer-button',
        buyMoreCreditButton:
          'id-leads-components-add-to-sequence-buy-more-credit-button',
        previousPageButton: 'id-leads-components-add-to-sequence-previous-page',
      },
      AssigneeSelector: {
        selectInput: 'id-leads-components-sdr-selector-select-input',
      },
      SequenceSelector: {
        selectInput: 'id-leads-components-sequence-selector-select-input',
      },
      EnrichmentCard: {
        emailAndPhoneSwitchButton:
          'id-leads-components-enrichment-card-email-and-phone-switch-button',
        emailOnlySwitchButton:
          'id-leads-components-enrichment-card-email-only-switch-button',
      },
      ContactSequenceStepProgressList: {
        popoverButtonId:
          'id-leads-components-contact-sequence-step-progress-list-popover-button',
        popoverContentId:
          'id-leads-components-contact-sequence-step-progress-list-popover-content',
      },
      ContactSequenceStepCard: {
        removeLeadButton:
          'id-leads-components-contact-sequence-step-card-remove-lead-button',
        viewDetailsLink:
          'id-leads-components-contact-sequence-step-card-view-details-link',
      },
      ContactSequenceActivities: {
        goToSequenceButtonId:
          'id-leads-components-contact-sequence-activities-go-to-sequence-button',
        cancelButtonId:
          'id-leads-components-contact-sequence-activities-cancel-button',
        removeLeadButton:
          'id-leads-components-contact-sequence-activities-remove-lead-button',
        sdrEmailAccountNotSync: {
          googleButton:
            'id-leads-components-ContactSequenceActivities-sdrEmailAccountNotSync-googleButton',
          microsoftButton:
            'id-leads-components-ContactSequenceActivities-sdrEmailAccountNotSync-microsoftButton',
        },
      },
      LeadActionPhone: {
        phoneInput: 'id-lead-action-phone-input',
        emailInput: 'id-lead-action-email-input',
      },
      LeadActionsCell: {
        popoverPhoneAction:
          'id-lead-actions-cell-button-leads-action-phone-input',
        popoverEmailAction:
          'id-lead-actions-cell-button-leads-action-email-input',
      },
      ContactSequenceStepProgress: {
        addToSequenceButton:
          'id-leads-components-contact-sequence-step-progress-add-to-sequence-button',
        popover: 'id-leads-components-contact-sequence-step-progress-popover',
      },
      addContactsToSequenceDrawer: {
        seeSequenceLink:
          'id-leads-components-add-contacts-to-sequence-drawer-see-sequence-link',
      },
      leadsModals: {
        enrichLeadsModal: {
          buyMoreCreditsLink:
            'id-leads-modals-enrich-leads-buy-more-credits-link',
        },
        bulkEditModal: {
          bulkActionSearch: 'id-leads-modals-bulk-edit-modal',
          bulkEditStatus: 'id-leads-modals-bulk-edit-status',
          bulkEditLabels: 'id-leads-modals-bulk-edit-labels',
        },
      },
      leadsCollectionCard: {
        components: {
          header: {
            leadFilterButton:
              'id-leads-collection-card-components-header-lead-filter-button',
            selectActiveFieldsButton:
              'id-leads-collection-card-components-header-select-active-fields-button',
            leadFilterPopover: {
              popover: 'id-leads-collection-card-components-header-popover',
              newFilterButton:
                'id-leads-collection-card-components-header-new-filter-button',
              createViewButton:
                'id-leads-collection-card-components-header-create-view-button',
              updateViewButton:
                'id-leads-collection-card-components-header-update-view-button',
            },
          },
          modals: {
            assignLeadsModal: {
              assignLeadsToUserModal: {
                selectAssignUser:
                  'id-leads-collection-card-components-modals-assign-leads-modal-assign-leads-to-user-modal-select-assign-user',
              },
            },
          },
        },
      },
      enrichment: {
        newEnrichmentAvailable: {
          banner: {
            seeDetailsButton:
              'id-leads-components-enrichment-banner-see-details-button',
            enrichAllButton:
              'id-leads-components-enrichment-banner-enrich-all-button',
          },
          table: {
            enrichSelectionButton:
              'id-leads-components-enrichment-table-enrich-selection-button',
          },
        },
      },
      CompanyAssignee: {
        assignLeadButton: 'id-leads-components-company-assignee-select-input',
      },
      ContactAssignee: {
        assignLeadButton:
          'id-leads-components-contact-assignee-assign-lead-button',
      },
      LeadViewCard: {
        createButton: 'id-leads-components-LeadViewCard-createButton',
        dropdownMenu: 'id-leads-components-LeadViewCard-dropdownMenu',
      },
      ContactUnsubscribed: {
        removeLead: 'id-leads-components-contact-unsubscribed-remove-lead',
      },
      DateSelect: {
        datePickerFrom: 'id-leads-components-DateSelect-datePickerFrom',
        datePickerTo: 'id-leads-components-DateSelect-datePickerTo',
        DateInputFrom: 'id-leads-components-DateSelect-DateInputFrom',
        DateInputTo: 'id-leads-components-DateSelect-DateInputTo',
      },
      selectLeadEmail: 'id-leads-components-select-lead-email',
      selectEngagementPriority:
        'id-leads-components-select-engagement-priority',
      groupBySelect: 'id-leads-components-group-by-select',
      table: {
        engagementPriorityCell: {
          clickedLink:
            'id-leads-components-table-engagement-priority-cell-clicked-link',
          clickedEmail:
            'id-leads-components-table-engagement-priority-cell-clicked-email',
          seeMore:
            'id-leads-components-table-engagement-priority-cell-clicked-seeMore',
        },
      },
      AddLeadButton: {
        dropdown: 'id-leads-components-AddLeadButton-dropdown',
      },
      LeadStatusCell: {
        statusAttribute: 'id-leads-components-LeadStatusCell-statusAttribute',
      },
      ConfirmUnassignContactWithTasksModal: {
        confirmButton:
          'id-leads-components-ConfirmUnassignContactWithTasksModal-confirmButton',
        cancelButton:
          'id-leads-components-ConfirmUnassignContactWithTasksModal-cancelButton',
      },
      LeadContactResultRowItem: {
        assignUser: 'id-leads-components-LeadContactResultRowItem-assignUser',
      },
      EmailBounced: {
        selectLeadEmail: 'id-leads-components-email-bounced-select-lead-email',
        validateButton: 'id-leads-components-email-bounced-validate-button',
      },
      Attribute: {
        enrichButton: 'id-leads-components-Attribute-enrich-button',
        enrichmentProgress: 'id-leads-components-Attribute-enrichment-progress',
        plgCreditTag: 'id-leads-components-Attribute-plgCreditTag',
        enrichButtonTag: 'id-leads-components-Attribute-enrich-button-tag',
      },
      ExternalContactsTableHeader: {
        openFiltersButton:
          'id-leads-components-ExternalContactsTableHeader-open-filters-button',
      },
      activeLeadCategoryToggle:
        'id-leads-components-active-lead-category-toggle',
      viewOption: 'id-leads-components-active-lead-view-option',
      ContactActionButton: {
        popover: 'id-leads-components-ContactActionButton-popover',
        menu: 'id-leads-components-ContactActionButton-menu',
      },
      selectLeadView: {
        editButton: 'id-leads-components-SelectLeadView-edit-button',
        deleteButton: 'id-leads-components-SelectLeadView-delete-button',
        options: 'id-leads-components-SelectLeadView-options',
      },
      AssignCompaniesContactsAndAddTaskModal: {
        userSelector:
          'id-leads-components-AssignCompaniesContactsAndAddTaskModal-user-selector',
      },
      dataHealthLayout: 'id-leads-components-dataHealthLayout',
      export: {
        leadExportFilters: 'id-leads-components-LeadExportFilters',
      },
    },
    hooks: {
      useEnrichLead: {
        buyMoreCreditsButton:
          'id-leads-hooks-useEnrichLead-buy-more-credits-button',
      },
    },
    Search: {
      AddLeadButton: {
        alreadyButton: 'id-leads-search-add-lead-button-already-button',
        addLeadButton: 'id-leads-search-add-lead-button-add-lead-button',
        unarchiveButton: 'id-leads-search-add-lead-button-unarchive-button',
      },
      ContactSidePanelOverview: {
        addLeadButton:
          'id-leads-search-contact-side-panel-overview-add-lead-button',
      },
    },
    filters: {
      engagementPriority: 'id-leads-filters-engagement-priority',
    },
  },
  tasks: {
    taskPage: {
      mainAccordion: 'id-tasks-outreach-page-main-accordion',
      panelRight: {
        tabs: 'id-tasks-outreach-page-panel-right-tabs',
        callTab: 'id-tasks-outreach-page-panel-right-call-tab',
        emailTab: 'id-tasks-outreach-page-panel-right-email-tab',
        calendarButton: 'id-tasks-outreach-page-panel-right-calendar-button',
        callButton: 'id-tasks-outreach-page-panel-right-call-button',
        email: {
          templatesButton:
            'id-tasks-outreach-page-panel-right-email-templates-button',
          sendEmailButton:
            'id-tasks-outreach-page-panel-right-email-send-email-button',
          subjectInput:
            'id-tasks-outreach-page-panel-right-email-subject-input',
          toInput: 'id-tasks-outreach-page-panel-right-email-to-input',
          bodyInput: 'id-tasks-outreach-page-panel-right-email-body-input',
          subject: 'id-tasks-outreach-page-panel-right-email-subject',
        },
        noEmail: 'id-tasks-outreach-page-panel-right-no-email',
      },
    },
    modals: {
      userTaskListAndSchedule: {
        modal: 'id-tasks-modals-user-task-list-and-schedule-modal',
        mainContainer:
          'id-tasks-modals-user-task-list-and-schedule-main-container',
      },
    },
    components: {
      multipleSelectTaskTypeAndDate: {
        unassignLead: 'id-tasks-components-multiple-select-task-type-and-date',
        scheduleFollowUpTask:
          'id-tasks-components-multiple-schedule-follow-up-task',
        callButton: 'id-tasks-components-multiple-call-button',
        emailButton: 'id-tasks-components-multiple-email-button',
        SegmentedTabulation:
          'id-tasks-components-multiple-segmented-tabulation',
        endingLeadTaskDatePickerDateInput:
          'id-tasks-components-multiple-ending-lead-task-DatePicker-date-input',
        endingLeadTaskPopperTimeInput:
          'id-tasks-components-multiple-ending-lead-task-popper-time',
        endingLeadTaskDatePickerTimeInput:
          'id-tasks-components-ending-lead-task-DatePicker-time-input',
        trashButton: 'id-tasks-components-trash-button',
        addTaskButton: 'id-tasks-components-add-task-button',
      },
      selectTaskTypeAndDate: {
        tabulation: 'id-tasks-components-select-task-type-and-date-tabulation',
        tabulationWithLabel:
          'id-tasks-components-select-task-type-and-date-tabulation-with-label',
        callTab: 'id-tasks-components-select-task-type-and-date-call-tab',
        emailTab: 'id-tasks-components-select-task-type-and-date-email-tab',
        dateTimeContainer: 'id-tasks-components-select-task-type-and-date',
        datePicker: 'id-tasks-components-select-task-type-and-date-date-picker',
        timePicker: 'id-tasks-components-select-task-type-and-date-time-picker',
        toggleTimeSwitch:
          'id-tasks-components-select-task-type-and-date-toggle-time-switch',
        scheduleButton:
          'id-tasks-components-select-task-type-and-date-schedule-button',
        cancelButton:
          'id-tasks-components-select-task-type-and-date-cancel-button',
        timmeInput: 'id-tasks-components-select-task-type-and-date-time-input',
        timeSwitch: 'id-tasks-components-select-task-type-and-date-time-switch',
        dateInput: 'id-tasks-components-select-task-type-and-date-date-input',
      },
      note: {
        plusTemplate: 'id-tasks-components-note-plus-template',
        lexical: 'id-tasks-components-note-lexical',
        editButton: 'id-tasks-components-edit-button',
      },
      call: {
        selectTemplateButton: 'id-tasks-components-call-select-template-button',
      },
      headerTitle: {
        viewContactDetailsLink:
          'id-tasks-components-header-title-view-contact-details-link',
      },
      HeaderActions: {
        taskListButton: 'id-tasks-components-header-actions-task-list-button',
        snoozeButton: 'id-tasks-components-header-actions-snooze-button',
        doneButton: 'id-tasks-components-header-actions-done-button',
      },
      TaskListItem: {
        deleteTaskButton:
          'id-tasks-components-task-list-item-delete-task-button',
        scheduleTaskButton: 'id-tasks-components-task-list-item-schedule-task',
        launchTaskButton: 'id-tasks-components-task-list-item-launch-task',
      },
      taskListDrawer: {
        closeButton: 'id-tasks-components-task-list-drawer-close-button',
        backButton: 'id-tasks-components-task-list-drawer-back-button',
      },
      TaskListEmpty: {
        addNewTask: 'id-tasks-components-task-list-empty-add-new-task',
      },
      ContactStrategy: {
        textInput: 'id-tasks-components-ContactStrategy-text-input',
        generateButton: 'id-tasks-components-ContactStrategy-generate-button',
        inputTextarea: 'id-tasks-components-ContactStrategy-input-textarea',
      },
      StrategyGenerationValidationModalContent: {
        cancel:
          'id-tasks-components-StrategyGenerationValidationModalContent-cancel',
        validate:
          'id-tasks-components-StrategyGenerationValidationModalContent-validate',
      },
    },
    pages: {
      taskPage: {
        accordion: 'id-tasks-pages-task-page-accordion',
        taskListButton: 'id-tasks-pages-task-page-task-list-button',
        createTaskButton: 'id-tasks-pages-task-page-create-task-button',
      },
    },
  },
  templates: {
    components: {
      templateCard: 'id-templates-components-template-card',
      drawerTaskSelectTemplate: {
        useTemplateButton:
          'id-templates-components-drawer-task-select-template-use-template-button',
      },
      TemplateLayout: {
        tabulation: 'id-templates-components-template-layout-tabulation',
        cancelButton: 'id-templates-components-template-layout-cancel-button',
        submitButton: 'id-templates-components-template-layout-submit-button',
      },
      TemplateDetail: {
        dropdownMenu: 'id-templates-components-TemplateDetail-dropdownMenu',
      },
      DeleteTemplateModal: {
        cancelButton:
          'id-templates-components-DeleteTemplateModal-cancelButton',
        deleteButton:
          'id-templates-components-DeleteTemplateModal-deleteButton',
      },
    },
  },
  landingPage: {
    column: {
      title: 'id-landing-page-column-title',
      engageButton: 'id-landing-page-column-engagement-button',
      enrichButton: 'id-landing-page-column-enrich-button',
      searchButton: 'id-landing-page-column-search-button',
    },
    extension: {
      button: 'id-landing-page-extension-button',
    },
    onboarding: {
      onboardingStepItemClaimButton:
        'landingPage-validate-onboarding-step-onboardingStepItemClaimButton',
      thumbNavigationButton:
        'landingPage-validate-onboarding-step-thumbNavigationButton',
      onboardingStepDetailsView: {
        navigateBackButton:
          'landingPage-validate-onboarding-step-onboardingStepDetailsView-navigateBackButton',
      },
      closeButton: 'landingPage-close-onboarding-step-button',
      earnTag: 'id-landing-page-validate-onboarding-step-earn-tag',
    },
    video: {
      link: 'link-discover-our-other-videos',
    },
  },
  settings: {
    profile: {
      submitButton: 'id-settings-profile-update-button',
      firstNameInput: 'id-settings-profile-firstName-input',
      lastNameInput: 'id-settings-profile-lastName-input',
      emailInput: 'id-settings-profile-emailInput-input',
      linkedinInput: 'id-settings-profile-linkedinInput-input',
      phoneInput: 'id-settings-profile-phoneInput-input',
      organizationInput: 'id-settings-profile-organizationInput-input',
      selectLanguage: 'id-settings-profile-selectLanguage-select',
      planButton: 'id-settings-profile-planButton-button',
      upgradeAccountButton: 'id-settings-profile-upgradeAccountButton-button',
      managePlanButton: 'id-settings-profile-managePlanButton-button',
      helperPlanButton: 'id-settings-profile-helperPlanButton-button',
      localizationSelect: 'id-settings-profile-localization-select',
      timezoneSelect: 'id-settings-profile-timezone-select',
      legalsButton: 'id-settings-profile-legals-button',
      privacyPolicyButton: 'id-settings-profile-privacy-policy-button',
      termsOfUseButton: 'id-settings-profile-terms-of-use-button',
    },
    organization: {
      addMemberButton: 'id-settings-organization-add-member-button',
      sendInviteButton: 'id-settings-organization-send-invite-button',
    },
    emailsAccounts: {
      connectGoogleAccount: {
        connectButton: 'id-settings-emails-accounts-connect-google-account',
      },
      connectOutlookAccount: {
        connectButton: 'id-settings-emails-accounts-connect-outlook-account',
      },
      connectLinkedinAccount: {
        connectButton: 'id-settings-emails-accounts-connect-linkedin-account',
      },
      downloadExtensionLinkedin:
        'id-settings-emails-accounts-download-extension-linkedin',
      refreshExtensionLinkedin:
        'id-settings-emails-accounts-extension-linkedin',
      refreshCookiesLinkedin:
        'id-settings-emails-accounts-refresh-cookies-linkedin',
      connectedToExtensionLinkedin:
        'id-settings-emails-accounts-connected-to-linkedin',
      linkedinNeedHelp: 'id-settings-emails-accounts-need-help-linkedin',
      linkedinRemoveCancelButton: 'id-settings-emails-accounts-remove-cancel',
      linkedinRemoveConfirmButton: 'id-settings-emails-accounts-remove-confirm',
      linkedinRemoveOpenModal: 'id-settings-emails-accounts-remove-open-modal',
      linkedinContactSupport: 'id-settings-emails-accounts-contact-support',
    },
    domainList: {
      connectEmailAccountButton: 'id-settings-emails-account-connect',
      domainListItem: 'id-settings-emails-account-domain-list-item',
    },
    payment: {
      priceTable: {
        freePlanButton: 'id-settings-payment-price-table-free-plan-button',
        starterPlanButton:
          'id-settings-payment-price-table-starter-plan-button',
        customPlanButton: 'id-settings-payment-price-table-custom-plan-button',
        periodToggle: 'id-settings-payment-price-table-period-toggle',
        monthlyTab: 'id-settings-payment-price-table-monthly-tab',
        annuallyTab: 'id-settings-payment-price-table-annually-tab',
      },
      cards: {
        addMoreCreditsBtn: 'id-settings-payment-cards-add-more-credits',
      },
    },
    crm: {
      list: {
        hubspotConnectButton: 'id-settings-crm-list-hubspot-connect-button',
        hubspotAlreadyConnectedButton:
          'id-settings-crm-list-hubspot-already-connected-button',
        hubspotSyncedTag: 'id-settings-crm-list-hubspot-synced-tag',
        hubspotNotInSyncTag: 'id-settings-crm-list-hubspot-not-in-sync-tag',
        prismaticSyncedTag: 'id-settings-crm-list-prismatic-synced-tag',
        prismaticAlreadyConnectedButton:
          'id-settings-crm-list-prismatic-already-connected-button',
        prismaticConnectButton: 'id-settings-crm-list-prismatic-connect-button',
      },
      push: {
        edit: {
          closeButton: 'id-settings-crm-push-edit-close-button',
          saveButton: 'id-settings-crm-push-edit-save-button',
        },
      },
      selectiveSync: {
        edit: {
          closeButton: 'id-settings-crm-selectiveSync-edit-close-button',
          saveButton: 'id-settings-crm-selectiveSync-edit-save-button',
        },
      },
      mappings: {
        customZeliqFieldInput:
          'id-settings-crm-settings-custom-zeliq-field-input',
        customHubspotFieldSelect:
          'id-settings-crm-settings-custom-hubspot-field-select',
        customSyncRuleSelect:
          'id-settings-crm-settings-custom-sync-rule-select',
        defaultZeliqFieldInput:
          'id-settings-crm-settings-default-zeliq-field-input',
        defaultHubspotFieldSelect:
          'id-settings-crm-settings-default-hubspot-field-select',
        defaultSyncRuleSelect:
          'id-settings-crm-settings-default-sync-rule-select',
        crmStatusSelect: 'id-settings-crm-settings-crm-status-select',
        mainStatusSelect: 'id-settings-crm-settings-main-status-select',
        edit: {
          closeButton: 'id-settings-crm-settings-mappings-edit-close-button',
          saveButton: 'id-settings-crm-settings-mappings-edit-save-button',
        },
        addCustomPropertyButton:
          'id-settings-crm-settings-mappings-add-custom-property-button',
      },
      exclusionRules: {
        selectProperty:
          'id-settings-crm-settings-exclusion-rules-select-property',
        addButton: 'id-settings-crm-settings-exclusion-rules-add-button',
        selectValue: 'id-settings-crm-settings-exclusion-rules-select-value',
        toDatePicker: 'id-settings-crm-settings-exclusion-rules-to-date-picker',
        fromDatePicker:
          'id-settings-crm-settings-exclusion-rules-from-date-picker',
        edit: {
          closeButton:
            'id-settings-crm-settings-exclusion-rules-edit-close-button',
          saveButton:
            'id-settings-crm-settings-exclusion-rules-edit-save-button',
        },
      },
      settingListPage: {
        setUpConfigurationButton:
          'id-settings-crm-settings-set-up-configuration-button',
        turnOffConfigurationButton:
          'id-settings-crm-settings-turn-off-configuration-button',
        turnOnConfigurationButton:
          'id-settings-crm-settings-turn-on-configuration-button',
        backButton: 'id-settings-crm-settings-back-button',
        disconnectButton: 'id-settings-crm-settings-disconnect-button',
        disconnectCancelButton:
          'id-settings-crm-settings-disconnect-cancel-button',
        desychronizeButton: 'id-settings-crm-settings-desychronize-button',
        desychronizeCancelButton:
          'id-settings-crm-settings-desychronize-cancel-button',
        disconnectButtonConfirmation:
          'id-settings-crm-settings-disconnect-button-confirmation',
        desychronizeButtonConfirmation:
          'id-settings-crm-settings-desychronize-button-confirmation',
        editSelectiveSyncCard: 'id-settings-crm-settings-sync-type-card',
        editPushCard: 'id-settings-crm-settings-push-card',
        editMappingsCard: 'id-settings-crm-settings-mappings-card',
        editExclusionRulesCard: 'id-settings-crm-settings-exclusion-rules-card',
      },
    },
    integration: {
      connectButton: 'id-settings-integration-connect-button',
      revealApiKeyButton: 'id-settings-integration-reveal-api-key-button',
      linkToDocumentation: 'id-settings-integration-link-to-documentation',
    },
    faq: {
      container: 'id-settings-faq-container',
      accordion: 'id-settings-faq-accordion',
    },
  },
  sequence: {
    components: {
      leadErrorReview: {
        validation: 'id-sequence-components-lead-error-review-validation',
        menu: 'id-sequence-components-lead-error-review-menu',
        deleteButton: 'id-sequence-components-lead-error-review-delete-button',
        attributes: {
          prefix: 'id-sequence-components-lead-error-attribute',
        },
      },
      stepCardSequence: {
        duplicateButton:
          'id-sequence-components-step-card-sequence-duplicate-button',
        removeButton: 'id-sequence-components-step-card-sequence-remove-button',
      },
      listSequenceItem: {
        switchButton: 'id-sequence-components-list-sequence-item-switch-button',
        dropdownMenu: 'id-sequence-components-list-sequence-item-dropdown-menu',
      },
      stepAddLeads: {
        buttonEmptyStateCreateView:
          'button-create-view-empty-drawer-organization-leads-sequence',
      },
      stepReviewSequence: {
        reviewErrorsButton:
          'id-sequence-components-step-review-sequence-review-errors-button',
        closeDrawerButton:
          'id-sequence-components-step-review-sequence-close-drawer-button',
        removeAllLeadsErrorButton:
          'id-sequence-components-remove-all-leads-error-button',
      },
      modalDeleteLeads: {
        deleteButton: 'id-sequence-components-modal-delete-leads-delete-button',
        cancelButton: 'id-sequence-components-modal-delete-leads-cancel-button',
      },
      addSteps: {
        email: {
          subject: 'id-sequences-components-add-steps-email-subject',
          body: 'id-sequences-components-add-steps-email-body',
        },
        linkedInMessage: {
          message: 'id-sequences-components-add-steps-linkedIn-message',
        },
      },
      sequenceListing: {
        searchInput: 'id-sequences-components-sequence-listing-search-input',
        newSequenceButton:
          'id-sequences-components-sequence-listing-new-sequence-button',
      },
      StepAddStepsSequence: {
        EmailAddStepsSequence: {
          templateSelectionButton:
            'id-sequences-step-add-steps-sequence-email-template-selection-button',
        },
      },
      StepSequenceSetting: {
        settingsAccordion:
          'id-sequences-step-sequence-setting-settings-accordion',
        leadRepliedSwitchButton:
          'id-sequences-step-sequence-setting-lead-replied-switch-button',
        leadUnsubscridedSwitchButton:
          'id-sequences-step-sequence-setting-lead-unsubscribed-switch-button',
        openAdvancedSettingsButton:
          'id-sequences-step-sequence-setting-open-advanced-settings-button',
        leadBouncedSwitchButton:
          'id-sequences-step-sequence-setting-lead-bounced-switch-button',
      },
      StepImportSettingsSequence: {
        switchButton:
          'id-sequences-step-import-settings-sequence-switch-button',
      },
      DrawerScheduleSequenceSettings: {
        day: 'id-sequences-drawer-schedule-sequence-settings-day',
        SelectTime: {
          startDate:
            'id-sequences-drawer-schedule-sequence-settings-select-time-start-date',
          startDateInput:
            'id-sequences-drawer-schedule-sequence-settings-start-date-input',
          endDate: 'id-sequences-drawer-schedule-sequence-settings-end-date',
          endDateInput:
            'id-sequences-drawer-schedule-sequence-settings-end-date-input',
        },
        submitButton:
          'id-sequences-drawer-schedule-sequence-settings-submit-button',
      },
      sequenceHeader: {
        sequenceNameInput: 'id-sequences-new-sequence-title-input',
        submitButton: 'id-sequences-new-sequence-submit-button',
        editButton: 'id-sequences-new-sequence-edit-button',
        cancelButton: 'id-sequences-new-sequence-cancel-button',
      },
      TableShowAddedLeads: {
        selectUserInput: 'id-sequences-table-show-added-leads-select-user',
        floatingButtonAssignToSDR:
          'id-sequences-table-show-added-leads-floating-button-assign-to-sdr',
        floatingButtonRemoveFromSequence:
          'id-sequences-table-show-added-leads-floating-button-remove-from-sequence',
      },
      ModalDeleteSequence: {
        cancelButton: 'id-sequences-modal-delete-sequence-cancel-button',
        deleteButton: 'id-sequences-modal-delete-sequence-delete-button',
      },
      MoreButton: {
        dropdownMenu: 'id-sequences-more-button-dropdown-menu',
      },
      StepAddLeadsScreen: {
        addMoreLeadsButton:
          'id-sequences-step-add-leads-screen-add-more-leads-button',
      },
      EmptyPage: {
        createSequenceButton: 'id-sequences-empty-page-create-sequence-button',
      },
      ContactSequenceStepActivity: {
        replyButton: 'id-sequences-contact-sequence-step-activity-reply-button',
        enlargeButton:
          'id-sequences-contact-sequence-step-activity-en-large-button',
      },
      ErrorLinkedInFieldEmpty: {
        linkedInUrl: 'id-sequences-error-linkedIn-field-empty-linkedIn-url',
      },
      ErrorEmailField: {
        emailInput: 'id-sequences-error-email-field-wrapper-email-input',
        emailSelect: 'id-sequences-error-email-field-wrapper-email-select',
        enrichButton: 'id-sequences-error-email-field-wrapper-enrich-button',
        removeLeadButton:
          'id-sequences-error-email-field-wrapper-remove-lead-button',
        saveLeadButton:
          'id-sequences-error-email-field-wrapper-save-lead-button',
        selectEmailWithEnrichment:
          'id-sequences-error-email-field-select-email',
      },
      ErrorGeneric: {
        contactSupportButton:
          'id-sequences-error-generic-contact-support-button',
      },
      ErrorUnsubscribed: {
        removeLeadButton: 'id-sequences-error-unsubscribed-remove-lead-button',
        saveButton: 'id-sequences-error-unsubscribed-save-button',
      },
      ErrorNoSender: {
        saveButton: 'id-sequences-error-no-sender-save-button',
      },
      ErrorVariables: {
        saveButton: 'id-sequences-error-variables-save-button',
        attributes: 'id-sequences-error-variables-attributes',
        selectEmail: 'id-sequences-error-variables-select-email',
      },
      SequencePageBeta: {
        previousButton: 'id-sequences-sequence-page-beta-previous-button',
        nextButton: 'id-sequences-sequence-page-beta-next-button',
      },
      LeadTableHeader: {
        dropdownMenu: 'id-sequences-lead-table-header-dropdown-menu',
        addLeads: 'id-sequences-lead-table-header-add-leads',
      },
      settingsDrawer: {
        nextExecutionDate: {
          datePicker: 'id-sequences-settings-drawer-date-picker',
        },
      },
    },
    pages: {
      SequenceFormPage: {
        previous: 'id-sequence-pages-sequence-form-page-previous',
        next: 'id-sequence-pages-sequence-form-page-next',
        addMoreLeadsButton:
          'id-sequence-pages-sequence-form-page-add-more-leads-button',
      },
      SequencePage: {
        accordion: 'id-sequence-pages-sequence-page-accordion',
      },
    },
  },
  websocket: {
    enrich: {
      sequences: {
        stepEnrich: {
          source: 'sequence-step-enrich',
        },
      },
    },
  },
  auth: {
    pages: {
      forgotPassword: {
        title: 'id-auth-pages-forgot-password-title',
        description: 'id-auth-pages-forgot-password-description',
        emailInput: 'id-auth-pages-forgot-password-email-input',
        submitButton: 'id-auth-pages-forgot-password-submit-button',
        openGmailButton: 'id-auth-pages-forgot-password-open-gmail-button',
        openOutlookButton: 'id-auth-pages-forgot-password-open-outlook-button',
      },
      confirmForgotPassword: {
        passwordInput: 'id-auth-pages-confirm-forgot-password-password-input',
        confirmPasswordInput:
          'id-auth-pages-confirm-forgot-password-confirm-password-input',
        submitButton: 'id-auth-pages-confirm-forgot-password-submit-button',
        resendCodeButton: 'id-auth-pages-confirm-forgot-password-resend-code',
      },
      signIn: {
        emailInput: 'id-auth-pages-sign-in-email-input',
        passwordInput: 'id-auth-pages-sign-in-password-input',
        submitButton: 'id-auth-pages-sign-in-submit-button',
        signInWithGoogleButton: 'id-auth-pages-sign-in-with-google-btn',
        signInWithMicrosoftButton: 'id-auth-pages-sign-in-with-microsoft-btn',
        signUpButton: 'id-auth-pages-sign-in-signup-btn',
        forgotPasswordButton: 'id-auth-pages-sign-in-forgot-password-btn',
        errorCardButton: 'id-auth-pages-sign-errorCardButton',
      },
      signUp: {
        emailAndTerms: {
          emailInput: 'id-auth-pages-sign-up-emailAndTerms-emailInput',
          termsOfUseCheckbox:
            'id-auth-pages-sign-up-emailAndTerms-termsOfUseCheckbox',
          receiveMarketingCheckbox:
            'id-auth-pages-sign-up-emailAndTerms-receiveMarketingCheckbox',
          submitButton: 'id-auth-pages-sign-up-emailAndTerms-submitButton',
          goToLoginButton:
            'id-auth-pages-sign-up-emailAndTerms-goToLoginButton',
          userExistsError:
            'id-auth-pages-sign-up-emailAndTerms-userExistsError',
          userNotExistsError:
            'id-auth-pages-sign-up-emailAndTerms-userNotExistsError',
          helpButton: 'id-auth-pages-sign-up-emailAndTerms-helpButton',
        },
        createPassword: {
          submitButton: 'id-auth-pages-sign-up-createPassword-submitButton',
        },
        verifyEmail: {
          submitButton: 'id-auth-pages-sign-up-verifyEmail-submitButton',
          resendButton: 'id-auth-pages-sign-up-verifyEmail-resendButton',
          errorResendButton:
            'id-auth-pages-sign-up-verifyEmail-errorResendButton',
        },
        userInfo: {
          firstNameInput: 'id-auth-pages-sign-up-UserInfo-firstNameInput',
          lastNameInput: 'id-auth-pages-sign-up-UserInfo-lastNameInput',
          submitButton: 'id-auth-pages-sign-up-UserInfo-submitButton',
          phoneInput: 'id-auth-pages-sign-up-UserInfo-phoneInput',
          selectLanguage: 'id-auth-pages-sign-up-UserInfo-selectLanguage',
          verifyCodeButton: 'id-auth-pages-sign-up-UserInfo-verifyCodeButton',
        },
        workspaceSetup: {
          organizationNameInput:
            'id-auth-pages-sign-up-workspaceSetup-organizationNameInput',
          websiteUrlInput: 'id-auth-pages-sign-up-workspaceSetup-websiteUrl',
          countryInput: 'id-auth-pages-sign-up-workspaceSetup-countryInput',
          zipcodeInput: 'id-auth-pages-sign-up-workspaceSetup-zipcodeInput',
          provinceInput: 'id-auth-pages-sign-up-workspaceSetup-provinceInput',
          submitButton: 'id-auth-pages-sign-up-workspaceSetup-submitButton',
        },
      },
      blockedUserAccountPage: {
        container: 'id-auth-pages-blockedUserAccountPage-container',
        contactButton: 'id-auth-pages-blockedUserAccountPage-contact-button',
        closeButton: 'id-auth-pages-blockedUserAccountPage-close-button',
      },
    },
    components: {
      passwordForm: {
        passwordInput: 'id-auth-components-passwordForm-passwordInput',
        confirmPasswordInput:
          'id-auth-components-passwordForm-confirmPasswordInput',
        submitButton: 'id-auth-components-passwordForm-submitButton',
      },
      userInfoForm: {
        resendCodeButton: 'id-auth-components-userInfoForm-resendCodeButton',
      },
    },
  },
  lexical: {
    components: {
      dropdown: {
        button: 'id-lexical-components-open-dropdown-button',
        itemButton: 'id-lexical-components-dropdown-item-button',
      },
    },
  },
  business: {
    lead: {
      contact: {
        contactInCompany: {
          addLeadButton: 'id-business-lead-contact-in-company-add-lead-button',
        },
        dropdownButtonContactAssignation: {
          assignLeadButton:
            'id-business-lead-contact-dropdown-button-assign-lead-button',
        },
        EnrichmentSelector: {
          enrichPhone: 'id-business-lead-contact-enrich-phone',
          enrichEmail: 'id-business-lead-contact-enrich-email',
        },
        EnrichmentBill: {
          totalCostTag: 'id-business-lead-contact-total-cost-tag',
          currentCreditTag: 'id-business-lead-contact-current-credit-tag',
          missingCreditTag: 'id-business-lead-contact-missing-credit-tag',
        },
      },
    },
    ContactSequenceActivitiesDrawer: {
      goToSequenceButton:
        'id-business-contact-sequence-activities-go-to-sequence-button',
      removeLeadAndCreateTaskButton:
        'id-business-contact-sequence-activities-drawer-remove-lead-and-create-task-button',
      removeLeadButton:
        'id-business-contact-sequence-activities-drawer-remove-lead-button',
      contactDetailsButton:
        'id-business-contact-sequence-activities-drawer-contact-details-button',
    },
  },
  common: {
    dataDisplay: {
      helper: {
        hideButton: 'id-common-data-display-helper-hide-button',
        endButton: 'id-common-data-display-helper-end-button',
        button: 'id-common-data-display-helper-button',
      },
      Grid: {
        Cells: {
          ContactNameCell: {
            enrichmentTag:
              'id-common-data-display-grid-cells-contact-name-cell-enrichment-tag',
            newDataTag:
              'id-common-data-display-grid-cells-contact-name-cell-new-data-tag',
          },
        },
      },
    },
    dataEntry: {
      popover: {
        popover: 'id-common-data-display-popover-popover',
        popoverTrigger: 'id-common-data-display-popover-popover-trigger',
      },
      phoneInput: {
        callIcon: 'id-common-data-entry-phone-input-call-icon',
        trashIcon: 'id-common-data-entry-phone-input-trash-icon',
      },
      lexical: {
        plugin: {
          floatingLinkEditor: {
            input:
              'id-common-data-entry-lexical-plugin-floating-link-editor-input',
            trashIcon:
              'id-common-data-entry-lexical-plugin-floating-link-editor-trash-icon',
          },
        },
      },
      textInput: {
        trashIcon: 'id-common-data-entry-text-input-trash-icon',
        organizationName: 'id-common-data-entry-text-input-organization-name',
        website: 'id-common-data-entry-text-input-website',
        promotionalCode: 'id-common-data-entry-text-input-promotional-code',
        proBadge: 'id-common-data-entry-text-input-pro-badge',
      },
      countryInput: {
        selectInput: 'id-common-data-entry-select-input-country',
        buttonSubmit: 'id-common-data-entry-button-submit-country',
        inputCGUAccept: 'id-common-data-entry-input-cgu-accept-country',
      },
    },
    feedback: {
      progressBar: 'id-common-data-entry-feedback-progress-bar',
    },
  },
  personality: {
    components: {
      generateProfilePanel: {
        generateProfileButton:
          'id-personality-components-generate-profile-panel-generate-profile-button',
        plgCreditTag:
          'id-personality-components-generate-profile-panel-plgCreditTag',
      },
      noLinkedinUrl: {
        addLinkedinUrlButton:
          'id-personality-components-no-linkedin-url-add-linkedin-url-button',
      },
      personalityFull: {
        accordion: 'id-personality-components-personality-full-accordion',
      },
      personalitySample: {
        viewFullProfileButton:
          'id-personality-components-view-full-profile-button',
      },
      qualities: 'id-personality-components-qualities-tag',
    },
  },
  organization: {
    InvitationsAdministration: {
      autoJoinSwitch:
        'id-organization-invitations-administration-auto-join-switch',
    },
    useInvitationsOrganization: {
      dropdownMenu:
        'id-organization-use-invitations-organization-dropdown-menu',
    },
    useMembersOrganization: {
      dropdownMenu: 'id-organization-use-members-organization-dropdown-menu',
    },
  },
  enrichmentHub: {
    enrichFromCSVButton: 'id-enrichment-hub-enrich-from-csv-button',
    enrichFromSalesNavigatorButton:
      'id-enrichment-hub-enrich-from-sales-navigator-button',
    closeButton: 'id-enrichment-hub-close-button',
    enrichPhoneAndEmailLead:
      'id-enrichment-hub-enrich-phone-and-email-lead-button',
    sliderbox: 'id-enrichment-hub-slider-box',
    talkToSalesButton: 'id-enrichment-hub-talk-to-sales-button',
    launchEnrichmentButton: 'id-enrichment-hub-launch-enrichment-button',
    closeModal: 'id-enrichment-hub-close-modal',
    cancelModalButton: 'id-enrichment-hub-cancel-modal-button',
    quitHubModalButton: 'id-enrichment-hub-quit-hub-modal-button',
    iconTag: 'id-enrichment-hub-icon-tag',
    fromTag: 'id-enrichment-hub-from-tag',
    newDataAvailableTag: 'id-enrichment-hub-new-data-available-tag',
    totalCostCreditTag: 'id-enrichment-hub-total-cost-credit-tag',
    missingCreditTag: 'id-enrichment-hub-missing-credit-tag',
    addToMyLeadsButton: 'id-enrichment-hub-add-to-my-leads-button',
    exportMyLeadsInCsvButton: 'id-enrichment-hub-export-my-leads-in-csv-button',
    startEnrichmentCsvAndImport:
      'id-enrichment-hub-start-enrichment-csv-import-button',
    importLoadingModal: 'id-enrichment-hub-import-loading-modal',
    popover: {
      emailsStats: 'id-enrichment-hub-popover-emails-stats',
    },
    button: {
      leadActionCellEnrichmentHubPendingButton:
        'lead-action-cell-enrichment-hub-pending-button',
      leadActionCellEnrichmentHubConversionButton:
        'lead-action-cell-enrichment-hub-conversion-button',
      previous: 'id-enrichment-hub-previous-button',
    },
  },
  home: {
    banner: {
      discoverButton: 'id-home-banner-discover-button',
      dismissButton: 'id-home-banner-dismiss-button',
    },
    overview: {
      dailyTaskButton: 'id-home-overview-daily-task-button',
      currentSequenceButton: 'id-home-overview-current-sequence-button',
      weeklySuccessButton: 'id-home-overview-weekly-success-button',
    },
    whatsNew: {
      hotLeadsUrgentTag: 'id-home-whatsnew-hot-leads-urgent-tag',
      hotLeadsHighTag: 'id-home-whatsnew-hot-leads-high-tag',
      latestLeads: 'id-home-whatsnew-latest-leads-tag',
      newAvailableData: 'id-home-whatsnew-new-available-data-leads-tag',
      latestLeadsSelect: 'id-home-whatsnew-latest-leads-select',
      goToLeadsButton: 'id-home-whatsnew-go-to-leads-button',
      hotLeadsPopover: 'id-home-whatsnew-hot-leads-popover',
    },
  },
  featureGateway: {
    sequenceLimitBanner: {
      overTheLimitButton:
        'id-feature-gateway-sequence-limit-banner-over-the-limit-button',
      limitNotReachedButton: 'id-feature-gateway-sequence-limit-not-reached',
    },
  },
  toast: {
    closeButton: 'id-toast-close-button',
  },
  importModule: {
    modal: {
      importModule: 'id-import-module-modal',
      button: {
        confirmImport: 'id-import-module-modal-confirm-import-button',
      },
    },
    history: {
      statusTag: 'id-import-module-history-status-tag',
      filterButton: 'id-import-module-history-filter-button',
      continueLeadImportButton:
        'id-import-module-history-continue-leads-button',
    },
  },
}
