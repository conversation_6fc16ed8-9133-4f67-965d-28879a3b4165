export const PaperEnvelope = ({ size = '4rem' }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox={`0 0 64 64`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0 12C0 5.37258 5.37258 0 12 0H52C58.6274 0 64 5.37258 64 12V52C64 58.6274 58.6274 64 52 64H12C5.37258 64 0 58.6274 0 52V12Z"
        fill="#F2FFF0"
      />
      <path
        d="M26.4062 31.669C25.4556 30.8995 24.6499 30.2748 23.8713 29.6139C23.1833 29.0255 22.5134 28.428 21.8616 27.7943C21.246 27.1968 20.7209 26.5631 20.8657 25.5854C20.911 25.2595 20.7028 24.8974 20.6304 24.5443C20.5579 24.1912 20.4674 23.8382 20.4584 23.476C20.4403 22.7246 21.0196 22.2358 21.762 22.3806C22.106 22.4531 22.441 22.607 22.7669 22.7609C25.4918 24.0464 28.2168 25.3229 30.9236 26.6355C33.9292 28.0931 37.0887 29.0255 40.3658 29.632C40.9543 29.7407 41.5518 29.8765 42.1131 30.0666C42.6019 30.2295 42.8916 30.6188 42.8916 31.1711C42.8826 31.6509 42.611 32.0582 42.0316 32.185C38.9626 32.864 36.3644 34.5025 33.7481 36.1049C30.2537 38.2505 26.6506 40.1878 22.8846 41.8173C22.4953 41.9803 22.1151 42.1613 21.7077 42.279C21.2279 42.4148 20.748 42.4058 20.404 41.9441C20.1143 41.5548 20.2139 41.1836 20.3678 40.7582C21.4813 37.7797 23.4277 35.3445 25.3651 32.9002C25.6548 32.52 25.9807 32.1669 26.4062 31.669ZM24.4145 38.5402C28.9681 36.5485 32.8338 33.9956 36.8171 31.669C36.8714 31.6328 36.8533 31.4607 36.8804 31.2616C36.2196 31.0624 35.5316 30.8814 34.8526 30.6641C34.2279 30.4559 33.6033 30.2386 32.9877 29.9942C32.3177 29.7316 31.6478 29.451 30.987 29.1613C30.3261 28.8716 29.6743 28.5548 29.0225 28.256C28.3707 27.9573 27.7098 27.6585 27.0489 27.3598C26.4514 27.0791 25.863 26.7713 25.2474 26.5269C24.6046 26.2643 24.0343 25.8117 23.2376 25.7212C23.3372 25.9566 23.3463 26.0924 23.4187 26.1557C25.1297 27.7671 26.886 29.3062 28.95 30.4559C30.1631 31.1258 30.1541 31.9225 28.9681 32.7463C28.3797 33.1537 27.7822 33.5882 27.3296 34.1314C26.379 35.2901 25.5099 36.5214 24.6227 37.7345C24.5594 37.8612 24.5503 38.0513 24.4145 38.5402Z"
        fill="#326E5E"
      />
    </svg>
  )
}
