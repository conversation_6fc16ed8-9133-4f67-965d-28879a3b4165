import { clsx } from 'clsx'

import type { TagColor } from '@getheroes/ui'
import { Tag } from '@getheroes/ui'

interface RecommendationPanelProps {
  title: string
  elements: string[] | undefined
  cutEndLine?: boolean
  bgColor: string
  textColor: string
  tagColor?: TagColor
}

export const RecommendationPanel = ({
  title,
  elements,
  bgColor,
  textColor,
  cutEndLine = true,
  tagColor,
}: RecommendationPanelProps) => {
  if (!elements) {
    return null
  }

  return (
    <div className={clsx('flex flex-col p-3 rounded-xl', bgColor)}>
      <div className={clsx('flex gap-2 items-center mb-3', textColor)}>
        <Tag
          variant={'outlined'}
          icon={'Check'}
          size={'small'}
          color={tagColor}
        />
        <p className="body-m-medium">{title}</p>
      </div>
      {elements.map(element =>
        cutEndLine ? (
          <p
            key={element}
            title={element}
            className="body-s-regular text-textSubtle line-clamp-1"
          >
            {element}
          </p>
        ) : (
          <p key={element} className="body-s-regular text-textSubtle">
            {element}
          </p>
        )
      )}
    </div>
  )
}
