import { useEffect, useMemo } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Company, Organization } from '@getheroes/shared'
import { useLazySearchCompaniesQuery } from '@internals/features/lead/api/companyApi'
import type { SearchLeadsPayloadType } from '@internals/features/lead/types/genericLeadType'
import { useTypedSelector } from '@internals/store/store'

const DEFAULT_NB_COMPANIES_OPTIONS = 25

export const useSearchCompanyName = (searchTerm: string) => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const [searchCompanies, { data }] = useLazySearchCompaniesQuery()
  const companies = useMemo(() => data?.items || {}, [data])

  useEffect(() => {
    const payload: SearchLeadsPayloadType = {
      organizationId,
      searchText: searchTerm,
      page: 1,
      limitPerPage: DEFAULT_NB_COMPANIES_OPTIONS,
    }
    searchCompanies(payload)
  }, [searchTerm, organizationId, searchCompanies])

  return useMemo(
    () => ({
      companies: Object.values(companies as Company[]),
    }),
    [companies]
  )
}
