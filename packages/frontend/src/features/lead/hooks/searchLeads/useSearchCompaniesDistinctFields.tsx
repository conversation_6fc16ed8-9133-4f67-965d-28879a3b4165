import { useEffect, useMemo } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { useLazySearchCompaniesDistinctFieldsQuery } from '@internals/features/lead/api/companyApi'
import type { SearchCompaniesDistinctFieldsPayloadType } from '@internals/features/lead/types/genericLeadType'
import { useTypedSelector } from '@internals/store/store'

const LIMIT_PER_PAGE = 20

interface UseSearchCompaniesDistinctFieldsProps {
  searchTerm: string
  field: string
}
export const useSearchCompaniesDistinctFields = ({
  searchTerm,
  field,
}: UseSearchCompaniesDistinctFieldsProps) => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const [searchCompaniesDistinctFields, { data, isFetching, isLoading }] =
    useLazySearchCompaniesDistinctFieldsQuery()

  useEffect(() => {
    const payload: SearchCompaniesDistinctFieldsPayloadType = {
      organizationId,
      searchText: searchTerm,
      distinctField: field,
      limitPerPage: LIMIT_PER_PAGE,
    }
    searchCompaniesDistinctFields(payload)
  }, [searchTerm, organizationId, field, searchCompaniesDistinctFields])

  const distinctFields = useMemo(() => data?.items || [], [data])

  return useMemo(
    () => ({ distinctFields, isFetching, isLoading }),
    [distinctFields, isFetching, isLoading]
  )
}
