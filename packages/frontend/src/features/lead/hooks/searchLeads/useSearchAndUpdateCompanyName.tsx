import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentLeadCategory } from '@getheroes/frontend/config/store/selectors/leadSelectors'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Contact, Company, Organization } from '@getheroes/shared'
import { Icon } from '@getheroes/ui'
import type { SelectItemProps } from '@internals/components/common/dataEntry/SelectMenu/SelectMenuItem'
import { useLazySearchCompaniesQuery } from '@internals/features/lead/api/companyApi'
import { useEditLead } from '@internals/features/lead/hooks/useEditLead'
import type { SearchLeadsPayloadType } from '@internals/features/lead/types/genericLeadType'
import { useTypedSelector } from '@internals/store/store'
import { capitalizeFirstLetter } from '@internals/utils/string'

interface UseSearchAndUpdateCompanyNameProps {
  contact: Contact
  searchTerm: string
  handleCreateCompany: () => void
}

interface UseSearchAndUpdateCompanyNameResult {
  items: SelectItemProps[]
  isUpdateLoading: boolean
}

const DEFAULT_NB_COMPANIES_OPTIONS = 5

export const useSearchAndUpdateCompanyName = ({
  searchTerm,
  contact,
  handleCreateCompany,
}: UseSearchAndUpdateCompanyNameProps): UseSearchAndUpdateCompanyNameResult => {
  const { t } = useTranslation('lead')
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const [searchCompanies, { data }] = useLazySearchCompaniesQuery()
  const companies = data?.items || {}

  const currentLeadCategory = useTypedSelector(selectCurrentLeadCategory)
  const [editLead, { isLoading: isUpdateLoading }] = useEditLead({
    leadCategory: currentLeadCategory,
  })

  useEffect(() => {
    const payload: SearchLeadsPayloadType = {
      organizationId,
      searchText: searchTerm,
      page: 1,
      limitPerPage: DEFAULT_NB_COMPANIES_OPTIONS,
    }
    searchCompanies(payload)
  }, [searchTerm, organizationId, searchCompanies])

  const updateCompany = (companyId: string) => {
    const company: Company | undefined = companies?.[companyId]
    if (contact.company.id === companyId || !company) {
      return
    }
    editLead(contact.id, { companyId })
  }

  // Sort the items to have the active option first, then alphabetically
  const items: SelectItemProps[] = !companies
    ? []
    : Object.values(companies as Company[])
        .map((company: Company) => ({
          key: company.id,
          label: company?.name ? capitalizeFirstLetter(company?.name) : '',
          onClick: () => updateCompany(company.id),
          isActive: contact?.company
            ? contact.company?.id === company.id
            : false,
        }))
        .sort((a, b) => a.label.localeCompare(b.label))
        .sort((a, b) => (a.isActive ? 0 : 1) - (b.isActive ? 0 : 1))

  if (searchTerm.length) {
    items.unshift({
      key: 'addCompany',
      label: t('Add {{searchTerm}}', {
        searchTerm: capitalizeFirstLetter(searchTerm),
      }) as string,
      onClick: handleCreateCompany,
      isActive: false,
      isDisabled: !searchTerm.length,
      startIcon: <Icon name={'PlusCircle'} size={'small'} />,
    })
  }

  return { items, isUpdateLoading }
}
