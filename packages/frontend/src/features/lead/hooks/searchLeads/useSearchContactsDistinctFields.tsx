import { useEffect, useMemo } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { ContactLeadFieldEnum } from '@getheroes/frontend/types'
import type { Organization } from '@getheroes/shared'
import { useLazySearchContactsDistinctFieldsQuery } from '@internals/features/lead/api/contactApi'
import type { SearchContactsDistinctFieldsPayloadType } from '@internals/features/lead/types/genericLeadType'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import type { LeadFilterApiType } from '@internals/models/lead'
import { useTypedSelector } from '@internals/store/store'

const LIMIT_PER_PAGE = 100

interface UseSearchContactsDistinctFieldsProps {
  searchTerm: string
  field: string
}
export const useSearchContactsDistinctFields = ({
  searchTerm,
  field,
}: UseSearchContactsDistinctFieldsProps) => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const [searchContactsDistinctFields, { data, isLoading, isFetching }] =
    useLazySearchContactsDistinctFieldsQuery()

  const filters = useMemo<Array<LeadFilterApiType>>(() => {
    if (!searchTerm) {
      return []
    }
    return [
      {
        field: field as unknown as ContactLeadFieldEnum,
        operator: OperatorFilterType.CONTAINS,
        value: searchTerm,
      },
    ] satisfies Array<LeadFilterApiType>
  }, [field, searchTerm])

  useEffect(() => {
    const payload: SearchContactsDistinctFieldsPayloadType = {
      organizationId,
      distinctField: field,
      limitPerPage: LIMIT_PER_PAGE,
      filters,
    }
    searchContactsDistinctFields(payload)
  }, [searchTerm, organizationId, field, searchContactsDistinctFields, filters])

  return useMemo(
    () => ({
      distinctFields: data?.items || [],
      isLoading,
      isFetching,
    }),
    [data, isLoading, isFetching]
  )
}
