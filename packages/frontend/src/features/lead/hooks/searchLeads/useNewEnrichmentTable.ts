import type { UseQueryHookResult } from '@reduxjs/toolkit/dist/query/react/buildHooks'
import { useMemo } from 'react'
import { useLocation } from 'react-router-dom'

import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import type { Organization } from '@getheroes/shared'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import {
  useSearchContactsQuery,
  useSearchMyContactsQuery,
} from '@internals/features/lead/api/contactApi'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { privateRoutes } from '@internals/hooks/useRoute'
import type { GenericResponseError } from '@internals/models/errors'
import { useTypedSelector } from '@internals/store/store'

type NewEnrichmentTableReturnType = {
  data: any
  refetch: () => void
  isLoading: boolean
  isError: boolean
  isFetching: boolean
  isUninitialized: boolean
  isSuccess: boolean
  error?: GenericResponseError
}

const NO_QUERY_HOOK_RESULT: NewEnrichmentTableReturnType = {
  data: null,
  refetch: () => {
    return
  },
  isLoading: false,
  isFetching: false,
  isError: false,
  isUninitialized: true,
  isSuccess: false,
}

/**
 * Retrieves all new enrichment available leads.
 *
 * @returns {NewEnrichmentTableReturnType} The result object containing leads data and query status.
 */
export const useNewEnrichmentTable = (): NewEnrichmentTableReturnType => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const currentUser = useTypedSelector(selectCurrentUser)
  const location = useLocation()
  const pageContext = location.pathname
  const { currentPage, pageSize, debouncedSearchText, sort } = useGridContext()

  const queryHook = useMemo(() => {
    let queryHook: UseQueryHookResult<any, any>

    if (
      pageContext.includes(privateRoutes.myLeadsContact.path) ||
      pageContext.includes(privateRoutes.myLeadsCompany.path) ||
      pageContext.includes(privateRoutes.landingPage.path)
    ) {
      queryHook = useSearchMyContactsQuery
    }

    if (
      pageContext.includes(privateRoutes.allLeadsContact.path) ||
      pageContext.includes(privateRoutes.allLeadsCompany.path)
    ) {
      queryHook = useSearchContactsQuery
    }

    return queryHook
  }, [pageContext])

  const {
    data,
    refetch,
    isLoading,
    isFetching,
    isError,
    isUninitialized,
    isSuccess,
    error,
  } = queryHook
    ? queryHook(
        {
          organizationId,
          searchText: debouncedSearchText,
          page: currentPage,
          limitPerPage: pageSize,
          order: sort.sortType,
          orderBy: sort.sortBy,
          filters: [
            {
              field:
                ExclusiveContactLeadFieldEnum.ENRICHMENT_NEW_DATA_AVAILABLE,
              operator: OperatorFilterType.EQUALS,
              value: 'true',
            },
            {
              field: ExclusiveContactLeadFieldEnum.ENRICHMENT_STATUS,
              operator: OperatorFilterType.NOT_CONTAINS,
              value: 'IN_PROGRESS',
            },
          ],
          userId: pageContext.includes(privateRoutes.allLeadsContact.path)
            ? undefined
            : currentUser?.id,
        },
        {
          skip: !currentUser?.id || false,
        }
      )
    : NO_QUERY_HOOK_RESULT

  return useMemo(
    () => ({
      data,
      refetch,
      isLoading,
      isFetching,
      isError,
      error,
      isUninitialized,
      isSuccess,
    }),
    [
      data,
      refetch,
      isLoading,
      isFetching,
      isError,
      error,
      isUninitialized,
      isSuccess,
    ]
  )
}
