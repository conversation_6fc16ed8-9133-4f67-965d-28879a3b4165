import { useEffect, useMemo } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { LeadCategory } from '@getheroes/shared'
import { useLazyGetCompanyTableColumnsQuery } from '@internals/features/lead/api/companyApi'
import { useLazyGetContactTableColumnsQuery } from '@internals/features/lead/api/contactApi'
import { useTypedSelector } from '@internals/store/store'

type UseLeadFieldsQueryProps = {
  leadCategory: LeadCategory
  preferredCacheValue?: boolean
}

/**
 * Hook to get lead fields from the API
 * @param leadCategory - The category of the lead
 * @param preferredCacheValue - The preferred value to get from the cache
 * @returns The result of the query
 * @example
 * const { data, isLoading, isFetching, isError, isSuccess } = useLeadFieldsQuery({
 *  leadCategory: LeadCategory.CONTACT,
 * })
 *
 * The data result is matched in TRK Query extra reducer in leadSlice.ts
 */
export const useLeadFieldsQuery = ({
  leadCategory,
  preferredCacheValue = false,
}: UseLeadFieldsQueryProps) => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const [triggerContact, resultContact] = useLazyGetContactTableColumnsQuery()
  const [triggerCompany, resultCompany] = useLazyGetCompanyTableColumnsQuery()

  useEffect(() => {
    switch (leadCategory) {
      case LeadCategory.CONTACT:
        triggerContact({ organizationId }, preferredCacheValue)
        break
      case LeadCategory.COMPANY:
        triggerCompany({ organizationId }, preferredCacheValue)
        break
    }
  }, [
    triggerContact,
    triggerCompany,
    organizationId,
    leadCategory,
    preferredCacheValue,
  ])

  return useMemo(() => {
    switch (leadCategory) {
      case LeadCategory.CONTACT:
        return resultContact
      case LeadCategory.COMPANY:
        return resultCompany
    }
  }, [leadCategory, resultContact, resultCompany])
}
