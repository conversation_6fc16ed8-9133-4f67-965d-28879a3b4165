import type { ColDef } from 'ag-grid-community'
import isEmpty from 'lodash/isEmpty'
import orderBy from 'lodash/orderBy'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import {
  selectCurrentPageContext,
  selectLeadsField,
} from '@getheroes/frontend/config/store/selectors/leadSelectors'
import type {
  ContactLeadFieldEnum,
  ContactOrCompanyLeadFieldId,
} from '@getheroes/frontend/types'
import {
  CommonLeadFieldEnum,
  ExclusiveContactLeadFieldEnum,
} from '@getheroes/frontend/types'
import type { Contact } from '@getheroes/shared'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { WrapperCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/WrapperCell'
import type {
  GridColDef,
  RowData,
} from '@internals/components/common/dataDisplay/Grid/types/grid'
import { formatLeadsFieldLabel } from '@internals/features/lead/components/SelectLeadsActiveFields/formatLeadsFieldLabel'
import { contactsColumnOrderByPageContext } from '@internals/features/lead/config/leadsFieldConfig'
import { getLeadColumnOrder } from '@internals/features/lead/hooks/leadsTableColumns/getLeadColumnOrder'
import { CONTACT_SPECIAL_FORMATTING_FIELDS } from '@internals/features/lead/hooks/leadsTableColumns/specialFormattingAndMergeFields'
import { useLeadsTableFeatureGateway } from '@internals/features/lead/hooks/leadsTableColumns/useLeadsTableFeatureGateway'
import { LeadPageContext } from '@internals/features/lead/types/genericLeadType'
import type { LeadsField } from '@internals/features/lead/types/leadsTableColumn'
import { KindLeadEnum } from '@internals/features/lead/types/leadsTableColumn'
import {
  getContactColumn,
  getStateColumnToLocalStorage,
} from '@internals/features/lead/utils/columns/contact/getContactColumn'
import { useTypedSelector } from '@internals/store/store'
import { LocalStorageType } from '@internals/types/localStorage'
import { capitalizeFirstLetter } from '@internals/utils/string'

import { ContactSequenceCell } from '../../components/tables/components/Cells/ContactSequenceCell/ContactSequenceCell'

export const useContactColumns = () => {
  const { t } = useTranslation([
    'lead',
    'common',
    'sequence',
    'utils',
    'hubspot',
  ])
  const currentPageContext = useTypedSelector(selectCurrentPageContext)
  const leadsFields: Record<ContactLeadFieldEnum, LeadsField> =
    useTypedSelector(selectLeadsField)

  const { name } = useGridContext()

  const { engagementScoreFeatureGatewayLabel } = useLeadsTableFeatureGateway()

  const columns: ColDef[] = useMemo(() => {
    let extraColumns: GridColDef[] = []
    let defaultColumns: GridColDef[] = []
    const columnState = JSON.parse(
      localStorage.getItem(LocalStorageType.GRID_CACHE) as string
    )?.[name]

    const leadFieldsColumnOrderConfiguration: ContactOrCompanyLeadFieldId[] =
      currentPageContext?.page
        ? contactsColumnOrderByPageContext[currentPageContext.page]
        : []

    if (
      currentPageContext?.page !== LeadPageContext.SEARCH_CONTACTS &&
      !isEmpty(leadsFields)
    ) {
      defaultColumns = Object.values(leadsFields)
        .filter(
          field => field.type !== undefined // ArchiveById, AssignedUser.id not type string, number etc...
        )
        .filter(field => !field.isHidden)
        .map(field => {
          let params = {}
          const columnOrder = columnState?.findIndex(
            item => item.colId === field.id
          )
          if (
            CONTACT_SPECIAL_FORMATTING_FIELDS.includes(
              field.id as ContactLeadFieldEnum
            )
          ) {
            // Socials cell
            params = {
              tooltipText: t(
                field.id === CommonLeadFieldEnum.ENRICHED_LINKEDIN_URL
                  ? 'Enriched personal linkedin'
                  : 'Personal linkedin',
                {
                  ns: 'lead',
                }
              ) as string,
              className: 'w-full flex items-center justify-center',
              label: ' ',
              icon: 'Linkedin',
            }
          }

          return getContactColumn({
            gridName: name,
            label: capitalizeFirstLetter(
              t(formatLeadsFieldLabel(field.id), { ns: 'lead' })
            ),
            engagementScoreFeatureGatewayLabel:
              field.id ===
              ExclusiveContactLeadFieldEnum.ENGAGEMENT_SCORING_PRIORITY
                ? engagementScoreFeatureGatewayLabel
                : undefined,
            field: field,
            ...params,
            order:
              columnOrder && columnOrder !== -1
                ? columnOrder
                : getLeadColumnOrder({
                    leadFieldsColumnOrderConfiguration,
                    fieldId: field.id,
                    isCustomField: field.kind === KindLeadEnum.CUSTOM,
                    isEnrichedField: field?.isEnrichment,
                  }),
            pageContext: currentPageContext?.page as LeadPageContext,
            t,
          }) as ColDef
        })
    }
    if (
      [LeadPageContext.MY_LEADS, LeadPageContext.ALL_LEADS].includes(
        currentPageContext?.page as LeadPageContext
      )
    ) {
      const columnOrder = columnState?.findIndex(
        item => item.colId === 'Sequence status'
      )
      extraColumns = [
        {
          sortable: false,
          filter: false,
          cellRendererParams: {
            order: columnOrder && columnOrder !== -1 ? columnOrder : 7,
          },
          cellRenderer: ({ data }: { data: RowData }) => {
            const lead = data as Contact
            return (
              <WrapperCell data={lead}>
                <ContactSequenceCell contact={lead} />
              </WrapperCell>
            )
          },
          field: 'Sequences status',
          headerName: t('Sequences', { ns: 'sequence' }) as string,
          minWidth: 170,
          width: 170,
          ...getStateColumnToLocalStorage(name, 'Sequences status'),
        },
      ]
    }

    const allColumns = [...defaultColumns, ...extraColumns]

    return orderBy(allColumns, ['cellRendererParams.order'], ['asc'])
  }, [
    name,
    currentPageContext?.page,
    leadsFields,
    t,
    engagementScoreFeatureGatewayLabel,
  ])

  return { columns }
}
