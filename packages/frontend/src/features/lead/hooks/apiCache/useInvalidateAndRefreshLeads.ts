import { useCallback } from 'react'

import { api } from '@getheroes/frontend/config/api'
import { selectGlobalState } from '@getheroes/frontend/config/store/selectors/enrichmentSelectors'
import type {
  Contact,
  Company,
  TransformedMatchingContactsResponseType,
} from '@getheroes/shared'
import { getCompanyEndpoints } from '@internals/features/lead/hooks/apiCache/tags/companyTags'
import { getContactEndpoints } from '@internals/features/lead/hooks/apiCache/tags/contactTags'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'

interface List<T> {
  items: Record<string, T>
}

export const useInvalidateAndRefreshLeads = () => {
  const state = useTypedSelector(selectGlobalState)
  const dispatch = useAppDispatch()

  const invalidateAndRefreshCompanies = useCallback(
    (companiesList: Record<string, Partial<Company>>) => {
      if (!state) {
        return
      }
      const companies = Object.entries(companiesList).map(([id, company]) => ({
        id,
        ...company,
      })) as Company[]

      return companies.forEach(company => {
        const endpoints = getCompanyEndpoints(state, company)
        endpoints.forEach(({ originalArgs, endpointName }) => {
          switch (endpointName) {
            case 'searchMyCompanies':
            case 'searchCompanies':
              dispatch(
                api.util.updateQueryData(
                  endpointName as never,
                  originalArgs as never,
                  (draft: List<Company>) => {
                    if (draft.items[company.id]) {
                      Object.assign(draft.items[company.id], {
                        ...draft.items[company.id],
                        ...company,
                      })
                    }
                  }
                )
              )
              break
            case 'searchAllCompanies':
              dispatch(
                api.util.updateQueryData(
                  endpointName as never,
                  originalArgs as never,
                  (draft: List<Company>) => {
                    const index = draft.items.findIndex(
                      (companyItem: Company) => {
                        return (
                          companyItem.id === company.id ||
                          companyItem.externalId === company.externalId
                        )
                      }
                    )
                    if (index !== -1) {
                      Object.assign(draft.items[index], {
                        ...draft.items[index],
                        ...company,
                      })
                    }
                  }
                )
              )
              break
            case 'getCompany':
              dispatch(
                api.util.updateQueryData(
                  endpointName as never,
                  originalArgs as never,
                  (draft: Contact) => {
                    Object.assign(draft, {
                      ...draft,
                      ...company,
                    })
                  }
                )
              )
              break
          }
        })
      })
    },
    [state, dispatch]
  )

  const invalidateAndRefreshContacts = useCallback(
    (contactsList: Record<string, Partial<Contact>>) => {
      if (!state) {
        return
      }
      const contacts = Object.entries(contactsList).map(([id, contact]) => ({
        id,
        ...contact,
      })) as Contact[]

      contacts.forEach(contact => {
        const endpoints = getContactEndpoints(state, contact.id)

        endpoints.forEach(({ originalArgs, endpointName }) => {
          switch (endpointName) {
            case 'searchMyContacts':
            case 'searchContacts':
              dispatch(
                api.util.updateQueryData(
                  endpointName as never,
                  originalArgs as never,
                  (draft: List<Contact>) => {
                    if (draft.items[contact.id]) {
                      Object.assign(draft.items[contact.id], {
                        ...draft.items[contact.id],
                        ...contact,
                        company: {
                          ...draft.items[contact.id].company,
                          ...contact.company,
                        },
                      })
                    }
                  }
                )
              )
              break
            case 'searchMatchingContacts':
              dispatch(
                api.util.updateQueryData(
                  endpointName as never,
                  originalArgs as never,
                  (draft: TransformedMatchingContactsResponseType) => {
                    draft.forEach(({ matches }) => {
                      Object.entries(matches).forEach(([id, match]) => {
                        if (contactsList[id]) {
                          Object.assign(match.contact, {
                            ...match.contact,
                            ...contactsList[id],
                          })
                        }
                      })
                    })
                  }
                )
              )
              break
            case 'searchAllContacts':
              dispatch(
                api.util.updateQueryData(
                  endpointName as never,
                  originalArgs as never,
                  (draft: List<Contact>) => {
                    const index = draft.items.findIndex(
                      (contactItem: Contact) => {
                        return (
                          contactItem.id === contact.id ||
                          contactItem.externalId === contact.externalId
                        )
                      }
                    )
                    if (index !== -1) {
                      Object.assign(draft.items[index], {
                        ...draft.items[index],
                        ...contact,
                        company: {
                          ...draft.items[index].company,
                          ...contact.company,
                        },
                      })
                    }
                  }
                )
              )
              break
            case 'getContact':
              dispatch(
                api.util.updateQueryData(
                  endpointName as never,
                  originalArgs as never,
                  (draft: Contact) => {
                    Object.assign(draft, {
                      ...draft,
                      ...contact,
                      company: {
                        ...draft.company,
                        ...contact.company,
                      },
                    })
                  }
                )
              )
              break
          }

          const companies = contacts.map(contact => contact.company)
          invalidateAndRefreshCompanies(
            companies.reduce(
              (acc, company) => {
                acc[company.id] = company
                return acc
              },
              {} as Record<string, Partial<Company>>
            )
          )
        })
      })
    },
    [state, invalidateAndRefreshCompanies, dispatch]
  )

  return { invalidateAndRefreshContacts, invalidateAndRefreshCompanies }
}
