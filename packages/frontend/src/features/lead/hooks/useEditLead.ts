import { useMemo } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { LeadCategory } from '@getheroes/shared'
import { useEditCompanyByIdMutation } from '@internals/features/lead/api/companyApi'
import { useEditContactByIdMutation } from '@internals/features/lead/api/contactApi'
import type { EditCompanyPayloadType } from '@internals/features/lead/types/companyType'
import type { EditContactPayloadType } from '@internals/features/lead/types/contactType'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'

type EditContactMutationResultType = ReturnType<
  typeof useEditContactByIdMutation
>[1]
type EditCompanyMutationResultType = ReturnType<
  typeof useEditCompanyByIdMutation
>[1]
export type EditLeadMutationResultType =
  | EditContactMutationResultType
  | EditCompanyMutationResultType

export type EditLeadType = (
  leadId: string,
  args:
    | (Omit<EditContactPayloadType, 'organizationId' | 'contactId'> & {
        companyId?: string
      })
    | Omit<EditCompanyPayloadType, 'organizationId' | 'companyId'>
) => EditLeadMutationResultType

export const useEditLead = ({
  leadCategory,
}: {
  leadCategory: LeadCategory
}): [EditLeadType, EditLeadMutationResultType] => {
  const dispatch = useAppDispatch()
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const [editContact, resultEditContact] = useEditContactByIdMutation()
  const [editCompany, resultEditCompany] = useEditCompanyByIdMutation()

  const result = useMemo(() => {
    return leadCategory === LeadCategory.CONTACT
      ? resultEditContact
      : resultEditCompany
  }, [leadCategory, resultEditCompany, resultEditContact])

  return useMemo(
    () => [
      async (leadId, args) => {
        if (leadCategory === LeadCategory.CONTACT) {
          await editContact({
            ...args,
            contactId: leadId,
            organizationId,
          } as EditContactPayloadType)

          return
        }
        if (leadCategory === LeadCategory.COMPANY) {
          return editCompany({
            ...args,
            companyId: leadId,
            organizationId,
          } as EditCompanyPayloadType)
        }
        throw new Error("Lead category doesn't exist")
      },
      result,
    ],
    [dispatch, editCompany, editContact, leadCategory, organizationId, result]
  )
}
