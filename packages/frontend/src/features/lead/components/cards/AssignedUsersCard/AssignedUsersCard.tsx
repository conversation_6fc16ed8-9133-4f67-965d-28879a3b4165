import { isEmpty } from 'lodash'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { setAssignAccountFilter } from '@getheroes/frontend/config/store/slices/leadSlice'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import type { Organization } from '@getheroes/shared'
import { Icon, Typography } from '@getheroes/ui'
import { CollectionCard } from '@internals/features/lead/components/cards/components/CollectionCard/CollectionCard'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { useGetUsersAssignationsQuery } from '@internals/features/organization/api/organizationApi'
import { useFiltersParam } from '@internals/hooks/useFiltersParam'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'
import { getPersonFullName } from '@internals/utils/string'

export const AssignedUsersCard = () => {
  const dispatch = useAppDispatch()
  const { t } = useTranslation('lead')
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const { data: usersAssignation, isLoading } = useGetUsersAssignationsQuery({
    organizationId,
    meta: true,
  })
  const { setFilters } = useFiltersParam()

  const buildAssignationCollection = useMemo(() => {
    if (!isEmpty(usersAssignation) && usersAssignation !== undefined) {
      return usersAssignation.map(assignation => {
        const { id: memberId, firstName, lastName } = assignation.member
        const { leads } = assignation.meta
        return (
          <button
            key={memberId}
            className={
              'flex gap-2 items-center justify-between rounded-m min-h-rowItem max-h-min hover:bg-interactionBaseActive overflow-hidden px-3'
            }
            onClick={() => {
              setFilters([
                {
                  field: ExclusiveContactLeadFieldEnum.ASSIGNED_USER_ID,
                  operator: OperatorFilterType.ANY_OF_VALUES,
                  values: [memberId],
                },
              ])
              dispatch(setAssignAccountFilter(memberId))
            }}
          >
            <div className={'flex items-center'}>
              <Icon name={'User'} />
              <p>
                <Typography
                  variant={'body'}
                  size={'s'}
                  whitespace={'nowrap'}
                  weight={'medium'}
                  isTruncate
                >
                  {getPersonFullName({ firstName, lastName })}
                </Typography>
              </p>
            </div>
            <div className={'flex gap-2 items-center shrink-0'}>
              <Icon name={'UserCircle'} size={'small'} color={'base-subtle'} />
              <Typography variant={'label'} size={'s'} color={'base-subtle'}>
                {leads.nbContacts}
              </Typography>
              <Typography variant={'label'} size={'s'} color={'base-subtle'}>
                {'|'}
              </Typography>
              <Icon name={'Building'} size={'small'} color={'base-subtle'} />
              <Typography variant={'label'} size={'s'} color={'base-subtle'}>
                {leads.nbCompanies}
              </Typography>
            </div>
          </button>
        )
      })
    } else {
      return <></>
    }
  }, [dispatch, setFilters, usersAssignation])

  return (
    <CollectionCard
      header={
        <div className="flex">
          <Typography variant="label" size="s">
            {t('Assigned accounts')}
          </Typography>
        </div>
      }
      collection={buildAssignationCollection}
      isLoading={isLoading}
    />
  )
}
