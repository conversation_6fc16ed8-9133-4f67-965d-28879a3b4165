import type { MouseEvent } from 'react'
import { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router-dom'

import { AllLeadsEvents } from '@getheroes/frontend/hooks'
import { RegexClass } from '@getheroes/shared'
import { Button } from '@getheroes/ui'
import { ContactSequenceStepProgressContent } from '@internals/features/lead/components/ContactSequenceStepProgressContent/ContactSequenceStepProgressContent'
import { PROGRESS_CIRCULAR_SIZE } from '@internals/features/lead/components/ContactSequenceStepProgressContent/ContactSequenceStepProgressContent-export'
import { getContactSequenceStepProgressTitle } from '@internals/features/lead/components/ContactSequenceStepProgressContent/utils/getContactSequenceStepProgressTitle'
import { getCurrentStepOrder } from '@internals/features/lead/components/ContactSequenceStepProgressContent/utils/getCurrentStepOrder'
import { getIsErrorContactInSequence } from '@internals/features/lead/components/ContactSequenceStepProgressContent/utils/getIsErrorContactInSequence'
import {
  LEFT_PIXELS_OFFSET,
  MAX_CONTACT_SEQUENCE_STEPS,
} from '@internals/features/lead/components/ContactSequenceStepProgressList/ContactSequenceStepProgressList-export'
import { LeadPageContext } from '@internals/features/lead/types/genericLeadType'
import type {
  LeadBySequence,
  SequencesContactSteps,
} from '@internals/features/sequence/types/sequence'
import { useNavigateApp } from '@internals/hooks/useNavigateApp'
import { privateRoutes } from '@internals/hooks/useRoute'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'
import { idReferentials } from '@internals/utils/idReferentials'

const SEQUENCE_NUMBER_BADGE_WIDTH = 24

export type ContactSequenceStepProgressListProps = {
  contactSequenceStepList: SequencesContactSteps[]
  onClick?: (e: MouseEvent) => void
  review?: LeadBySequence['review']
  sequenceId?: string
}

export const ContactSequenceStepProgressList = ({
  contactSequenceStepList,
  onClick,
  review,
  sequenceId,
}: ContactSequenceStepProgressListProps) => {
  const { t } = useTranslation()

  const location = useLocation()
  const { context, page, sendEvent } = useTrackingContext()
  const navigate = useNavigateApp()

  const isSequenceCreation = RegexClass.SEQUENCE_URL.test(location.pathname)

  const handleTracking = useCallback(() => {
    if (context === 'Table' && page === LeadPageContext.ALL_LEADS) {
      sendEvent(AllLeadsEvents.ALL_LEADS_TABLE_BUTTON_CLICK_ADD_TO_SEQUENCE)
    }
  }, [context, page, sendEvent])

  if (!contactSequenceStepList.length && review?.status === 'pending') {
    return (
      <Button
        dataTestId={
          idReferentials.leads.components.ContactSequenceStepProgress
            .addToSequenceButton
        }
        variant="danger"
        iconLeft="Eye"
        fullWidth
        onClick={() =>
          navigate(`${privateRoutes.sequences.path}/${sequenceId}/step/preview`)
        }
      >
        {t('Lead to review', { ns: 'lead' })}
      </Button>
    )
  }

  if (!contactSequenceStepList.length) {
    return (
      <div className={'flex'}>
        <Button
          dataTestId={
            idReferentials.leads.components.ContactSequenceStepProgress
              .addToSequenceButton
          }
          variant={'secondary'}
          disabled={isSequenceCreation}
          iconLeft={'PlusCircle'}
          onClick={handleTracking}
          fullWidth
        >
          {t('Add to sequence', { ns: 'lead' })}
        </Button>
      </div>
    )
  }

  const updatedContactSequenceStepList: (SequencesContactSteps & {
    currentStepOrder: number
  })[] = contactSequenceStepList
    .slice(0, MAX_CONTACT_SEQUENCE_STEPS)
    .map(sequenceContactSteps => ({
      currentStepOrder: getCurrentStepOrder(
        sequenceContactSteps.currentStep,
        sequenceContactSteps.sequenceContact.status
      ),
      ...sequenceContactSteps,
    }))

  return (
    <div
      style={{
        height: PROGRESS_CIRCULAR_SIZE,
      }}
      className="relative hover:bg-backgroundSecondary rounded-m cursor-pointer bg-white border border-s-ui-base-subtle w-full"
      onClick={onClick}
    >
      {updatedContactSequenceStepList.map(
        ({ sequenceContact, sequence, progress }, i) => (
          <div
            key={`${sequence.id}-${i}`}
            style={{ left: i * LEFT_PIXELS_OFFSET }}
            className="absolute"
          >
            <ContactSequenceStepProgressContent
              dataTestId={`contact-sequence-step-progress-${sequence.id}`}
              value={progress}
              isError={getIsErrorContactInSequence({ sequenceContact })}
            />
          </div>
        )
      )}

      {updatedContactSequenceStepList.length === 1 && (
        <span
          style={{ left: LEFT_PIXELS_OFFSET * 3 }}
          className="body-m-medium absolute flex items-center h-full"
        >
          {getContactSequenceStepProgressTitle(
            updatedContactSequenceStepList[0]
          )}
        </span>
      )}

      {/* Badge to show complete the number of sequences */}
      {contactSequenceStepList.length > MAX_CONTACT_SEQUENCE_STEPS && (
        <div
          style={{
            // This value was calculated by trial and error
            left: (MAX_CONTACT_SEQUENCE_STEPS + 1) * LEFT_PIXELS_OFFSET + 12,
            width: SEQUENCE_NUMBER_BADGE_WIDTH,
          }}
          className="absolute flex items-center justify-start label-xs text-textSubtle h-1/2"
        >
          {`+${contactSequenceStepList.length - MAX_CONTACT_SEQUENCE_STEPS}`}
        </div>
      )}
    </div>
  )
}
