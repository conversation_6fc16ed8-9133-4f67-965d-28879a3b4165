import { useMemo } from 'react'

import { api } from '@getheroes/frontend/config/api'
import type { Contact } from '@getheroes/shared'
import { AddContactsToSequenceDrawer } from '@internals/features/lead/components/AddContactsToSequenceDrawer/AddContactsToSequenceDrawer'
import { ContactActionEnum } from '@internals/features/lead/components/ContactActionButton/ContactActionButton-export'
import { AssignContactsAndAddTaskModal } from '@internals/features/lead/components/LeadsCollectionCard/components/modals/AssignLeadsModal/AssignContactsAndAddTaskModal'
import { AssignLeadsToUserModal } from '@internals/features/lead/components/LeadsCollectionCard/components/modals/AssignLeadsModal/AssignLeadsToUserModal/AssignLeadsToUserModal'
import { ModalLeadBulkEdit } from '@internals/features/lead/components/LeadsModals/ModalLeadBulkEdit/ModalLeadBulkEdit'
import { ModalLeadBulkEditAction } from '@internals/features/lead/components/LeadsModals/ModalLeadBulkEdit/ModalLeadBulkEdit-export'
import { useCurrentUser } from '@internals/hooks/useCurrentUser'
import { useAppDispatch } from '@internals/store/store'

export type ContactActionProps = {
  contact?: Contact
  action?: ContactActionEnum
  isOpen: boolean
  onClose: () => void
}

export const ContactAction = ({
  contact,
  action,
  isOpen,
  onClose,
}: ContactActionProps) => {
  const { isCurrentUserAdminOrManager } = useCurrentUser()
  const dispatch = useAppDispatch()

  const contactList = useMemo(() => {
    if (!contact) {
      return []
    }
    return [contact]
  }, [contact])

  return (
    <>
      {action === ContactActionEnum.TASK && contact && (
        <AssignContactsAndAddTaskModal
          contactsList={[contact]}
          isOpen={isOpen}
          handleClose={onClose}
          onAssignCallback={() => {
            onClose()
          }}
        />
      )}

      {isOpen && action === ContactActionEnum.SEQUENCE && (
        <AddContactsToSequenceDrawer
          isOpen={isOpen && action === ContactActionEnum.SEQUENCE}
          onClose={() => {
            onClose()
          }}
          contactsMap={contact ? { [contact.id]: contact } : {}}
          isActiveAssignation={isCurrentUserAdminOrManager}
        />
      )}

      {action === ContactActionEnum.LABEL && isOpen && (
        <ModalLeadBulkEdit
          isOpen={isOpen}
          contactList={contactList}
          onClose={onClose}
          showSingleStep={ModalLeadBulkEditAction.LABELS}
          onValidate={() => {
            dispatch(
              api.util.invalidateTags(
                contactList.map(({ id }) => ({
                  type: 'Contact',
                  id: `CONTACT_${id}`,
                }))
              )
            )
            dispatch(
              api.util.invalidateTags([
                {
                  type: 'Search',
                  id: 'ALL_CONTACTS_LEADS',
                },
              ])
            )
            onClose()
          }}
        />
      )}

      {action === ContactActionEnum.ASSIGN_TO && (
        <AssignLeadsToUserModal
          isOpen={isOpen}
          handleClose={() => {
            onClose()
          }}
          contactsList={contactList}
        />
      )}
    </>
  )
}
