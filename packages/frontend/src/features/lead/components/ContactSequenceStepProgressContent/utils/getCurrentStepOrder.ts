import { SequenceContactStatus } from '@getheroes/shared'
import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'

export const getCurrentStepOrder = (
  currentStepFromApi: Pick<SequenceStepApi, 'order'>,
  status: SequenceContactStatus
): number => {
  if (
    status === SequenceContactStatus.DONE ||
    status === SequenceContactStatus.CONTACT_ANSWERED ||
    status === SequenceContactStatus.CONTACT_UNSUBSCRIBED
  ) {
    return currentStepFromApi.order
  }

  return currentStepFromApi?.order ? currentStepFromApi.order - 1 : 0
}
