import { clsx } from 'clsx'
import { isEmpty } from 'lodash'
import { useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { selectScrollToAttribute } from '@getheroes/frontend/config/store/selectors/leadSelectors'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import {
  ExclusiveContactLeadFieldEnum,
  TaskType,
} from '@getheroes/frontend/types'
import type { Contact, Organization } from '@getheroes/shared'
import { EnrichmentType, LeadCategory } from '@getheroes/shared'
import {
  getEmailInputFieldFromContact,
  getPhoneInputFieldFromContact,
  InputAttributeArray,
} from '@getheroes/ui-business'
import { Attribute } from '@internals/components/business/lead/common/Attribute/Attribute'
import { EnrichmentButtonWithWaterfall } from '@internals/components/business/lead/contact/EnrichmentButtonWithWaterfall/EnrichmentButtonWithWaterfall'
import { useContactEnrichAttributesFeatureGateway } from '@internals/components/business/lead/contact/MainContactAttributes/utils/useContactEnrichAttributesFeatureGateway'
import { AutoSave } from '@internals/components/common/dataEntry/AutoSave/AutoSave'
import { SkeletonList } from '@internals/components/common/feedback/Skeletons/SkeletonList/SkeletonList'
import {
  useEditContactByIdMutation,
  useGetContactFieldsQuery,
} from '@internals/features/lead/api/contactApi'
import { useLeadEnrichmentProgress } from '@internals/features/lead/hooks/enrichment/useLeadEnrichmentProgress'
import { useQuickActionsButton } from '@internals/features/lead/hooks/quickActions/useQuickActionsButton'
import type { LeadsField } from '@internals/features/lead/types/leadsTableColumn'
import { KindLeadEnum } from '@internals/features/lead/types/leadsTableColumn'
import { getAttributeScrollToErrorMessage } from '@internals/features/lead/utils/getAttributeScrollToErrorMessage'
import { mapLeadsFieldsFromApiToUi } from '@internals/features/lead/utils/mapLeadFieldsFromApiToUi'
import { useNavigateApp } from '@internals/hooks/useNavigateApp'
import { privateRoutes } from '@internals/hooks/useRoute'
import { useTypedSelector } from '@internals/store/store'

import { LeadSidePanelPaper } from '../../../../LeadSidePanelPaper/LeadSidePanelPaper'
import {
  COMPANY_ATTRIBUTE_FIELD_ID_LIST,
  ENRICHMENT_ATTRIBUTE_FIELD_ID_LIST,
  GLOBAL_ATTRIBUTE_FIELD_ID_LIST,
  HISTORY_ATTRIBUTE_FIELD_ID_LIST,
  OPERATIONAL_ATTRIBUTE_FIELD_ID_LIST,
} from '../../../constants/ContactAttributeFieldIdGrouped'

type ContactAttributesFormValues = {
  [ExclusiveContactLeadFieldEnum.PHONES]: string[]
  [ExclusiveContactLeadFieldEnum.EMAILS]: string[]
}

export type ContactAttributesPage = {
  contact: Contact
  onClickEnrich: (
    fieldId:
      | ExclusiveContactLeadFieldEnum.PHONES
      | ExclusiveContactLeadFieldEnum.EMAILS
  ) => void
}

export const ContactAttributesPage = ({
  contact,
  onClickEnrich,
}: ContactAttributesPage) => {
  const { t } = useTranslation('lead')
  const navigate = useNavigateApp()
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const scrollToAttribute = useTypedSelector(selectScrollToAttribute)

  useEffect(() => {
    if (!isEmpty(scrollToAttribute)) {
      const element = document.getElementById(scrollToAttribute!)

      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' })

        const input = element.querySelector('input')
        if (input) {
          input.focus()
        }
      }
    }
  }, [scrollToAttribute])

  // #region Hooks

  const {
    featureGatewayPopoverProps,
    hasEnoughCreditsToEnrichPhone,
    hasEnoughCreditsToEnrichEmail,
  } = useContactEnrichAttributesFeatureGateway()
  const { handleTaskAction } = useQuickActionsButton({ navigate })
  const { isLoadingEnrichmentState } = useLeadEnrichmentProgress(contact)

  // #region Query

  const {
    data: fieldsFetched,
    isLoading,
    isSuccess,
  } = useGetContactFieldsQuery({
    organizationId,
  })

  const [editContactById, { isLoading: isLoadingEditContactById }] =
    useEditContactByIdMutation()

  // #region Form

  const inputAttributeArrayPhoneName = ExclusiveContactLeadFieldEnum.PHONES
  const inputAttributeArrayEmailName = ExclusiveContactLeadFieldEnum.EMAILS
  const formMethods = useForm<ContactAttributesFormValues>({
    mode: 'onChange',
    values: {
      [inputAttributeArrayPhoneName]: contact.phones || [],
      [inputAttributeArrayEmailName]: contact.emails || [],
    },
  })
  const { control, watch } = formMethods

  const phoneField = useMemo(() => {
    return getPhoneInputFieldFromContact({
      name: inputAttributeArrayPhoneName,
      contact,
      onClickPhoneIcon: value =>
        handleTaskAction(TaskType.CALL, contact, value),
      placeholder: t('Add a phone number'),
      errorMessage: t('Invalid phone number'),
    })
  }, [contact, handleTaskAction, inputAttributeArrayPhoneName, t])
  const emailField = useMemo(() => {
    return getEmailInputFieldFromContact({
      name: inputAttributeArrayEmailName,
      contact,
      onClickMailIcon: () => handleTaskAction(TaskType.EMAIL, contact),
      placeholder: t('Add an email'),
      errorMessage: t('Invalid email address'),
    })
  }, [contact, handleTaskAction, inputAttributeArrayEmailName, t])

  // #region Fields

  const fields = fieldsFetched
    ? mapLeadsFieldsFromApiToUi(fieldsFetched, LeadCategory.CONTACT)
    : []

  const customFieldList = Object.values(fields).filter(
    field => field.kind === KindLeadEnum.CUSTOM
  )

  // #region Enrichment

  const isLoadingPhoneEnrichment =
    isLoadingEnrichmentState[ExclusiveContactLeadFieldEnum.PHONES]
  const isLoadingEmailEnrichment =
    isLoadingEnrichmentState[ExclusiveContactLeadFieldEnum.EMAILS]
  const showEnrichmentButtonPhone =
    (contact.phones || []).length === 0 &&
    watch(inputAttributeArrayPhoneName)?.length === 0
  const showEnrichmentButtonEmail =
    (contact.emails || []).length === 0 &&
    watch(inputAttributeArrayEmailName)?.length === 0

  // #region Functions

  const handleSubmit = useCallback(
    (values: ContactAttributesFormValues) => {
      editContactById({
        organizationId,
        contactId: contact.id,
        ...values,
      })
    },
    [contact.id, editContactById, organizationId]
  )

  // #region Render

  if (isLoading || !isSuccess || !fields) {
    return <SkeletonList itemsNumber={20} />
  }

  return (
    <>
      <LeadSidePanelPaper
        error={getAttributeScrollToErrorMessage({
          fieldName: ExclusiveContactLeadFieldEnum.PHONES,
          scrollToFieldName: scrollToAttribute,
        })}
      >
        <div className="flex flex-col gap-2">
          <InputAttributeArray
            label={t('Phones')}
            isDisabled={isLoadingPhoneEnrichment || isLoadingEditContactById}
            isLoadingEnrichment={isLoadingPhoneEnrichment}
            onClickEnrich={() =>
              onClickEnrich(ExclusiveContactLeadFieldEnum.PHONES)
            }
            field={phoneField}
            control={control}
            featureGatewayProps={{
              ...featureGatewayPopoverProps,
              isActive: !hasEnoughCreditsToEnrichPhone,
              onClickUpgrade: () =>
                navigate(privateRoutes.creditsAndPlans.path),
            }}
            isShowGdprWarning={contact.gdprOptOut}
          />

          <div className={clsx({ hidden: !showEnrichmentButtonPhone })}>
            <EnrichmentButtonWithWaterfall
              contact={contact}
              enrichmentType={EnrichmentType.PHONE}
              onClickEnrich={() =>
                onClickEnrich(ExclusiveContactLeadFieldEnum.PHONES)
              }
              label={t('Enrich phone')}
            />
          </div>
        </div>
      </LeadSidePanelPaper>

      <LeadSidePanelPaper
        error={getAttributeScrollToErrorMessage({
          fieldName: ExclusiveContactLeadFieldEnum.EMAILS,
          scrollToFieldName: scrollToAttribute,
        })}
      >
        <div className="flex flex-col gap-2">
          <InputAttributeArray
            label={t('Emails')}
            isDisabled={isLoadingEmailEnrichment || isLoadingEditContactById}
            isLoadingEnrichment={isLoadingEmailEnrichment}
            onClickEnrich={() =>
              onClickEnrich(ExclusiveContactLeadFieldEnum.EMAILS)
            }
            field={emailField}
            control={control}
            featureGatewayProps={{
              ...featureGatewayPopoverProps,
              isActive: !hasEnoughCreditsToEnrichEmail,
              onClickUpgrade: () =>
                navigate(privateRoutes.creditsAndPlans.path),
            }}
            isShowGdprWarning={contact.gdprOptOut}
          />

          <div className={clsx({ hidden: !showEnrichmentButtonEmail })}>
            <EnrichmentButtonWithWaterfall
              contact={contact}
              enrichmentType={EnrichmentType.EMAIL}
              onClickEnrich={() =>
                onClickEnrich(ExclusiveContactLeadFieldEnum.EMAILS)
              }
              label={t('Enrich email')}
            />
          </div>
        </div>
      </LeadSidePanelPaper>

      {/* AutoSave only handle emails & phones for now */}
      <AutoSave onSubmit={handleSubmit} methods={formMethods} />

      <LeadSidePanelPaper title={t('Global information') as string}>
        <div className={'flex flex-col gap-2'}>
          {GLOBAL_ATTRIBUTE_FIELD_ID_LIST.map(fieldId => fields[fieldId]).map(
            field => (
              <Attribute
                key={field.id}
                dataTestIdPrefix={field.id as string}
                lead={contact}
                attribute={field as LeadsField}
                editAndShowError={getAttributeScrollToErrorMessage({
                  fieldName: field.id,
                  scrollToFieldName: scrollToAttribute,
                })}
              />
            )
          )}
        </div>
      </LeadSidePanelPaper>

      <LeadSidePanelPaper title={t('Operational information') as string}>
        <div className={'flex flex-col gap-2'}>
          {OPERATIONAL_ATTRIBUTE_FIELD_ID_LIST.map(
            fieldId => fields[fieldId]
          ).map(field => (
            <Attribute
              key={field.id}
              dataTestIdPrefix={field.id as string}
              lead={contact}
              attribute={field as LeadsField}
              editAndShowError={getAttributeScrollToErrorMessage({
                fieldName: field.id,
                scrollToFieldName: scrollToAttribute,
              })}
            />
          ))}
        </div>
      </LeadSidePanelPaper>

      <LeadSidePanelPaper title={t('Enrichment') as string}>
        <div className={'flex flex-col gap-2'}>
          {ENRICHMENT_ATTRIBUTE_FIELD_ID_LIST.map(
            fieldId => fields[fieldId]
          ).map(field => (
            <Attribute
              key={field.id}
              dataTestIdPrefix={field.id as string}
              lead={contact}
              attribute={field as LeadsField}
              editAndShowError={getAttributeScrollToErrorMessage({
                fieldName: field.id,
                scrollToFieldName: scrollToAttribute,
              })}
            />
          ))}
        </div>
      </LeadSidePanelPaper>

      <LeadSidePanelPaper title={t('History') as string}>
        <div className={'flex flex-col gap-2'}>
          {HISTORY_ATTRIBUTE_FIELD_ID_LIST.map(fieldId => fields[fieldId]).map(
            field => (
              <Attribute
                key={field.id}
                dataTestIdPrefix={field.id as string}
                lead={contact}
                attribute={field as LeadsField}
                editAndShowError={getAttributeScrollToErrorMessage({
                  fieldName: field.id,
                  scrollToFieldName: scrollToAttribute,
                })}
              />
            )
          )}
        </div>
      </LeadSidePanelPaper>

      <LeadSidePanelPaper title={t('Company') as string}>
        <div className={'flex flex-col gap-2'}>
          {COMPANY_ATTRIBUTE_FIELD_ID_LIST.map(fieldId => fields[fieldId]).map(
            field => (
              <Attribute
                key={field.id}
                dataTestIdPrefix={field.id as string}
                lead={contact}
                attribute={field as LeadsField}
                editAndShowError={getAttributeScrollToErrorMessage({
                  fieldName: field.id,
                  scrollToFieldName: scrollToAttribute,
                })}
              />
            )
          )}
        </div>
      </LeadSidePanelPaper>

      {customFieldList.length > 0 && (
        <LeadSidePanelPaper title={t('Custom properties') as string}>
          <div className={'flex flex-col gap-2'}>
            {customFieldList.map(field => (
              <Attribute
                key={field?.id}
                dataTestIdPrefix={field?.id as string}
                lead={contact}
                attribute={field as LeadsField}
                editAndShowError={getAttributeScrollToErrorMessage({
                  fieldName: field?.id,
                  scrollToFieldName: scrollToAttribute,
                })}
              />
            ))}
          </div>
        </LeadSidePanelPaper>
      )}
    </>
  )
}
