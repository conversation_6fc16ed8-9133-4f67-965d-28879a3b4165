import type { FormEvent } from 'react'
import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router-dom'

import {
  selectCurrentFiltersApi,
  selectNbLeadsMatchingCurrentFilters,
} from '@getheroes/frontend/config/store/selectors/leadSelectors'
import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { AllLeadsEvents } from '@getheroes/frontend/hooks'
import type {
  LeadExportableFieldDTO,
  Organization,
  User,
} from '@getheroes/shared'
import { FieldKind, LeadCategory } from '@getheroes/shared'
import { useToast } from '@getheroes/ui'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import i18n from '@internals/config/i18n'
import { useGetCompanyExportableFieldsQuery } from '@internals/features/lead/api/companyApi'
import { useGetContactExportableFieldsQuery } from '@internals/features/lead/api/contactApi'
import { useExportLeadsAsyncMutation } from '@internals/features/lead/api/csvApi'
import { LeadExportHeader } from '@internals/features/lead/components/LeadExport/LeadExportHeader'
import type {
  ExportLeadsTableRequestType,
  Fields,
} from '@internals/features/lead/types/csvType'
import { LeadPageContext } from '@internals/features/lead/types/genericLeadType'
import { getFiltersList } from '@internals/features/lead/utils/getFiltersList'
import { LeadExportColumns } from '@internals/features/lead-export/components/LeadExportColumns'
import { LeadExportFooter } from '@internals/features/lead-export/components/LeadExportFooter'
import { LeadExportOption } from '@internals/features/lead-export/enum/LeadExportOption.enum'
import { useLeadExport } from '@internals/features/lead-export/hooks/useLeadExport'
import { useFiltersParam } from '@internals/hooks/useFiltersParam'
import { privateRoutes } from '@internals/hooks/useRoute'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'
import { useTypedSelector } from '@internals/store/store'

const getLeadExportFields = (
  choice: LeadExportOption,
  exportableFields: LeadExportableFieldDTO[],
  selectedFields: string[],
  isAddingCustomFields = false
): Fields => {
  let exportable: Fields = []

  switch (choice) {
    case LeadExportOption.DEFAULT:
      exportable = exportableFields
        .filter(({ isDefault }) => isDefault)
        .map(({ id }) => ({
          id,
          name: i18n.t(id, { ns: 'export' }),
        }))
      break
    case LeadExportOption.ALL:
      exportable = exportableFields
        .filter(({ kind }) => kind !== FieldKind.CUSTOM)
        .map(({ id }) => ({
          id,
          name: i18n.t(id, { ns: 'export' }),
        }))
      break
    case LeadExportOption.PERSONALIZED:
      exportable = exportableFields
        .filter(({ kind }) => kind !== FieldKind.CUSTOM)
        .filter(({ id }) => selectedFields.includes(id))
        .map(({ id }) => ({
          id,
          name: i18n.t(id, { ns: 'export' }),
        }))
      break
  }

  if (isAddingCustomFields) {
    exportable = [
      ...exportable,
      ...exportableFields
        .filter(({ kind }) => kind === FieldKind.CUSTOM)
        .map(({ id }) => ({
          id,
          name: i18n.t(id, { ns: 'export' }),
        })),
    ]
  }

  return exportable
}

interface LeadExportProps {
  currentPageContext?: LeadPageContext
  leadCategory?: LeadCategory
}

export const LeadExport = ({
  currentPageContext = LeadPageContext.ALL_LEADS,
  leadCategory = LeadCategory.CONTACT,
}: LeadExportProps) => {
  const { isExportOpen, toggleExport } = useLeadExport()
  const currentOrganization = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const { createToast } = useToast()
  const { t } = useTranslation(['export', 'lead'])
  const currentFiltersApi = useTypedSelector(selectCurrentFiltersApi)
  const { selections } = useGridContext()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const currentUser = useTypedSelector(selectCurrentUser) as User
  const nbLeadsMatchingCurrentFilters = useTypedSelector(
    selectNbLeadsMatchingCurrentFilters
  )
  const location = useLocation()
  const { getFilters } = useFiltersParam()

  const filters = useMemo(() => {
    return getFiltersList({
      leadCategory,
      currentFiltersApi,
      currentFiltersUrl: getFilters(),
    })
  }, [currentFiltersApi, getFilters, leadCategory])

  const { sendEvent } = useTrackingContext()

  const isCompanyCategory =
    location.pathname === privateRoutes.myLeadsCompany.path ||
    location.pathname === privateRoutes.allLeadsCompany.path

  const useGetLeadExportableFieldsQuery = isCompanyCategory
    ? useGetCompanyExportableFieldsQuery
    : useGetContactExportableFieldsQuery

  const { data: getContactExportableFieldsData } =
    useGetLeadExportableFieldsQuery(
      {
        organizationId: currentOrganization?.id,
        filters,
      },
      {
        skip: !currentOrganization,
      }
    )

  let count = 0
  if (selections.length > 0) {
    count = selections.length
  } else if (nbLeadsMatchingCurrentFilters > 0) {
    count = nbLeadsMatchingCurrentFilters
  }

  const [exportCsvMutationV2] = useExportLeadsAsyncMutation()

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)

    const formData = new FormData(e.currentTarget)
    const formObj: Record<string, string> = {}
    for (const [key, value] of Array.from(formData.entries())) {
      formObj[key] = value.toString()
    }

    const selectValue = formObj['select-fields']

    const payload: ExportLeadsTableRequestType = {
      exportFields: getLeadExportFields(
        selectValue as LeadExportOption,
        getContactExportableFieldsData?.exportableFields as unknown as LeadExportableFieldDTO[],
        Object.keys(formObj).filter(
          f => !['select-fields', 'add-columns'].includes(f)
        ), // remove filters and select from the form
        !!formObj['add-columns']
      ),
      filters: currentFiltersApi,
      leadCategory: isCompanyCategory
        ? LeadCategory.COMPANY
        : LeadCategory.CONTACT,
      organizationId: currentOrganization?.id,
    }

    if (currentPageContext === LeadPageContext.MY_LEADS) {
      payload.assignedTo = currentUser?.id
    }

    if (selections.length > 0) {
      payload.selection = selections
    }

    exportCsvMutationV2(payload)
      .unwrap()
      .then(() => {
        toggleExport()

        if (currentPageContext === LeadPageContext.ALL_LEADS) {
          const format = selectValue
          sendEvent(AllLeadsEvents.ALL_LEADS_MODALE_BUTTON_CLICK_EXPORT, {
            format,
          })
        }
        createToast({
          type: 'main',
          message: t("CSV export is in progress, you'll receive an email"),
          isUniq: false,
        })
      })
      .catch(error => {
        createToast({
          type: 'error',
          message: t(
            error.data?.message ?? 'An error occurred while exporting your data'
          ),
        })
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  return (
    <Drawer
      classNamePanel="!max-w-[768px]"
      onClose={toggleExport}
      open={isExportOpen}
    >
      <form className="h-full flex flex-col p-6" onSubmit={handleSubmit}>
        <LeadExportHeader leadCount={count} />
        <div className="flex-1 overflow-y-auto my-4">
          {getContactExportableFieldsData && (
            <LeadExportColumns
              categories={getContactExportableFieldsData?.exportableCategories}
              fields={
                getContactExportableFieldsData?.exportableFields as LeadExportableFieldDTO[]
              }
            />
          )}
        </div>
        <LeadExportFooter isDisabled={count === 0} isLoading={isLoading} />
      </form>
    </Drawer>
  )
}
