.c-select-lead-status {
  &__label {
    display: flex;
    border-radius: 8px;
    width: 100%;
    cursor: pointer;
    color: var(--textSubtle) !important;
    background-color: transparent !important;
    justify-content: flex-start;
    &:hover {
      background-color: var(--backgroundSecondary) !important;
    }
  }

  .lead-status-popover__menu {
    .c-select-menu-item {
      div {
        cursor: pointer;
        p {
          margin: 0;
        }
      }
    }
  }

  .c-lead-status__selected {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px;
    width: 100%;

    &:hover {
      color: var(--textSubtle) !important;
      background-color: var(--backgroundSecondary) !important;
    }
  }

  .c-select-menu {
    button:has(div.c-lead-status__selected__type) {
      border-radius: 8px;
      &:hover {
        background-color: var(--backgroundSecondary) !important;
      }
    }
  }
}
