import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

import { selectCurrentPageContext } from '@getheroes/frontend/config/store/selectors/leadSelectors'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { SearchEvents } from '@getheroes/frontend/hooks'
import {
  ExclusiveContactLeadFieldEnum,
  TaskType,
} from '@getheroes/frontend/types'
import type {
  Contact,
  ExternalContact,
  Organization,
  User,
} from '@getheroes/shared'
import {
  EnrichmentType,
  FeatureLimit,
  getEnrichmentCost,
} from '@getheroes/shared'
import { Icon } from '@getheroes/ui'
import { ConversionButtonSize } from '@internals/components/common/navigation/Buttons/ConversionButton/ConversionButton-export'
import { useHasAccessToFeature } from '@internals/features/featureGateway/hooks/useHasAccessToFeature'
import { ContactAction } from '@internals/features/lead/components/ContactAction/ContactAction'
import { useContactAction } from '@internals/features/lead/components/ContactAction/useContactAction'
import { ContactActionButton } from '@internals/features/lead/components/ContactActionButton/ContactActionButton'
import { ContactActionEnum } from '@internals/features/lead/components/ContactActionButton/ContactActionButton-export'
import { EnrichmentWaterfall } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover'
import { useQuickActionsButton } from '@internals/features/lead/hooks/quickActions/useQuickActionsButton'
import { useUnarchiveContacts } from '@internals/features/lead/hooks/unarchive/useUnarchiveContacts'
import { LeadPageContext } from '@internals/features/lead/types/genericLeadType'
import { AddContactButton } from '@internals/features/search/components/AddContactButton/AddContactButton'
import type { UseSubscriptionsResult } from '@internals/features/subscription/hooks/useSubscriptions'
import { useSubscriptions } from '@internals/features/subscription/hooks/useSubscriptions'
import { useCurrentUser } from '@internals/hooks/useCurrentUser'
import { privateRoutes } from '@internals/hooks/useRoute'
import { useHandleEnrichmentProvider } from '@internals/providers/EnrichmentProvider/useHandleEnrichmentProvider'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'
import { useTypedSelector } from '@internals/store/store'

type LeadActionsCellProps = {
  lead: Contact | ExternalContact
  onClickAction?: (action: ContactActionEnum, contact: Contact) => void
  showActionButton?: boolean
  hidePhoneButton?: boolean
  hideEmailButton?: boolean
  isDraftEnrichmentHub?: boolean
}

/**
 * List of actions that can be performed on a lead
 * @returns ```JSX.Element```
 */
export const LeadActionsCell = ({
  lead,
  showActionButton = false,
  hidePhoneButton = false,
  hideEmailButton = false,
}: LeadActionsCellProps) => {
  const [isLoadingContactCreation, setIsLoadingContactCreation] =
    useState(false)
  const isContactAlreadyAdded = Boolean(lead.createdAt)
  const isContactArchived = Boolean(lead?.archivedAt)
  const { t } = useTranslation('lead')
  const navigate = useNavigate()
  const { currentPlan } = useSubscriptions() as UseSubscriptionsResult
  const { currentUser, userRole } = useCurrentUser()
  const currentPageContext = useTypedSelector(selectCurrentPageContext)
  const {
    hasAccess: canAddMoreLeadsFromSearch,
    accessLimit,
    currentUsage,
  } = useHasAccessToFeature(FeatureLimit.MAX_LEADS_ADDED)
  const isAccessLimitApplying = !!accessLimit && accessLimit !== Infinity

  const {
    setSelectedContact,
    selectedContact,
    onClose,
    isOpen,
    onOpen,
    setSelectedAction,
    selectedAction,
  } = useContactAction()

  const handleClickAction = useCallback(
    (action: ContactActionEnum, contact: Contact) => {
      setSelectedContact(contact)
      setSelectedAction(action)
      onOpen()
    },
    [onOpen, setSelectedAction, setSelectedContact]
  )

  const { unarchiveContacts, isLoading: isLoadingUnarchiveContacts } =
    useUnarchiveContacts()

  const { page, context, sendEvent } = useTrackingContext()

  const isSearchContactContext =
    currentPageContext?.page === LeadPageContext.SEARCH_CONTACTS
  const isAddLeadDisabled = isSearchContactContext && !canAddMoreLeadsFromSearch
  const leadsLeftToAdd = isAccessLimitApplying
    ? (accessLimit || 0) - (currentUsage ?? 0)
    : 0

  const { handleTaskAction } = useQuickActionsButton({
    navigate,
  })

  const {
    enrichContact,
    handleCreateExternalContact,
    createAndAssignExternalContactProcessState,
  } = useHandleEnrichmentProvider()

  const { creditBalance } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const handleClickEnrich = async (
    fieldId:
      | ExclusiveContactLeadFieldEnum.PHONES
      | ExclusiveContactLeadFieldEnum.EMAILS
  ) => {
    const enrichmentType =
      fieldId === ExclusiveContactLeadFieldEnum.PHONES
        ? EnrichmentType.PHONE
        : EnrichmentType.EMAIL
    const hasEnoughCredits =
      creditBalance >= getEnrichmentCost(enrichmentType, currentPlan)

    if (!hasEnoughCredits) {
      navigate(privateRoutes.creditsAndPlans.path)
      return
    }

    enrichContact(lead, fieldId)
  }

  const shouldDisplayAddContactButton =
    showActionButton &&
    (!isContactAlreadyAdded ||
      isContactArchived ||
      (createAndAssignExternalContactProcessState[lead.externalId || ''] &&
        createAndAssignExternalContactProcessState[lead.externalId || '']
          ?.state !== 'created'))

  useEffect(() => {
    if (
      createAndAssignExternalContactProcessState[lead.externalId || '']
        ?.state === 'created' ||
      createAndAssignExternalContactProcessState[lead.externalId || '']
        ?.state === 'error'
    ) {
      setIsLoadingContactCreation(false)
    }
  }, [createAndAssignExternalContactProcessState, lead.externalId])

  return (
    <div className="flex items-center w-full h-full gap-2 me-2 pe-2">
      {!hidePhoneButton && (
        <EnrichmentWaterfall.Popover
          placement={'top-end'}
          dataTestId={`lead-action-cell-popover-phone-${lead.id}`}
        >
          <EnrichmentWaterfall.Trigger
            lead={lead}
            handleClickEnrich={handleClickEnrich}
            enrichmentType={EnrichmentType.PHONE}
            conversionButtonProps={{
              startIcon: (
                <Icon name={'Phone'} size={'small'} color={'decorative-blue'} />
              ),
              size: ConversionButtonSize.SMALL,
            }}
          >
            {t('Enrich')}
          </EnrichmentWaterfall.Trigger>
          <EnrichmentWaterfall.Content
            lead={lead}
            handleClickEnrich={handleClickEnrich}
            enrichmentType={EnrichmentType.PHONE}
          />
        </EnrichmentWaterfall.Popover>
      )}

      {!hideEmailButton && (
        <EnrichmentWaterfall.Popover
          placement={'top-end'}
          dataTestId={`lead-action-cell-popover-email-${lead.id}`}
        >
          <EnrichmentWaterfall.Trigger
            lead={lead}
            handleClickEnrich={handleClickEnrich}
            enrichmentType={EnrichmentType.EMAIL}
            conversionButtonProps={{
              startIcon: (
                <Icon name={'Mail'} size={'small'} color={'decorative-blue'} />
              ),
              size: ConversionButtonSize.SMALL,
            }}
          >
            {t('Enrich')}
          </EnrichmentWaterfall.Trigger>
          <EnrichmentWaterfall.Content
            lead={lead}
            handleClickEnrich={handleClickEnrich}
            enrichmentType={EnrichmentType.EMAIL}
          />
        </EnrichmentWaterfall.Popover>
      )}

      {shouldDisplayAddContactButton ? (
        <div className="w-full">
          <AddContactButton
            tooltipText={
              isSearchContactContext && isAccessLimitApplying
                ? t(
                    isAddLeadDisabled
                      ? `You've hit the Free plan limit. Upgrade to add more leads.`
                      : `Only {{leadsLeftToAdd}} left before hitting the Free plan limit`,
                    { ns: 'featureGateway', leadsLeftToAdd: leadsLeftToAdd }
                  )
                : undefined
            }
            isDisabled={isAddLeadDisabled}
            isLoading={isLoadingContactCreation ?? isLoadingUnarchiveContacts}
            onClickAddContact={() => {
              setIsLoadingContactCreation(true)
              handleCreateExternalContact(lead as ExternalContact)
            }}
            size={'medium'}
            isFullWidth
            isContactArchived={isContactArchived}
            onClickUnarchiveContact={async () => {
              await unarchiveContacts([lead as Contact])
            }}
          />
        </div>
      ) : (
        showActionButton && (
          <div className="w-full">
            <ContactActionButton
              isDisabled={!isContactAlreadyAdded}
              currentUser={currentUser as unknown as User}
              userRole={userRole}
              contact={lead as Contact}
              onClick={(action: ContactActionEnum) => {
                if (
                  context === 'Table' &&
                  page === LeadPageContext.SEARCH_CONTACTS
                ) {
                  switch (action) {
                    case ContactActionEnum.EMAIL:
                      sendEvent(
                        SearchEvents.SEARCH_MAKE_ACTION_BUTTON_CLICK_LAUNCH_EMAIL
                      )
                      break
                    case ContactActionEnum.PHONE:
                      sendEvent(
                        SearchEvents.SEARCH_MAKE_ACTION_BUTTON_CLICK_LAUNCH_CALL
                      )
                      break
                    case ContactActionEnum.TASK:
                      sendEvent(
                        SearchEvents.SEARCH_MAKE_ACTION_BUTTON_CLICK_PLAN_TASK
                      )
                      break
                    case ContactActionEnum.SEQUENCE:
                      sendEvent(
                        SearchEvents.SEARCH_MAKE_ACTION_BUTTON_CLICK_ADD_TO_SEQUENCE
                      )
                      break
                    case ContactActionEnum.LABEL:
                      sendEvent(
                        SearchEvents.SEARCH_MAKE_ACTION_BUTTON_CLICK_ADD_LABEL
                      )
                      break
                    case ContactActionEnum.ASSIGN_TO:
                      sendEvent(
                        SearchEvents.SEARCH_MAKE_ACTION_BUTTON_CLICK_ASSIGN
                      )
                      break
                  }
                }
                if (action === ContactActionEnum.EMAIL) {
                  handleTaskAction(TaskType.EMAIL, lead as Contact)
                  return
                }
                if (action === ContactActionEnum.PHONE) {
                  handleTaskAction(
                    TaskType.CALL,
                    lead as Contact,
                    (lead.phones || [])[0]
                  )
                  return
                }
                handleClickAction(action, lead as Contact)
              }}
            />
          </div>
        )
      )}
      <ContactAction
        contact={selectedContact}
        action={selectedAction}
        isOpen={isOpen}
        onClose={onClose}
      />
    </div>
  )
}
