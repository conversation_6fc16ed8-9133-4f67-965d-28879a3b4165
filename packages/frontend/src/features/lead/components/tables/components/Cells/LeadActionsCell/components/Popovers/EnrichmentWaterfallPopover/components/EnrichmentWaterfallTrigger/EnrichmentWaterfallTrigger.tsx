import type { PropsWithChildren } from 'react'
import { useCallback } from 'react'

import { selectCurrentPageContext } from '@getheroes/frontend/config/store/selectors/leadSelectors'
import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import type { Contact, ExternalContact } from '@getheroes/shared'
import { EnrichmentType } from '@getheroes/shared'
import { Popover } from '@getheroes/ui'
import { useContactEnrichAttributesFeatureGateway } from '@internals/components/business/lead/contact/MainContactAttributes/utils/useContactEnrichAttributesFeatureGateway'
import { useEnrichmentHubStatus } from '@internals/features/enrichmentHub/hooks/useEnrichmentHubStatus'
import { LeadFeatureGatewayEmailPhonePopover } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/LeadFeatureGatewayPopover/LeadFeatureGatewayEmailPhonePopover'
import { DataButton } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/components/EnrichmentWaterfallTrigger/components/DataButton'
import { EnrichmentButton } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/components/EnrichmentWaterfallTrigger/components/EnrichmentButton'
import { NotFoundSection } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/components/EnrichmentWaterfallTrigger/components/NotFoundSection'
import type { EnrichmentWaterfallTriggerConversionButtonProps } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/components/EnrichmentWaterfallTrigger/enrichment-waterfall-trigger.type'
import { useGetButtonStatus } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/hooks/useGetButtonStatus'
import { useGetProvidersStatus } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/hooks/useGetProvidersStatus'
import { LeadPageContext } from '@internals/features/lead/types/genericLeadType'
import { useCurrentPlan } from '@internals/features/subscription/hooks/useCurrentPlan'
import { useTypedSelector } from '@internals/store/store'

type EnrichmentWaterfallTriggerProps = {
  lead: Contact | ExternalContact
  enrichmentType: EnrichmentType
  handleClickEnrich: (
    fieldId:
      | ExclusiveContactLeadFieldEnum.PHONES
      | ExclusiveContactLeadFieldEnum.EMAILS
  ) => void
  conversionButtonProps: EnrichmentWaterfallTriggerConversionButtonProps
  isDraftEnrichmentHub?: boolean
}

export const EnrichmentWaterfallTrigger = ({
  lead,
  enrichmentType,
  handleClickEnrich,
  conversionButtonProps,
  children,
}: PropsWithChildren<EnrichmentWaterfallTriggerProps>) => {
  const { hasEnoughCreditsToEnrichPhone, hasEnoughCreditsToEnrichEmail } =
    useContactEnrichAttributesFeatureGateway()
  const { isFreeOrStarterPlan } = useCurrentPlan()
  const enrichmentHub = useEnrichmentHubStatus()

  const { isLoadingEnrichment } = useGetProvidersStatus({
    lead,
    enrichmentType,
  })

  const currentPageContext = useTypedSelector(selectCurrentPageContext)

  const { showEnrichButton, showDataButton, showNotFound } = useGetButtonStatus(
    {
      enrichmentType,
      lead,
    }
  )

  const handleEnrichment = useCallback(() => {
    const fieldId =
      enrichmentType === EnrichmentType.EMAIL
        ? ExclusiveContactLeadFieldEnum.EMAILS
        : ExclusiveContactLeadFieldEnum.PHONES
    handleClickEnrich(fieldId)
  }, [enrichmentType, handleClickEnrich])

  if (
    !isLoadingEnrichment &&
    ((enrichmentType === EnrichmentType.PHONE &&
      !hasEnoughCreditsToEnrichPhone &&
      lead?.enrichmentPhoneDate === null) ||
      (enrichmentType === EnrichmentType.EMAIL &&
        !hasEnoughCreditsToEnrichEmail &&
        lead?.enrichmentEmailDate === null &&
        isFreeOrStarterPlan)) &&
    currentPageContext?.page === LeadPageContext.ENRICHMENT_HUB &&
    !enrichmentHub?.isInProcess
  ) {
    return <LeadFeatureGatewayEmailPhonePopover />
  }

  return (
    <Popover.Trigger asChild>
      <>
        {showEnrichButton && (
          <EnrichmentButton
            conversionButtonProps={conversionButtonProps}
            handleEnrichment={handleEnrichment}
            lead={lead}
            enrichmentType={enrichmentType}
            isFreeOrStarterPlan={isFreeOrStarterPlan}
            pageContext={currentPageContext?.page}
          >
            {children}
          </EnrichmentButton>
        )}

        {showDataButton && (
          <DataButton
            enrichmentType={enrichmentType}
            lead={lead}
            conversionButtonProps={conversionButtonProps}
          />
        )}

        {showNotFound && (
          <NotFoundSection
            enrichmentType={enrichmentType}
            lead={lead}
            conversionButtonProps={conversionButtonProps}
            handleEnrichment={handleEnrichment}
            pageContext={currentPageContext?.page}
          />
        )}
      </>
    </Popover.Trigger>
  )
}
