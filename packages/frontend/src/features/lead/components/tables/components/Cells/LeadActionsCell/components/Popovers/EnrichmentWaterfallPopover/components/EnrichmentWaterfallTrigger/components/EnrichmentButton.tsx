import type { MouseEvent, PropsWithChildren } from 'react'
import { useCallback } from 'react'
import { useTranslation } from 'react-i18next'

import { AllLeadsEvents, SearchEvents } from '@getheroes/frontend/hooks'
import type { Contact, ExternalContact } from '@getheroes/shared'
import { EnrichmentType } from '@getheroes/shared'
import { Button } from '@getheroes/ui'
import { useContactEnrichAttributesFeatureGateway } from '@internals/components/business/lead/contact/MainContactAttributes/utils/useContactEnrichAttributesFeatureGateway'
import { ConversionButton } from '@internals/components/common/navigation/Buttons/ConversionButton/ConversionButton'
import { ConversionButtonVariant } from '@internals/components/common/navigation/Buttons/ConversionButton/ConversionButton-export'
import { FeatureGatewayPopover } from '@internals/features/featureGateway/components/FeatureGatewayPopover/FeatureGatewayPopover'
import { EnrichmentHubButton } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/components/EnrichmentWaterfallTrigger/components/EnrichmentHubButton'
import { LoadingLabel } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/components/EnrichmentWaterfallTrigger/components/LoadingLabel'
import type { EnrichmentWaterfallTriggerConversionButtonProps } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/components/EnrichmentWaterfallTrigger/enrichment-waterfall-trigger.type'
import { useEnrichmentButtonStatus } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/components/EnrichmentWaterfallTrigger/hooks/useEnrichmentButtonStatus'
import { useGetProvidersStatus } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/hooks/useGetProvidersStatus'
import { LeadPageContext } from '@internals/features/lead/types/genericLeadType'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'
import { idReferentials } from '@internals/utils/idReferentials'

interface EnrichmentButtonProps {
  conversionButtonProps: EnrichmentWaterfallTriggerConversionButtonProps
  handleEnrichment: () => void
  lead: Contact | ExternalContact
  enrichmentType: EnrichmentType
  isFreeOrStarterPlan: boolean
  pageContext?: LeadPageContext
  container?: HTMLElement | null | React.MutableRefObject<HTMLElement | null>
}

export const EnrichmentButton = ({
  conversionButtonProps,
  handleEnrichment,
  lead,
  enrichmentType,
  children,
  isFreeOrStarterPlan,
  pageContext,
  container,
}: PropsWithChildren<EnrichmentButtonProps>) => {
  const { t } = useTranslation('lead')
  const {
    hasEnoughCreditsToEnrichPhone,
    hasEnoughCreditsToEnrichEmail,
    featureGatewayPopoverProps,
  } = useContactEnrichAttributesFeatureGateway()
  const { currentPageContext, isDisabled } =
    useEnrichmentButtonStatus(enrichmentType)
  const {
    isLoadingEnrichment,
    isPendingEnrichment,
    loadingText,
    currentProviderName,
  } = useGetProvidersStatus({
    lead,
    enrichmentType,
  })

  const { context, sendEvent } = useTrackingContext()

  const handleTracking = useCallback(() => {
    if (!context) {
      return
    }

    const eventMap = {
      [LeadPageContext.ALL_LEADS]: {
        Table: {
          [EnrichmentType.EMAIL]:
            AllLeadsEvents.ALL_LEADS_TABLE_BUTTON_CLICK_ENRICH_MAIL,
          [EnrichmentType.PHONE]:
            AllLeadsEvents.ALL_LEADS_TABLE_BUTTON_CLICK_ENRICH_PHONE,
        },
        SidePanel: {
          [EnrichmentType.EMAIL]:
            AllLeadsEvents.ALL_LEADS_SIDE_PANEL_BUTTON_CLICK_ENRICH_MAIL,
          [EnrichmentType.PHONE]:
            AllLeadsEvents.ALL_LEADS_SIDE_PANEL_BUTTON_CLICK_ENRICH_PHONE,
        },
      },
      [LeadPageContext.SEARCH_CONTACTS]: {
        Table: {
          [EnrichmentType.EMAIL]:
            SearchEvents.SEARCH_TABLE_BUTTON_CLICK_ENRICH_MAIL,
          [EnrichmentType.PHONE]:
            SearchEvents.SEARCH_TABLE_BUTTON_CLICK_ENRICH_PHONE,
        },
        SidePanel: {
          [EnrichmentType.EMAIL]:
            SearchEvents.SEARCH_SIDE_PANEL_BUTTON_CLICK_ENRICH_MAIL,
          [EnrichmentType.PHONE]:
            SearchEvents.SEARCH_SIDE_PANEL_BUTTON_CLICK_ENRICH_PHONE,
        },
      },
      [LeadPageContext.SEARCH_COMPANIES]: {
        Table: {
          [EnrichmentType.EMAIL]:
            SearchEvents.SEARCH_TABLE_BUTTON_CLICK_ENRICH_MAIL,
          [EnrichmentType.PHONE]:
            SearchEvents.SEARCH_TABLE_BUTTON_CLICK_ENRICH_PHONE,
        },
        SidePanel: {
          [EnrichmentType.EMAIL]:
            SearchEvents.SEARCH_SIDE_PANEL_BUTTON_CLICK_ENRICH_MAIL,
          [EnrichmentType.PHONE]:
            SearchEvents.SEARCH_SIDE_PANEL_BUTTON_CLICK_ENRICH_PHONE,
        },
      },
    }
    const event = eventMap[pageContext]?.[context]?.[enrichmentType]
    if (event) {
      sendEvent(event)
    }
  }, [context, enrichmentType, pageContext, sendEvent])

  return currentPageContext?.page === LeadPageContext.ENRICHMENT_HUB ? (
    <EnrichmentHubButton
      isLoadingEnrichment={isLoadingEnrichment}
      conversionButtonProps={conversionButtonProps}
      handleEnrichment={handleEnrichment}
      currentProviderName={currentProviderName}
      loadingText={loadingText}
      lead={lead}
      enrichmentType={enrichmentType}
      isDisabled={isDisabled}
    >
      {children}
    </EnrichmentHubButton>
  ) : (
    <FeatureGatewayPopover
      {...featureGatewayPopoverProps}
      container={container}
      isActive={
        (enrichmentType === EnrichmentType.PHONE &&
          !hasEnoughCreditsToEnrichPhone) ||
        (enrichmentType === EnrichmentType.EMAIL &&
          !hasEnoughCreditsToEnrichEmail &&
          isFreeOrStarterPlan)
      }
      title={t('Buy more credits') as string}
      buttonLabel={t('Buy credits') as string}
      body={
        t(
          'You don’t have credits anymore. Subscribe to more credits and finish enriching your leads.'
        ) as string
      }
      className="w-full"
    >
      {isPendingEnrichment ? (
        <Button
          variant={'tertiary-outlined'}
          size={'medium'}
          dataTestId={`${idReferentials.enrichmentHub.button.leadActionCellEnrichmentHubPendingButton}-${lead.id}`}
          iconLeft={'Alarm'}
          disabled={isDisabled}
          fullWidth
        >
          {t('Pending')}
        </Button>
      ) : (
        <ConversionButton
          dataTestId=""
          {...conversionButtonProps}
          isLoading={isLoadingEnrichment}
          isFullWidth
          variant={ConversionButtonVariant.DATA}
          onClick={(e: MouseEvent<HTMLButtonElement>) => {
            e.stopPropagation()
            e.preventDefault()
            handleEnrichment()
            handleTracking()
          }}
          LoadingLabel={
            <LoadingLabel
              loadingText={loadingText}
              providerName={currentProviderName}
            />
          }
          isDisabled={isDisabled}
        >
          {children}
        </ConversionButton>
      )}
    </FeatureGatewayPopover>
  )
}
