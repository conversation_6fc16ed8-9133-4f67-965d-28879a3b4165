import isEmpty from 'lodash/isEmpty'
import { useCallback } from 'react'

import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import type { Contact, ExternalContact } from '@getheroes/shared'
import { EnrichmentType } from '@getheroes/shared'
import { Popover } from '@getheroes/ui'
import { ProviderList } from '@getheroes/ui-business'
import { LeadAction } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/LeadAction/LeadAction'
import { useGetProvidersStatus } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/hooks/useGetProvidersStatus'

export type EnrichmentWaterfallContentProps = {
  lead: Contact | ExternalContact
  enrichmentType: EnrichmentType
  handleClickEnrich: (
    fieldId:
      | ExclusiveContactLeadFieldEnum.PHONES
      | ExclusiveContactLeadFieldEnum.EMAILS
  ) => void
  isShowResults?: boolean
}

export const EnrichmentWaterfallContent = ({
  lead,
  enrichmentType,
  handleClickEnrich,
  isShowResults = true,
}: EnrichmentWaterfallContentProps) => {
  const { items, isLoadingEnrichment, hasAtLeastOneData } =
    useGetProvidersStatus({
      enrichmentType,
      lead,
    })

  const isShowEnrichmentWaterfall = !isEmpty(items) && isLoadingEnrichment
  const isShowData = hasAtLeastOneData && !isLoadingEnrichment && isShowResults

  const handleEnrichment = useCallback(() => {
    if (enrichmentType === EnrichmentType.EMAIL) {
      handleClickEnrich(ExclusiveContactLeadFieldEnum.EMAILS)
    }

    if (enrichmentType === EnrichmentType.PHONE) {
      handleClickEnrich(ExclusiveContactLeadFieldEnum.PHONES)
    }
  }, [enrichmentType, handleClickEnrich])

  return (
    (isShowEnrichmentWaterfall || isShowData) && (
      <Popover.Content>
        {isShowEnrichmentWaterfall && (
          <div className={'w-80'}>
            <ProviderList items={items} />
          </div>
        )}
        {isShowData && (
          <div className="z-zPopoverAboveDrawer w-[22rem]">
            <LeadAction
              lead={lead}
              onClickEnrich={handleEnrichment}
              isLoadingEnrichment={isLoadingEnrichment}
              enrichmentType={enrichmentType}
            />
          </div>
        )}
      </Popover.Content>
    )
  )
}
