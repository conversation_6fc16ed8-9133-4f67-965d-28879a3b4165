import type { Mouse<PERSON><PERSON>, PropsWithChildren } from 'react'
import { useTranslation } from 'react-i18next'

import { EnrichmentHubStatusEnum } from '@getheroes/shared'
import type {
  EnrichmentType,
  Contact,
  ExternalContact,
} from '@getheroes/shared'
import { Button, Typography } from '@getheroes/ui'
import { ConversionButton } from '@internals/components/common/navigation/Buttons/ConversionButton/ConversionButton'
import { ConversionButtonVariant } from '@internals/components/common/navigation/Buttons/ConversionButton/ConversionButton-export'
import { useEnrichmentHubDetail } from '@internals/features/enrichmentHub/hooks/useEnrichmentHubDetail'
import { LoadingLabel } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/components/EnrichmentWaterfallTrigger/components/LoadingLabel'
import type { EnrichmentWaterfallTriggerConversionButtonProps } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover/components/EnrichmentWaterfallTrigger/enrichment-waterfall-trigger.type'
import { idReferentials } from '@internals/utils/idReferentials'

interface EnrichmentHubButtonProps {
  isLoadingEnrichment: boolean
  conversionButtonProps: EnrichmentWaterfallTriggerConversionButtonProps
  handleEnrichment: () => void
  currentProviderName: string
  loadingText: string | null
  lead: Contact | ExternalContact
  enrichmentType: EnrichmentType
  isDisabled: boolean
}

export const EnrichmentHubButton = ({
  isLoadingEnrichment,
  conversionButtonProps,
  handleEnrichment,
  children,
  currentProviderName,
  loadingText,
  lead,
  isDisabled = false,
}: PropsWithChildren<EnrichmentHubButtonProps>) => {
  const { t } = useTranslation('lead')

  const { hubDetail } = useEnrichmentHubDetail()

  const isHubPartiallyDone =
    hubDetail?.data?.status === EnrichmentHubStatusEnum.PARTIALLY_DONE
  const isHubInError =
    hubDetail?.data?.status === EnrichmentHubStatusEnum.FAILED

  if (isHubPartiallyDone || isHubInError) {
    return null
  }

  return !isLoadingEnrichment ? (
    <Button
      variant={'tertiary-outlined'}
      size={'small'}
      dataTestId={`${idReferentials.enrichmentHub.button.leadActionCellEnrichmentHubPendingButton}-${lead.id}`}
      iconLeft={'Alarm'}
      disabled={isDisabled}
      fullWidth
    >
      <Typography color="base-placeholder">{t('Pending')}</Typography>
    </Button>
  ) : (
    <ConversionButton
      isDisabled={isDisabled}
      dataTestId={
        idReferentials.enrichmentHub.button
          .leadActionCellEnrichmentHubConversionButton
      }
      {...conversionButtonProps}
      isFullWidth
      isLoading={isLoadingEnrichment}
      variant={ConversionButtonVariant.DATA}
      onClick={(e: MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation()
        e.preventDefault()
        handleEnrichment()
      }}
      LoadingLabel={
        <LoadingLabel
          loadingText={loadingText}
          providerName={currentProviderName}
        />
      }
    >
      {children}
    </ConversionButton>
  )
}
