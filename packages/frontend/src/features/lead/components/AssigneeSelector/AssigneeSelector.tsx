import i18next from 'i18next'
import type { ChangeEvent } from 'react'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization, User } from '@getheroes/shared'
import { UserTitle } from '@internals/components/business/organization/user/UserTitle'
import type { SelectOption } from '@internals/components/common/dataEntry/Select/SelectInput'
import { SelectInput } from '@internals/components/common/dataEntry/Select/SelectInput'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

export type AssigneeSelectorProps = {
  name?: string
  showLabel?: boolean
  isStackingFormField?: boolean
  defaultUserId?: string
  placeholder?: string
} & (
  | {
      isMulti: true
      onChange: (userIds: string[]) => void
    }
  | {
      isMulti?: false
      onChange: (userId: string) => void
    }
)

/**
 * @deprecated use import { SelectUser } from '@getheroes/ui-business'
 */
export const AssigneeSelector = ({
  name = idReferentials.leads.components.AssigneeSelector.selectInput,
  onChange,
  showLabel = true,
  isStackingFormField = true,
  defaultUserId,
  isMulti = false,
  placeholder = i18next.t('Assign to...', { ns: 'lead' }) as string,
}: AssigneeSelectorProps) => {
  const { t } = useTranslation('common')
  const { members } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const membersOptions: SelectOption[] = useMemo(() => {
    return members
      .map(({ member }) => ({
        label: `${member.firstName} ${member.lastName}`,
        value: member.id,
        key: member.id,
        ...member,
      }))
      .sort((a, b) => a.label.localeCompare(b.label))
  }, [members])

  const defaultValue = useMemo(() => {
    if (defaultUserId) {
      return membersOptions.find(member => member.value === defaultUserId)
    }
    return undefined
  }, [defaultUserId, membersOptions])

  return (
    <SelectInput
      name={name}
      dataTestId={idReferentials.leads.components.AssigneeSelector.selectInput}
      isLoading={false}
      placeholder={placeholder}
      label={showLabel ? (t('Assign user') as string) : ''}
      isStackingFormfield={isStackingFormField}
      onChange={(e: ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value
        if (value) {
          onChange(value)
        }
      }}
      options={membersOptions}
      formatOptionLabel={option => (
        <UserTitle user={option as User} avatarColor="light" />
      )}
      defaultValue={defaultValue}
      isMulti={isMulti}
    />
  )
}
