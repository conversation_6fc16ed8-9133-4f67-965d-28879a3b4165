import { useTranslation } from 'react-i18next'

import { <PERSON><PERSON>, Help<PERSON>, I<PERSON><PERSON>utt<PERSON>, Typography } from '@getheroes/ui'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { DrawerNewEnrichmentAvailable } from '@internals/features/lead/components/Enrichment/NewEnrichmentAvailable/DrawerNewEnrichmentAvailable/DrawerNewEnrichmentAvailable'
import { useNewEnrichmentContext } from '@internals/features/lead/components/Enrichment/NewEnrichmentAvailable/Provider/useNewEnrichmentContext'
import { EnrichLeadsModal } from '@internals/features/lead/components/LeadsModals/EnrichLeadsModal/EnrichLeadsModal'
import { useEnrichPhoneOrEmail } from '@internals/features/lead/hooks/enrichment/useEnrichPhoneOrEmail'
import { idReferentials } from '@internals/utils/idReferentials'

export type BannerNewEnrichmentAvailableProps = {
  isClosable?: boolean
}

export const BannerNewEnrichmentAvailable = ({
  isClosable = false,
}: BannerNewEnrichmentAvailableProps) => {
  const { t } = useTranslation(['lead', 'common'])

  const {
    isModalOpen,
    handleCloseModal,
    handleOpenDrawer,
    handleCloseBanner,
    isShowBanner,
    afterSubmit,
  } = useNewEnrichmentContext()
  const { setSelections, selections, selectedRowsData } = useGridContext()
  const enrichPhoneOrEmail = useEnrichPhoneOrEmail()

  if (!isShowBanner) {
    return null
  }

  return (
    <>
      <Helper
        description={
          <Typography variant={'body'} size={'s'} weight={'medium'}>
            {t(
              'We have discovered additional data related to the leads you previously tried to enrich.'
            )}
          </Typography>
        }
        color={'blue-secondary'}
        endComponent={
          <>
            <Button
              size={'small'}
              onClick={handleOpenDrawer}
              dataTestId={
                idReferentials.leads.components.enrichment
                  .newEnrichmentAvailable.banner.seeDetailsButton
              }
            >
              {t('See details')}
            </Button>
            {isClosable && (
              <IconButton
                icon={'Xmark'}
                onClick={handleCloseBanner}
                size={'small'}
              />
            )}
          </>
        }
      />
      <DrawerNewEnrichmentAvailable />
      {isModalOpen && (
        <EnrichLeadsModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onClickEnrich={enrichmentType => {
            enrichPhoneOrEmail(Object.values(selectedRowsData), enrichmentType)
            setSelections([])
            afterSubmit()
          }}
          nbContacts={selections.length}
        />
      )}
    </>
  )

  return
}
