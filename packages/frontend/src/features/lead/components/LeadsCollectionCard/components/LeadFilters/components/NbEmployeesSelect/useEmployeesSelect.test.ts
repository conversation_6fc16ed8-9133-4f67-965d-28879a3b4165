import { renderHook } from '@testing-library/react-hooks'
import { describe, expect, it, vi } from 'vitest'
import { useEmployeesSelect } from './useEmployeesSelect'
import {
  LeadFilterType,
  OperatorFilterType,
} from '@internals/features/lead/types/leadFilterType'
import { ChangeEvent } from 'react'
import { ExclusiveCompanyLeadFieldEnum } from '@getheroes/frontend/types'
import { NbEmployeesList } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/components/NbEmployeesSelect/nbEmployees'

// Mock data
const mockOnChange = vi.fn()
const mockFilter: LeadFilterType = {
  field: ExclusiveCompanyLeadFieldEnum.INDUSTRY,
  operator: OperatorFilterType.ANY_OF_VALUES,
  id: 'test-id',
  from: '1',
  to: '10',
}

describe('useEmployeesSelect Hook', () => {
  it('should return the correct options and defaultValue', () => {
    const { result } = renderHook(() =>
      useEmployeesSelect({ onChange: mockOnChange, filter: mockFilter })
    )

    expect(result.current.options.length).toBe(
      Object.keys(NbEmployeesList).length
    )
    expect(result.current.defaultValue).toEqual({
      value: '1-10',
      label: '1-10',
    })
  })

  it('should trigger onChange with correct params on handleClick', () => {
    const { result } = renderHook(() =>
      useEmployeesSelect({ onChange: mockOnChange, filter: mockFilter })
    )

    const mockEvent = {
      target: { value: '1-10' },
    } as ChangeEvent<HTMLInputElement>

    result.current.handleClick(mockEvent)

    expect(mockOnChange).toHaveBeenCalledWith([
      { id: 'test-id', value: OperatorFilterType.BETWEEN, key: 'operator' },
      { id: 'test-id', value: '1-10', key: 'value' },
      { id: 'test-id', value: '1', key: 'from' },
      { id: 'test-id', value: '10', key: 'to' },
    ])
  })
})
