import { useState } from 'react'
import { useDebounce } from 'usehooks-ts'

import { SelectInput } from '@internals/components/common/dataEntry/Select/SelectInput'
import type { LeadsFilterSelectProps } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/components/selectComponents.types'
import { useSearchContactsDistinctFields } from '@internals/features/lead/hooks/searchLeads/useSearchContactsDistinctFields'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { capitalizeFirstLetter } from '@internals/utils/string'

const DEBOUNCE_TIME = 500

interface ContactsDistinctFieldsSelectProps extends LeadsFilterSelectProps {
  field: string
  placeholder: string
  shouldLowercaseLabels?: boolean
}

export const ContactsDistinctFieldsSelect = ({
  onChange,
  filter: { id, disabled, values },
  field,
  placeholder,
  shouldLowercaseLabels = true,
}: ContactsDistinctFieldsSelectProps) => {
  const [searchTerm, setSearchTerm] = useState('')

  const debouncedSearchTerm = useDebounce(searchTerm, DEBOUNCE_TIME)

  const { distinctFields, isFetching } = useSearchContactsDistinctFields({
    searchTerm: debouncedSearchTerm,
    field,
  })

  const options = distinctFields
    .map(field => ({
      value: field,
      label: shouldLowercaseLabels
        ? capitalizeFirstLetter(field.toLowerCase().replace(/_/g, ' '))
        : field,
    }))
    .sort((a, b) => a.label.localeCompare(b.label))

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange([
      {
        id,
        value: OperatorFilterType.ANY_OF_VALUES,
        key: 'operator',
      },
      {
        id,
        value: event.target.value,
        key: 'values',
      },
    ])
  }

  return (
    <SelectInput
      forceZindex
      onInputChange={setSearchTerm}
      name={`source-${id}`}
      placeholder={placeholder}
      className="w-full"
      isStackingFormfield={false}
      disabled={disabled}
      isMulti
      defaultValues={values?.map(value => ({ label: value, value }))}
      options={options}
      onChange={handleChange}
      isLoading={isFetching}
    />
  )
}
