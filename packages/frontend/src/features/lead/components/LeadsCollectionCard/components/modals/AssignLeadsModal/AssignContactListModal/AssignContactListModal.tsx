import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type {
  EnrichmentType,
  Contact,
  ExternalContact,
  Organization,
} from '@getheroes/shared'
import { EnrichmentBill } from '@internals/components/business/lead/contact/EnrichmentBill/EnrichmentBill'
import { SelectInput } from '@internals/components/common/dataEntry/Select/SelectInput'
import { AssignLeadsCommonModal } from '@internals/features/lead/components/LeadsCollectionCard/components/modals/AssignLeadsModal/AssignLeadsCommonModal/AssignLeadsCommonModal'
import { TransmitOrArchiveContactTasksModal } from '@internals/features/lead/components/LeadsModals/TransmitOrArchiveContactTasksModal/TransmitOrArchiveContactTasksModal'
import { isContactListWithTasks } from '@internals/features/lead/utils/isContactListWithTasks'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

export type AssignContactListModalProps = {
  isOpen: boolean
  isLoading?: boolean
  isMulti: boolean
  contactList: (Contact | ExternalContact)[]
  onSubmit: (data: {
    assigneeIdList: string[]
    enrichmentType?: EnrichmentType
    archiveExistingTasks?: boolean
  }) => void
  onClose: () => void
}

export const AssignContactListModal = ({
  isOpen,
  isLoading = false,
  isMulti,
  contactList,
  onSubmit,
  onClose,
}: AssignContactListModalProps) => {
  const { t } = useTranslation('lead')
  const { members, plan, creditBalance } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const [
    isOpenArchiveOrTransmitTasksModal,
    setIsOpenArchiveOrTransmitTasksModal,
  ] = useState(false)
  const [assigneeIdList, setAssigneeIdList] = useState<string[]>([])
  const [enrichmentCreditBill, setEnrichmentCreditBill] = useState(0)
  const [enrichmentType, setEnrichmentType] = useState<EnrichmentType>()

  const selectOptions = useMemo(
    () =>
      members.map(({ member }) => ({
        value: member.id,
        label: `${member.firstName} ${member.lastName}`,
        key: member.id,
      })),
    [members]
  )

  const contactListHasTasks = useMemo(() => {
    return isContactListWithTasks(contactList)
  }, [contactList])

  return (
    <>
      <AssignLeadsCommonModal
        title={t('Assign leads', { count: contactList.length }) as string}
        isOpen={!isOpenArchiveOrTransmitTasksModal && isOpen}
        handleClose={onClose}
        isLoading={isLoading}
        isContactsCategory={true}
        isDisabledSubmit={
          assigneeIdList.length === 0 || creditBalance < enrichmentCreditBill
        }
        onSubmit={() => {
          if (contactListHasTasks) {
            setIsOpenArchiveOrTransmitTasksModal(true)
            return
          }

          onSubmit({
            assigneeIdList,
            enrichmentType,
          })
        }}
        submitButtonLabel={
          t('Assign {{nbSelectedLeads}} {{leads}}', {
            leads: t('contacts'),
            nbSelectedLeads: contactList.length,
          }) as string
        }
      >
        <SelectInput
          isStackingFormfield={false}
          name={'select-user-input'}
          isMulti={isMulti}
          disabled={isLoading}
          options={selectOptions}
          onChange={(e: { target: { value: string | string[] } }) => {
            const value = e.target.value
            if (!value) {
              return
            }
            setAssigneeIdList((isMulti ? value : [value]) as string[])
          }}
          label={t('Assign leads to:', { count: contactList.length }) as string}
          forceZindex
          dataTestId={
            idReferentials.leads.components.leadsCollectionCard.components
              .modals.assignLeadsModal.assignLeadsToUserModal.selectAssignUser
          }
        />

        <div className="mt-4 p-4 border border-borderSubtle rounded-md">
          <EnrichmentBill
            nbContact={contactList.length}
            orgaPlan={plan}
            creditBalance={creditBalance}
            onChange={(bill, type) => {
              setEnrichmentCreditBill(bill)
              setEnrichmentType(type)
            }}
            title={
              t(
                'Do you want to find contact information after assignation?'
              ) as string
            }
          />
        </div>
      </AssignLeadsCommonModal>

      <TransmitOrArchiveContactTasksModal
        isOpen={isOpenArchiveOrTransmitTasksModal}
        isLoading={isLoading}
        handleClose={() => {
          setIsOpenArchiveOrTransmitTasksModal(false)
        }}
        handleConfirm={async (archiveExistingTasks: boolean) => {
          onSubmit({
            assigneeIdList,
            enrichmentType,
            archiveExistingTasks,
          })
          setIsOpenArchiveOrTransmitTasksModal(false)
        }}
        isMultipleContacts
      />
    </>
  )
}
