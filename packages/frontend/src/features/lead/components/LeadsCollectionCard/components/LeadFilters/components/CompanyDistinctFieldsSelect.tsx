import { useState } from 'react'
import { useDebounce } from 'usehooks-ts'

import { SelectInput } from '@internals/components/common/dataEntry/Select/SelectInput'
import type { LeadsFilterSelectProps } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/components/selectComponents.types'
import { useSearchCompaniesDistinctFields } from '@internals/features/lead/hooks/searchLeads/useSearchCompaniesDistinctFields'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { capitalizeFirstLetter } from '@internals/utils/string'

const DEBOUNCE_TIME = 500

interface CompanyDistinctFieldsSelectProps extends LeadsFilterSelectProps {
  field: string
  placeholder: string
}

export const CompanyDistinctFieldsSelect = ({
  onChange,
  filter: { id, disabled, values },
  field,
  placeholder,
}: CompanyDistinctFieldsSelectProps) => {
  const [searchTerm, setSearchTerm] = useState('')

  const debouncedSearchTerm = useDebounce(searchTerm, DEBOUNCE_TIME)

  const { distinctFields, isFetching } = useSearchCompaniesDistinctFields({
    searchTerm: debouncedSearchTerm,
    field,
  })

  const options = distinctFields
    .map(field => ({
      value: field,
      label: capitalizeFirstLetter(field.toLowerCase().replace(/_/g, ' ')),
    }))
    .sort((a, b) => a.label.localeCompare(b.label))

  const onClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange([
      {
        id,
        value: OperatorFilterType.ANY_OF_VALUES,
        key: 'operator',
      },
      {
        id,
        value: event.target.value,
        key: 'values',
      },
    ])
  }

  return (
    <SelectInput
      forceZindex
      onInputChange={setSearchTerm}
      name={`source-${id}`}
      placeholder={placeholder}
      className="w-full"
      isStackingFormfield={false}
      disabled={disabled}
      isMulti
      defaultValues={values?.map(value => ({ label: value, value }))}
      options={options}
      onChange={onClick}
      isLoading={isFetching}
    />
  )
}
