import { useState } from 'react'
import { useTranslation } from 'react-i18next'

import { SelectInput } from '@internals/components/common/dataEntry/Select/SelectInput'
import type { LeadsFilterSelectProps } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/components/selectComponents.types'
import { useSearchCompanyName } from '@internals/features/lead/hooks/searchLeads/useSearchCompanyName'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { capitalizeFirstLetter } from '@internals/utils/string'

export const CompanySelect = ({
  onChange,
  filter: { id, disabled, values },
}: LeadsFilterSelectProps) => {
  const { t } = useTranslation('lead')
  const [searchTerm, setSearchTerm] = useState('')

  const { companies } = useSearchCompanyName(searchTerm)
  const options = companies
    .map(company => ({
      value: company?.name,
      label: company?.name ? capitalizeFirstLetter(company?.name) : '',
    }))
    .sort((a, b) => a.label.localeCompare(b.label))

  const onClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange([
      {
        id,
        value: OperatorFilterType.ANY_OF_VALUES,
        key: 'operator',
      },
      {
        id,
        value: event.target.value,
        key: 'values',
      },
    ])
  }

  return (
    <SelectInput
      forceZindex
      onInputChange={setSearchTerm}
      name={`source-${id}`}
      placeholder={t('Select one or more companies') as string}
      className="w-full"
      isStackingFormfield={false}
      disabled={disabled}
      isMulti
      defaultValues={values?.map(value => ({ label: value, value }))}
      options={options}
      onChange={onClick}
    />
  )
}
