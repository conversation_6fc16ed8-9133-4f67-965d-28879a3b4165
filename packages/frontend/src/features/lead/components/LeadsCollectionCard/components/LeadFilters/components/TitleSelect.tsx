import { useTranslation } from 'react-i18next'

import { ContactsDistinctFieldsSelect } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/components/ContactDistinctFieldsSelect'
import type { LeadsFilterSelectProps } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/components/selectComponents.types'

export const TitleSelect = (props: LeadsFilterSelectProps) => {
  const { t } = useTranslation('lead')

  return (
    <ContactsDistinctFieldsSelect
      field={props.filter.field}
      placeholder={t('Select one or more job titles') as string}
      {...props}
    />
  )
}
