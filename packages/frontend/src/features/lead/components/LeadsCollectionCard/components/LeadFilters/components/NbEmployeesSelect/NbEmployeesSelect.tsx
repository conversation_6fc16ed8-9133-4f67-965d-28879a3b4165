import { useTranslation } from 'react-i18next'

import { SelectInput } from '@internals/components/common/dataEntry/Select/SelectInput'
import type { LeadsFilterSelectProps } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/components/selectComponents.types'

import { useEmployeesSelect } from './useEmployeesSelect'

export const NbEmployeesSelect = ({
  onChange,
  filter,
}: LeadsFilterSelectProps) => {
  const { t } = useTranslation('lead')
  const { id, disabled } = filter

  // eslint-disable-next-line import/namespace
  const { options, defaultValue, handleClick } = useEmployeesSelect({
    filter,
    onChange,
  })

  return (
    <SelectInput
      forceZindex
      name={`source-${id}`}
      placeholder={t('Select number of employees') as string}
      className="w-full"
      defaultValue={defaultValue}
      isStackingFormfield={false}
      disabled={disabled}
      options={options}
      onChange={handleClick}
    />
  )
}
