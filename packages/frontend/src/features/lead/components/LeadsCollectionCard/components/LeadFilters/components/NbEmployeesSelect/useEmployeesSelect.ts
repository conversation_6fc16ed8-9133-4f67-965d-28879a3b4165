import type { ChangeEvent } from 'react'
import { useMemo } from 'react'

import { NbEmployeesList } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/components/NbEmployeesSelect/nbEmployees'
import type {
  LeadFilterType,
  UpdateFilterParamsType,
} from '@internals/features/lead/types/leadFilterType'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'

const mapEnumToRange = (nbEmployees: any) => {
  const [from, toOrUndefined] = nbEmployees.split('-')
  // If the 'to' string is not a number (e.g. '+'), leave it as undefined
  const to = isNaN(parseInt(toOrUndefined)) ? undefined : toOrUndefined
  return { from, to }
}

export const useEmployeesSelect = ({
  onChange,
  filter,
}: {
  filter?: LeadFilterType
  onChange: (params: UpdateFilterParamsType[]) => void
}) => {
  const options = Object.entries(NbEmployeesList).map(([key, value]) => ({
    label: value,
    value,
  }))

  const defaultValue = useMemo(() => {
    const value = options.find(
      option => option.value === `${filter?.from}-${filter?.to}`
    )
    return value
      ? { value: value.value, label: `${filter?.from}-${filter?.to}` }
      : undefined
  }, [filter?.from, options, filter?.to])

  const handleClick = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event?.value ? event?.value : event?.target?.value
    const { from, to } = mapEnumToRange(value)
    if (!filter?.id) {
      return
    }
    const updateParams: UpdateFilterParamsType[] = [
      {
        id: filter?.id,
        value: OperatorFilterType.BETWEEN,
        key: 'operator',
      },
      {
        id: filter?.id,
        value,
        key: 'value',
      },
      {
        id: filter?.id,
        value: from,
        key: 'from',
      },
    ]
    if (to) {
      updateParams.push({
        id: filter?.id,
        value: to,
        key: 'to',
      })
    }
    onChange(updateParams)
  }

  return {
    options,
    defaultValue,
    handleClick,
  }
}
