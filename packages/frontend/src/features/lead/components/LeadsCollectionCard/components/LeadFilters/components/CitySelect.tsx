import { useTranslation } from 'react-i18next'

import { CompanyDistinctFieldsSelect } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/components/CompanyDistinctFieldsSelect'
import type { LeadsFilterSelectProps } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/components/selectComponents.types'

export const CitySelect = (props: LeadsFilterSelectProps) => {
  const { t } = useTranslation('lead')

  return (
    <CompanyDistinctFieldsSelect
      field={'city'}
      placeholder={t('Select one or more cities') as string}
      {...props}
    />
  )
}
