import type { LeadCategory, ExportedFieldType } from '@getheroes/shared'
import type { LeadFilterApiType } from '@internals/models/lead'

export type Fields = { id: string; name: string }[] | []

export type ExportLeadsTableRequestType = {
  leadCategory: LeadCategory
  organizationId: string
  assignedTo?: string
  searchText?: string
  filters?: LeadFilterApiType[]
  exportFields: Fields
  enrichmentHubImportId?: string
  selection?: string[]
  exportType?: ExportedFieldType
}
