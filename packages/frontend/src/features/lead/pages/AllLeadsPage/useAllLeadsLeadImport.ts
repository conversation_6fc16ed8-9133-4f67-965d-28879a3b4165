import { useOnExitLeadImport } from '@internals/features/leadImport/core/hooks/useOnExitLeadImport'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { useImportModuleStepperContext } from '@internals/features/leadImport/core/providers/ImportModuleStepperProvider/useImportModuleStepperContext'

export const useAllLeadsLeadImport = () => {
  const { resetState } = useImportModuleContext()
  const { resetStepperState } = useImportModuleStepperContext()

  useOnExitLeadImport(() => {
    resetState()
    resetStepperState()
  })
}
