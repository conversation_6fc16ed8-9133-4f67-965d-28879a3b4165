import { type FetchBaseQueryError } from '@reduxjs/toolkit/dist/query'

import { api } from '@getheroes/frontend/config/api'
import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import type {
  GetContactExportableFieldsResponseDTO,
  Contact,
  MatchingContactsPayloadType,
  MatchingContactsResponseType,
  ContactWithMatchingSourceAndScoring,
  TransformedMatchingContactsResponseType,
} from '@getheroes/shared'
import i18n from '@internals/config/i18n'
import {
  updateContactOptimistic,
  updateManyContactOptimistic,
} from '@internals/features/lead/api/optimistic/contactOptimistic'
import type {
  Activity,
  GetContactActivitiesPayload,
} from '@internals/features/lead/types/activity'
import type {
  ArchiveContactsPayload,
  CountEngagementPriorityPayload,
  CountEngagementPriorityResponse,
  CountStatusWeeklySuccessPayload,
  CountStatusWeeklySuccessResponse,
  CreateContactPayloadType,
  CreateLeadContactsBulkPayload,
  EditContactBulkPayloadType,
  EditContactPayloadType,
  GetContactPayloadType,
  SearchContactsDistinctFieldsResponseType,
  SearchInternalContactsResponseType,
} from '@internals/features/lead/types/contactType'
import type {
  SearchContactsDistinctFieldsPayloadType,
  SearchLeadsPayloadType,
  SearchMyLeadsPayloadType,
} from '@internals/features/lead/types/genericLeadType'
import type { LeadFilterType } from '@internals/features/lead/types/leadFilterType'
import type { LeadsFieldResponseType } from '@internals/features/lead/types/leadsTableColumn'
import type { SearchExternalLeadsFilter } from '@internals/features/search/types/searchLeadsType'
import type { LeadFilterApiType } from '@internals/models/lead'
import { getPaginationQueryString } from '@internals/utils/getPaginationQueryString'

export const contactApi = api.injectEndpoints({
  endpoints: builder => {
    return {
      // ********************************************************
      // Use case : search contacts associated to an organization
      // ********************************************************
      searchContacts: builder.query<
        {
          items: Record<string, Contact>
          meta: SearchInternalContactsResponseType['meta']
        },
        SearchLeadsPayloadType
      >({
        query: ({ organizationId, searchText, filters, ...paginationQs }) => {
          const params = getPaginationQueryString(paginationQs)
          return {
            url: `${organizationId}/leads/contacts/search?${params.toString()}`,
            method: 'POST',
            body: {
              searchText: searchText,
              filters: filters,
            },
          }
        },
        providesTags: () => {
          return [
            { type: 'Contact' as const, id: 'ORGANIZATION_CONTACTS_LEADS' },
          ]
        },
        transformResponse: (
          data: SearchInternalContactsResponseType
        ): {
          items: Record<string, Contact>
          meta: SearchInternalContactsResponseType['meta']
        } => {
          return {
            ...data,
            items: Object.values(data.items).reduce(
              (acc: Record<string, Contact>, contact: Contact) => {
                if (contact?.id !== undefined) {
                  acc[contact.id] = contact
                }
                return acc
              },
              {}
            ),
          }
        },
        transformErrorResponse: () => ({
          message: i18n.t('An error occurred while searching contacts'),
        }),
      }),
      // ********************************************************
      // Use case : search contacts assigned to me
      // ********************************************************
      searchMyContacts: builder.query<
        {
          items: Record<string, Contact>
          meta: SearchInternalContactsResponseType['meta']
        },
        SearchMyLeadsPayloadType
      >({
        query: ({ organizationId, searchText, filters, ...paginationQs }) => {
          const params = getPaginationQueryString(paginationQs)

          return {
            url: `${organizationId}/leads/contacts/search?${params.toString()}`,
            method: 'POST',
            body: {
              searchText: searchText,
              filters: filters,
            },
          }
        },
        transformResponse: (data: SearchInternalContactsResponseType) => {
          return {
            ...data,
            items: Object.values(data.items).reduce(
              (acc: Record<string, Contact>, contact: Contact) => {
                if (contact?.id) {
                  acc[contact.id] = contact
                }
                return acc
              },
              {}
            ),
          }
        },
        providesTags: () => {
          return [{ type: 'Contact', id: 'MY_CONTACTS_LEADS' }]
        },
        transformErrorResponse: () => {
          return {
            message: i18n.t('An error occurred while searching contacts'),
          }
        },
      }),
      // ********************************************************
      // Use case : search matching contacts
      // ********************************************************
      searchMatchingContactsForChromeExtension: builder.query<
        TransformedMatchingContactsResponseType,
        MatchingContactsPayloadType
      >({
        query: payload => {
          if (!payload) {
            return ''
          }
          const { organizationId, matchingInput } = payload
          return {
            url: `${organizationId}/matching/chrome-extension/contacts`,
            method: 'POST',
            body: matchingInput,
          }
        },
        providesTags: () => [
          { type: 'Contact', id: 'CHROME_EXTENSION_MATCHING_CONTACTS' },
        ],
        transformResponse: (data: MatchingContactsResponseType) => {
          return data.map(({ source, matches }) => ({
            source,
            matches: Object.values(matches).reduce(
              (acc, contact) => {
                if (contact?.contact?.id) {
                  acc[contact.contact?.id] = contact
                }
                return acc
              },
              {} as Record<string, ContactWithMatchingSourceAndScoring>
            ),
          }))
        },
        transformErrorResponse: () => {
          return {
            message: i18n.t('An error occurred while searching contacts'),
          }
        },
      }),
      createLeadContactsBulk: builder.mutation({
        query: (payload: CreateLeadContactsBulkPayload) => {
          const { organizationId, contacts } = payload
          return {
            url: `${organizationId}/leads/contacts/bulk`,
            method: 'POST',
            body: { contacts },
          }
        },
        invalidatesTags: [
          { type: 'FeatureGateway', id: 'GET_USAGE' },
          { type: 'Contact', id: 'ORGANIZATION_CONTACTS_LEADS' },
          { type: 'Contact', id: 'MY_CONTACTS_LEADS' },
          { type: 'Contact', id: 'CHROME_EXTENSION_MATCHING_CONTACTS' },
          { type: 'Search', id: 'ALL_CONTACTS_LEADS' },
          { type: 'Lead', id: 'COUNT_LEADS' },
          // when creating a contact in Search, the related company might also be created if it doesn't exist
          { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
          { type: 'Company', id: 'MY_COMPANIES_LEADS' },
          { type: 'Company', id: 'MATCHING_COMPANIES' },
        ],
      }),
      getContactTableColumns: builder.query<
        LeadsFieldResponseType[],
        { organizationId: string | undefined }
      >({
        query: ({ organizationId }) => `${organizationId}/leads/fields/contact`,
        providesTags: () => [{ type: 'Contact', id: 'CONTACT_FIELDS' }],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'An error occurred while retrieving the contacts fields',
            { ns: 'lead' }
          )
          return { data: { message: errorMsg } }
        },
      }),
      getContactFields: builder.query<
        LeadsFieldResponseType[],
        { organizationId: string | undefined }
      >({
        query: ({ organizationId }) => `${organizationId}/leads/fields/contact`,
        providesTags: () => [{ type: 'Contact', id: 'CONTACT_FIELDS' }],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'An error occurred while retrieving the contacts fields',
            { ns: 'lead' }
          )
          return { data: { message: errorMsg } }
        },
      }),
      getContact: builder.query<Contact, GetContactPayloadType>({
        query: ({ organizationId, contactId }) => {
          return `${organizationId}/leads/contacts/${contactId}`
        },
        providesTags: (_, __, args) => [
          { type: 'Contact', id: 'BY_ID' },
          {
            type: 'Contact',
            id: `CONTACT_${args.contactId}`,
          },
        ],
      }),
      editContactById: builder.mutation({
        query: (payload: EditContactPayloadType) => {
          const { contactId, organizationId, ...rest } = payload
          return {
            url: `/${organizationId}/leads/contacts/${contactId}`,
            method: 'PATCH',
            body: rest,
          }
        },
        invalidatesTags: (_, error, args) => {
          if (error) {
            return []
          }
          return [
            { type: 'Contact', id: 'COUNT_WEEKLY_SUCCESS' },
            { type: 'Contact', id: '' },
            { type: 'Sequence', id: `SEQUENCE_CONTACT_STEP_${args.contactId}` },
            {
              type: 'Sequence',
              id: `SEQUENCE_CONTACT_ACTIVITIES_${args.contactId}`,
            },
          ]
        },
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an error while editing the contact',
            {
              ns: 'lead',
            }
          )
          return { data: { message: errorMsg } }
        },
        async onQueryStarted(
          { contactId },
          { dispatch, queryFulfilled, getState }
        ) {
          const { data } = await queryFulfilled

          const attributeToUpdate = data as Contact

          updateContactOptimistic({
            id: contactId,
            attributeToUpdate,
            dispatch,
            queryFulfilled,
            getState,
          })
        },
      }),
      editContactsBulk: builder.mutation<
        Array<Contact>,
        EditContactBulkPayloadType
      >({
        query: payload => {
          const { organizationId, ...rest } = payload
          return {
            url: `/${organizationId}/leads/contacts`,
            method: 'PATCH',
            body: {
              ...rest,
            },
          }
        },
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an error while editing the contacts status',
            {
              ns: 'lead',
            }
          )
          return { data: { message: errorMsg } }
        },
        onQueryStarted(
          { contactIds, organizationId, ...attributeAtUpdate },
          { dispatch, queryFulfilled, getState }
        ) {
          // Format data at update to match with the API
          let newAttributeAtUpdated = {}
          if (attributeAtUpdate?.labels) {
            newAttributeAtUpdated = {
              labels: attributeAtUpdate?.labels?.add,
            }
          }
          newAttributeAtUpdated = {
            ...attributeAtUpdate,
            ...newAttributeAtUpdated,
          }
          updateManyContactOptimistic({
            ids: contactIds,
            attributeAtUpdate: newAttributeAtUpdated,
            dispatch,
            queryFulfilled,
            getState,
          })
        },
      }),
      createLeadContact: builder.mutation<Contact, CreateContactPayloadType>({
        query: payload => {
          const { organizationId, ...rest } = payload
          return {
            url: `/${organizationId}/leads/contacts`,
            method: 'POST',
            body: rest,
          }
        },
        async onQueryStarted(_, { dispatch, queryFulfilled }) {
          try {
            await queryFulfilled
            // Add delay before invalidating tags
            // It's a patch for API issue (elastic search re-indexing)
            await new Promise(resolve => setTimeout(resolve, 1000))

            dispatch(
              api.util.invalidateTags([
                { type: 'FeatureGateway', id: 'GET_USAGE' },
                { type: 'Contact', id: 'ORGANIZATION_CONTACTS_LEADS' },
                { type: 'Contact', id: 'MY_CONTACTS_LEADS' },
                { type: 'Contact', id: 'CHROME_EXTENSION_MATCHING_CONTACTS' },
                { type: 'Lead', id: 'COUNT_LEADS' },
                { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
                { type: 'Company', id: 'MY_COMPANIES_LEADS' },
                { type: 'Search', id: 'ALL_CONTACTS_LEADS' },
                { type: 'Company', id: 'MATCHING_COMPANIES' },
              ])
            )
          } catch {
            // eslint-disable-next-line no-console
            console.error('Error while creating contact')
          }
        },
        transformErrorResponse: (error: FetchBaseQueryError) => {
          const isPhoneNumberInvalid = (error.data as Error).message.includes(
            ExclusiveContactLeadFieldEnum.PHONES
          )
          const errorMsg = i18n.t(
            isPhoneNumberInvalid
              ? 'You must enter a valid phone number'
              : 'There was an error while creating the contact',
            {
              ns: 'lead',
            }
          )
          return { data: { message: errorMsg } }
        },
      }),
      archiveContactById: builder.mutation({
        query: ({ organizationId, contactId }) => {
          return {
            url: `${organizationId}/leads/contacts/${contactId}/archive`,
            method: 'POST',
          }
        },
        invalidatesTags: (_, __, args) => [
          { type: 'Contact', id: 'ORGANIZATION_CONTACTS_LEADS' },
          { type: 'Contact', id: 'MY_CONTACTS_LEADS' },
          { type: 'Contact', id: 'CHROME_EXTENSION_MATCHING_CONTACTS' },
          { type: 'Contact', id: `CONTACT_${args.contactId}` },
          { type: 'Company', id: 'MATCHING_COMPANIES' },
        ],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an error while archiving the contact',
            {
              ns: 'lead',
            }
          )
          return { message: errorMsg }
        },
      }),
      archiveContacts: builder.mutation({
        query: ({ organizationId, contactsIds }: ArchiveContactsPayload) => {
          return {
            url: `${organizationId}/leads/contacts/archive`,
            method: 'POST',
            body: { contacts: contactsIds },
          }
        },
        invalidatesTags: [
          { type: 'Lead', id: 'COUNT_LEADS' },
          { type: 'Contact', id: 'ORGANIZATION_CONTACTS_LEADS' },
          { type: 'Contact', id: 'MY_CONTACTS_LEADS' },
          { type: 'Contact', id: 'CHROME_EXTENSION_MATCHING_CONTACTS' },
          { type: 'Contact', id: 'BY_ID' },
          { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
          { type: 'Company', id: 'MY_COMPANIES_LEADS' },
          { type: 'Company', id: 'MATCHING_COMPANIES' },
        ],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an error while archiving the {{leads}}',
            {
              leads: i18n.t('contacts'),
              ns: 'lead',
            }
          )
          return { message: errorMsg }
        },
      }),
      unarchiveContacts: builder.mutation<
        Contact[],
        {
          ids: string[]
          organizationId: string
          companiesToUnarchive?: { contactId: string; companyId: string }[]
        }
      >({
        query: ({ ids, organizationId, ...rest }) => {
          return {
            url: `/${organizationId}/leads/contacts/unarchive`,
            method: 'POST',
            body: {
              contacts: ids,
              ...rest,
            },
          }
        },
      }),
      getContactActivities: builder.query<
        Activity[],
        GetContactActivitiesPayload
      >({
        query: ({ organizationId, contactId }) => {
          return `${organizationId}/leads/contacts/${contactId}/activities`
        },
        providesTags: () => [{ type: 'Activity', id: 'LIST' }],
      }),
      searchContactsDistinctFields: builder.query<
        SearchContactsDistinctFieldsResponseType,
        SearchContactsDistinctFieldsPayloadType
      >({
        query: ({
          organizationId,
          distinctField,
          searchText,
          filters,
          limitPerPage,
        }) => {
          const urlSearchParams = new URLSearchParams()
          if (limitPerPage) {
            urlSearchParams.append('limitPerPage', limitPerPage.toString())
          }

          return {
            url: `${organizationId}/leads/contacts/distinct/${distinctField}?${urlSearchParams.toString()}`,
            method: 'POST',
            body: {
              searchText: searchText,
              filters: filters,
            },
          }
        },
        transformResponse: (data: string[]) => {
          return {
            items: data,
          }
        },
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'An error occurred while searching {{leads}} distinct fields',
            {
              ns: 'lead',
              leads: i18n.t('contacts'),
            }
          )
          return { message: errorMsg }
        },
      }),
      generateStrategy: builder.mutation({
        query: ({ organizationId, contactId, locale }) => {
          return {
            url: `${organizationId}/leads/contacts/${contactId}/generate-strategy`,
            method: 'POST',
            body: { locale },
          }
        },
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an error while generating the strategy',
            {
              ns: 'lead',
            }
          )
          return { message: errorMsg }
        },
      }),
      updateEngagementScoringPriority: builder.mutation({
        query: ({ organizationId, contactId, engagementScoringPriority }) => {
          return {
            url: `${organizationId}/leads/contacts/${contactId}/priority`,
            method: 'PATCH',
            body: { engagementScoringPriority },
          }
        },
        invalidatesTags: (_, __, args) => [
          { type: 'Contact', id: 'MY_CONTACTS_LEADS' },
          { type: 'Contact', id: 'CHROME_EXTENSION_MATCHING_CONTACTS' },
          { type: 'Contact', id: 'ORGANIZATION_CONTACTS_LEADS' },
          { type: 'Contact', id: `CONTACT_${args.contactId}` },
          { type: 'Company', id: 'MATCHING_COMPANIES' },
        ],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an error while update the engagement priority',
            {
              ns: 'lead',
            }
          )
          return { message: errorMsg }
        },
      }),
      getCountEngagementPriority: builder.query<
        CountEngagementPriorityResponse,
        CountEngagementPriorityPayload
      >({
        query: ({ organizationId }) => {
          return {
            url: `${organizationId}/users/leads/contacts/count/engagement`,
            method: 'GET',
          }
        },
      }),
      getCountStatusWeeklySuccess: builder.query<
        CountStatusWeeklySuccessResponse,
        CountStatusWeeklySuccessPayload
      >({
        query: ({ organizationId }) => {
          return `/${organizationId}/count/weekly`
        },
        providesTags: () => [
          { type: 'Contact' as const, id: 'COUNT_WEEKLY_SUCCESS' },
        ],
      }),
      getContactExportableFields: builder.query<
        GetContactExportableFieldsResponseDTO,
        {
          organizationId: string
          searchText?: string
          filters?:
            | (SearchExternalLeadsFilter | LeadFilterApiType)[]
            | LeadFilterType[]
        }
      >({
        query: ({ organizationId, searchText, filters }) => {
          return {
            url: `${organizationId}/leads/contacts/exportable-fields`,
            method: 'POST',
            body: {
              searchText: searchText,
              filters: filters,
            },
          }
        },
        keepUnusedDataFor: 24 * 60 * 60,
        providesTags: () => [
          {
            type: 'Contact' as const,
            id: 'GET_CONTACT_EXPORTABLE_FIELDS_AND_COUNTERS',
          },
        ],
      }),
    }
  },
})

export const {
  useSearchContactsQuery,
  useLazySearchContactsQuery,
  useSearchMyContactsQuery,
  useSearchMatchingContactsForChromeExtensionQuery,
  useLazySearchMatchingContactsForChromeExtensionQuery,
  useCreateLeadContactsBulkMutation,
  useLazyGetContactTableColumnsQuery,
  useGetContactTableColumnsQuery,
  useGetContactFieldsQuery,
  useLazyGetContactFieldsQuery,
  useGetContactQuery,
  useLazyGetContactQuery,
  useEditContactByIdMutation,
  useGetContactActivitiesQuery,
  useCreateLeadContactMutation,
  useLazySearchContactsDistinctFieldsQuery,
  useArchiveContactsMutation,
  useUnarchiveContactsMutation,
  useGenerateStrategyMutation,
  useEditContactsBulkMutation,
  useUpdateEngagementScoringPriorityMutation,
  useGetCountEngagementPriorityQuery,
  useGetCountStatusWeeklySuccessQuery,
  useGetContactExportableFieldsQuery,
} = contactApi

export const { searchContacts } = contactApi.endpoints
