import { api } from '@getheroes/frontend/config/api'
import type {
  AutocompleteOption,
  GetAutocompleteFilterPayloadType,
} from '@internals/features/search/types/searchLeadsType'

export const filtersApi = api.injectEndpoints({
  endpoints: builder => {
    return {
      getAutocompleteFilter: builder.query<
        AutocompleteOption[],
        GetAutocompleteFilterPayloadType
      >({
        query: ({ searchText, organizationId, filterId }) =>
          `${organizationId}/leads/search/filters/autocomplete/${filterId}?searchText=${searchText}`,
        providesTags: (_, query, args) => {
          return [
            {
              type: 'SearchFilters',
              id: `AUTOCOMPLETE_FILTER_${args.filterId}`,
            },
          ]
        },
      }),
    }
  },
})

export const {
  useLazyGetAutocompleteFilterQuery,
  useGetAutocompleteFilterQuery,
} = filtersApi
