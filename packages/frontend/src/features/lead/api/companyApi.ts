import { api } from '@getheroes/frontend/config/api'
import type {
  GetCompanyExportableFieldsResponseDTO,
  Company,
  TransformedMatchingCompaniesResponseType,
  MatchingCompaniesPayloadType,
  MatchingCompaniesResponseType,
  CompanyWithMatchingSourceAndScoring,
} from '@getheroes/shared'
import i18n from '@internals/config/i18n'
import { updateCompanyOptimistic } from '@internals/features/lead/api/optimistic/companyOptimistic'
import type {
  ArchiveCompaniesPayload,
  CreateCompaniesPayloadType,
  CreateCompanyPayloadType,
  EditCompanyPayloadType,
  GetCompanyActivityRequestType,
  GetCompanyActivityResponseType,
  GetCompanyPayloadType,
  SearchCompaniesDistinctFieldsResponseType,
  SearchCompaniesResponseType,
} from '@internals/features/lead/types/companyType'
import type {
  SearchCompaniesDistinctFieldsPayloadType,
  SearchLeadsPayloadType,
} from '@internals/features/lead/types/genericLeadType'
import type { LeadFilterType } from '@internals/features/lead/types/leadFilterType'
import type { LeadsFieldResponseType } from '@internals/features/lead/types/leadsTableColumn'
import type { SearchExternalLeadsFilter } from '@internals/features/search/types/searchLeadsType'
import type { LeadFilterApiType } from '@internals/models/lead'

export const companyApi = api.injectEndpoints({
  endpoints: builder => {
    return {
      searchCompanies: builder.query<
        SearchCompaniesResponseType,
        SearchLeadsPayloadType
      >({
        query: ({
          organizationId,
          order,
          orderBy,
          page,
          limitPerPage,
          searchText,
          filters,
        }) => {
          const params = new URLSearchParams()
          if (order) {
            params.append('order', order)
          }
          if (orderBy && order) {
            params.append('orderBy', orderBy)
          }
          if (page) {
            params.append('page', page.toString())
          }
          if (limitPerPage) {
            params.append('limitPerPage', limitPerPage.toString())
          }
          return {
            url: `${organizationId}/leads/companies/search${
              params.toString() ? `?${params.toString()}` : ''
            }`,
            method: 'POST',
            body:
              {
                searchText: searchText,
                filters: filters,
              } || {},
          }
        },
        providesTags: () => [
          { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
        ],
        transformResponse: (data: SearchCompaniesResponseType) => {
          return {
            ...data,
            items: data.items.reduce(
              (acc: Record<string, Company>, company: Company) => {
                acc[company.id] = company
                return acc
              },
              {}
            ),
          }
        },
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'An error occurred while searching companies',
            {
              ns: 'lead',
            }
          )
          return { message: errorMsg }
        },
      }),
      searchMyCompanies: builder.query<
        SearchCompaniesResponseType,
        SearchLeadsPayloadType & { userId: string }
      >({
        query: ({
          organizationId,
          order,
          orderBy,
          page,
          limitPerPage,
          searchText,
          filters,
          userId,
        }) => {
          const params = new URLSearchParams()
          if (order) {
            params.append('order', order)
          }
          if (orderBy && order) {
            params.append('orderBy', orderBy)
          }
          if (page) {
            params.append('page', page.toString())
          }
          if (limitPerPage) {
            params.append('limitPerPage', limitPerPage.toString())
          }
          if (userId) {
            params.append('assignedTo', userId)
          }
          return {
            url: `${organizationId}/leads/companies/search${
              params.toString() ? `?${params.toString()}` : ''
            }`,
            method: 'POST',
            body: {
              searchText,
              filters,
            },
          }
        },
        providesTags: () => [{ type: 'Company', id: 'MY_COMPANIES_LEADS' }],
        transformResponse: (data: SearchCompaniesResponseType) => {
          return {
            ...data,
            items: data.items.reduce(
              (acc, company) => {
                acc[company.id] = company
                return acc
              },
              {} as Record<string, Company>
            ),
          }
        },
        transformErrorResponse: () => {
          return {
            message: i18n.t('An error occurred while searching companies'),
          }
        },
      }),
      searchMatchingCompanies: builder.query<
        TransformedMatchingCompaniesResponseType,
        MatchingCompaniesPayloadType
      >({
        query: payload => {
          if (!payload) {
            return ''
          }
          const { organizationId, source } = payload
          return {
            url: `${organizationId}/matching/companies`,
            method: 'POST',
            body: [...source],
          }
        },
        providesTags: () => [{ type: 'Company', id: 'MATCHING_COMPANIES' }],
        transformResponse: (data: MatchingCompaniesResponseType) => {
          return data.map(({ source, matches }) => ({
            source,
            matches: Object.values(matches).reduce(
              (acc, company) => {
                if (company?.company?.id) {
                  acc[company.company?.id] = company
                }
                return acc
              },
              {} as Record<string, CompanyWithMatchingSourceAndScoring>
            ),
          }))
        },
        transformErrorResponse: () => {
          return {
            message: i18n.t('An error occurred while searching companies'),
          }
        },
      }),
      getCompanyTableColumns: builder.query<
        LeadsFieldResponseType[],
        { organizationId: string }
      >({
        query: ({ organizationId }) => `${organizationId}/leads/fields/company`,
        providesTags: () => [{ type: 'Company', id: 'COMPANY_FIELDS' }],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'An error occurred while retrieving the companies fields',
            { ns: 'lead' }
          )
          return { data: { message: errorMsg } }
        },
      }),
      getCompanyFields: builder.query<
        LeadsFieldResponseType[],
        { organizationId: string | undefined }
      >({
        query: ({ organizationId }) => `${organizationId}/leads/fields/company`,
        providesTags: () => [{ type: 'Company', id: 'COMPANY_FIELDS' }],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'An error occurred while retrieving the companies fields',
            { ns: 'lead' }
          )
          return { data: { message: errorMsg } }
        },
      }),
      getCompany: builder.query<Company, GetCompanyPayloadType>({
        query: ({ organizationId, companyId }) => {
          return `${organizationId}/leads/companies/${companyId}`
        },
        providesTags: (_, __, args) => [
          { type: 'Company', id: 'BY_ID' },
          { type: 'Company', id: `COMPANY_${args.companyId}` },
        ],
        transformResponse: (summary: Company) => summary,
      }),
      editCompanyById: builder.mutation({
        query: (payload: EditCompanyPayloadType) => {
          const { companyId, organizationId, ...rest } = payload
          return {
            url: `/${organizationId}/leads/companies/${companyId}`,
            method: 'PATCH',
            body: rest,
          }
        },
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an error while editing the company',
            {
              ns: 'lead',
            }
          )
          return { data: { message: errorMsg } }
        },
        async onQueryStarted(
          { companyId, organizationId, ...attributeAtUpdate },
          { dispatch, queryFulfilled, getState }
        ) {
          updateCompanyOptimistic({
            id: companyId,
            attributeAtUpdate,
            dispatch,
            queryFulfilled,
            getState,
          })
        },
      }),
      createLeadCompany: builder.mutation({
        query: (payload: CreateCompanyPayloadType) => {
          const { organizationId, ...rest } = payload
          return {
            url: `/${organizationId}/leads/companies`,
            method: 'POST',
            body: rest,
          }
        },
        async onQueryStarted(_, { dispatch, queryFulfilled }) {
          try {
            await queryFulfilled
            // Add delay before invalidating tags
            // It's a patch for API issue (elastic search re-indexing)
            await new Promise(resolve => setTimeout(resolve, 1000))

            dispatch(
              api.util.invalidateTags([
                { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
                { type: 'Company', id: 'MY_COMPANIES_LEADS' },
                { type: 'Lead', id: 'COUNT_LEADS' },
                { type: 'Search', id: 'ALL_COMPANIES_LEADS' },
                { type: 'Company', id: 'MATCHING_COMPANIES' },
              ])
            )
          } catch {
            // eslint-disable-next-line no-console
            console.error('Error while creating contact')
          }
        },
        transformErrorResponse: error => {
          const isCompanyExists = error.data.error === 'company_exists'
          const isPhoneNumberInvalid = error.data.message.includes(
            'switchboardPhoneNumber'
          )
          const errorMsg = i18n.t(
            isCompanyExists
              ? 'A company with this name already exists'
              : isPhoneNumberInvalid
                ? 'You must enter a valid phone number'
                : 'There was an error while creating the company',
            {
              ns: 'lead',
            }
          )
          return { data: { message: errorMsg } }
        },
      }),
      createCompanies: builder.mutation({
        query: (payload: CreateCompaniesPayloadType) => {
          const { organizationId, ...rest } = payload
          return {
            url: `/${organizationId}/leads/companies/bulk`,
            method: 'POST',
            body: rest,
          }
        },
        invalidatesTags: [
          { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
          { type: 'Company', id: 'MY_COMPANIES_LEADS' },
          { type: 'Lead', id: 'COUNT_LEADS' },
          { type: 'Search', id: 'ALL_COMPANIES_LEADS' },
          { type: 'Search', id: 'ALL_CONTACTS_LEADS' },
          { type: 'Company', id: 'MATCHING_COMPANIES' },
        ],
        transformErrorResponse: error => {
          const isCompanyExists = error.data.error === 'company_exists'
          const isPhoneNumberInvalid = error.data.message.includes(
            'switchboardPhoneNumber'
          )
          const errorMsg = i18n.t(
            isCompanyExists
              ? 'A company with this name already exists'
              : isPhoneNumberInvalid
                ? 'You must enter a valid phone number'
                : 'There was an error while creating the company',
            {
              ns: 'lead',
            }
          )
          return { data: { message: errorMsg } }
        },
      }),
      archiveCompanyById: builder.mutation({
        query: ({ organizationId, companyId }) => {
          return {
            url: `${organizationId}/leads/companies/${companyId}/archive`,
            method: 'POST',
          }
        },
        invalidatesTags: (_, __, args) => [
          { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
          { type: 'Company', id: 'MY_COMPANIES_LEADS' },
          { type: 'Company', id: `COMPANY_${args.companyId}` },
          { type: 'Lead', id: 'COUNT_LEADS' },
          { type: 'Search', id: 'ALL_COMPANIES_LEADS' },
          { type: 'Company', id: 'MATCHING_COMPANIES' },
        ],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an error while archiving the company',
            {
              ns: 'lead',
            }
          )
          return { data: { message: errorMsg } }
        },
      }),
      archiveCompanies: builder.mutation({
        query: ({ organizationId, companiesIds }: ArchiveCompaniesPayload) => {
          return {
            url: `${organizationId}/leads/companies/archive`,
            method: 'POST',
            body: { companies: companiesIds },
          }
        },
        invalidatesTags: [
          { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
          { type: 'Company', id: 'MY_COMPANIES_LEADS' },
          { type: 'Company', id: 'BY_ID' },
          { type: 'Lead', id: 'COUNT_LEADS' },
          { type: 'Contact', id: 'ORGANIZATION_CONTACTS_LEADS' },
          { type: 'Contact', id: 'MY_CONTACTS_LEADS' },
          { type: 'Search', id: 'ALL_COMPANIES_LEADS' },
          { type: 'Contact', id: 'CHROME_EXTENSION_MATCHING_CONTACTS' },
          { type: 'Company', id: 'MATCHING_COMPANIES' },
        ],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an error while archiving the {{leads}}',
            {
              leads: i18n.t('companies'),
              ns: 'lead',
            }
          )
          return { message: errorMsg }
        },
      }),
      searchCompaniesDistinctFields: builder.query<
        SearchCompaniesDistinctFieldsResponseType,
        SearchCompaniesDistinctFieldsPayloadType
      >({
        query: ({
          organizationId,
          distinctField,
          searchText,
          filters,
          limitPerPage,
        }) => {
          const urlSearchParams = new URLSearchParams()
          if (limitPerPage) {
            urlSearchParams.append('limitPerPage', limitPerPage.toString())
          }

          return {
            url: `${organizationId}/leads/companies/distinct/${distinctField}?${urlSearchParams.toString()}`,
            method: 'POST',
            body:
              {
                searchText: searchText,
                filters: filters,
              } || {},
          }
        },
        transformResponse: (data: string[]) => {
          return {
            items: data,
          }
        },
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'An error occurred while searching {{leads}} distinct fields',
            {
              ns: 'lead',
              leads: i18n.t('companies'),
            }
          )
          return { message: errorMsg }
        },
      }),
      getCompanyActivity: builder.query<
        GetCompanyActivityResponseType,
        GetCompanyActivityRequestType
      >({
        query: ({ organizationId, companyId }) => {
          return {
            url: `${organizationId}/leads/companies/${companyId}/activities`,
            method: 'GET',
          }
        },
        providesTags: [{ type: 'Activity', id: 'BY_COMPANY_ID' }],
      }),
      unarchiveCompanies: builder.mutation<
        Company[],
        {
          ids: string[]
          organizationId: string
        }
      >({
        query: ({ ids, organizationId }) => {
          return {
            url: `/${organizationId}/leads/companies/unarchive`,
            method: 'POST',
            body: {
              companies: ids,
            },
          }
        },
      }),
      getCompanyExportableFields: builder.query<
        GetCompanyExportableFieldsResponseDTO,
        {
          organizationId: string
          searchText?: string
          filters?:
            | (SearchExternalLeadsFilter | LeadFilterApiType)[]
            | LeadFilterType[]
        }
      >({
        query: ({ organizationId, searchText, filters }) => {
          return {
            url: `${organizationId}/leads/companies/exportable-fields`,
            method: 'POST',
            body: {
              searchText: searchText,
              filters: filters,
            },
          }
        },
        keepUnusedDataFor: 24 * 60 * 60,
      }),
    }
  },
})

export const {
  useSearchCompaniesQuery,
  useLazySearchCompaniesQuery,
  useSearchMyCompaniesQuery,
  useSearchMatchingCompaniesQuery,
  useLazySearchMatchingCompaniesQuery,
  useLazyGetCompanyTableColumnsQuery,
  useGetCompanyFieldsQuery,
  useGetCompanyQuery,
  useLazyGetCompanyQuery,
  useEditCompanyByIdMutation,
  useCreateLeadCompanyMutation,
  useLazySearchCompaniesDistinctFieldsQuery,
  useArchiveCompaniesMutation,
  useGetCompanyActivityQuery,
  useCreateCompaniesMutation,
  useUnarchiveCompaniesMutation,
  useGetCompanyExportableFieldsQuery,
} = companyApi

export const { searchCompanies } = companyApi.endpoints
