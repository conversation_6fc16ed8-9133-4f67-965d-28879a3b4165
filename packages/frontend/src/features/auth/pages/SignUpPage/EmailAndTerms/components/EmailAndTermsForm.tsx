import { yupResolver } from '@hookform/resolvers/yup'
import { clsx } from 'clsx'
import { useEffect } from 'react'
import { useForm, Controller } from 'react-hook-form'
import { Trans, useTranslation } from 'react-i18next'
import { useSearchParams } from 'react-router-dom'

import { Icon, Typography, Button } from '@getheroes/ui'
import { InputCheckbox } from '@internals/components/common/dataEntry/Checkbox/Checkbox'
import { MailInput } from '@internals/components/common/dataEntry/Mail/MailInput'
import { useAuthValidation } from '@internals/features/auth/hooks/useAuthValidation'
import { useIsEmailValid } from '@internals/features/auth/hooks/useIsEmailValid'
import { AccountError } from '@internals/features/auth/pages/SignUpPage/EmailAndTerms/components/AccountError'
import { useEmailAndTermsForm } from '@internals/features/auth/pages/SignUpPage/EmailAndTerms/utils/useEmailAndTermsForm'
import { publicRoutes } from '@internals/hooks/useRoute'
import { idReferentials } from '@internals/utils/idReferentials'

interface EmailAndTermsFormProps {
  onTermsAcceptedChange: (value: boolean) => void
}

export const EmailAndTermsForm = ({
  onTermsAcceptedChange,
}: EmailAndTermsFormProps) => {
  const { signupEmailValidationSchema } = useAuthValidation()
  const { t } = useTranslation('auth')
  const [searchParams] = useSearchParams()
  const defaultEmail = searchParams.get('email')
  const { onSubmit, initialValues, isLoading, error } =
    useEmailAndTermsForm(defaultEmail)

  const {
    control,
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
  } = useForm({
    resolver: yupResolver(signupEmailValidationSchema),
    defaultValues: initialValues,
  })

  const emailInputValue = watch('email') as string
  const isTermsOfUseAccepted = watch('isTermsOfUseAccepted')
  const { isEmailValid } = useIsEmailValid(emailInputValue)

  useEffect(() => {
    onTermsAcceptedChange(isTermsOfUseAccepted)
  }, [isTermsOfUseAccepted, onTermsAcceptedChange])

  return (
    <form
      className={clsx('w-full')}
      onSubmit={handleSubmit(onSubmit)}
      noValidate // used to disable the browser validation because we use react-hook-form
    >
      <div className="flex flex-col w-full gap-4">
        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <MailInput
              startIcon={
                <Icon name={'Mail'} size={'small'} color={'base-placeholder'} />
              }
              fullWidth={true}
              id="email"
              placeholder={t('Professional email address') as string}
              error={errors.email?.message}
              disabled={isSubmitting}
              required
              isStackingFormfield={false}
              dataTestId={
                idReferentials.auth.pages.signUp.emailAndTerms.emailInput
              }
              {...field}
            />
          )}
        />
        <AccountError error={error} />

        <div className={'flex flex-col gap-2 mb-4'}>
          <div className={'flex items-center'}>
            <label className={'flex items-center body-s-medium gap-2'}>
              <InputCheckbox
                dataTestId={
                  idReferentials.auth.pages.signUp.emailAndTerms
                    .termsOfUseCheckbox
                }
                id={'isTermsOfUseAccepted'}
                isStackingFormfield={false}
                {...register('isTermsOfUseAccepted')}
                onBlur={undefined}
                disabled={isLoading}
              />
              <Typography variant="body" size="s" weight="medium">
                <Trans
                  ns="auth"
                  i18nKey="I accept the <1>terms of use*</1>"
                  components={{
                    1: (
                      <a
                        className="cursor-pointer text-textAccent"
                        href={publicRoutes.termsOfUse.path}
                        target="_blank"
                        rel="noreferrer"
                      />
                    ),
                  }}
                />
              </Typography>
            </label>
          </div>

          <div className={'flex items-center'}>
            <label className={'flex gap-2'}>
              <InputCheckbox
                dataTestId={
                  idReferentials.auth.pages.signUp.emailAndTerms
                    .receiveMarketingCheckbox
                }
                id={'isReceiveMarketingMaterialAccepted'}
                isStackingFormfield={false}
                {...register('isReceiveMarketingMaterialAccepted')}
                onBlur={undefined}
                disabled={isLoading}
              />
              <Typography variant="body" size="s" weight="medium">
                {t(
                  'I agree to receive information about product updates and offers from ZELIQ'
                )}
              </Typography>
            </label>
          </div>
        </div>

        <Button
          loading={isLoading}
          disabled={!isEmailValid || !isTermsOfUseAccepted}
          type={'submit'}
          dataTestId={
            idReferentials.auth.pages.signUp.emailAndTerms.submitButton
          }
          fullWidth
          size="large"
          variant="primary"
        >
          {t('Continue', { ns: 'common' })}
        </Button>
      </div>
    </form>
  )
}
