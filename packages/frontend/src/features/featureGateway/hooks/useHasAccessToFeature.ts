import { useMemo, useRef } from 'react'

import type { FeatureGateway, FeatureLimit } from '@getheroes/shared'
import { PlanLimitsFreeTrialMap, PlanLimitsMap } from '@getheroes/shared'
import { useGetFeaturesUsage } from '@internals/features/featureGateway/hooks/useGetFeaturesUsage'
import { useSubscriptions } from '@internals/features/subscription/hooks/useSubscriptions'

type UseHasAccessToFeatureReturn = {
  hasAccess: boolean | undefined
  hasLoaded: boolean
  hasCurrentUsageLoaded: boolean
  currentUsage: number | undefined
  accessLimit: number
}

/**
 * Determines whether a user has access to a specific feature.
 *
 * @param {FeatureGateway | FeatureLimit} feature - The feature to check access for.
 * @returns {{
 *   hasAccess: boolean;
 *   hasLoaded: boolean;
 *   hasCurrentUsageLoaded: boolean;
 *   currentUsage: number | undefined;
 *   accessLimit: number | undefined;
 * }} - An object containing access information for the feature.
 */
export const useHasAccessToFeature = (
  feature: FeatureGateway | FeatureLimit
): UseHasAccessToFeatureReturn => {
  const subscriptions = useSubscriptions()
  const { data: currentUsage, isSuccess: hasCurrentUsageLoaded } =
    useGetFeaturesUsage()

  const currentPlan = subscriptions?.currentPlan
  const isFreeTrial = subscriptions?.serviceSubscription?.isTrial

  const accessLimitRef = useRef<number>(0)
  const hasAccess = useMemo(() => {
    if (!!currentPlan && !!currentUsage) {
      const gatewayConfig = isFreeTrial ? PlanLimitsFreeTrialMap : PlanLimitsMap
      const limitOrHasAccess = gatewayConfig[currentPlan][feature]
      const isFeatureLimit = typeof limitOrHasAccess === 'number'
      if (isFeatureLimit) {
        accessLimitRef.current = limitOrHasAccess
      }

      return isFeatureLimit
        ? currentUsage[feature as FeatureLimit] < limitOrHasAccess
        : limitOrHasAccess
    }
  }, [currentPlan, currentUsage, feature, isFreeTrial])

  return useMemo(
    () => ({
      hasAccess,
      hasLoaded: hasCurrentUsageLoaded,
      hasCurrentUsageLoaded,
      currentUsage: currentUsage?.[feature as FeatureLimit],
      accessLimit: accessLimitRef?.current,
    }),
    [hasAccess, hasCurrentUsageLoaded, currentUsage, feature]
  )
}
