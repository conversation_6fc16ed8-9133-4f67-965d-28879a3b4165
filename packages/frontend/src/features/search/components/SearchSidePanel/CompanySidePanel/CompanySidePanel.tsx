import type { Company } from '@getheroes/shared'
import { IconButton } from '@getheroes/ui'
import { AddCompanyButton } from '@internals/features/search/components/AddCompanyButton/AddCompanyButton'
import { CompanySidePanelOverview } from '@internals/features/search/components/SearchSidePanel/CompanySidePanel/CompanySidePanelOverview/CompanySidePanelOverview'
import { idReferentials } from '@internals/utils/idReferentials'

export type CompanySidePanelProps = {
  company: Company
  onClose: () => void
}

export const CompanySidePanel = ({
  company,
  onClose,
}: CompanySidePanelProps) => {
  return (
    <>
      {/* HEADER */}
      <div className="c-side-panel-header flex items-center justify-between gap-1">
        <AddCompanyButton lead={company} />

        <IconButton
          icon={'Xmark'}
          variant={'tertiary-outlined'}
          onClick={onClose}
          dataTestId={`${idReferentials.leads.sidePanel.attributes.prefix}-close-button`}
        />
      </div>

      {/* BODY */}
      <div className="overflow-hidden h-full py-2">
        <div className="flex flex-col h-full gap-2 overflow-y-auto">
          <CompanySidePanelOverview key={company.id} company={company} />
        </div>
      </div>
    </>
  )
}
