import pick from 'lodash/pick'
import type { ChangeEvent, ComponentProps } from 'react'
import { useMemo, useCallback } from 'react'

import type { SearchFiltersType } from '@getheroes/shared'
import { TextInput } from '@internals/components/common/dataEntry/Text/TextInput'

type TextInputProps = Omit<
  ComponentProps<typeof TextInput>,
  'value' | 'onChange' | 'dataTestId'
>

export type FilterItemInputProps<T extends object, S = keyof T> = {
  value: T | undefined
  inputKey: S
  filterDefinition: SearchFiltersType
  unit?: string
  onChange: (value: string, key: string) => void
  dataTestId: string
} & Partial<TextInputProps>

export const FilterItemInput = <T extends object>({
  filterDefinition,
  value,
  inputKey,
  unit = '',
  onChange,
  dataTestId,
  ...inputProps
}: FilterItemInputProps<T>) => {
  const handleChange = useCallback(
    (newValue: string) => {
      onChange(newValue, String(inputKey))
    },
    [onChange, inputKey]
  )

  const valueFromObject = useMemo(() => {
    if (!value) {
      return ''
    }
    const val = pick(value, [inputKey])
    return val[inputKey] || ''
  }, [inputKey, value])

  return (
    <TextInput
      value={valueFromObject}
      onChange={(e: ChangeEvent<HTMLInputElement>) =>
        handleChange(e.target.value)
      }
      dataTestId={dataTestId}
      name={`${filterDefinition.id}-${String(inputKey)}`}
      className="w-full"
      type="number"
      min={0}
      endIcon={<InputUnit unit={unit} />}
      {...inputProps}
    />
  )
}

const InputUnit = ({ unit }: { unit?: string }) => {
  return <span className="body-xs-regular text-textSubtle">{unit}</span>
}
