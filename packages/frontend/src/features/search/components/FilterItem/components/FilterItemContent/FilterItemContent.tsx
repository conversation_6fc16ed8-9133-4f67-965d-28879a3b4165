import { clsx } from 'clsx'
import React from 'react'

import type {
  ExternalLeadsFilterIdEnum,
  SearchFiltersType,
} from '@getheroes/shared'
import { FeatureGatewayPopover } from '@internals/features/featureGateway/components/FeatureGatewayPopover/FeatureGatewayPopover'
import { FilterItem } from '@internals/features/search/components/FilterItem/FilterItem'
import { useFilterListContext } from '@internals/features/search/components/SearchPageContent/provider/useFilterListContext'
import { useAdvancedSearchFeatureGateway } from '@internals/features/search/hooks/useAdvancedSearchFeatureGateway'
import { getIsLockedFilterOnFreePlan } from '@internals/features/search/utils/getIsLockedFilterOnFreePlan'
import { useCurrentPlan } from '@internals/features/subscription/hooks/useCurrentPlan'

export interface FilterItemContentProps {
  filterDefinition: SearchFiltersType
  filtersDefinition: Array<SearchFiltersType>
  disabled?: boolean
}

export const FilterItemContent = ({
  filterDefinition,
  filtersDefinition,
  disabled,
}: FilterItemContentProps) => {
  const { formState, handleChangeExcluded, handleChange } =
    useFilterListContext()
  const { id } = filterDefinition
  const { isFreePlan } = useCurrentPlan()
  const values = formState[id as ExternalLeadsFilterIdEnum]

  const isLocked = getIsLockedFilterOnFreePlan({
    filterId: id,
    isFreePlan,
  })
  const { featureGatewayPopoverProps } =
    useAdvancedSearchFeatureGateway(isLocked)

  return (
    <FeatureGatewayPopover {...featureGatewayPopoverProps} className={'w-full'}>
      <div className={clsx('flex flex-col gap-2 w-full')}>
        <FilterItem
          filterDefinition={filterDefinition}
          filtersDefinition={filtersDefinition}
          value={values}
          formState={formState}
          onChange={handleChange}
          onChangeExclude={handleChangeExcluded}
          disabled={disabled}
        />
      </div>
    </FeatureGatewayPopover>
  )
}
