import isEmpty from 'lodash/isEmpty'

import type { ExternalLeadsFilterIdEnum } from '@getheroes/shared'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import type {
  ExternalLeadsFilterFormState,
  SearchExternalLeadsFilter,
} from '@internals/features/search/types/searchLeadsType'
import { getIsBlockedByAnotherFilter } from '@internals/features/search/utils/getIsBlockedByAnotherFilter'
import { getIsLockedFilterOnFreePlan } from '@internals/features/search/utils/getIsLockedFilterOnFreePlan'

export const filterSearchFilters = ({
  searchFilters,
  formState,
  isFreePlan = false,
}: {
  searchFilters: Array<SearchExternalLeadsFilter>
  formState: ExternalLeadsFilterFormState
  isFreePlan: boolean
}): Array<SearchExternalLeadsFilter> => {
  return searchFilters
    .filter(
      filter =>
        !getIsLockedFilterOnFreePlan({
          filterId: filter.field as ExternalLeadsFilterIdEnum,
          isFreePlan,
        })
    )
    .filter(
      filter =>
        !getIsBlockedByAnotherFilter(
          (filter as SearchExternalLeadsFilter)
            .field as ExternalLeadsFilterIdEnum,
          formState
        )
    )
    .filter(filter => {
      const isCheckValue =
        filter.operator === OperatorFilterType.ANY_OF_VALUES ||
        filter.operator === OperatorFilterType.NOT_ANY_OF_VALUES

      return isCheckValue ? !isEmpty(filter.values) : true
    })
}
