import { describe, it, expect } from 'vitest'
import { getFilterDefinitionByFilterId } from './getFilterDefinitionByFilterId'
import {
  ExternalLeadsFilterIdEnum,
  SearchFiltersType,
  SearchFilterType,
} from '@getheroes/shared'

describe('getFilterDefinitionByFilterId', () => {
  const mockFiltersDefinition: Array<SearchFiltersType> = [
    {
      id: ExternalLeadsFilterIdEnum.JOB_COMPANY_SIZE,
      resources: [],
      type: SearchFilterType.ENUM,
      payload: [
        { id: 'small', label: 'Small' },
        { id: 'medium', label: 'Medium' },
      ],
    },
    {
      id: ExternalLeadsFilterIdEnum.INDUSTRY,
      resources: [],
      type: SearchFilterType.AUTOCOMPLETE,
      searchId: 'industry',
    },
  ]

  it('should return the correct filter definition for a given filterId', () => {
    const filterId = ExternalLeadsFilterIdEnum.JOB_COMPANY_SIZE
    const result = getFilterDefinitionByFilterId(
      filterId,
      mockFiltersDefinition
    )
    expect(result).toEqual(mockFiltersDefinition[0])
  })

  it('should return undefined if the filterId does not exist', () => {
    const filterId = 'non_existent_filter_id'
    const result = getFilterDefinitionByFilterId(
      filterId,
      mockFiltersDefinition
    )
    expect(result).toBeUndefined()
  })
})
