import {
  ExternalLeadsFilterIdEnum,
  LeadCategory,
  SearchFilterFormatEnum,
  SearchFilterType,
} from '@getheroes/shared'
import { BusinessUnitSizeEvolutionFilter } from '@internals/features/search/components/FilterItem/components/BusinessUnitSizeEvolutionFilter/BusinessUnitSizeEvolutionFilter'
import { CompanyBusinessUnitFilter } from '@internals/features/search/components/FilterItem/components/CompanyBusinessUnitFilter/CompanyBusinessUnitFilter'
import { CompanyFoundedFilter } from '@internals/features/search/components/FilterItem/components/CompanyFoundedFilter/CompanyFoundedFilter'
import { CompanySizeEvolutionFilter } from '@internals/features/search/components/FilterItem/components/CompanySizeEvolutionFilter/CompanySizeEvolutionFilter'
import { FilterItemAutocomplete } from '@internals/features/search/components/FilterItem/components/FilterItemAutocomplete/FilterItemAutocomplete'
import { FilterItemEnum } from '@internals/features/search/components/FilterItem/components/FilterItemEnum/FilterItemEnum'
import { FilterItemSwitch } from '@internals/features/search/components/FilterItem/components/FilterItemSwitch/FilterItemSwitch'
import { FullNameFilter } from '@internals/features/search/components/FilterItem/components/FullNameFilter/FullNameFilter'
import { FundingRoundAmountFilter } from '@internals/features/search/components/FilterItem/components/FundingRoundAmountFilter/FundingRoundAmountFilter'
import { IndustryFilter } from '@internals/features/search/components/FilterItem/components/IndustryFilter/IndustryFilter'
import { LocationFilter } from '@internals/features/search/components/FilterItem/components/LocationFilter/LocationFilter'
import { RecruitingCompanyFilter } from '@internals/features/search/components/FilterItem/components/RecruitingCompanyFilter/RecruitingCompanyFilter'
import { RevenueFilter } from '@internals/features/search/components/FilterItem/components/RevenueFilter/RevenueFilter'
import { SeniorityFilter } from '@internals/features/search/components/FilterItem/components/SeniorityFilter/SeniorityFilter'
import type { FilterItemProps } from '@internals/features/search/components/FilterItem/types/filter-item.types'

const ALLOW_NEW_OPTIONS_FILTERS_IDS: Array<ExternalLeadsFilterIdEnum> = [
  ExternalLeadsFilterIdEnum.COMPANY,
  ExternalLeadsFilterIdEnum.JOB_TITLE,
]

export const FilterItem = (props: FilterItemProps) => {
  const { id, type } = props.filterDefinition

  const allowNewOption = ALLOW_NEW_OPTIONS_FILTERS_IDS.includes(
    id as ExternalLeadsFilterIdEnum
  )

  const passedProps = {
    ...props,
    allowNewOption,
  }

  // ID
  if (id === ExternalLeadsFilterIdEnum.SENIORITY_TIME_IN_CURRENT_ROLE) {
    return <SeniorityFilter {...passedProps} />
  }
  if (id === ExternalLeadsFilterIdEnum.CONTACT_LOCATION) {
    return (
      <LocationFilter {...passedProps} leadCategory={LeadCategory.CONTACT} />
    )
  }
  if (id === ExternalLeadsFilterIdEnum.COMPANY_LOCATION) {
    return (
      <LocationFilter {...passedProps} leadCategory={LeadCategory.COMPANY} />
    )
  }
  if (id === ExternalLeadsFilterIdEnum.COMPANY_FOUNDED) {
    return <CompanyFoundedFilter {...passedProps} />
  }

  if (id === ExternalLeadsFilterIdEnum.FULL_NAME) {
    return <FullNameFilter {...passedProps} />
  }

  if (id === ExternalLeadsFilterIdEnum.FUNDING_ROUNDS_TOTAL_AMOUNT) {
    return <FundingRoundAmountFilter {...passedProps} />
  }
  if (id === ExternalLeadsFilterIdEnum.COMPANY_BUSINESS_UNIT) {
    return <CompanyBusinessUnitFilter {...passedProps} />
  }

  if (id === ExternalLeadsFilterIdEnum.COMPANY_REVENUE) {
    return <RevenueFilter {...passedProps} />
  }

  if (id === ExternalLeadsFilterIdEnum.RECRUITING_LAST_SIX_MONTHS) {
    return <RecruitingCompanyFilter {...passedProps} />
  }

  if (id === ExternalLeadsFilterIdEnum.COMPANY_SIZE_EVOLUTION) {
    return <CompanySizeEvolutionFilter {...passedProps} />
  }

  if (id === ExternalLeadsFilterIdEnum.BUSINESS_UNIT_SIZE_EVOLUTION) {
    return <BusinessUnitSizeEvolutionFilter {...passedProps} />
  }

  if (id === ExternalLeadsFilterIdEnum.INDUSTRY) {
    return <IndustryFilter {...passedProps} />
  }
  if (
    'format' in props.filterDefinition &&
    props.filterDefinition.format === SearchFilterFormatEnum.BOOLEAN
  ) {
    // FORMAT
    return <FilterItemSwitch {...passedProps} />
  }
  // TYPE
  if (type === SearchFilterType.ENUM) {
    return <FilterItemEnum {...passedProps} />
  }
  if (type === SearchFilterType.AUTOCOMPLETE) {
    return <FilterItemAutocomplete {...passedProps} />
  }

  return null
}
