import { isEmpty } from 'lodash'
import isNull from 'lodash/isNull'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { setSelectedTemplateId } from '@getheroes/frontend/config/store/slices/templateSlice'
import { I<PERSON><PERSON><PERSON>on, Tooltip, Spinner } from '@getheroes/ui'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { Tabs } from '@internals/components/common/navigation/Tabs'
import { TabSelectorVariant } from '@internals/components/common/navigation/Tabs/TabSelector-export'
import type { TabType } from '@internals/components/common/navigation/Tabs/types'
import { NoteEditor } from '@internals/features/task/components/Note/Note'
import { CalendarButton } from '@internals/features/task/components/PanelRight/Call/CalendarButton/CalendarButton'
import { Pitch } from '@internals/features/task/components/PanelRight/Call/Pitch'
import { DrawerTaskSelectTemplate } from '@internals/features/task/components/PanelRight/DrawerTaskSelectTemplate/DrawerTaskSelectTemplate'
import { StartCallButton } from '@internals/features/task/components/StartCallButton/StartCallButton'
import { useFetchContact } from '@internals/features/task/hooks/useFetchContact'
import { useLeadSettingsLocalStorage } from '@internals/hooks/useLeadSettingsLocalStorage'
import { TemplateType } from '@internals/models/template'
import { useAppDispatch } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

import './call.scoped.scss'

export const Call = ({
  contactId,
  taskId,
  endingTask = false,
}: {
  contactId: string
  taskId: string
  endingTask?: boolean
}) => {
  /* Vars */

  const { t } = useTranslation('common')
  const dispatchStoreAction = useAppDispatch()

  const [isTemplateDrawerOpen, setIsTemplateDrawerOpen] =
    useState<boolean>(false)
  const [isLoadingUnMount, setIsLoadingUnMount] = useState(false)

  /* Queries */

  const { data: contact } = useFetchContact(contactId)
  const { leadSettings, setLeadSettings } = useLeadSettingsLocalStorage()

  /* Effects */

  useEffect(() => {
    // Force unmount of the email tab to avoid the email content to be displayed
    setIsLoadingUnMount(true)
    setTimeout(() => {
      setIsLoadingUnMount(false)
    }, 200)
  }, [taskId])

  /* Memos */

  const tabCallList: Array<TabType> = useMemo(
    () => [
      {
        key: 'PITCH',
        label: t('Pitch') as string,
        children: isLoadingUnMount ? (
          <div className="flex w-full h-full items-center justify-center">
            <Spinner />
          </div>
        ) : (
          <Pitch
            taskId={taskId}
            templateContent={leadSettings?.selectedTemplate || ''}
            setIsTemplateDrawerOpen={setIsTemplateDrawerOpen}
          />
        ),
      },
      {
        key: 'NOTE',
        label: t('Note') as string,
        children: isLoadingUnMount ? (
          <div className="flex w-full h-full items-center justify-center">
            <Spinner />
          </div>
        ) : (
          <div className="flex h-full">
            <NoteEditor taskId={taskId} editable={!endingTask} />
          </div>
        ),
      },
    ],
    [t, isLoadingUnMount, taskId, endingTask, leadSettings?.selectedTemplate]
  )

  /* Functions */

  const handleDrawerOpen = () => {
    setIsTemplateDrawerOpen(true)
  }

  return (
    <>
      <div className="c-call-content">
        <Tabs.Container activeTab={tabCallList[0].key} items={tabCallList}>
          <div className="flex justify-between items-center">
            <Tabs.Selector variant={TabSelectorVariant.SEGMENTED} />

            <div className="flex gap-2 h-[36px]">
              <Tooltip content={t('Select a template')}>
                <IconButton
                  icon={'Page'}
                  dataTestId={
                    idReferentials.tasks.components.call.selectTemplateButton
                  }
                  variant={'tertiary-outlined'}
                  onClick={handleDrawerOpen}
                />
              </Tooltip>

              <CalendarButton />

              {contact && <StartCallButton contactId={contact.id} />}
            </div>
          </div>

          <Tabs.Content className="c-call__tabs p-2 overflow-hidden" />
        </Tabs.Container>
      </div>

      <Drawer
        classNamePanel="!max-w-7xl"
        open={isTemplateDrawerOpen}
        onClose={() => {
          dispatchStoreAction(setSelectedTemplateId(null))
          setIsTemplateDrawerOpen(false)
        }}
      >
        <DrawerTaskSelectTemplate
          setOpen={setIsTemplateDrawerOpen}
          setTemplate={({ lexical, content }) => {
            setLeadSettings({
              selectedTemplate:
                !isNull(lexical) && !isEmpty(lexical)
                  ? lexical
                  : (content as string),
            })
          }}
          type={TemplateType.PITCH}
        />
      </Drawer>
    </>
  )
}
