// This file is not correct and should be updated to use form.scss classes
.c-form-task-email {
  .c-form-task-email__from,
  .c-form-task-email__to {
    .c-form-field__label {
      min-width: 48px;
      margin: 0;
    }
    .c-form-field__form {
      width: 100%;
    }
  }

  .rdw-editor-toolbar {
    border-color: transparent;
  }

  .c-email__subject-input {
    div:first-of-type {
      label {
        margin-bottom: 0;
      }
    }
    div:last-of-type {
      flex-grow: 1;
    }
  }

  .c-email__subject-input {
    div {
      margin-bottom: 0;
      div {
        margin-bottom: 0;
      }
    }
  }
}

.c-task-template-tab {
  li {
    background-color: var(--grey200);
    margin: 0 !important;
    padding: calc(var(--defaultSpacing) / 2);

    &:first-of-type {
      border-radius: var(--borderRadiusS) 0 0 var(--borderRadiusS);
    }
    &:last-of-type {
      border-radius: 0 var(--borderRadiusS) var(--borderRadiusS) 0;
    }

    .border-borderAccent {
      border-color: transparent;
      background-color: var(--white);
      border-radius: var(--borderRadiusS);
    }
  }
}
