import { clsx } from 'clsx'
import isEmpty from 'lodash/isEmpty'
import isNull from 'lodash/isNull'
import isUndefined from 'lodash/isUndefined'
import { useEffect, useMemo, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import sanitizeHtml from 'sanitize-html'

import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Contact, Organization } from '@getheroes/shared'
import {
  EmailAttachment,
  EmailProvider,
  RegexClass,
  toMo,
} from '@getheroes/shared'
import {
  Button,
  Divider,
  IconButton,
  Spinner,
  Tooltip,
  Typography,
  useToast,
} from '@getheroes/ui'
import { getEmailsOption } from '@internals/components/business/ContactSequenceActivities/components/SequenceActivitiesErrors/utils/emails'
import { AutoSave } from '@internals/components/common/dataEntry/AutoSave/AutoSave'
import { Lexical } from '@internals/components/common/dataEntry/Lexical/Lexical'
import { useValidator } from '@internals/components/common/dataEntry/Lexical/hooks/useValidator'
import { useVariableValidator } from '@internals/components/common/dataEntry/Lexical/hooks/useVariableValidator'
import type {
  EditorOnChangeType,
  LexicalImperativeHandle,
} from '@internals/components/common/dataEntry/Lexical/lexical.type'
import type { AttachmentModel } from '@internals/components/common/dataEntry/Lexical/ui/AttachmentBar'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { useUpdateEmailMutation } from '@internals/features/gmail/api/mailApi'
import { useEmailTracking } from '@internals/features/gmail/hook/useEmailTracking'
import { useMail } from '@internals/features/gmail/hook/useMail'
import type {
  FromToType,
  SendEmailWithMailPayloadType,
} from '@internals/features/gmail/types/gmailType'
import { EmailConnectButtons } from '@internals/features/settings/pages/EmailAccount/EmailConnectButtons'
import { DrawerTaskSelectTemplate } from '@internals/features/task/components/PanelRight/DrawerTaskSelectTemplate/DrawerTaskSelectTemplate'
import { From } from '@internals/features/task/components/PanelRight/Email/From'
import { EmailRecipients } from '@internals/features/task/components/PanelRight/Email/Recipients/Recipients'
import { useFetchContact } from '@internals/features/task/hooks/useFetchContact'
import { TemplateType } from '@internals/models/template'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'
import { sleep } from '@internals/utils/sleep'

import './email.scoped.scss'
import type { ToolbarOption } from '@internals/components/common/dataEntry/Lexical/plugins/ToolbarPlugin/toolbar-plugin.type'

const isEditable = false

type EmailProps = {
  contactId: string
  subject?: string | null
  isDisableSubject?: boolean
  namespaceLocalStorage?: string
  messageId?: string
  onEmailSent?: () => void
  lexicalOptions?: ToolbarOption[]
  hasSubject?: boolean
  email: Partial<SendEmailWithMailPayloadType> | undefined
  isSaveInline?: boolean
  forceRefetchEmail?: () => void
  isModal?: boolean
  showEmailEnvelope?: boolean
  showFloatingToolbar?: boolean
}

interface EmailReplyProps extends EmailProps {
  taskId?: undefined
  isReply: true
}

interface EmailSendProps extends EmailProps {
  taskId: string
  isReply?: false
}

interface FormValues {
  id?: string
  to?: string[]
  cc?: string[]
  bcc?: string[]
  subject?: string
  body?: string
  attachments?: AttachmentModel[]
  addSignature?: boolean
}

export const Email = ({
  contactId,
  namespaceLocalStorage,
  messageId = '',
  onEmailSent,
  isReply,
  taskId,
  lexicalOptions = [],
  hasSubject = true,
  email,
  isSaveInline = false,
  isModal = false,
  showEmailEnvelope = true,
  showFloatingToolbar = true,
}: EmailSendProps | EmailReplyProps): JSX.Element => {
  /* Vars */

  const { createToast } = useToast()
  const { t } = useTranslation('task')

  const [openDrawer, setOpenDrawer] = useState(false)
  const [isSendingEmail, setIsSendingEmail] = useState(false)

  const mailIdRef = useRef<string>()

  const currentUser = useTypedSelector(selectCurrentUser) || null
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const { trackEmailSent } = useEmailTracking()
  const { checkEmptyError } = useValidator()
  const { checkVariableError } = useVariableValidator({ organizationId })

  const [isSubjectPreview, setIsSubjectPreview] = useState(false)
  const [isBodyPreview, setIsBodyPreview] = useState(false)

  const lexicalSubjectRef = useRef<LexicalImperativeHandle>(null)
  const lexicalBodyRef = useRef<LexicalImperativeHandle>(null)
  /* Queries */

  const { data: contact } = useFetchContact(contactId)
  const {
    account,
    isConnected,
    isLoading,
    isLoadingReply,
    isFetching,
    replyMailWithMail,
  } = useMail()

  const [updateEmail, { isLoading: isUpdatingEmail }] = useUpdateEmailMutation()

  /* Memos */

  const toolbarOptions = [
    'isSentByZeliq',
    'signature',
    'insertAttachmentBtn',
    ...lexicalOptions,
  ]

  /* Memos */

  const emailsOptions = useMemo(() => {
    const options = getEmailsOption(contact as Partial<Contact>)
    const emailsOptions = ((email?.to as string[]) || [])
      .filter(value => !options.find(option => option.value === value))
      .map((value: string) => ({
        label: value,
        value,
      }))

    return [...options, ...emailsOptions]
  }, [contact, email])

  const defaultValues = useMemo(() => {
    return {
      id: email?.id || '',
      to: (email?.to as string[]) || [],
      cc: email?.cc || [],
      bcc: email?.bcc || [],
      subject: (email?.subject as string) || '',
      body: (email?.body as string) || '',
      attachments: email?.attachments || [],
      addSignature: email?.addSignature,
    }
  }, [email])

  /* Form */

  const methods = useForm<FormValues>({
    defaultValues,
    mode: 'onChange',
  })

  const {
    reset,
    setValue,
    control,
    handleSubmit,
    formState: { errors },
  } = methods

  /* Effects */

  useEffect(() => {
    if (email && email?.id !== mailIdRef.current) {
      mailIdRef.current = email.id

      setValue('id', email.id || '')
      setValue('to', (email.to as string[]) || [])
      setValue('cc', email.cc || [])
      setValue('bcc', email.bcc || [])
      setValue('subject', (email.subject as string) || '')
      setValue('body', (email.body as string) || '')
      setValue('attachments', email.attachments || [])
      setValue('addSignature', email.addSignature)

      if (lexicalSubjectRef.current) {
        lexicalSubjectRef.current.setHtmlContent(email.subject || '')
      }

      if (lexicalBodyRef.current) {
        lexicalBodyRef.current.setAttachments(email.attachments || [])
        lexicalBodyRef.current.setHasSignature(email.addSignature || false)
        lexicalBodyRef.current.setHtmlContent(email.body || '')
      }
    }
  }, [email, emailsOptions, setValue])

  useEffect(() => {
    if (lexicalSubjectRef.current) {
      lexicalSubjectRef.current.setIsPreview(isSubjectPreview)
    }
  }, [isSubjectPreview])

  useEffect(() => {
    if (lexicalBodyRef.current) {
      lexicalBodyRef.current.setIsPreview(isBodyPreview)
    }
  }, [isBodyPreview])

  /* Functions */

  const sendEmail = async (data: FormValues) => {
    try {
      setIsSendingEmail(true)

      /**
       * TODO: Remove this once we have a proper sanitization when backend is ready
       **/
      lexicalBodyRef?.current?.setIsPreview(true)
      lexicalSubjectRef?.current?.setIsPreview(true)

      await sleep()

      const body = lexicalBodyRef?.current?.getEditor()?.getRootElement()
      const sanitizedBody = sanitizeHtml(body?.innerHTML || '')
      const subject = lexicalSubjectRef?.current?.getEditor()?.getRootElement()

      /*************************/

      const dataAttributes = {
        id: data.id,
        to: data.to as string[],
        cc: data.cc || [],
        bcc: data.bcc || [],
        subject: subject?.innerText || '',
        body: sanitizedBody,
        attachments: data.attachments || [],
        addSignature: !!data.addSignature,
      }

      if (isReply) {
        await replyMailWithMail({
          ...dataAttributes,
          messageId,
          organizationId,
        }).unwrap()
      } else {
        await updateEmail({
          ...dataAttributes,
          organizationId,
          taskId,
          isDraft: false,
        }).unwrap()
      }

      // TODO: Remove this once we have a proper sanitization when backend is ready
      lexicalBodyRef?.current?.setIsPreview(false)
      lexicalSubjectRef?.current?.setIsPreview(false)

      createToast({
        type: 'main',
        message: t('Email sent', { ns: 'task' }) as string,
      })

      onEmailSent?.()

      trackEmailSent({
        provider: EmailProvider.GMAIL,
        lead_status: contact?.status,
      })
    } catch (error: any) {
      createToast({
        type: 'error',
        message: t(error.message || error.data?.message) as string,
      })
    } finally {
      setIsSendingEmail(false)
    }

    reset()
  }

  const saveEmail = async (data: FormValues) => {
    if (isUndefined(taskId) || !email?.id || !isSaveInline) {
      return
    }

    if (data.id) {
      await updateEmail({
        taskId,
        organizationId,
        isDraft: true,
        ...data,
        to: (data.to as string[]) || [],
        subject: data.subject || '',
        body: data.body || '',
        attachments: data.attachments || [],
      })
    } else {
      createToast({
        type: 'error',
        message: t('The email address is bounced', { ns: 'task' }),
      })
    }
  }

  const handleBodyChange =
    (onChange: any) =>
    ({ html, addSignature }: EditorOnChangeType) => {
      if (!isUndefined(addSignature)) {
        setValue('addSignature', addSignature as boolean)
        return
      }

      onChange(html)
    }

  const handleSubjectChange = (onChange: any) => () => {
    const editor = lexicalSubjectRef?.current?.getEditor()
    const rootElement = editor?.getRootElement()
    const cleanText = rootElement?.innerText.replace(RegexClass.NEWLINES, '')

    onChange(cleanText)
  }

  /* Render */

  if (!isConnected && !isLoading && !isFetching) {
    return (
      <div className="flex gap-2 flex-col items-center justify-center h-full w-full">
        <EmailConnectButtons />
      </div>
    )
  }

  return (
    <>
      {isLoadingReply || isLoading ? (
        <div className="flex items-center justify-center h-full w-full">
          <Spinner />
        </div>
      ) : (
        <>
          {showEmailEnvelope && (
            <div className="flex flex-col gap-4 relative">
              <From
                contact={{ ...currentUser, email: account } as FromToType}
                isEditable={isEditable}
                errors={errors}
              />

              {isUpdatingEmail && (
                <div className="flex items-center gap-1 absolute top-[-3px] right-0">
                  <Spinner size="extra-small" color="decorative-brand" />

                  <Typography variant="body" size="s" color="decorative-brand">
                    {t('Save loading...', { ns: 'task' })}
                  </Typography>
                </div>
              )}

              <EmailRecipients
                dataTestId={
                  idReferentials.tasks.taskPage.panelRight.email.toInput
                }
                disabled={!!isEditable}
                control={control}
                emailsOptions={emailsOptions}
                errors={errors}
                contact={contact}
              />

              {hasSubject && (
                <>
                  <Divider />

                  <div className="grid grid-cols-10 gap-2">
                    <div className="pt-3">
                      <Typography
                        variant="body"
                        size="xs"
                        weight="medium"
                        color="base-label"
                      >
                        {t('subject')}
                      </Typography>
                    </div>

                    <div className="col-span-9">
                      <Controller
                        control={control}
                        name="subject"
                        rules={{
                          validate: (value?: string) =>
                            checkEmptyError(
                              value,
                              t('You should add a subject content', {
                                ns: 'sequence',
                              })
                            ),
                        }}
                        render={({ field: { onChange, value } }) => (
                          <Lexical
                            initialContent={value}
                            isPreview={isSubjectPreview}
                            name={
                              idReferentials.tasks.taskPage.panelRight.email
                                .subject
                            }
                            dataTestId={
                              idReferentials.tasks.taskPage.panelRight.email
                                .subject
                            }
                            ref={lexicalSubjectRef}
                            toolbarOptions={['singleLine', 'variable']}
                            variableReferences={contact}
                            placeholder=""
                            onChange={handleSubjectChange(onChange)}
                            error={errors.subject?.message}
                            variant="input"
                          />
                        )}
                      />
                    </div>
                  </div>
                </>
              )}

              <Divider />
            </div>
          )}

          <div className="flex-1 mt-4 overflow-hidden">
            <Controller
              control={control}
              name="body"
              rules={{
                validate: {
                  variable: value => checkVariableError(value),
                  empty: value =>
                    checkEmptyError(
                      value,
                      t('You should write a message', { ns: 'sequence' })
                    ),
                },
              }}
              render={({ field: { name, onChange, value } }) => (
                <Lexical
                  initialContent={value}
                  name={name}
                  dataTestId={
                    idReferentials.tasks.taskPage.panelRight.email.bodyInput
                  }
                  ref={lexicalBodyRef}
                  toolbarOptions={toolbarOptions}
                  showFloatingToolbar={showFloatingToolbar}
                  floatingToolbarOptions={[
                    'textAlign',
                    'familyFont',
                    'blockTypeFormat',
                  ]}
                  attachmentOptions={{
                    maxTotalSize: toMo(EmailAttachment.MAX_TOTAL_SIZE),
                    maxFiles: EmailAttachment.MAX_FILE,
                  }}
                  variableReferences={contact}
                  namespaceLocalStorage={namespaceLocalStorage}
                  setIsEditorPreview={setIsBodyPreview}
                  onChange={handleBodyChange(onChange)}
                  getAttachments={attachments =>
                    setValue('attachments', attachments)
                  }
                  error={errors.body?.message}
                >
                  <div
                    className={clsx('flex items-center gap-2', {
                      '!mr-8': !isModal,
                    })}
                  >
                    {!isBodyPreview && (
                      <Tooltip content={t('Templates', { ns: 'template' })}>
                        <IconButton
                          dataTestId={
                            idReferentials.tasks.taskPage.panelRight.email
                              .templatesButton
                          }
                          variant={'tertiary-outlined'}
                          onClick={() => setOpenDrawer(true)}
                          icon={'Page'}
                        />
                      </Tooltip>
                    )}

                    <Button
                      dataTestId={
                        idReferentials.tasks.taskPage.panelRight.email
                          .sendEmailButton
                      }
                      onClick={handleSubmit(sendEmail)}
                      loading={isSendingEmail}
                      disabled={isSendingEmail}
                      iconLeft={'SendMail'}
                      variant={'primary'}
                    >
                      {t('Send email', { ns: 'mail' })}
                    </Button>
                  </div>
                </Lexical>
              )}
            />
          </div>

          <AutoSave
            defaultValues={defaultValues}
            onSubmit={saveEmail}
            methods={methods}
            options={{ forceSubmit: true }}
          />
        </>
      )}

      <Drawer
        classNamePanel="!max-w-7xl"
        open={openDrawer}
        onClose={() => {
          setOpenDrawer(false)
        }}
      >
        <DrawerTaskSelectTemplate
          setOpen={setOpenDrawer}
          setTemplate={({ lexical, name, content, subject }) => {
            if (isEmpty(lexicalBodyRef.current)) {
              return
            }
            if (!isNull(lexical) && !isEmpty(lexical)) {
              lexicalBodyRef.current.setEditorState(lexical as string)
            } else {
              lexicalBodyRef.current.setHtmlContent(content)
            }
            !isReply &&
              lexicalSubjectRef?.current?.setHtmlContent(subject as string)
          }}
          type={TemplateType.EMAIL}
        />
      </Drawer>
    </>
  )
}
