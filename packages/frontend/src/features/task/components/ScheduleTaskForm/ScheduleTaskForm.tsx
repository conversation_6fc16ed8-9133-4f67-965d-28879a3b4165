import isEmpty from 'lodash/isEmpty'
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from 'react'
import { useForm } from 'react-hook-form'

import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Task } from '@getheroes/frontend/types'
import { TaskType } from '@getheroes/frontend/types'
import { todayNextHour, updateDateTime } from '@getheroes/frontend/utils'
import type { Contact, Organization } from '@getheroes/shared'
import { useCreateTasksMutation } from '@internals/features/task/api/taskApi'
import { FormField } from '@internals/features/task/components/ScheduleTaskForm/ScheduleTaskForm-export'
import { SelectTaskTypeAndDate } from '@internals/features/task/components/SelectTaskTypeAndDate/SelectTaskTypeAndDate'
import { useTaskPageRescheduleTaskMutation } from '@internals/features/task/hooks/useTaskPageRescheduleTaskMutation'
import { useTypedSelector } from '@internals/store/store'

const DEFAULT_TASK_TYPE = TaskType.CALL

const getTaskDate = (task: Task): Date => {
  const taskDate = task.withTime
    ? task.dateOfExecution
    : new Date(task.dateOfExecution).toISOString().split('T')[0]

  return new Date(taskDate)
}

function getDefaultFromValues(task?: Task) {
  return {
    [FormField.DATE_PICKER_FIELD]:
      task && !isEmpty(task) ? getTaskDate(task) : todayNextHour(),
    [FormField.TASK_TYPE_FIELD]: task?.type || DEFAULT_TASK_TYPE,
    [FormField.WITH_TIME_FIELD]: task?.withTime,
  }
}

export type ScheduleTaskModalFormValuesType = {
  [FormField.DATE_PICKER_FIELD]: Date
  [FormField.TASK_TYPE_FIELD]?: TaskType
  [FormField.WITH_TIME_FIELD]: boolean
}

export type ScheduleTaskFormRef = {
  submit: () => void
}

export type ScheduleTaskFormProps = {
  task?: Task
  contact?: Contact
  onSubmit: (values: ScheduleTaskModalFormValuesType) => void
  onSuccess?: (values: ScheduleTaskModalFormValuesType) => void
  onError?: () => void
  popperContainer?: (props: { children: React.ReactNode[] }) => React.ReactNode
}

export const ScheduleTaskForm = forwardRef<
  ScheduleTaskFormRef,
  ScheduleTaskFormProps
>(({ task, contact, onSubmit, onSuccess, onError, popperContainer }, ref) => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    getValues,
    // If not present, radio buttons are not handle correctly
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    formState: { errors, isSubmitting },
    reset,
  } = useForm<ScheduleTaskModalFormValuesType>({
    defaultValues: getDefaultFromValues(task),
  })

  useEffect(() => {
    reset(getDefaultFromValues(task))
  }, [reset, task])

  // We don't want the ref here because of the DatePicker component
  const { ref: unusedRef, ...registerDatePicker } = register(
    FormField.DATE_PICKER_FIELD
  )
  const currentDateValue = watch(FormField.DATE_PICKER_FIELD)
  const currentWithTimeValue = watch(FormField.WITH_TIME_FIELD)
  const currentTaskType = watch(FormField.TASK_TYPE_FIELD)

  const epochDay = Math.floor(Date.now() / (1000 * 60 * 60 * 24))
  // We need a date object that changes only when the day changes, not on every render
  // I found no better way to do it other than adding a special dependency to the following
  // useMemo, and disabling the eslint rule
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const refDate: Date = useMemo(() => new Date(), [epochDay])

  const [
    rescheduleTask,
    {
      isSuccess: isSuccessRescheduleTask,
      isError: isErrorRescheduleTask,
      reset: resetRescheduleTask,
    },
  ] = useTaskPageRescheduleTaskMutation()
  const [
    createTask,
    {
      isSuccess: isSuccessCreateTask,
      isError: isErrorCreateTask,
      reset: resetCreateTask,
    },
  ] = useCreateTasksMutation()
  const currentUser = useTypedSelector(selectCurrentUser)

  const onSubmitForm = useCallback(
    ({
      DATE_PICKER_FIELD,
      WITH_TIME_FIELD,
      TASK_TYPE_FIELD,
    }: ScheduleTaskModalFormValuesType) => {
      if (task?.id) {
        rescheduleTask({
          taskId: task?.id || '',
          organizationId,
          dateOfExecution: DATE_PICKER_FIELD,
          withTime: WITH_TIME_FIELD,
          type: TASK_TYPE_FIELD,
        })
      } else {
        createTask({
          organizationId,
          tasks: [
            {
              type: TASK_TYPE_FIELD,
              // Contact MUST be provided
              contact: contact?.id || '',
              assign: currentUser?.id || '',
              dateOfExecution: DATE_PICKER_FIELD,
              withTime: WITH_TIME_FIELD,
            },
          ],
        })
      }
      onSubmit(getValues())
    },
    [
      contact?.id,
      createTask,
      currentUser?.id,
      getValues,
      onSubmit,
      organizationId,
      rescheduleTask,
      task?.id,
    ]
  )

  useImperativeHandle(ref, () => ({
    submit: () => {
      handleSubmit(onSubmitForm)()
    },
  }))

  useEffect(() => {
    if (isSuccessRescheduleTask || isSuccessCreateTask) {
      if (onSuccess) {
        onSuccess(getValues())
      }
      resetRescheduleTask()
      resetCreateTask()
    }
  }, [
    getValues,
    isSuccessCreateTask,
    isSuccessRescheduleTask,
    onSuccess,
    resetCreateTask,
    resetRescheduleTask,
  ])

  useEffect(() => {
    if (onError && (isErrorRescheduleTask || isErrorCreateTask)) {
      onError()
    }
  }, [isErrorCreateTask, isErrorRescheduleTask, onError])

  return (
    <form className="flex flex-col mb-4" onSubmit={handleSubmit(onSubmitForm)}>
      <SelectTaskTypeAndDate
        popperContainer={popperContainer}
        taskType={currentTaskType}
        date={currentDateValue}
        time={currentWithTimeValue ? currentDateValue : undefined}
        minDate={refDate}
        onChangeDate={date => {
          const isSameDate = date.getDate() === refDate.getDate()
          const isTimeBefore = date.getTime() < refDate.getTime()
          if (isSameDate && isTimeBefore) {
            date.setTime(refDate.getTime())
          }
          setValue(FormField.DATE_PICKER_FIELD, date)
        }}
        onChangeTime={time => {
          const newDate: Date = updateDateTime(currentDateValue, time)
          setValue(FormField.DATE_PICKER_FIELD, newDate)
          setValue(FormField.WITH_TIME_FIELD, true)
        }}
        filterTime={time => refDate.getTime() <= time.getTime()}
        registerDatePicker={registerDatePicker}
        onChangeTaskType={(newTaskType: TaskType | undefined) => {
          setValue(FormField.TASK_TYPE_FIELD, newTaskType)
        }}
        onChangeWithTime={(value: boolean) => {
          setValue(FormField.WITH_TIME_FIELD, value)
        }}
        withTime={watch(FormField.WITH_TIME_FIELD)}
      />
    </form>
  )
})
