import type { SearchExternalLeadsFilter } from '@internals/features/search/types/searchLeadsType'
import type { LeadFilterApiType } from '@internals/models/lead'

/**
 * Get import leads params
 * @param selections
 * @param isSelectedAllPage
 * @param filters
 * @param searchText
 * @example
 * // Select all leads
 * // - isSelectedAllPage is true
 * {
 *     "filters": [],
 *     "searchText": ""
 * }
 *
 * // Select all leads with filters
 * // - isSelectedAllPage is true
 * {
 *     "filters": [{"field":"engagementScoringPriority","values":["HIGH"],"operator":"ANY_OF_VALUES"}],
 *     "searchText": ""
 * }
 *
 * // Leads selected
 * // - isSelectedAllPage is false
 * {
 *     "leadsIds": ['ad9cb581-b341-4a7d-936f-a4c5bd34d619'],
 * }
 */
export const getImportLeadsParams = (
  selections: string[],
  isSelectedAllPage: boolean,
  filters: Array<LeadFilterApiType | SearchExternalLeadsFilter>,
  searchText: string
) => {
  if (isSelectedAllPage) {
    return {
      filters,
      searchText,
    }
  }

  return {
    leadsIds: selections,
  }
}
