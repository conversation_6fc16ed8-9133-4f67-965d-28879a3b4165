import type { SerializedError } from '@reduxjs/toolkit'
import type { FetchBaseQueryError } from '@reduxjs/toolkit/query'

import { api } from '@getheroes/frontend/config/api'
import type {
  LeadCategory,
  LeadImport,
  LeadImportContextEnum,
  LeadImportContextSpecificProps,
  LeadImportSourceEnum,
  LeadImportStatusEnum,
} from '@getheroes/shared'
import i18n from '@internals/config/i18n'
import type { LeadsFieldResponseType } from '@internals/features/lead/types/leadsTableColumn'
import type {
  FileImportAuthPayloadType,
  FileImportAuthResponseType,
} from '@internals/features/leadImport/Modules/CsvModule/types/FileImportType'
import type { SearchExternalLeadsFilter } from '@internals/features/search/types/searchLeadsType'
import type { LeadFilterApiType } from '@internals/models/lead'

type AddPayload =
  | { data: LeadImport }
  | { error: FetchBaseQueryError | SerializedError }

interface AddParams {
  organizationId: string
  source: LeadImportSourceEnum | null
  leadType: LeadCategory | null
  leadsIds?: string[]
  searchText?: string
  context: LeadImportContextEnum
  contextSpecific?: LeadImportContextSpecificProps
  filters?: Array<LeadFilterApiType | SearchExternalLeadsFilter>
}

export interface GetLeadImport {
  organizationId: string
  leadImportId: string
}

export interface UpdateLeadImport {
  organizationId: string
  leadImportId: string
  status?: LeadImportStatusEnum
  leadType?: LeadCategory | null
}

export interface DeleteLeadImport {
  organizationId: string
  leadImportId: string
  leadType?: LeadCategory | null
}

export interface ExcludeLeadImport {
  organizationId: string
  leadImportId: string
  leadsIdsToRemove: string[]
  leadType: LeadCategory
}

export const leadImportApi = api.injectEndpoints({
  endpoints: builder => {
    return {
      getAllImports: builder.query<
        LeadImport[],
        Pick<GetLeadImport, 'organizationId'> & {
          filters?: {
            context?: LeadImportContextEnum
            status?: LeadImportStatusEnum[]
            leadType?: LeadCategory
            afterDate?: string
            internalRelationId?: string
          }
        }
      >({
        query: ({ organizationId, filters }) => ({
          url: `${organizationId}/lead-imports`,
          params: filters,
        }),
        providesTags: () => [
          { type: 'LeadImport', id: 'LEAD_IMPORT' },
          { type: 'LeadImport', id: 'BY_ID' },
        ],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an issue while retrieving lead imports history',
            {
              ns: 'leadImport',
            }
          )
          return { message: errorMsg }
        },
      }),
      getOneImport: builder.query<LeadImport, GetLeadImport>({
        query: ({ organizationId, leadImportId }) => ({
          url: `${organizationId}/lead-imports/${leadImportId}`,
        }),
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an issue while retrieving lead imports history',
            {
              ns: 'leadImport',
            }
          )
          return { message: errorMsg }
        },
      }),
      importLeads: builder.mutation<AddPayload, AddParams>({
        query: ({
          organizationId,
          source,
          leadType,
          context,
          contextSpecific,
          ...params
        }) => {
          return {
            url: `${organizationId}/lead-imports`,
            method: 'POST',
            body: {
              source,
              leadType,
              context,
              ...contextSpecific,
              sourceSpecificProps: {
                ...params,
              },
            },
          }
        },
        invalidatesTags: (_, __, args) => {
          return args.leadType === 'contact'
            ? [
                { type: 'Contact', id: 'ORGANIZATION_CONTACTS_LEADS' },
                { type: 'Contact', id: 'MY_CONTACTS_LEADS' },
                { type: 'Contact', id: 'CHROME_EXTENSION_MATCHING_CONTACTS' },
                { type: 'Contact', id: 'BY_ID' },
                { type: 'Sequence', id: 'LIST' },
                { type: 'EnrichmentHub', id: 'LIST' },
                { type: 'LeadImport', id: 'BY_ID' },
              ]
            : [
                { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
                { type: 'Company', id: 'MY_COMPANIES_LEADS' },
                { type: 'Company', id: 'BY_ID' },
                { type: 'LeadImport', id: 'BY_ID' },
                { type: 'Company', id: 'MATCHING_COMPANIES' },
              ]
        },
        transformErrorResponse: error =>
          error.status === 403
            ? {
                status: 'CUSTOM_ERROR',
                message: 'You do not have sufficient permissions to import',
              }
            : error,
      }),
      updateLeadImport: builder.mutation<AddPayload, UpdateLeadImport>({
        query: ({ organizationId, leadImportId, ...params }) => {
          return {
            url: `${organizationId}/lead-imports/${leadImportId}`,
            method: 'PATCH',
            body: {
              ...params,
            },
          }
        },
        invalidatesTags: (_, __, args) => {
          return args.leadType === 'contact'
            ? [
                { type: 'Contact', id: 'ORGANIZATION_CONTACTS_LEADS' },
                { type: 'Contact', id: 'MY_CONTACTS_LEADS' },
                { type: 'Sequence', id: 'LIST' },
                { type: 'Contact', id: 'BY_ID' },
                { type: 'EnrichmentHub', id: 'LIST' },
                { type: 'LeadImport', id: 'BY_ID' },
              ]
            : [
                { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
                { type: 'Company', id: 'MY_COMPANIES_LEADS' },
                { type: 'Company', id: 'BY_ID' },
                { type: 'LeadImport', id: 'BY_ID' },
              ]
        },
        transformErrorResponse: error =>
          error.status === 403
            ? {
                status: 'CUSTOM_ERROR',
                message: 'You do not have sufficient permissions to import',
              }
            : error,
      }),
      deleteLeadImport: builder.mutation<
        FileImportAuthResponseType,
        DeleteLeadImport
      >({
        query: ({ leadImportId, organizationId }) => ({
          url: `${organizationId}/lead-imports/${leadImportId}`,
          method: 'DELETE',
        }),
        invalidatesTags: [{ type: 'LeadImport', id: 'LEAD_IMPORT' }],
      }),
      excludeLeads: builder.mutation<unknown, ExcludeLeadImport>({
        query: ({ organizationId, leadImportId, leadsIdsToRemove }) => {
          return {
            url: `${organizationId}/lead-imports/remove-leads/${leadImportId}`,
            method: 'PATCH',
            body: {
              leadsIdsToRemove,
            },
          }
        },
        transformErrorResponse: error =>
          error.status === 403
            ? {
                status: 'CUSTOM_ERROR',
                message: 'You do not have sufficient permissions to import',
              }
            : error,
      }),
      matchingLeadImport: builder.query<unknown, any>({
        query: ({ organizationId, leadCategory, leadImportId }) => {
          return {
            url: `${organizationId}/matching/lead-import/${leadCategory}/${leadImportId}`,
          }
        },
        // invalidatesTags: (_, __, args) => {
        //   return args.leadType === 'contact'
        //     ? [
        //         { type: 'Contact', id: 'ORGANIZATION_CONTACTS_LEADS' },
        //         { type: 'Contact', id: 'MY_CONTACTS_LEADS' },
        //         { type: 'Contact', id: 'MATCHING_CONTACTS' },
        //         { type: 'Contact', id: 'BY_ID' },
        //         { type: 'Sequence', id: 'LIST' },
        //         { type: 'EnrichmentHub', id: 'LIST' },
        //         { type: 'LeadImport', id: 'LEAD_IMPORT' },
        //       ]
        //     : [
        //         { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
        //         { type: 'Company', id: 'MY_COMPANIES_LEADS' },
        //         { type: 'Company', id: 'BY_ID' },
        //         { type: 'LeadImport', id: 'LEAD_IMPORT' },
        //         { type: 'Company', id: 'MATCHING_COMPANIES' },
        //       ]
        // },
        transformErrorResponse: error =>
          error.status === 403
            ? {
                status: 'CUSTOM_ERROR',
                message: 'You do not have sufficient permissions to import',
              }
            : error,
      }),
      // #region Flatfile
      fileImportAuth: builder.mutation<FileImportAuthResponseType, unknown>({
        query: ({ organizationId }: FileImportAuthPayloadType) => {
          return {
            // Not sure that a good idea to use "flatfile" in the url because if we want to change the provider, we will have to change the url
            // So I prefer to have a generic file name / function name
            url: `${organizationId}/leads/flatfile/auth`,
            method: 'POST',
          }
        },
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'An error occurred while generating flatfile token',
            { ns: 'lead' }
          )
          return { data: { message: errorMsg } }
        },
      }),
      getFilterFields: builder.query<
        LeadsFieldResponseType[],
        {
          organizationId: string | undefined
          leadCategory: LeadCategory | null
        }
      >({
        query: ({ organizationId, leadCategory }) =>
          `${organizationId}/leads/fields/${leadCategory}?type=lead-import`,
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            `An error occurred while retrieving the fields`,
            { ns: 'lead' }
          )
          return { data: { message: errorMsg } }
        },
      }),
    }
  },
})

export const {
  useGetAllImportsQuery,
  useGetOneImportQuery,
  useLazyGetOneImportQuery,
  useImportLeadsMutation,
  useFileImportAuthMutation,
  useDeleteLeadImportMutation,
  useExcludeLeadsMutation,
  useUpdateLeadImportMutation,
  useGetFilterFieldsQuery,
  useMatchingLeadImportQuery,
} = leadImportApi
