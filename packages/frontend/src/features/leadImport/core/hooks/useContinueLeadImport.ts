import { useCallback } from 'react'

import { LeadImportEvents } from '@getheroes/frontend/hooks'
import { CommonLeadFieldEnum } from '@getheroes/frontend/types'
import type { LeadCategory, LeadImport } from '@getheroes/shared'
import { LeadImportContextEnum, LeadImportStatusEnum } from '@getheroes/shared'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { useImportModuleStepperContext } from '@internals/features/leadImport/core/providers/ImportModuleStepperProvider/useImportModuleStepperContext'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'

export const useContinueLeadImport = () => {
  const {
    setIsOpenDrawer,
    setStateLeadImport,
    setCurrentFiltersApi,
    setCurrentFiltersInProcess,
  } = useImportModuleContext()
  const { goToStep, currentStep, removeStep } = useImportModuleStepperContext()
  const { sendEvent } = useTrackingContext()

  const handleContinueImport = useCallback(
    (leadImport: LeadImport) => {
      sendEvent(LeadImportEvents.IMPORT_HISTORY_BUTTON_CLICK_PREVIEW, {
        leadImport,
      })

      const filters = [
        {
          field: CommonLeadFieldEnum.LEAD_IMPORT_IDS,
          operator: OperatorFilterType.EQUALS,
          value: leadImport.id,
        },
      ]
      setCurrentFiltersApi(filters)
      setCurrentFiltersInProcess(filters)
      setStateLeadImport({
        context: leadImport.context,
        leadImportId: leadImport.id,
        contextSpecificProps: leadImport.contextSpecificProps,
        currentLeadCategory: leadImport.leadType.toLowerCase() as LeadCategory,
        currentModule: leadImport.source,
        status: leadImport.status,
        isLoading: leadImport.status === LeadImportStatusEnum.IMPORTING,
        specificProps: {
          source: leadImport.sourceSpecificProps,
          context: leadImport.contextSpecificProps,
        },
      })

      if (
        leadImport.status === LeadImportStatusEnum.LEADS_IMPORTED ||
        leadImport.status === LeadImportStatusEnum.LEADS_EXCLUDED ||
        leadImport.status === LeadImportStatusEnum.SUCCESS
      ) {
        goToStep('review-selections')
      } else if (
        leadImport.status === LeadImportStatusEnum.IMPORTING ||
        leadImport.status === LeadImportStatusEnum.DRAFT ||
        leadImport.status === LeadImportStatusEnum.EXECUTE_ACTIONS
      ) {
        if (leadImport.context === LeadImportContextEnum.SEQUENCES) {
          goToStep('execute-action')
        }
      } else {
        goToStep(currentStep.value)
      }

      setIsOpenDrawer(true)
      if (leadImport.context !== LeadImportContextEnum.SEQUENCES) {
        removeStep(['execute-action'])
      }
    },
    [
      currentStep.value,
      goToStep,
      removeStep,
      sendEvent,
      setCurrentFiltersApi,
      setCurrentFiltersInProcess,
      setIsOpenDrawer,
      setStateLeadImport,
    ]
  )

  return {
    handleContinueImport,
  }
}
