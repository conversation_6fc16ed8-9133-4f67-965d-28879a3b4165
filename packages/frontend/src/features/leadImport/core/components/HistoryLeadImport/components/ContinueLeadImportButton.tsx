import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

import type { LeadImport } from '@getheroes/shared'
import { LeadImportContextEnum, LeadImportStatusEnum } from '@getheroes/shared'
import type { ButtonProps } from '@getheroes/ui'
import { Button } from '@getheroes/ui'
import { useContinueLeadImport } from '@internals/features/leadImport/core/hooks/useContinueLeadImport'
import { useOnExitLeadImport } from '@internals/features/leadImport/core/hooks/useOnExitLeadImport'
import type { ExitLeadCallbackArgs } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { privateRoutes } from '@internals/hooks/useRoute'
import { idReferentials } from '@internals/utils/idReferentials'

interface ContinueLeadImportButtonProps extends ButtonProps {
  leadImport: LeadImport
}

export const ContinueLeadImportButton = ({
  leadImport,
  ...buttonProps
}: ContinueLeadImportButtonProps) => {
  const { t } = useTranslation('leadImport')
  const navigate = useNavigate()
  const { handleContinueImport } = useContinueLeadImport()

  useOnExitLeadImport(
    ({ state, executeAction, contextSpecificProps }: ExitLeadCallbackArgs) => {
      if (
        state.context === LeadImportContextEnum.SEQUENCES &&
        contextSpecificProps.sequenceId
      ) {
        navigate(
          `${privateRoutes.sequences.path}/${contextSpecificProps.sequenceId}/step/leads`,
          {
            state: {
              executeActionLeadImport: executeAction,
              leadImportId: state.leadImportId,
            },
          }
        )
      }
      if (
        state.context === LeadImportContextEnum.ENRICHMENT_HUB &&
        contextSpecificProps.enrichmentHubId
      ) {
        navigate(
          `${privateRoutes.enrichmentHubV2.path}/${contextSpecificProps.enrichmentHubId}`
        )
      }
      if (state.context === LeadImportContextEnum.ALL_LEADS) {
        navigate(`${privateRoutes.allLeadsContact.path}`)
      }
    }
  )

  return (
    [
      LeadImportStatusEnum.DRAFT,
      LeadImportStatusEnum.LEADS_IMPORTED,
      LeadImportStatusEnum.LEADS_EXCLUDED,
      LeadImportStatusEnum.EXECUTE_ACTIONS,
      LeadImportStatusEnum.IMPORTING,
      LeadImportStatusEnum.DRAFT,
    ].includes(leadImport.status) && (
      <Button
        dataTestId={
          idReferentials.importModule.history.continueLeadImportButton
        }
        {...buttonProps}
        onClick={() => handleContinueImport(leadImport)}
      >
        {t('Continue import')}
      </Button>
    )
  )
}
