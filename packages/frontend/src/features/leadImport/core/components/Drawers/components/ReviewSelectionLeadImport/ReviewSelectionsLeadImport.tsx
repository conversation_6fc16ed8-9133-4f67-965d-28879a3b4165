import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { useMatchingLeadImportQuery } from '@internals/features/leadImport/core/api/leadImportApi'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { useTypedSelector } from '@internals/store/store'

import contactMatchingGroups from './contact-matching-groups.json'

import { MergeDataRowLeadImport } from './components/MergeDataLeadImport/MergeDataRowLeadImport'

export const ReviewSelectionsLeadImport = () => {
  // const { data, isLoading, isFetching } = useLeadImportAllLeadsTable({
  //   skip: false,
  // })

  const { leadImportId, currentLeadCategory } = useImportModuleContext()
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const { data } = useMatchingLeadImportQuery({
    organizationId,
    leadCategory: currentLeadCategory,
    leadImportId,
  })

  return (
    <>
      {contactMatchingGroups && (
        <MergeDataRowLeadImport data={contactMatchingGroups} />
      )}
      {/*<ExcludeLeadsLeadImport />*/}
    </>
  )
}
