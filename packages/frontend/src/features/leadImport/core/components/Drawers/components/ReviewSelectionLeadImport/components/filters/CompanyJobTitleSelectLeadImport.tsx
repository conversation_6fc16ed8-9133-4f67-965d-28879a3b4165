import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { v4 as uuidv4 } from 'uuid'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import {
  CommonLeadFieldEnum,
  ExclusiveContactLeadFieldEnum,
} from '@getheroes/frontend/types'
import type { Organization } from '@getheroes/shared'
import { Select } from '@getheroes/ui'
import { useLazySearchContactsDistinctFieldsQuery } from '@internals/features/lead/api/contactApi'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import type { LeadFilterApiType } from '@internals/models/lead'
import { useTypedSelector } from '@internals/store/store'
import { capitalizeFirstLetter } from '@internals/utils/string'

export const CompanyJobTitleSelectLeadImport = () => {
  const [selectedValues, setSelectedValues] = useState<string[]>([])
  const {
    currentFiltersInProcess,
    setCurrentFiltersApi,
    setCurrentFiltersInProcess,
    leadImportId,
  } = useImportModuleContext()
  const { t } = useTranslation('lead')

  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const [searchContactsDistinctFields, { data }] =
    useLazySearchContactsDistinctFieldsQuery()

  useEffect(() => {
    searchContactsDistinctFields({
      organizationId,
      distinctField: ExclusiveContactLeadFieldEnum.JOB_TITLE,
      filters: [
        {
          field: CommonLeadFieldEnum.LEAD_IMPORT_IDS,
          operator: OperatorFilterType.EQUALS,
          value: leadImportId as string,
        },
      ],
    })
  }, [leadImportId, organizationId, searchContactsDistinctFields])

  const options = useMemo(
    () =>
      Object.values(data?.items ?? {})
        .map(jobTitle => ({
          value: jobTitle,
          label: capitalizeFirstLetter(jobTitle),
        }))
        .filter(option => !!option.value)
        .sort((a, b) => a.label.localeCompare(b.label)),
    [data?.items]
  )

  useEffect(() => {
    // Sync state with LeadImportFilters
    const filter = currentFiltersInProcess.find(
      filter => filter.field === ExclusiveContactLeadFieldEnum.JOB_TITLE
    )
    if (!filter) {
      setSelectedValues([])
    } else {
      setSelectedValues(filter.values as string[])
    }
  }, [currentFiltersInProcess])

  return (
    <Select
      placeholder={t('Job title')}
      options={options}
      multiple
      value={selectedValues}
      disabled={options.length === 0}
      onChange={values => {
        setSelectedValues(values as string[])
        const newFilter = {
          field: ExclusiveContactLeadFieldEnum.JOB_TITLE,
          operator: OperatorFilterType.ANY_OF_VALUES,
          values: values as string[],
        }

        // Remove existing company filters to avoid duplication
        const filteredFilters = currentFiltersInProcess.filter(
          filter => filter.field !== ExclusiveContactLeadFieldEnum.JOB_TITLE
        )

        // Add the new filter only if values are selected
        const filtersApi =
          values.length > 0 ? [...filteredFilters, newFilter] : filteredFilters
        const filtersInProcess =
          values.length > 0
            ? [...filteredFilters, { id: uuidv4(), ...newFilter }]
            : filteredFilters

        setCurrentFiltersApi(filtersApi as LeadFilterApiType[])
        setCurrentFiltersInProcess(filtersInProcess)
      }}
    />
  )
}
