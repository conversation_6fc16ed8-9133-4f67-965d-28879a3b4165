import type { MatchingCompanies, MatchingContacts } from '@getheroes/shared'
import { CardV2, Tag, Typography } from '@getheroes/ui'

const CARD_WIDTH = 248

type MatchingRulesLeadListProps = {
  leads: Array<MatchingContacts & MatchingCompanies>
  setSelectedLead: (lead: MatchingContacts & MatchingCompanies) => void
  selectedLead: (MatchingContacts & MatchingCompanies) | null
}

export const MatchingRulesLeadList = ({
  leads,
  setSelectedLead,
  selectedLead,
}: MatchingRulesLeadListProps) => {
  return (
    <div style={{ minWidth: CARD_WIDTH, maxWidth: CARD_WIDTH }}>
      <div
        className={
          'rounded-lg border w-full p-4 h-full shadow-drop-shadow-medium overflow-hidden flex '
        }
      >
        <div
          className={
            'overflow-y-auto flex flex-col gap-2 transition-all duration-300 w-full'
          }
        >
          {leads?.map(lead => (
            <div key={lead.source.id} onClick={() => setSelectedLead(lead)}>
              <CardV2
                isShadow
                padding={'p-2'}
                isHoverable
                variant={selectedLead === lead ? 'selected' : 'default'}
              >
                <div className="flex justify-between items-center truncate">
                  <Typography variant="heading" size="xs">
                    {lead.source.firstName} {lead.source.lastName}
                  </Typography>
                  <Tag
                    color="grey"
                    label={`${lead.matches.length} matches`}
                    size="extra-small"
                    variant="default"
                  />
                </div>
                <div className=" truncate">
                  <Typography variant="body" size="s" weight={'regular'}>
                    {lead.source.companyName}
                  </Typography>
                </div>
              </CardV2>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
