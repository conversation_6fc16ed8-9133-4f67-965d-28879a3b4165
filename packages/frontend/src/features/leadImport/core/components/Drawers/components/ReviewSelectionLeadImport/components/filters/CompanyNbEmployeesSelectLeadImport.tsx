import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { v4 as uuidv4 } from 'uuid'

import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import type { SelectValue } from '@getheroes/ui'
import { Select } from '@getheroes/ui'
import { NbEmployeesList } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/components/NbEmployeesSelect/nbEmployees'
import type { LeadFilterType } from '@internals/features/lead/types/leadFilterType'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import type { LeadFilterApiType } from '@internals/models/lead'

export const CompanyNbEmployeesSelectLeadImport = () => {
  const {
    currentFiltersInProcess,
    currentFiltersApi,
    setCurrentFiltersA<PERSON>,
    setCurrentFiltersInProcess,
  } = useImportModuleContext()
  const { t } = useTranslation('lead')

  const options = NbEmployeesList.map(value => ({
    label: value,
    value,
  }))

  const defaultOption = {
    label: t('Company size'),
    value: '',
  }

  const [selectedValue, setSelectedValue] = useState<SelectValue<string>>('')

  const handleChange = (value: SelectValue<string>) => {
    if (value === '') {
      const filteredInProcess = currentFiltersInProcess.filter(
        filter =>
          filter.field !== ExclusiveContactLeadFieldEnum.COMPANY_NB_EMPLOYEES
      )
      const filteredApi = currentFiltersApi.filter(
        filter =>
          filter.field !== ExclusiveContactLeadFieldEnum.COMPANY_NB_EMPLOYEES
      )

      setCurrentFiltersApi(filteredApi as LeadFilterApiType[])
      setCurrentFiltersInProcess(filteredInProcess as LeadFilterType[])
      setSelectedValue('')
      return
    }
    setSelectedValue(value)

    const [from, to] = (value as string).split('-')

    const newFilter: LeadFilterType = {
      field: ExclusiveContactLeadFieldEnum.COMPANY_NB_EMPLOYEES,
      operator: OperatorFilterType.BETWEEN,
      from,
      to,
    }

    // Remove existing company filters to avoid duplication
    const filteredInProcess = currentFiltersInProcess.filter(
      filter =>
        filter.field !== ExclusiveContactLeadFieldEnum.COMPANY_NB_EMPLOYEES
    )
    const filteredApi = currentFiltersApi.filter(
      filter =>
        filter.field !== ExclusiveContactLeadFieldEnum.COMPANY_NB_EMPLOYEES
    )

    const filtersApi = [...filteredApi, newFilter]
    const filtersInProcess = [
      ...filteredInProcess,
      { id: uuidv4(), ...newFilter },
    ]

    setCurrentFiltersApi(filtersApi as LeadFilterApiType[])
    setCurrentFiltersInProcess(filtersInProcess)
  }

  useEffect(() => {
    // Sync state with LeadImportFilters
    const filter = currentFiltersInProcess.find(
      filter =>
        filter.field === ExclusiveContactLeadFieldEnum.COMPANY_NB_EMPLOYEES
    )
    if (!filter) {
      setSelectedValue('')
    } else {
      if (filter?.value === undefined) {
        return
      }
      setSelectedValue(filter.value as string)
    }
  }, [currentFiltersInProcess])

  return (
    <Select
      disabled={options.length === 0}
      placeholder={t('Company size')}
      options={[defaultOption, ...options]}
      value={selectedValue}
      // @ts-expect-error TS2322: Type (value: SelectValue<string>) => void is not assignable to type
      onChange={handleChange}
    />
  )
}
