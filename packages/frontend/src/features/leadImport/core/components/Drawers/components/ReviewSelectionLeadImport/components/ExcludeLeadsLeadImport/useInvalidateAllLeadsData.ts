import { useEffect } from 'react'

import { api } from '@getheroes/frontend/config/api'
import { NameEvent } from '@getheroes/shared'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { useWebSocketMessage } from '@internals/hooks/websockets/useWebSocketMessage'
import { useAppDispatch } from '@internals/store/store'

export type UseInvalidateAllLeadsDataProps = {
  onDataInvalidation: () => void
}

export const useInvalidateAllLeadsData = ({
  onDataInvalidation,
}: UseInvalidateAllLeadsDataProps) => {
  const dispatch = useAppDispatch()
  const { leadImportId } = useImportModuleContext()

  const { lastMessage } = useWebSocketMessage<{
    leadImportId: string
  }>(NameEvent.LEAD_IMPORT_LEADS_UPDATED)

  useEffect(() => {
    // Wait for backend indexation and invalidate tags to be sure the data is updated
    if (lastMessage?.payload?.leadImportId === leadImportId) {
      dispatch(
        api.util.invalidateTags([
          { type: 'Contact', id: 'ORGANIZATION_CONTACTS_LEADS' },
          { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
        ])
      )
      onDataInvalidation()
    }
  }, [
    dispatch,
    lastMessage?.payload,
    lastMessage?.payload?.leadImportId,
    leadImportId,
    onDataInvalidation,
  ])
}
