import { Fragment } from 'react'

import type {
  LeadCategory,
  MatchingCompanies,
  MatchingContacts,
} from '@getheroes/shared'
import { Divider, Icon, Typography } from '@getheroes/ui'
import { RadioCardLeadImport } from '@internals/features/leadImport/core/components/Drawers/components/ReviewSelectionLeadImport/components/MergeDataLeadImport/RadioCardLeadImport'
// import { MergeDataLeadImport } from '@internals/features/leadImport/core/components/Drawers/components/ReviewSelectionLeadImport/components/MergeDataLeadImport/MergeDataLeadImport'

type MatchingRulesResolutionProps = {
  leadMatchingRules: MatchingContacts & MatchingCompanies
  leadCategory: LeadCategory
}

export const MatchingRulesResolution = ({
  leadMatchingRules,
  leadCategory,
}: MatchingRulesResolutionProps) => {
  if (!leadMatchingRules) {
    return null
  }

  return (
    <div className={'flex flex-col flex-grow overflow-hidden'}>
      <div
        className={
          'flex w-full px-4 py-2 bg-grey-100 border border-borderBase rounded-t-lg'
        }
      >
        <Typography variant={'heading'} size={'s'} weight={'semi-bold'}>
          {leadMatchingRules?.source?.firstName}{' '}
          {leadMatchingRules?.source?.lastName}
        </Typography>
      </div>
      <div
        className={
          'rounded-b-lg border border-t-0 w-full transition-all duration-300 p-4 h-full shadow-drop-shadow-medium flex flex-col gap-2 overflow-hidden'
        }
      >
        <RadioCardLeadImport
          lead={leadMatchingRules?.source}
          matchingId={leadMatchingRules?.source?.matchingId}
          isCreateNewLead
        />
        <div className={'flex items-center'}>
          <div className={'relative'} style={{ top: '-2px' }}>
            <Icon name={'LongArrowDownRight'} />
          </div>
          <Typography variant="label" size="s">
            {leadMatchingRules?.matches?.length} matches found
          </Typography>
        </div>
        <div className={'flex flex-col overflow-y-auto gap-2 mb-2 flex-grow'}>
          {leadMatchingRules?.matches?.map(lead => (
            <Fragment key={lead?.[leadCategory]?.id}>
              <RadioCardLeadImport
                matchingId={leadMatchingRules?.source?.matchingId}
                lead={lead?.[leadCategory]}
                scoring={lead?.scoring}
                matchingSource={lead?.matchingSource}
              />
            </Fragment>
          ))}
        </div>
        <Divider />
        {/*<div className={'flex flex-col gap-2'}>*/}
        {/*  <div className={'flex items-center gap-2'}>*/}
        {/*    <Icon name={'StarHalfDashed'} />*/}
        {/*    <Typography variant="label" size="s" weight={'semi-bold'}>*/}
        {/*      Comparision*/}
        {/*    </Typography>*/}
        {/*  </div>*/}
        {/*  <MergeDataLeadImport data={[leadMatchingRules]} />*/}
        {/*</div>*/}
      </div>
    </div>
  )
}
