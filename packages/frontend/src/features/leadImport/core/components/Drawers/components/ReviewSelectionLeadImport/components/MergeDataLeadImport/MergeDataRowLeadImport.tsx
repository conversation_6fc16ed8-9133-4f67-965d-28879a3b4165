import { useState } from 'react'

import type { MatchingCompanies, MatchingContacts } from '@getheroes/shared'
import { ErrorBoundary } from '@internals/components/technical/ErrorBoundary/ErrorBoundary'
import { MatchingRulesLeadList } from '@internals/features/leadImport/core/components/Drawers/components/ReviewSelectionLeadImport/components/MergeDataLeadImport/MatchingRulesLeadList'
import { MatchingRulesProvider } from '@internals/features/leadImport/core/components/Drawers/components/ReviewSelectionLeadImport/components/MergeDataLeadImport/MatchingRulesProvider/MatchingRulesProvider'
import { MatchingRulesResolution } from '@internals/features/leadImport/core/components/Drawers/components/ReviewSelectionLeadImport/components/MergeDataLeadImport/MatchingRulesResolution'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { ErrorPage } from '@internals/pages/ErrorPage/ErrorPage'

export const MergeDataRowLeadImport = ({
  data,
}: {
  data: Array<MatchingContacts & MatchingCompanies>
}) => {
  const [selectedLead, setSelectedLead] = useState<
    (MatchingContacts & MatchingCompanies) | null
  >(null)
  const { currentLeadCategory } = useImportModuleContext()

  return (
    <MatchingRulesProvider>
      <div className={'flex w-full gap-2 mb-4'}>
        <ErrorBoundary fallbackRender={() => <ErrorPage />}>
          <MatchingRulesLeadList
            leads={data}
            setSelectedLead={setSelectedLead}
            selectedLead={selectedLead}
          />
        </ErrorBoundary>
        {currentLeadCategory !== null && selectedLead && (
          <ErrorBoundary fallbackRender={() => <ErrorPage />}>
            <MatchingRulesResolution
              leadMatchingRules={selectedLead}
              leadCategory={currentLeadCategory}
            />
          </ErrorBoundary>
        )}
      </div>
    </MatchingRulesProvider>
  )
}
