[{"source": {"matchingId": "match-0", "id": "match-0", "email": "<EMAIL>", "linkedinUrl": "https://linkedin.com/in/user0", "firstName": "First0", "lastName": "Last0", "companyName": "Company0", "phoneNumbers": ["+33123456780"], "assignUser": {"id": "user-0", "firstName": "AssigneFirst0", "lastName": "AssigneLast0"}}, "matches": [{"contact": {"id": "match-0-0", "firstName": "First0", "lastName": "Last0", "company": {"id": "company-0", "name": "Company0"}, "email": "<EMAIL>", "externalId": "ext-0", "source": "HUBSPOT"}, "matchingSource": "HUBSPOT", "scoring": {"score": 95, "criterias": [{"name": "EMAIL", "result": "MATCH"}, {"name": "LINKEDIN", "result": "MATCH"}]}}]}, {"source": {"matchingId": "match-1", "id": "match-1", "email": "<EMAIL>", "linkedinUrl": "https://linkedin.com/in/user1", "firstName": "First1", "lastName": "Last1", "companyName": "Company1", "phoneNumbers": ["+33123456781"], "assignUser": {"id": "user-0", "firstName": "AssigneFirst0", "lastName": "AssigneLast0"}}, "matches": [{"contact": {"id": "match-1-0", "firstName": "First1", "lastName": "Last1", "company": {"id": "company-1", "name": "Company1"}, "email": "<EMAIL>", "externalId": "ext-1", "source": "HUBSPOT"}, "matchingSource": "HUBSPOT", "scoring": {"score": 95, "criterias": [{"name": "EMAIL", "result": "MATCH"}, {"name": "LINKEDIN", "result": "MATCH"}]}}]}, {"source": {"matchingId": "match-2", "id": "match-2", "email": "<EMAIL>", "linkedinUrl": "https://linkedin.com/in/user2", "firstName": "First2", "lastName": "Last2", "companyName": "Company2", "phoneNumbers": ["+33123456782"], "assignUser": {"id": "user-0", "firstName": "AssigneFirst0", "lastName": "AssigneLast0"}}, "matches": [{"contact": {"id": "match-2-0", "firstName": "First2", "lastName": "Last2", "company": {"id": "company-2", "name": "Company2"}, "email": "<EMAIL>", "externalId": "ext-2", "source": "HUBSPOT"}, "matchingSource": "HUBSPOT", "scoring": {"score": 95, "criterias": [{"name": "EMAIL", "result": "MATCH"}, {"name": "LINKEDIN", "result": "MATCH"}]}}]}, {"source": {"matchingId": "match-3", "id": "match-3", "email": "<EMAIL>", "linkedinUrl": "https://linkedin.com/in/user3", "firstName": "First3", "lastName": "Last3", "companyName": "Company3", "phoneNumbers": ["+33123456783"], "assignUser": {"id": "user-0", "firstName": "AssigneFirst0", "lastName": "AssigneLast0"}}, "matches": [{"contact": {"id": "match-3-0", "firstName": "First3", "lastName": "Last3", "company": {"id": "company-3", "name": "Company3"}, "email": "<EMAIL>", "externalId": "ext-3", "source": "ALL_LEADS"}, "matchingSource": "ALL_LEADS", "scoring": {"score": 95, "criterias": [{"name": "EMAIL", "result": "MATCH"}, {"name": "LINKEDIN", "result": "MATCH"}]}}]}, {"source": {"matchingId": "match-4", "id": "match-4", "email": "<EMAIL>", "linkedinUrl": "https://linkedin.com/in/user4", "firstName": "First4", "lastName": "Last4", "companyName": "Company4", "phoneNumbers": ["+33123456784"], "assignUser": {"id": "user-0", "firstName": "AssigneFirst0", "lastName": "AssigneLast0"}}, "matches": [{"contact": {"id": "match-4-0", "firstName": "First4", "lastName": "Last4", "company": {"id": "company-4", "name": "Company4"}, "email": "<EMAIL>", "externalId": "ext-4", "source": "HUBSPOT"}, "matchingSource": "HUBSPOT", "scoring": {"score": 95, "criterias": [{"name": "EMAIL", "result": "MATCH"}, {"name": "LINKEDIN", "result": "MATCH"}]}}]}]