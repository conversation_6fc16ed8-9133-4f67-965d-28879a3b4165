import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { v4 as uuidv4 } from 'uuid'

import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import type { Company, Contact } from '@getheroes/shared'
import { LeadCategory } from '@getheroes/shared'
import { Select } from '@getheroes/ui'
import type { LeadFilterType } from '@internals/features/lead/types/leadFilterType'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { useSearchLeadImportLeadsQuery } from '@internals/features/leadImport/core/components/HistoryLeadImport/hooks/useSearchLeadImportLeadsQuery'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { capitalizeFirstLetter } from '@internals/utils/string'

export const CompanySelectLeadImport = () => {
  const [selectedValues, setSelectedValues] = useState<string[]>([])

  const {
    currentFiltersInProcess,
    setCurrentFiltersApi,
    setCurrentFiltersInProcess,
    currentLeadCategory,
  } = useImportModuleContext()
  const { t } = useTranslation('lead')
  const { data } = useSearchLeadImportLeadsQuery()

  const companies: Company[] = useMemo(
    () =>
      currentLeadCategory === LeadCategory.COMPANY
        ? (data?.items as unknown as Company[])
        : Object.values(data?.items ?? {}).map(
            item => (item as Contact).company
          ),
    [currentLeadCategory, data]
  )

  const options = useMemo(
    () =>
      Object.values(companies ?? [])
        .map(company => ({
          value: company?.name,
          label: company?.name ? capitalizeFirstLetter(company?.name) : '',
        }))
        .filter(option => !!option.value)
        .sort((a, b) => a.label.localeCompare(b.label)),
    [companies]
  )

  useEffect(() => {
    // Sync state with LeadImportFilters
    const filter = currentFiltersInProcess.find(
      filter => filter.field === ExclusiveContactLeadFieldEnum.COMPANY_NAME
    )
    if (!filter) {
      setSelectedValues([])
    } else {
      setSelectedValues(filter.values as string[])
    }
  }, [currentFiltersInProcess])

  return (
    <Select
      placeholder={t('Company')}
      options={options}
      multiple
      value={selectedValues}
      disabled={options.length === 0}
      onChange={values => {
        setSelectedValues(values as string[])
        const newFilter = {
          field: ExclusiveContactLeadFieldEnum.COMPANY_NAME,
          operator: OperatorFilterType.ANY_OF_VALUES,
          values: values as string[],
        }

        // Remove existing company filters to avoid duplication
        const filteredFilters = currentFiltersInProcess.filter(
          filter => filter.field !== ExclusiveContactLeadFieldEnum.COMPANY_NAME
        )

        // Add the new filter only if values are selected
        const filtersApi =
          values.length > 0 ? [...filteredFilters, newFilter] : filteredFilters
        const filtersInProcess =
          values.length > 0
            ? [...filteredFilters, { id: uuidv4(), ...newFilter }]
            : filteredFilters

        setCurrentFiltersApi(filtersApi)
        setCurrentFiltersInProcess(filtersInProcess as LeadFilterType[])
      }}
    />
  )
}
