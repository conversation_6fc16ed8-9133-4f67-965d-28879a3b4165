import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { v4 as uuidv4 } from 'uuid'

import { CommonLeadFieldEnum } from '@getheroes/frontend/types'
import { Select } from '@getheroes/ui'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { useSearchLeadImportLeadsQuery } from '@internals/features/leadImport/core/components/HistoryLeadImport/hooks/useSearchLeadImportLeadsQuery'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import type { LeadFilterApiType } from '@internals/models/lead'
import { capitalizeFirstLetter } from '@internals/utils/string'

export const CompanyLocationSelectLeadImport = () => {
  const [selectedValues, setSelectedValues] = useState<string[]>([])
  const {
    currentFiltersInProcess,
    setCurrentFiltersApi,
    setCurrentFiltersInProcess,
  } = useImportModuleContext()
  const { t } = useTranslation('lead')

  const { data } = useSearchLeadImportLeadsQuery()

  const options = useMemo(() => {
    const uniqueCities = new Map()

    Object.values(data?.items || {}).forEach(lead => {
      if (lead?.city) {
        const cityValue = lead.city
        const cityLabel = capitalizeFirstLetter(cityValue)
        uniqueCities.set(cityValue, { value: cityValue, label: cityLabel })
      }
    })

    return Array.from(uniqueCities.values()).sort((a, b) =>
      a.label.localeCompare(b.label)
    )
  }, [data?.items])

  useEffect(() => {
    // Sync state with LeadImportFilters
    const filter = currentFiltersInProcess.find(
      filter => filter.field === CommonLeadFieldEnum.CITY
    )
    if (!filter) {
      setSelectedValues([])
    } else {
      setSelectedValues(filter.values as string[])
    }
  }, [currentFiltersInProcess])

  return (
    <Select
      placeholder={t('Location')}
      options={options}
      multiple
      value={selectedValues}
      disabled={options.length === 0}
      onChange={values => {
        setSelectedValues(values as string[])
        const newFilter = {
          field: CommonLeadFieldEnum.CITY,
          operator: OperatorFilterType.ANY_OF_VALUES,
          values: values as string[],
        }

        // Remove existing company filters to avoid duplication
        const filteredFilters = currentFiltersInProcess.filter(
          filter => filter.field !== CommonLeadFieldEnum.CITY
        )

        // Add the new filter only if values are selected
        const filtersApi =
          values.length > 0 ? [...filteredFilters, newFilter] : filteredFilters
        const filtersInProcess =
          values.length > 0
            ? [...filteredFilters, { id: uuidv4(), ...newFilter }]
            : filteredFilters

        setCurrentFiltersApi(filtersApi as LeadFilterApiType[])
        setCurrentFiltersInProcess(filtersInProcess)
      }}
    />
  )
}
