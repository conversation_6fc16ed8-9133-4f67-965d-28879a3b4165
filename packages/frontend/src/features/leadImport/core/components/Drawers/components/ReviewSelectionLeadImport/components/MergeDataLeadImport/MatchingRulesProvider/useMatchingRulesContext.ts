import { createContext, useContext } from 'react'

// const listModule: ImportModuleList[] = ['list-module', 'my-leads-module']

export enum MatchingRulesActionEnum {
  BACKUP_MATCHING_RULES = 'BACKUP_MATCHING_RULES',
  MATCHING_RULES = 'MATCHING_RULES',
}

export type MatchingRulesReducer = {
  backupMatchingRules: any
  matchingRules: any
}

export type MatchingRulesActionContext = {
  setBackupMatchingRules: (leadMatchingRules: any) => void
  setMatchingRules: (leadMatchingRules: any) => void
}

export const initialState = {
  backupMatchingRules: {},
  matchingRules: {},
  setBackupMatchingRules: (leadMatchingRules: any) => {
    return
  },
  setMatchingRules: (leadMatchingRules: any) => {
    return
  },
} as MatchingRulesReducer & MatchingRulesActionContext

export const matchingRulesReducer = (state: any, action: any) => {
  switch (action.type) {
    case MatchingRulesActionEnum.BACKUP_MATCHING_RULES:
      return { ...state, backupMatchingRules: action.payload }
    case MatchingRulesActionEnum.MATCHING_RULES:
      return {
        ...state,
        matchingRules: {
          ...state.matchingRules,
          ...action.payload,
        },
      }
  }
}

export const MatchingRulesContext = createContext<
  MatchingRulesActionContext & MatchingRulesReducer
>(initialState)

export const useMatchingRulesContext = () => {
  const context = useContext(MatchingRulesContext)

  if (!context) {
    throw new Error(
      'useMatchingRulesContext must be used within MatchingRulesProvider'
    )
  }

  return context
}
