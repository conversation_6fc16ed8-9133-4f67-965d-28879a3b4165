import type { LeadCrmType } from '@getheroes/shared'
import { CardV2, Radio, Tag, Typography } from '@getheroes/ui'
import { LeadLinkIcons } from '@internals/components/business/lead/common/LeadLinkIcons/LeadLinkIcons'
import { useMatchingRulesContext } from '@internals/features/leadImport/core/components/Drawers/components/ReviewSelectionLeadImport/components/MergeDataLeadImport/MatchingRulesProvider/useMatchingRulesContext'

const getMatchingSource = (matchingSource: any) => {
  let finalMatchingSource = ''
  switch (matchingSource) {
    case 'HUBSPOT':
      finalMatchingSource = 'Hubspot'
      break
    case 'ALL_LEADS':
      finalMatchingSource = 'All leads'
      break
    case 'ENRICHMENT_HUB':
      finalMatchingSource = 'Enrichment Hub'
      break
  }
  return finalMatchingSource
}

export const RadioCardLeadImport = ({
  lead,
  isCreateNewLead,
  scoring,
  matchingSource,
  matchingId,
}: any) => {
  const { setMatchingRules, matchingRules } = useMatchingRulesContext()

  return (
    <label
      onClick={() =>
        setMatchingRules({
          [matchingId]: lead,
        })
      }
    >
      <CardV2 isShadow padding={'p-4'}>
        <div className={'flex justify-between items-center gap-2'}>
          <Radio
            id={`$merge-data-lead-import-radio-${lead.id}`}
            name={`merge-data-lead-import-${matchingId}`}
            label={''}
            onChange={e => {
              // console.log('lead', lead)
              // setBackupMatchingRules({
              //   [source.id]: lead,
              // })
            }}
            value={lead.id}
            checked={lead.id === matchingRules?.[matchingId]?.id}
          />
          <div className={'flex flex-col flex-grow gap-2'}>
            <div className="flex justify-between items-center truncate">
              <div className={'flex items-center gap-2'}>
                <Typography variant="heading" size="xs">
                  {lead.firstName} {lead.lastName}
                </Typography>
                <LeadLinkIcons
                  linkedInUrl={lead?.linkedinUrl}
                  crm={lead?.crm as LeadCrmType}
                  // onClickLink={handleClickLink}
                />
              </div>
              <div className={'flex gap-2'}>
                {isCreateNewLead ? (
                  <Tag
                    color="blue"
                    label={`Create new`}
                    size="extra-small"
                    variant="outlined"
                  />
                ) : (
                  <>
                    <Tag
                      color="green"
                      label={`${getMatchingSource(matchingSource)}`}
                      size="extra-small"
                      variant="outlined"
                    />
                    <Tag
                      color="blue"
                      label={`${scoring.score}% similar`}
                      size="extra-small"
                    />
                  </>
                )}
              </div>
            </div>
            <div className="truncate flex gap-2 justify-between">
              <div className={'flex gap-2 items-center'}>
                <Typography variant="body" size="xs" weight={'regular'}>
                  Company:
                </Typography>
                <Typography variant="body" size="s" weight={'medium'}>
                  {lead?.companyName || lead?.company?.name}
                </Typography>
              </div>

              {lead?.assignUser && (
                <div className={'flex gap-1 items-center'}>
                  <Typography variant="body" size="xs" weight={'regular'}>
                    Assigned to:
                  </Typography>
                  <Typography variant="body" size="s" weight={'medium'}>
                    {lead?.assignUser?.firstName} {lead?.assignUser?.lastName}
                  </Typography>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardV2>
    </label>
  )
}
