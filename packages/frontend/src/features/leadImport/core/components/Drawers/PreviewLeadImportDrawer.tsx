import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useSearchParams } from 'react-router-dom'

import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import {
  LeadImportStatusEnum,
  LIMIT_10K,
  type Organization,
} from '@getheroes/shared'
import { Typography } from '@getheroes/ui'
import { GridProvider } from '@internals/components/common/dataDisplay/Grid/GridProvider'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { useGetOneImportQuery } from '@internals/features/leadImport/core/api/leadImportApi'
import { ContinueLeadImportButton } from '@internals/features/leadImport/core/components/HistoryLeadImport/components/ContinueLeadImportButton'
import { HistoryLeadImportTable } from '@internals/features/leadImport/core/components/HistoryLeadImport/components/HistoryLeadImportTable'
import { useHistoryLeadImportContext } from '@internals/features/leadImport/core/components/HistoryLeadImport/hooks/useHistoryLeadImportContext'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { CloseButton } from '@internals/features/sequence/components/SequenceFormPageHeader/components/CloseButton'
import { useTypedSelector } from '@internals/store/store'

import { LeadImportStatusIndicator } from '../LeadImportStatusIndicator/LeadImportStatusIndicator'

export const PreviewLeadImportDrawer = () => {
  const { t } = useTranslation('leadImport')
  const currentUser = useTypedSelector(selectCurrentUser)
  const {
    isLeadImportDrawerOpen,
    openLeadImportDrawer,
    selectedLeadImport,
    closeLeadImportDrawer,
  } = useHistoryLeadImportContext()
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const [searchParams] = useSearchParams()
  const { setLeadImportId } = useImportModuleContext()

  const payload: { organizationId: string; leadImportId: string } = {
    organizationId,
    leadImportId: selectedLeadImport?.id as string,
  }

  const preview = searchParams.get('preview')
  if (preview) {
    payload.leadImportId = preview
  }

  const { data: leadImport } = useGetOneImportQuery(payload, {
    skip: !payload.leadImportId,
  })

  useEffect(() => {
    if (leadImport) {
      setLeadImportId(leadImport.id)
    }
  }, [leadImport, setLeadImportId])

  useEffect(() => {
    if (preview && leadImport) {
      openLeadImportDrawer(leadImport)
    }
  }, [leadImport, openLeadImportDrawer, preview])

  if (!leadImport || leadImport.status === LeadImportStatusEnum.ERROR) {
    return null
  }

  return (
    <Drawer
      classNamePanel="!max-w-full ml-auto"
      open={isLeadImportDrawerOpen}
      onClose={closeLeadImportDrawer}
      disableClickOutside
    >
      <div className="flex flex-col gap-2 p-4 h-full">
        <header className="flex justify-between py-2">
          <Typography variant="heading" size="s">
            {t(leadImport.source)}
          </Typography>
          <CloseButton onClick={closeLeadImportDrawer} />
        </header>
        <div className="flex items-center gap-4">
          <Typography color="base-subtle">
            {t('Created on {{createdAt, datetime}} by {{username}}', {
              createdAt: new Date(leadImport.createdAt),
              username: `${currentUser?.firstName} ${currentUser?.lastName}`,
            })}
          </Typography>
          <LeadImportStatusIndicator status={leadImport.status} />
          <ContinueLeadImportButton leadImport={leadImport} variant="primary" />
        </div>
        <GridProvider>
          <HistoryLeadImportTable limit={LIMIT_10K} />
        </GridProvider>
      </div>
    </Drawer>
  )
}
