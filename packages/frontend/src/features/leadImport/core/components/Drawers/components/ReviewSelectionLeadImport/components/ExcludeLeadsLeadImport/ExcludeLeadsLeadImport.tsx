import { clsx } from 'clsx'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { Button, FloatingBar, Typography } from '@getheroes/ui'
import { Grid } from '@internals/components/common/dataDisplay/Grid/Grid'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { GridSearchBar } from '@internals/components/common/dataDisplay/Grid/components/GridSearchBar/GridSearchBar'
import type { GridColDef } from '@internals/components/common/dataDisplay/Grid/types/grid'
import { AutoSizeStrategyEnum } from '@internals/components/common/dataDisplay/Grid/types/grid'
import { ErrorBoundary } from '@internals/components/technical/ErrorBoundary/ErrorBoundary'
import { LeadImportFilters } from '@internals/features/leadImport/Modules/AllLeadsModule/LeadImportFilters/LeadImportFilters'
import { useExcludeLeadsMutation } from '@internals/features/leadImport/core/api/leadImportApi'
import { ExcludeLeadsSummaryLeadImport } from '@internals/features/leadImport/core/components/Drawers/components/ReviewSelectionLeadImport/components/ExcludeLeadsLeadImport/ExcludeLeadsSummaryLeadImport'
import { useInvalidateAllLeadsData } from '@internals/features/leadImport/core/components/Drawers/components/ReviewSelectionLeadImport/components/ExcludeLeadsLeadImport/useInvalidateAllLeadsData'
import { useLeadImportAllLeadsTable } from '@internals/features/leadImport/core/hooks/useLeadImportAllLeadsTable'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { useDefaultColumnImportModule } from '@internals/features/leadImport/hooks/useDefaultColumnImportModule'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

export const ExcludeLeadsLeadImport = () => {
  const { t } = useTranslation('leadImport')
  const { id: organizationId } =
    useTypedSelector(selectCurrentUserOrganization) || {}
  const {
    currentLeadCategory,
    currentFiltersApi,
    setTotalItems,
    leadImportId,
    limit,
  } = useImportModuleContext()
  const columns = useDefaultColumnImportModule()
  const { selections, setSelections } = useGridContext()
  const [isLoadingDeleteLeads, setIsLoadingDeleteLeads] = useState(false)

  const [excludeLeads] = useExcludeLeadsMutation()

  const { data, isLoading: isLoadingLeads } = useLeadImportAllLeadsTable({
    skip: currentFiltersApi.length === 0,
  })

  const onDataInvalidation = useCallback(() => {
    setSelections([])
    setIsLoadingDeleteLeads(false)
  }, [setSelections])

  useInvalidateAllLeadsData({
    onDataInvalidation,
  })

  useEffect(() => {
    setTotalItems(data?.meta?.totalItems ?? 0)
  }, [data?.meta?.totalItems, setTotalItems])

  const handleRemoveLeads = useCallback(async () => {
    if (organizationId && leadImportId && currentLeadCategory) {
      setIsLoadingDeleteLeads(true)
      excludeLeads({
        organizationId: organizationId,
        leadImportId: leadImportId,
        leadType: currentLeadCategory,
        leadsIdsToRemove: selections || [],
      })
    }
  }, [
    organizationId,
    leadImportId,
    currentLeadCategory,
    excludeLeads,
    selections,
  ])

  return (
    <div
      className={clsx(
        'c-leads-table flex flex-col justify-between h-full w-full overflow-hidden gap-2'
      )}
    >
      <div className={'flex justify-between items-center'}>
        <ExcludeLeadsSummaryLeadImport />
      </div>

      <div className="flex gap-2">
        <ErrorBoundary
          fallbackRender={() => (
            <Button
              variant={'primary'}
              dataTestId={
                idReferentials.leads.myLeadsPage.header.errorBoundaryButton
              }
              iconLeft={'InfoCircle'}
              disabled
            >
              {t('Something went wrong')}
            </Button>
          )}
        >
          <LeadImportFilters />
        </ErrorBoundary>
        <GridSearchBar />
        {/* TODO: The filters are not working correctly, Use components from LeadImportFilters */}
        {/*<CompanySelectLeadImport />*/}
        {/*<CompanyNbEmployeesSelectLeadImport />*/}
        {/*<CompanyLocationSelectLeadImport />*/}
        {/*{currentLeadCategory === LeadCategory.CONTACT && (*/}
        {/*  <CompanyJobTitleSelectLeadImport />*/}
        {/*)}*/}
      </div>
      <div className={'flex flex-col flex-grow gap-2'}>
        <Grid
          overrideLimitSelection={limit}
          name={`import-module-all-leads-table`}
          data={Object.values(data?.items ?? {})}
          columns={
            currentLeadCategory
              ? (columns[currentLeadCategory] as GridColDef[])
              : []
          }
          isLoading={isLoadingLeads}
          paginationMeta={data?.meta}
          checkboxSelection
          hasWindowResize
          autoSizeColumnStrategy={{
            type: AutoSizeStrategyEnum.FILL_GRID_WIDTH,
          }}
          disablePaddingRightPagination
        />
        {selections.length > 0 && (
          <FloatingBar>
            <div className={'p-1 gap-2 flex justify-between items-center'}>
              <Typography color={'base-inverse'} size={'m'} variant={'label'}>
                {t('Leads selected', { count: selections.length })}
              </Typography>
              <Button
                disabled={selections.length === data?.meta?.totalItems}
                size={'small'}
                iconLeft={'Trash'}
                onClick={handleRemoveLeads}
                loading={isLoadingDeleteLeads}
              >
                {t('Remove', { ns: 'common' })}
              </Button>
            </div>
          </FloatingBar>
        )}
      </div>
    </div>
  )
}
