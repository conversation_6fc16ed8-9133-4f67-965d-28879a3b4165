import type { PropsWithChildren, Reducer } from 'react'
import React, { useCallback, useMemo, useReducer } from 'react'

import type { MatchingRulesReducer } from './useMatchingRulesContext'
import {
  initialState,
  MatchingRulesActionEnum,
  MatchingRulesContext,
  matchingRulesReducer,
} from './useMatchingRulesContext'

type MatchingRulesActionType =
  | {
      type: MatchingRulesActionEnum.BACKUP_MATCHING_RULES
      payload: any
    }
  | {
      type: MatchingRulesActionEnum.MATCHING_RULES
      payload: any
    }

export const MatchingRulesProvider = ({ children }: PropsWithChildren) => {
  const [state, dispatch] = useReducer<
    Reducer<MatchingRulesReducer, MatchingRulesActionType>
  >(matchingRulesReducer, initialState)

  const setMatchingRules = useCallback((leadMatchingRules: any) => {
    dispatch({
      type: MatchingRulesActionEnum.MATCHING_RULES,
      payload: leadMatchingRules,
    })
  }, [])

  const setBackupMatchingRules = useCallback((leadMatchingRules: any) => {
    dispatch({
      type: MatchingRulesActionEnum.BACKUP_MATCHING_RULES,
      payload: leadMatchingRules,
    })
    dispatch({
      type: MatchingRulesActionEnum.MATCHING_RULES,
      payload: leadMatchingRules,
    })
  }, [])

  const contextValue = useMemo(() => {
    return {
      ...state,
      setBackupMatchingRules,
      setMatchingRules,
    }
  }, [state, setBackupMatchingRules, setMatchingRules])

  return (
    <MatchingRulesContext.Provider value={contextValue}>
      {children}
    </MatchingRulesContext.Provider>
  )
}
