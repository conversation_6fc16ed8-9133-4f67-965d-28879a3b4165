import type { PropsWithChildren } from 'react'
import { useCallback, useEffect, useMemo, useRef } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { LeadImport } from '@getheroes/shared'
import { LeadImportStatusEnum } from '@getheroes/shared'
import { useToast } from '@getheroes/ui'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { useLazyGetOneImportQuery } from '@internals/features/leadImport/core/api/leadImportApi'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { useImportModuleStepperContext } from '@internals/features/leadImport/core/providers/ImportModuleStepperProvider/useImportModuleStepperContext'
import { useTypedSelector } from '@internals/store/store'

import type { StepsValueType } from '../../components/initialStep'
import { LeadImportPoolingContext } from './useLeadImportPoolingContext'

export const LeadImportPoolingProvider = ({ children }: PropsWithChildren) => {
  const { id: organizationId } =
    useTypedSelector(selectCurrentUserOrganization) || {}

  const { createToast } = useToast()

  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const [getLeadImport] = useLazyGetOneImportQuery()

  const {
    currentModule,
    setIsLoading,
    resetState,
    setStateLeadImport,
    setCurrentFiltersApi,
    setCurrentFiltersInProcess,
  } = useImportModuleContext()
  const stepperContext = useImportModuleStepperContext()

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  const goToStepReviewSelection = useCallback(
    (leadImport: LeadImport) => {
      let timeout: NodeJS.Timeout
      const nextStepIndex = stepperContext.steps.findIndex(
        step => step.value === stepperContext.currentStep.value
      )
      // leadImportWebsocketRef.current = stepperContext.currentStep.value
      setStateLeadImport({
        leadImportId: leadImport.id,
        status: leadImport.status,
        contextSpecificProps: leadImport.contextSpecificProps,
        specificProps: {
          context: leadImport.contextSpecificProps || {},
          source: leadImport.sourceSpecificProps ?? {},
        },
      })
      if (nextStepIndex !== -1) {
        timeout = setTimeout(() => {
          // Secure all data is ready
          if (stepperContext.steps[nextStepIndex + 1]?.value) {
            stepperContext.goToStep(
              stepperContext.steps[nextStepIndex + 1]?.value
            )
          }
          const filter = [
            {
              field: 'leadImportIds',
              operator: OperatorFilterType.EQUALS,
              value: leadImport.id,
            },
          ]
          setCurrentFiltersApi(filter)
          setCurrentFiltersInProcess(filter)
          setIsLoading(false)
          clearTimeout(timeout)
        }, 1000)
      } else {
        // eslint-disable-next-line no-console
        console.error(
          `[Step not found]: Are you sure that this step is included for the ${currentModule} module that is in useListModule?`,
          {
            previousStep: stepperContext.currentStep,
            availableSteps: stepperContext.steps,
          }
        )
      }
    },
    [
      currentModule,
      setCurrentFiltersApi,
      setCurrentFiltersInProcess,
      setIsLoading,
      setStateLeadImport,
      stepperContext,
    ]
  )

  const startPooling = useCallback(
    (id: string, step: StepsValueType) => {
      if (!id || !organizationId) {
        return
      }
      intervalRef.current = setInterval(async () => {
        const { data: leadImport } = await getLeadImport({
          organizationId,
          leadImportId: id,
        })
        if (
          leadImport?.status === LeadImportStatusEnum.IMPORTING ||
          leadImport?.status === LeadImportStatusEnum.DRAFT
        ) {
          return
        }

        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }

        if (leadImport?.status === LeadImportStatusEnum.ERROR) {
          stepperContext.resetStepperState()
          resetState()
          createToast({
            type: 'error',
            message: 'An error has occurred',
          })
          return
        }

        if (!leadImport) {
          return
        }

        if (step === 'select-leads') {
          goToStepReviewSelection(leadImport)
        }
      }, 4000)
    },
    [
      organizationId,
      getLeadImport,
      stepperContext,
      resetState,
      createToast,
      goToStepReviewSelection,
    ]
  )

  const stopPooling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }, [])

  const contextValue = useMemo(() => {
    return { startPooling, stopPooling }
  }, [startPooling, stopPooling])

  return (
    <LeadImportPoolingContext.Provider value={contextValue}>
      {children}
    </LeadImportPoolingContext.Provider>
  )
}
