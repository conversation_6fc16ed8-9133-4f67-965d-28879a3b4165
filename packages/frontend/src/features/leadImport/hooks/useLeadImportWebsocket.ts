import { useCallback, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

import { api } from '@getheroes/frontend/config/api/index'
import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice'
import type { LeadImport } from '@getheroes/shared'
import {
  LeadCategory,
  LeadImportContextEnum,
  LeadImportStatusEnum,
  LeadImportTypeEnum,
  NameEvent,
} from '@getheroes/shared'
import { useToast } from '@getheroes/ui'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { useImportModuleStepperContext } from '@internals/features/leadImport/core/providers/ImportModuleStepperProvider/useImportModuleStepperContext'
import { privateRoutes } from '@internals/hooks/useRoute'
import { useWebSocketMessage } from '@internals/hooks/websockets/useWebSocketMessage'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'

import { useLeadImportPoolingContext } from '../core/providers/LeadImportPoolingProvider/useLeadImportPoolingContext'

export const useLeadImportWebsocket = () => {
  const { t } = useTranslation('leadImport')
  const navigate = useNavigate()
  const leadImportWebsocketRef = useRef('')
  const dispatch = useAppDispatch()
  const { stopPooling } = useLeadImportPoolingContext()
  const { lastMessage } = useWebSocketMessage<LeadImport>(
    NameEvent.LEAD_IMPORT_UPDATED
  )

  const currentUser = useTypedSelector(selectCurrentUser)

  const { createToast } = useToast()
  const {
    setLeadImportId,
    resetState,
    currentLeadCategory,
    currentModule,
    setIsLoading,
    setCurrentFiltersInProcess,
    setCurrentFiltersApi,
    isOpenDrawer,
    leadImportId,
    context,
    contextSpecificProps,
    setStateLeadImport,
  } = useImportModuleContext()
  const stepperContext = useImportModuleStepperContext()

  const navigateToContext = useCallback(
    (status: LeadImportStatusEnum) => {
      const isCurrentRoute =
        location.pathname.includes(`${privateRoutes.allLeadsContact.path}`) ||
        location.pathname.includes(`${privateRoutes.allLeadsCompany.path}`) ||
        ('enrichmentHubId' in contextSpecificProps &&
          location.pathname.includes(
            `${privateRoutes.enrichmentHubV2.path}`
          )) ||
        ('sequenceId' in contextSpecificProps &&
          location.pathname.includes(
            `${privateRoutes.sequences.path}/${contextSpecificProps?.sequenceId}/step/leads`
          ))

      const timeout = setTimeout(() => {
        // Navigate to the context if we come from another import history
        if (
          !isCurrentRoute &&
          context === LeadImportContextEnum.ALL_LEADS &&
          [LeadImportStatusEnum.SUCCESS].includes(status)
        ) {
          if (currentLeadCategory === LeadCategory.CONTACT) {
            navigate(privateRoutes.allLeadsContact.path)
          } else {
            navigate(privateRoutes.allLeadsCompany.path)
          }
        }
        if (
          context === LeadImportContextEnum.ENRICHMENT_HUB &&
          'enrichmentHubId' in contextSpecificProps &&
          !isCurrentRoute &&
          [LeadImportStatusEnum.SUCCESS].includes(status)
        ) {
          navigate(
            `${privateRoutes.enrichmentHubV2.path}/${contextSpecificProps?.enrichmentHubId}`
          )
        }
        if (
          context === LeadImportContextEnum.SEQUENCES &&
          'sequenceId' in contextSpecificProps &&
          [LeadImportStatusEnum.SUCCESS].includes(status) &&
          !isCurrentRoute
        ) {
          navigate(
            `${privateRoutes.sequence.path}/${contextSpecificProps.sequenceId}/step/leads`
          )
        }
        clearTimeout(timeout)
      }, 100)
      return isCurrentRoute
    },
    [contextSpecificProps, context, currentLeadCategory, navigate]
  )

  // Le websocket est mal géré et on a aucun point de rattachement concret pour limiter le déclenchement du useEffect,
  // LEAD_IMPORT_LEADS_UPDATED à été conçu pour palier a se soucis, le problème c'est qu'il est parfois retourné avant le websocket LEAD_IMPORT_UPDATED.
  // Donc il sert à rien, il devrait être retourné après tous les websocket LEAD_IMPORT_UPDATED, ce qui permettrais de passer dans le useEffect 1 seul fois et pas X fois.
  useEffect(() => {
    if (!lastMessage) {
      return
    }

    let timeout: NodeJS.Timeout

    if (currentUser?.id !== lastMessage?.payload?.userId) {
      return
    }

    if (lastMessage?.payload?.status === LeadImportStatusEnum.ERROR) {
      stepperContext.resetStepperState()
      resetState()
      createToast({
        type: 'error',
        message: t('An error has occurred'),
      })
    }

    if (lastMessage?.payload?.status === LeadImportStatusEnum.IMPORTING) {
      setLeadImportId(lastMessage?.payload?.id)
    }
    // #region Select leads step response
    if (
      lastMessage?.payload?.status === LeadImportStatusEnum.LEADS_IMPORTED &&
      leadImportId === lastMessage?.payload?.id &&
      lastMessage?.payload?.nbLeadsImported !== 0 &&
      stepperContext.currentStep.value === 'select-leads' &&
      isOpenDrawer
    ) {
      stopPooling()
      const nextStepIndex = stepperContext.steps.findIndex(
        step => step.value === stepperContext.currentStep.value
      )
      leadImportWebsocketRef.current = stepperContext.currentStep.value
      setStateLeadImport({
        leadImportId: lastMessage?.payload?.id,
        status: lastMessage?.payload?.status,
        contextSpecificProps: lastMessage?.payload?.contextSpecificProps,
        specificProps: {
          context: lastMessage?.payload?.contextSpecificProps || {},
          source: lastMessage?.payload?.sourceSpecificProps || {},
        },
      })
      if (nextStepIndex !== -1) {
        timeout = setTimeout(() => {
          // Secure all data is ready
          if (stepperContext.steps[nextStepIndex + 1]?.value) {
            stepperContext.goToStep(
              stepperContext.steps[nextStepIndex + 1]?.value
            )
          }
          const filter = [
            {
              field: 'leadImportIds',
              operator: OperatorFilterType.EQUALS,
              value: lastMessage?.payload?.id,
            },
          ]
          setCurrentFiltersApi(filter)
          setCurrentFiltersInProcess(filter)
          setIsLoading(false)
          clearTimeout(timeout)
        }, 1000)
      } else {
        // eslint-disable-next-line no-console
        console.error(
          `[Step not found]: Are you sure that this step is included for the ${currentModule} module that is in useListModule?`,
          {
            previousStep: stepperContext.currentStep,
            availableSteps: stepperContext.steps,
          }
        )
      }
    }

    // #region Exit lead import
    if (
      lastMessage?.payload?.status === LeadImportStatusEnum.SUCCESS &&
      lastMessage?.payload?.id === leadImportId &&
      isOpenDrawer
    ) {
      stopPooling()
      leadImportWebsocketRef.current = stepperContext.currentStep.value

      setStateLeadImport({
        leadImportId: lastMessage?.payload?.id,
        status: lastMessage?.payload?.status,
        contextSpecificProps: lastMessage?.payload?.contextSpecificProps,
        specificProps: {
          context: lastMessage?.payload?.contextSpecificProps || {},
          source: lastMessage?.payload?.sourceSpecificProps || {},
        },
      })

      if (context) {
        // When lead import is completed, redirect to context
        // From import history
        navigateToContext(lastMessage?.payload?.status)
      }

      timeout = setTimeout(() => {
        if (context === LeadImportContextEnum.SEQUENCES) {
          resetState()
          stepperContext.resetStepperState()
        }

        switch (lastMessage?.payload?.leadType) {
          case LeadImportTypeEnum.CONTACT:
            dispatch(
              api.util.invalidateTags([
                { type: 'Contact', id: 'ORGANIZATION_CONTACTS_LEADS' },
                { type: 'Contact', id: 'MY_CONTACTS_LEADS' },
                { type: 'Contact', id: 'BY_ID' },
                { type: 'Sequence', id: 'LIST' },
                { type: 'EnrichmentHub', id: 'LIST' },
                { type: 'LeadImport', id: 'LEAD_IMPORT' },
              ])
            )
            break
          case LeadImportTypeEnum.COMPANY:
            dispatch(
              api.util.invalidateTags([
                { type: 'Company', id: 'ORGANIZATION_COMPANIES_LEADS' },
                { type: 'Company', id: 'BY_ID' },
                { type: 'Sequence', id: 'LIST' },
                { type: 'LeadImport', id: 'LEAD_IMPORT' },
              ])
            )
            break
        }
        clearTimeout(timeout)
      }, 1000)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lastMessage])
}
