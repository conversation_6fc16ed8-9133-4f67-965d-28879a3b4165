import { clsx } from 'clsx'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { v4 as uuidv4 } from 'uuid'

import {
  selectCurrentViewId,
  selectIsCurrentViewDefault,
} from '@getheroes/frontend/config/store/selectors/leadSelectors'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { ContactOrCompanyLeadFieldId } from '@getheroes/frontend/types'
import type { Organization } from '@getheroes/shared'
import { LeadCategory } from '@getheroes/shared'
import { Badge, Button, Popover, Tooltip, useToast } from '@getheroes/ui'
import { useLazyGetLeadViewByIdQuery } from '@internals/features/lead/api/leadViewApi'
import { getFilterNameFromLeadCategory } from '@internals/features/lead/utils/transferFiltersBetweenLeadsCategories'
import { useImportModuleContext } from '@internals/features/leadImport/core/providers/ImportModuleProvider/useImportModuleContext'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

import { LeadImportFilterItem } from './LeadImportFilterItem'
import './LeadImportFilters.scoped.scss'

interface LeadFiltersProps {
  disabled?: boolean
}

const MAX_FILTERS_AUTHORIZED = 10

export const LeadImportFilters = ({ disabled }: LeadFiltersProps) => {
  const { t } = useTranslation('lead')
  const { createToast } = useToast()
  const buttonRef = useRef(null)
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const [isOpen, setIsOpen] = useState(false)

  const {
    currentFiltersInProcess,
    currentFiltersNotApplying,
    currentFiltersApi,
    setCurrentFiltersInProcess,
    currentLeadCategory,
  } = useImportModuleContext()

  const isCurrentViewDefault = useTypedSelector(selectIsCurrentViewDefault)
  const currentViewId = useTypedSelector(selectCurrentViewId)

  const [getViewById, { data: view }] = useLazyGetLeadViewByIdQuery()

  const isContactsCategory = currentLeadCategory === LeadCategory.CONTACT

  const hasUserMaxFilters =
    currentFiltersInProcess.length >= MAX_FILTERS_AUTHORIZED

  useEffect(() => {
    if (currentFiltersNotApplying.length && !isContactsCategory) {
      createToast({
        type: 'main',
        message: t(
          `{{nbFiltersNotApplied}} filters will not be applied in Companies`,
          {
            nbFiltersNotApplied: currentFiltersNotApplying.length,
          }
        ),
      })
    }
  }, [
    createToast,
    currentFiltersNotApplying,
    currentLeadCategory,
    isContactsCategory,
    t,
  ])

  useEffect(() => {
    if (!isCurrentViewDefault && currentViewId) {
      const fetchView = async () => {
        await getViewById({
          organizationId,
          viewId: currentViewId,
        }).unwrap()
      }

      fetchView()
    }
  }, [
    currentFiltersApi,
    currentViewId,
    getViewById,
    isCurrentViewDefault,
    view,
    organizationId,
  ])

  const addFilter = useCallback(() => {
    const filter = {
      id: uuidv4(),
      field: '' as ContactOrCompanyLeadFieldId,
      operator: null,
    }
    setCurrentFiltersInProcess([...currentFiltersInProcess, filter])
  }, [currentFiltersInProcess, setCurrentFiltersInProcess])

  useEffect(() => {
    // Initialize the filters when the popover is opened
    if (!isOpen || currentFiltersInProcess.length !== 0) {
      return
    }
    addFilter()
  }, [addFilter, currentFiltersInProcess.length, isOpen])

  const fieldsWithExistingFilters = currentFiltersApi.map(filter =>
    getFilterNameFromLeadCategory({
      filter: filter.field,
      leadCategory: currentLeadCategory,
    })
  )

  const nbActiveFilters = useMemo(
    () =>
      currentFiltersInProcess.filter(
        filter => filter.field !== 'leadImportIds' && Boolean(filter.field)
      ).length,
    [currentFiltersInProcess]
  )

  return (
    <div className={clsx('c-lead-filters flex gap-2')}>
      <Popover onOpenChange={state => setIsOpen(state)}>
        <Popover.Trigger disabled={disabled} asChild>
          <div>
            <Tooltip content={!disabled ? t('Add / remove filters') : ''}>
              <Button
                variant="tertiary-outlined"
                iconLeft="FilterList"
                ref={buttonRef}
                disabled={disabled}
                dataTestId={
                  idReferentials.leads.components.leadsCollectionCard.components
                    .header.leadFilterButton
                }
                endComponent={
                  !!nbActiveFilters && <Badge count={nbActiveFilters} />
                }
              >
                {t('Filters')}
              </Button>
            </Tooltip>
          </div>
        </Popover.Trigger>

        <Popover.Content>
          <div className={clsx('c-lead-filters-popover rounded-m p-2')}>
            <div className={clsx('c-lead-filters-popover__filters space-y-2')}>
              {currentFiltersInProcess.map((filter, index) => (
                <div key={`${filter.field}-${index}`}>
                  <LeadImportFilterItem
                    filter={filter}
                    key={filter.field}
                    fieldsWithExistingFilters={fieldsWithExistingFilters}
                    leadCategory={currentLeadCategory}
                  />
                </div>
              ))}
            </div>

            <div className={clsx('c-lead-filters-popover__footer mt-6')}>
              <Button
                dataTestId={
                  idReferentials.leads.components.leadsCollectionCard.components
                    .header.leadFilterPopover.newFilterButton
                }
                disabled={hasUserMaxFilters}
                variant="tertiary-outlined"
                onClick={() => addFilter()}
                iconLeft="Plus"
              >
                {t('New filter')}
              </Button>
            </div>
          </div>
        </Popover.Content>
      </Popover>
    </div>
  )
}
