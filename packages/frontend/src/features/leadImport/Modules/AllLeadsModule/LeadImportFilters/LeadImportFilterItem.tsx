import { clsx } from 'clsx'
import { debounce } from 'lodash'
import isEmpty from 'lodash/isEmpty'
import type { ChangeEvent } from 'react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { selectLeadsField } from '@getheroes/frontend/config/store/selectors/leadSelectors'
import type { LeadCategory } from '@getheroes/shared'
import { IconButton } from '@getheroes/ui'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { SelectInput } from '@internals/components/common/dataEntry/Select/SelectInput'
import { TextInput } from '@internals/components/common/dataEntry/Text/TextInput'
import { LeadFilterCondition } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/LeadFilterCondition'
import {
  FIELDS_WITH_SELECT_OPTIONS,
  leadFiltersKeyConfig,
} from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/config/leadFiltersConfig'
import type {
  LeadFilterType,
  UpdateFilterParamsType,
} from '@internals/features/lead/types/leadFilterType'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { KindLeadEnum } from '@internals/features/lead/types/leadsTableColumn'
import { getFilterNameFromLeadCategory } from '@internals/features/lead/utils/transferFiltersBetweenLeadsCategories'
import { useLeadImportFilters } from '@internals/features/leadImport/Modules/AllLeadsModule/LeadImportFilters/useLeadImportFilters'
import { usePreventContradictingLeadImportFilters } from '@internals/features/leadImport/Modules/AllLeadsModule/LeadImportFilters/usePreventContradictingLeadImportFilters'
import { useTypedSelector } from '@internals/store/store'

export type LeadImportFilterProps = {
  filter: LeadFilterType
  fieldsWithExistingFilters: string[]
  leadCategory: LeadCategory
}

export const LeadImportFilterItem = ({
  filter,
  fieldsWithExistingFilters,
  leadCategory,
}: LeadImportFilterProps) => {
  const leadsFields = useTypedSelector(selectLeadsField)

  const customField = Object.values(leadsFields)
    .filter(field => field.kind === KindLeadEnum.CUSTOM)
    .find(field => field.id === filter.field)

  const calculatedFilter = useMemo(() => {
    const mappedField = Object.keys(leadFiltersKeyConfig).find(
      key => leadFiltersKeyConfig[key] === filter.field
    )

    return customField
      ? filter
      : ({
          ...filter,
          field: mappedField || filter.field,
        } as LeadFilterType)
  }, [customField, filter])

  const { id, field, operator, value, disabled } = calculatedFilter
  const { t } = useTranslation('lead')
  const { gridApi, setSelections, unGroupRow, setCurrentPage } =
    useGridContext()

  const { updateFilters, removeFilter, getFiltersOptions } =
    useLeadImportFilters()
  const { checkIfFilterIsValid } = usePreventContradictingLeadImportFilters()
  const [isValidFilter, setIsValidFilter] = useState(true)

  useEffect(() => {
    setIsValidFilter(checkIfFilterIsValid(calculatedFilter))
  }, [calculatedFilter, checkIfFilterIsValid])

  const resetSelectionGrid = () => {
    if (!isEmpty(gridApi)) {
      gridApi.deselectAll()
      setSelections([])
    }
  }

  const handleDebounceInputChange = debounce(
    (event: ChangeEvent<HTMLInputElement>) => {
      const { value } = event.target
      const updateParams: UpdateFilterParamsType = {
        id,
        value,
        key: 'value',
      }
      // Don't update the filter if it's not valid
      const isValid = checkIfFilterIsValid({ ...calculatedFilter, value })
      if (isValid) {
        unGroupRow()
        resetSelectionGrid()
        setIsValidFilter(true)
        updateFilters([updateParams])
      } else {
        setIsValidFilter(false)
      }
    },
    500
  )

  const isSelectField =
    FIELDS_WITH_SELECT_OPTIONS.includes(field) ||
    customField?.kind === KindLeadEnum.CUSTOM

  const handleChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value

      const value = leadFiltersKeyConfig[newValue] || newValue

      const updateParams: UpdateFilterParamsType = {
        id,
        value,
        key: 'field',
      }
      updateFilters([updateParams])
      setCurrentPage(1)
    },
    [id, setCurrentPage, updateFilters]
  )

  const defaultValue = useMemo(() => {
    if (field) {
      return {
        value: field,
        label:
          customField?.name ||
          t(
            getFilterNameFromLeadCategory({
              filter: field,
              leadCategory,
            })
          ),
      }
    }
    return undefined
  }, [customField?.name, field, leadCategory, t])

  if (filter.field === 'leadImportIds') {
    return
  }

  return (
    <div className={clsx(`grid grid-cols-leadFilterGrid items-center gap-2`)}>
      <div className="col-span-1">
        <SelectInput
          forceZindex
          name={`field-${id}`}
          placeholder={t('Select an attribute') as string}
          className="w-full"
          isStackingFormfield={false}
          options={getFiltersOptions(fieldsWithExistingFilters)}
          disabled={disabled}
          defaultValue={defaultValue}
          onChange={handleChange}
        />
      </div>
      <div className={isSelectField ? 'col-span-2' : 'col-span-1'}>
        {!!field && (
          <LeadFilterCondition
            filter={calculatedFilter}
            customField={customField}
            onChange={(updateParams: UpdateFilterParamsType[]) => {
              unGroupRow()
              resetSelectionGrid()
              updateFilters(updateParams)
              setCurrentPage(1)
            }}
            leadCategory={leadCategory}
          />
        )}
      </div>
      {!isSelectField && (
        <div className="col-span-1">
          {operator !== OperatorFilterType.EMPTY &&
            operator !== OperatorFilterType.NOT_EMPTY &&
            operator && (
              <TextInput
                placeholder={t('Value') as string}
                name={`value-${id}`}
                className={'w-full'}
                inputClassName={'pt-1 pb-1'}
                isStackingFormfield={false}
                disabled={disabled}
                {...(!isValidFilter && {
                  error: t('Invalid filter - will not be applied') as string,
                })}
                {...(value && { defaultValue: value })}
                onChange={handleDebounceInputChange}
              />
            )}
        </div>
      )}

      {(filter?.field.length !== 0 ||
        (filter.operator !== null && filter?.operator?.length !== 0)) && (
        <div className="justify-self-end col-span-1">
          {!disabled && (
            <IconButton
              icon={'Trash'}
              onClick={() => {
                removeFilter(id)
                setCurrentPage(1)
              }}
              disabled={disabled}
            />
          )}
        </div>
      )}
    </div>
  )
}
