import { useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { selectSubscriptionsSliceForTests } from '@getheroes/frontend/config/store/slices/subscriptionsSlice'
import {
  AppName,
  OrganizationServiceSubscriptionPlan,
  OrganizationSubscriptionType,
} from '@getheroes/shared'
import { isProductionEnv } from '@internals/components/technical/FeatureFlag/OnlyProd/OnlyProd-export'
import { useGetSubscriptionsQuery } from '@internals/features/subscription/api/subscriptionsApi'
import { OrganizationSubscriptionInterval } from '@internals/models/subscriptions/OrganizationSubscriptionInterval'
import { OrganizationSubscriptionStatus } from '@internals/models/subscriptions/OrganizationSubscriptionStatus'
import type { Subscription } from '@internals/models/subscriptions/subscription'
import { useTypedSelector } from '@internals/store/store'
import { useToast } from '@getheroes/ui'

export interface UseSubscriptionsResult {
  serviceSubscription: Subscription
  creditSubscriptions?: Subscription[]
  currentPlan: OrganizationServiceSubscriptionPlan | undefined
  creditBalance: number | undefined
  hasCreditSubscriptions: boolean
  isLoading: boolean
  isCustomPlan: boolean
}

export interface UseSubscriptionsTestResult extends UseSubscriptionsResult {
  currentPlan: OrganizationServiceSubscriptionPlan
}

export const useSubscriptions = ():
  | UseSubscriptionsResult
  | UseSubscriptionsTestResult => {
  const organization = useTypedSelector(selectCurrentUserOrganization)
  const isChromeExtensionContext =
    import.meta.env.VITE_APP_NAME === AppName.EXTENSION

  const { createToast } = useToast()
  const { t } = useTranslation('subscription')

  const { data, error, isLoading } = useGetSubscriptionsQuery(
    {
      organizationId: organization?.id || '',
    },
    {
      skip: !organization?.id || isChromeExtensionContext,
    }
  )

  useEffect(() => {
    if (error && isProductionEnv) {
      createToast({
        message: t('Failed to get subscription'),
        type: 'error',
      })
    }
  }, [createToast, error, t])

  const mockedSubscriptionsSlice = useTypedSelector(
    selectSubscriptionsSliceForTests
  )
  const subscriptionsData = (data || []) as Subscription[]

  const subscriptions = useMemo(() => {
    return {
      serviceSubscription: subscriptionsData.find(
        subscription =>
          subscription.type === OrganizationSubscriptionType.SERVICE
      ) as Subscription,
      creditSubscriptions: subscriptionsData.filter(
        subscription =>
          subscription.type === OrganizationSubscriptionType.CREDIT
      ),
      currentPlan: organization?.plan,
      creditBalance: organization?.creditBalance,
      hasCreditSubscriptions: subscriptionsData.some(
        sub => sub.type === OrganizationSubscriptionType.CREDIT
      ),
      isLoading,
      isCustomPlan:
        !!organization?.plan &&
        [
          OrganizationServiceSubscriptionPlan.ADVANCED,
          OrganizationServiceSubscriptionPlan.ESSENTIAL,
          OrganizationServiceSubscriptionPlan.ENTERPRISE,
        ].includes(organization?.plan),
    }
  }, [
    subscriptionsData,
    organization?.plan,
    organization?.creditBalance,
    isLoading,
  ])

  if (import.meta.env.MODE === 'test') {
    return {
      serviceSubscription:
        mockedSubscriptionsSlice.testMockData?.serviceSubscription ||
        ({
          id: '',
          organizationId: '',
          type: OrganizationSubscriptionType.SERVICE,
          plan: OrganizationServiceSubscriptionPlan.STARTER,
          status: OrganizationSubscriptionStatus.ACTIVE,
          interval: OrganizationSubscriptionInterval.MONTH,
          seats: 1,
          credits: 0,
          startDate: new Date(),
          isTrial: true,
        } as Subscription),
      currentPlan:
        organization?.plan || OrganizationServiceSubscriptionPlan.STARTER,
      creditBalance: organization?.creditBalance || 0,
      hasCreditSubscriptions: subscriptionsData.some(
        sub => sub.type === OrganizationSubscriptionType.CREDIT
      ),
      isLoading,
    }
  }

  return subscriptions
}
