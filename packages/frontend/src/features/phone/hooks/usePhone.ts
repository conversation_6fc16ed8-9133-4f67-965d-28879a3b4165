import { useCallback, useMemo } from 'react'

import { selectVoipIntegration } from '@getheroes/frontend/config/store/slices/integrationSlice'
import { AirCall } from '@internals/features/phone/classes/aircall/aircall'
import type { PhoneProviderInterface } from '@internals/features/phone/classes/interface/phone.interface'
import { Ringover } from '@internals/features/phone/classes/ringover/ringover'
import type { UsePhoneType } from '@internals/features/phone/hooks/types'
import {
  selectPhoneReady,
  setLastEvent,
  setPhoneReady,
} from '@internals/features/phone/slice/dialerSlice'
import type { PhoneEvent } from '@internals/features/phone/types/phone'
import { IntegrationServiceName } from '@internals/features/settings/types/IntegrationType'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'

export type PhoneProviderClasses = typeof AirCall

const initPhoneProviderMapping = new Map<
  IntegrationServiceName,
  PhoneProviderClasses
>([
  [IntegrationServiceName.AIRCALL, AirCall],
  [IntegrationServiceName.RINGOVER, Ringover],
])

export const usePhone = (): UsePhoneType => {
  /* Vars */

  const dispatch = useAppDispatch()
  const voipIntegration = useTypedSelector(selectVoipIntegration)
  const phoneReady = useTypedSelector(selectPhoneReady)

  /* Functions */

  const initPhone = useCallback(
    (containerId: string) => {
      if (!voipIntegration || !containerId) {
        return
      }

      const ClassProvider = initPhoneProviderMapping.get(
        voipIntegration.provider
      )

      if (!ClassProvider) {
        return
      }

      const onPhoneReady = () => {
        dispatch(setPhoneReady(true))
      }

      const onLogout = () => {
        dispatch(setPhoneReady(false))
      }

      const onPhoneEvent = (event: PhoneEvent) => {
        dispatch(setLastEvent(event))
      }

      return new ClassProvider({
        containerId,
        onPhoneReady,
        onPhoneEvent,
        onLogout,
      })
    },
    [dispatch, voipIntegration]
  )

  const sendPhoneNumber = useCallback(
    (phone: PhoneProviderInterface | undefined, phoneNumber: string) => {
      if (!voipIntegration || !phoneReady) {
        return
      }

      if (phone) {
        phone.sendPhoneNumber(phoneNumber)
      }
    },
    [phoneReady, voipIntegration]
  )

  return useMemo(
    () => ({
      initPhone,
      sendPhoneNumber,
    }),
    [initPhone, sendPhoneNumber]
  )
}
