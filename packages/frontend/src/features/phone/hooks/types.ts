import type { PhoneProviderInterface } from '@internals/features/phone/classes'
import type { PhoneEvent } from '@internals/features/phone/types'

export interface InitPhoneProps {
  containerId: string
  onPhoneReady: () => void
  onLogout: () => void
  onPhoneEvent: (event: PhoneEvent) => void
}

export type InitPhoneProviderFunction = ({
  containerId,
  onPhoneReady,
  onPhoneEvent,
}: InitPhoneProps) => void

export type UsePhoneType = {
  initPhone: (containerId: string) => PhoneProviderInterface | undefined
  sendPhoneNumber: (phone: PhoneProviderInterface, phoneNumber: string) => void
}
