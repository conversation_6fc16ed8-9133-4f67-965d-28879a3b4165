import { clsx } from 'clsx'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import { api } from '@getheroes/frontend/config/api'
import { selectVoipIntegration } from '@getheroes/frontend/config/store/slices/integrationSlice'
import { getTimeToStringDigits } from '@getheroes/frontend/utils'
import { Icon } from '@getheroes/ui'
import { StatusIndicator, CurrentStatusIndicator } from '@getheroes/ui-business'
import { PhoneIntegrationLogo } from '@internals/features/phone/components/PhoneIntegrationLogo/PhoneIntegrationLogo'
import { useHandleTracking } from '@internals/features/phone/hooks/useHandleTracking'
import { usePhoneSynchronize } from '@internals/features/phone/hooks/usePhoneSynchronize'
import { usePhoneContext } from '@internals/features/phone/providers/PhoneProvider/usePhoneContext'
import {
  selectCallDuration,
  selectLastEvent,
  selectPeopleTalking,
  selectPhoneReady,
  setPeopleTalking,
} from '@internals/features/phone/slice/dialerSlice'
import type { PhoneEventsPayloadMap } from '@internals/features/phone/types/phone'
import { PhoneEventName } from '@internals/features/phone/types/phone'
import { useHandleCallDuration } from '@internals/hooks/useHandleCallDuration'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'

interface PhoneDongleProps {
  isCollapsed: boolean
}

export const PhoneDongle = ({ isCollapsed }: PhoneDongleProps) => {
  const { t } = useTranslation('call')

  const { isOpen, sendPhoneNumber, togglePhone } = usePhoneContext()

  const dispatch = useAppDispatch()

  const PROVIDER_LOGO_SIZE = isCollapsed === false ? 36 : 24

  const voipIntegration = useTypedSelector(selectVoipIntegration)
  const phoneReady = useTypedSelector(selectPhoneReady)
  const { minutes, seconds } = useTypedSelector(selectCallDuration)
  const peopleTalking = useTypedSelector(selectPeopleTalking)
  const lastEvent = useTypedSelector(selectLastEvent)

  usePhoneSynchronize()
  useHandleCallDuration()
  useHandleTracking()

  useEffect(() => {
    if (!isOpen) {
      sendPhoneNumber('')
    }
  }, [isOpen, sendPhoneNumber])

  useEffect(() => {
    if (!lastEvent) {
      return
    }
    const { type, infos } = lastEvent

    if (type === PhoneEventName.INCOMING_CALL_RINGING) {
      togglePhone()
    }

    if (
      type === PhoneEventName.CALL_ENDED ||
      type === PhoneEventName.OUTGOING_ANSWERED ||
      type === PhoneEventName.INCOMING_CALL_END_RINGTONE
    ) {
      togglePhone()

      if (type === PhoneEventName.CALL_ENDED) {
        dispatch(api.util.invalidateTags([{ type: 'Task', id: 'COUNT_TASKS' }]))
      }
    }

    const incomingCallEndRingtonePayload =
      infos as PhoneEventsPayloadMap[PhoneEventName.INCOMING_CALL_END_RINGTONE]

    const callIsRunning =
      (type === PhoneEventName.INCOMING_CALL_END_RINGTONE &&
        incomingCallEndRingtonePayload.answerStatus === 'answered') ||
      type === PhoneEventName.OUTGOING_ANSWERED

    if (callIsRunning) {
      dispatch(setPeopleTalking(true))
    } else {
      dispatch(setPeopleTalking(false))
    }
  }, [dispatch, lastEvent, togglePhone])

  return (
    <div
      className={clsx(
        'h-full w-full px-1.5 flex rounded-m gap-2 items-center',
        {
          'w-full': isCollapsed,
          'justify-center relative': !isCollapsed,
          'bg-backgroundSuccess border border-backgroundSuccess': peopleTalking,
        }
      )}
    >
      <PhoneIntegrationLogo
        provider={voipIntegration?.provider}
        size={PROVIDER_LOGO_SIZE}
      />
      {peopleTalking ? (
        <div
          className={clsx('flex justify-between flex-1', {
            hidden: !isCollapsed,
          })}
        >
          <p className="label-s">{t('Online')}</p>
          <p className="label-s text-textSubtle">{`${getTimeToStringDigits(
            minutes
          )}:${getTimeToStringDigits(seconds)}`}</p>
        </div>
      ) : (
        <div
          className={clsx('flex items-center justify-between gap-2 flex-1', {
            'absolute top-1 right-1 bg-white rounded-full': !isCollapsed,
          })}
        >
          <div className="flex gap-2 items-center">
            <p className={clsx('label-s', { hidden: !isCollapsed })}>
              {phoneReady ? t('Start a call') : t('Phone not ready')}
            </p>
            <StatusIndicator
              currentStatus={
                phoneReady
                  ? CurrentStatusIndicator.RUNNING
                  : CurrentStatusIndicator.ERROR
              }
              type="integration"
            />
          </div>
          <div className={clsx({ hidden: !isCollapsed })}>
            <Icon name={isOpen ? 'NavArrowDown' : 'NavArrowRight'} />
          </div>
        </div>
      )}
    </div>
  )
}
