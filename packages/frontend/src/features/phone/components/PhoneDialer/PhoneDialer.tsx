import { selectVoipIntegration } from '@getheroes/frontend/config/store/slices/integrationSlice'
import { Tooltip } from '@getheroes/ui'
import { PhoneDongle } from '@internals/features/phone/components/PhoneDongle/PhoneDongle'
import { PhoneNoIntegration } from '@internals/features/phone/components/PhoneNoIntegration/PhoneNoIntegration'
import { useTypedSelector } from '@internals/store/store'
import { capitalizeFirstLetter } from '@internals/utils/string'

import { usePhoneContext } from '../../providers/PhoneProvider/usePhoneContext'

export const PhoneDialer = ({ isOpen: isCollapsed }: { isOpen: boolean }) => {
  /* Vars */

  const voipIntegration = useTypedSelector(selectVoipIntegration)
  const { togglePhone, isOpen } = usePhoneContext()

  /* Render */

  if (!voipIntegration || !voipIntegration.connected) {
    return <PhoneNoIntegration isOpen={isOpen} />
  }

  const Button = (
    <button
      className="cursor-pointer transition grid items-center rounded-m border-transparent border bg-transparent"
      onClick={togglePhone}
    >
      <PhoneDongle isCollapsed={isCollapsed} />
    </button>
  )

  if (isOpen) {
    return Button
  }

  return (
    <Tooltip
      placement="right"
      content={capitalizeFirstLetter(voipIntegration.provider)}
    >
      {Button}
    </Tooltip>
  )
}
