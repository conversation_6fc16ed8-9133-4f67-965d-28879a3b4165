import { act, renderHook } from '@testing-library/react'
import { vi } from 'vitest'

import type { PhoneIntegration } from '@getheroes/frontend/config/store/slices/integrationSlice'
import { usePhone } from '@internals/features/phone/hooks/usePhone'

import type { PhoneProviderInterface } from '../../classes/interface/phone.interface'
import { IFRAME_CONTAINER_ID } from './usePhoneContext'
import { usePhoneProvider } from './usePhoneProvider'

// Mock dependencies
vi.mock('@internals/features/phone/hooks/usePhone')

const mockUsePhone = usePhone as jest.Mock

describe('usePhoneProvider', () => {
  const mockInitPhone = vi.fn()
  const mockSendPhoneNumber = vi.fn()
  const mockPhoneInterface: PhoneProviderInterface = {
    phone: {},
    sendPhoneNumber: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()

    mockUsePhone.mockReturnValue({
      initPhone: mockInitPhone,
      sendPhoneNumber: mockSendPhoneNumber,
    })
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('Initial state', () => {
    it('should initialize with correct default values', () => {
      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: undefined,
          isSuccessIncomingCallTask: false,
        })
      )

      expect(result.current.isOpen).toBe(false)
      expect(result.current.isOpenContactResultModal).toBe(false)
      expect(typeof result.current.togglePhone).toBe('function')
      expect(typeof result.current.sendPhoneNumber).toBe('function')
      expect(typeof result.current.setIsOpenContactResultModal).toBe('function')
    })
  })

  describe('togglePhone', () => {
    it('should toggle isOpen state from false to true', () => {
      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: undefined,
          isSuccessIncomingCallTask: false,
        })
      )

      act(() => {
        result.current.togglePhone()
      })

      expect(result.current.isOpen).toBe(true)
    })

    it('should toggle isOpen state from true to false', () => {
      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: undefined,
          isSuccessIncomingCallTask: false,
        })
      )

      // First toggle to open
      act(() => {
        result.current.togglePhone()
      })

      // Second toggle to close
      act(() => {
        result.current.togglePhone()
      })

      expect(result.current.isOpen).toBe(false)
    })

    it('should reset phone to undefined when toggling', () => {
      mockInitPhone.mockReturnValue(mockPhoneInterface)

      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: { provider: 'aircall' } as PhoneIntegration,
          isSuccessIncomingCallTask: false,
        })
      )

      // Open phone to initialize it
      act(() => {
        result.current.togglePhone()
      })

      act(() => {
        vi.runAllTimers()
      })

      // Verify phone is initialized
      expect(mockInitPhone).toHaveBeenCalledWith(IFRAME_CONTAINER_ID)

      // Toggle again should reset phone
      act(() => {
        result.current.togglePhone()
      })

      expect(result.current.isOpen).toBe(false)
    })
  })

  describe('Phone initialization effect', () => {
    it('should not initialize phone when isOpen is false', () => {
      renderHook(() =>
        usePhoneProvider({
          voipIntegration: { provider: 'aircall' } as PhoneIntegration,
          isSuccessIncomingCallTask: false,
        })
      )

      expect(mockInitPhone).not.toHaveBeenCalled()
    })

    it('should initialize phone when isOpen is true and phone is undefined', () => {
      mockInitPhone.mockReturnValue(mockPhoneInterface)

      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: { provider: 'aircall' } as PhoneIntegration,
          isSuccessIncomingCallTask: false,
        })
      )

      act(() => {
        result.current.togglePhone()
      })

      act(() => {
        vi.runAllTimers()
      })

      expect(mockInitPhone).toHaveBeenCalledWith(IFRAME_CONTAINER_ID)
    })

    it('should not initialize phone when phone is already defined', () => {
      mockInitPhone.mockReturnValue(mockPhoneInterface)

      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: { provider: 'aircall' } as PhoneIntegration,
          isSuccessIncomingCallTask: false,
        })
      )

      // First initialization
      act(() => {
        result.current.togglePhone()
      })

      act(() => {
        vi.runAllTimers()
      })

      expect(mockInitPhone).toHaveBeenCalledTimes(1)

      // Second attempt should not call initPhone again
      act(() => {
        vi.runAllTimers()
      })

      expect(mockInitPhone).toHaveBeenCalledTimes(1)
    })

    it('should use setTimeout with 1ms delay for phone initialization', () => {
      const setTimeoutSpy = vi.spyOn(global, 'setTimeout')
      mockInitPhone.mockReturnValue(mockPhoneInterface)

      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: { provider: 'aircall' } as PhoneIntegration,
          isSuccessIncomingCallTask: false,
        })
      )

      act(() => {
        result.current.togglePhone()
      })

      expect(setTimeoutSpy).toHaveBeenCalledWith(expect.any(Function), 1)
    })
  })

  describe('VoipIntegration effect', () => {
    it('should reset phone when voipIntegration is undefined and phone exists', () => {
      mockInitPhone.mockReturnValue(mockPhoneInterface)

      const { result, rerender } = renderHook(
        ({ voipIntegration }) =>
          usePhoneProvider({
            voipIntegration,
            isSuccessIncomingCallTask: false,
          }),
        {
          initialProps: {
            voipIntegration: { provider: 'aircall' } as PhoneIntegration,
          },
        }
      )

      // Initialize phone
      act(() => {
        result.current.togglePhone()
      })

      act(() => {
        vi.runAllTimers()
      })

      // Remove voipIntegration
      rerender({ voipIntegration: undefined })

      // Phone should be reset
      expect(mockInitPhone).toHaveBeenCalledWith(IFRAME_CONTAINER_ID)
    })

    it('should not reset phone when voipIntegration exists', () => {
      mockInitPhone.mockReturnValue(mockPhoneInterface)

      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: { provider: 'aircall' } as PhoneIntegration,
          isSuccessIncomingCallTask: false,
        })
      )

      // Initialize phone
      act(() => {
        result.current.togglePhone()
      })

      act(() => {
        vi.runAllTimers()
      })

      // Phone should remain initialized
      expect(mockInitPhone).toHaveBeenCalledWith(IFRAME_CONTAINER_ID)
    })
  })

  describe('IncomingCallTask effect', () => {
    it('should open contact result modal when isSuccessIncomingCallTask is true', () => {
      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: undefined,
          isSuccessIncomingCallTask: true,
        })
      )

      expect(result.current.isOpenContactResultModal).toBe(true)
    })

    it('should not open contact result modal when isSuccessIncomingCallTask is false', () => {
      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: undefined,
          isSuccessIncomingCallTask: false,
        })
      )

      expect(result.current.isOpenContactResultModal).toBe(false)
    })

    it('should open contact result modal when isSuccessIncomingCallTask changes to true', () => {
      const { result, rerender } = renderHook(
        ({ isSuccessIncomingCallTask }) =>
          usePhoneProvider({
            voipIntegration: undefined,
            isSuccessIncomingCallTask,
          }),
        {
          initialProps: { isSuccessIncomingCallTask: false },
        }
      )

      expect(result.current.isOpenContactResultModal).toBe(false)

      rerender({ isSuccessIncomingCallTask: true })

      expect(result.current.isOpenContactResultModal).toBe(true)
    })
  })

  describe('sendPhoneNumber', () => {
    it('should call setPhoneNumber when phone exists', () => {
      mockInitPhone.mockReturnValue(mockPhoneInterface)

      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: { provider: 'aircall' } as PhoneIntegration,
          isSuccessIncomingCallTask: false,
        })
      )

      // Initialize phone
      act(() => {
        result.current.togglePhone()
      })

      act(() => {
        vi.runAllTimers()
      })

      // Send phone number
      act(() => {
        result.current.sendPhoneNumber('+33123456789')
      })

      expect(mockSendPhoneNumber).toHaveBeenCalledWith(
        mockPhoneInterface,
        '+33123456789'
      )
    })

    it('should not call setPhoneNumber when phone is undefined', () => {
      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: undefined,
          isSuccessIncomingCallTask: false,
        })
      )

      act(() => {
        result.current.sendPhoneNumber('+33123456789')
      })

      expect(mockSendPhoneNumber).not.toHaveBeenCalled()
    })
  })

  describe('setIsOpenContactResultModal', () => {
    it('should update isOpenContactResultModal state', () => {
      const { result } = renderHook(() =>
        usePhoneProvider({
          voipIntegration: undefined,
          isSuccessIncomingCallTask: false,
        })
      )

      expect(result.current.isOpenContactResultModal).toBe(false)

      act(() => {
        result.current.setIsOpenContactResultModal(true)
      })

      expect(result.current.isOpenContactResultModal).toBe(true)

      act(() => {
        result.current.setIsOpenContactResultModal(false)
      })

      expect(result.current.isOpenContactResultModal).toBe(false)
    })
  })
})
