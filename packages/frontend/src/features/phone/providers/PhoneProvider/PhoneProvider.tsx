import type { PropsWithChildren } from 'react'

import { selectVoipIntegration } from '@getheroes/frontend/config/store/slices/integrationSlice'
import { Popover, PopoverContent } from '@getheroes/ui'
import { IncomingCall } from '@internals/features/phone/components/IncomingCall/IncomingCall'
import {
  IFRAME_CONTAINER_ID,
  PhoneContext,
} from '@internals/features/phone/providers/PhoneProvider/usePhoneContext'
import { useGetIncomingCallTask } from '@internals/hooks/useGetIncomingCallTask'
import { useTypedSelector } from '@internals/store/store'

import { usePhoneProvider } from './usePhoneProvider'

export const PhoneProvider = ({
  children,
  container,
}: PropsWithChildren<{
  container?: HTMLElement | null | React.MutableRefObject<HTMLElement | null>
}>) => {
  /* Vars */

  const voipIntegration = useTypedSelector(selectVoipIntegration)

  /* Queries */

  const { data: task, isSuccess: isSuccessIncomingCallTask } =
    useGetIncomingCallTask()

  /* Hooks */

  const {
    isOpen,
    togglePhone,
    sendPhoneNumber,
    isOpenContactResultModal,
    setIsOpenContactResultModal,
  } = usePhoneProvider({ voipIntegration, isSuccessIncomingCallTask })

  return (
    <PhoneContext.Provider
      value={{
        isOpen,
        togglePhone,
        sendPhoneNumber,
      }}
    >
      <Popover
        open={isOpen}
        onOpenChange={() => togglePhone()}
        placement="bottom-end"
      >
        <IncomingCall
          isOpen={isOpenContactResultModal}
          setIsOpen={setIsOpenContactResultModal}
          task={task}
        />

        {children}

        <PopoverContent padding="s">
          <div className="max-h-screen overflow-auto">
            <div
              id={IFRAME_CONTAINER_ID}
              className="rounded-base w-[23.5rem]"
            />
          </div>
        </PopoverContent>
      </Popover>
    </PhoneContext.Provider>
  )
}
