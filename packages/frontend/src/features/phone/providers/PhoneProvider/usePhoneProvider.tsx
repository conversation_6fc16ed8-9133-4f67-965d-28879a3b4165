import { useEffect, useState } from 'react'

import type { PhoneIntegration } from '@getheroes/frontend/config/store/slices/integrationSlice'
import { usePhone } from '@internals/features/phone/hooks/usePhone'

import type { PhoneProviderInterface } from '../../classes/interface/phone.interface'
import { IFRAME_CONTAINER_ID } from './usePhoneContext'

const NO_PHONE = undefined

export const usePhoneProvider = ({
  voipIntegration,
  isSuccessIncomingCallTask,
}: {
  voipIntegration?: PhoneIntegration
  isSuccessIncomingCallTask: boolean
}) => {
  /* Vars */

  const { initPhone, sendPhoneNumber: setPhoneNumber } = usePhone()

  const [isOpen, setIsOpen] = useState(false)
  const [phone, setPhone] = useState<PhoneProviderInterface | typeof NO_PHONE>(
    NO_PHONE
  )
  const [isOpenContactResultModal, setIsOpenContactResultModal] =
    useState<boolean>(false)

  /* Effects */

  useEffect(() => {
    if (phone) {
      return
    }

    if (isOpen) {
      setTimeout(() => {
        const phoneInterface = initPhone(IFRAME_CONTAINER_ID)

        setPhone(phoneInterface)
      }, 1)
    }
  }, [initPhone, isOpen, phone])

  useEffect(() => {
    if (phone && !voipIntegration) {
      setPhone(NO_PHONE)
    }
  }, [phone, voipIntegration])

  useEffect(() => {
    if (isSuccessIncomingCallTask) {
      setIsOpenContactResultModal(true)
    }
  }, [isSuccessIncomingCallTask])

  /* Functions */

  const togglePhone = () => {
    setIsOpen((prev: boolean) => !prev)
    setPhone(NO_PHONE)
  }

  const sendPhoneNumber = (phoneNumber: string) => {
    if (phone) {
      setPhoneNumber(phone, phoneNumber)
    }
  }

  return {
    isOpen,
    togglePhone,
    sendPhoneNumber,
    isOpenContactResultModal,
    setIsOpenContactResultModal,
  }
}
