import { createContext, useContext } from 'react'

export type PhoneContextType = {
  sendPhoneNumber: (phoneNumber: string) => void
  togglePhone: () => void
  isOpen: boolean
}

const initialValues: PhoneContextType = {
  sendPhoneNumber: () => {
    return
  },
  togglePhone: () => {
    return
  },
  isOpen: false,
}

export const PREVENT_HIDE_IFRAME_CLASS_NAME = 'PHONE_PREVENT_DISMISS_CLASS_NAME'
export const IFRAME_CONTAINER_ID = 'iframe-phone-container' // If you change container id, please edit the PhoneProvider.scss
export const PhoneContext = createContext<PhoneContextType>(initialValues)
export const usePhoneContext = () => useContext(PhoneContext)
