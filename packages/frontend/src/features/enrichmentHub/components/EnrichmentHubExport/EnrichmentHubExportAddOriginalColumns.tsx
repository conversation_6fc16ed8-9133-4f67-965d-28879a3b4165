import { AccordionItem } from 'react-accessible-accordion'
import { useTranslation } from 'react-i18next'

import type { LeadExportableFieldDTO } from '@getheroes/shared'
import { LeadFieldExportCategoryEnum } from '@getheroes/shared'
import { Accordion, CardV2, Divider, Radio, Tag, Tooltip } from '@getheroes/ui'
import { useLeadExport } from '@internals/features/lead-export/hooks/useLeadExport'

interface LeadExportAddColumnsProps {
  filename: string
  fields: LeadExportableFieldDTO[]
  isExpanded: boolean
}

export const EnrichmentHubExportAddOriginalColumns = ({
  filename,
  fields,
  isExpanded,
}: LeadExportAddColumnsProps) => {
  const { t } = useTranslation('export')
  const { isOriginalColumnsChoice, setIsOriginalColumnsChoice } =
    useLeadExport()

  return (
    <CardV2 padding="p-4">
      <div className={'flex flex-col gap-4'}>
        <Radio
          label={t('Select all {{count}} fields from {{filename}}', {
            count: fields?.length ?? 0,
            filename,
          })}
          id="add-columns"
          name="fields-to-export"
          onChange={() => setIsOriginalColumnsChoice(true)}
          checked={isOriginalColumnsChoice}
        />
        <Divider />
        <AccordionItem uuid="item-1" dangerouslySetExpanded={isExpanded}>
          <Accordion.Header>{t('View details')}</Accordion.Header>
          <Accordion.Body>
            <div className="flex flex-wrap gap-1.5">
              {fields?.map(field =>
                field.category ===
                LeadFieldExportCategoryEnum.ORIGINAL_FIELDS ? (
                  <Tag key={field.id} color={'grey'} label={field.id} />
                ) : (
                  <Tooltip key={field.id} content={t('Data enriched by Zeliq')}>
                    <Tag color={'brand-secondary'} label={t(field.id)} />
                  </Tooltip>
                )
              )}
            </div>
          </Accordion.Body>
        </AccordionItem>
      </div>
    </CardV2>
  )
}
