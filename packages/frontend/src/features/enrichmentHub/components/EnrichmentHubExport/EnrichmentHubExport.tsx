import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { CommonLeadFieldEnum } from '@getheroes/frontend/types'
import type { Organization } from '@getheroes/shared'
import { LeadImportSourceEnum } from '@getheroes/shared'
import { Accordion, Card, Divider, Typography } from '@getheroes/ui'
import { Skeleton } from '@internals/components/common/feedback/Skeletons/Skeleton/Skeleton'
import { SkeletonList } from '@internals/components/common/feedback/Skeletons/SkeletonList/SkeletonList'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { useGetOneEnrichmentHubQuery } from '@internals/features/enrichmentHub/api/enrichmentHubImportApi'
import { useGetContactExportableFieldsQuery } from '@internals/features/lead/api/contactApi'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { LeadExportFooter } from '@internals/features/lead-export/components/LeadExportFooter'
import { useLeadExport } from '@internals/features/lead-export/hooks/useLeadExport'
import { useTypedSelector } from '@internals/store/store'

import { useEnrichmentHubExportForm } from '../../hooks/useEnrichmentHubExportForm'
import { EnrichmentHubExportAddOriginalColumns } from './EnrichmentHubExportAddOriginalColumns'
import { EnrichmentHubExportColumns } from './EnrichmentHubExportColumns'
import { EnrichmentHubExportLeads } from './EnrichmentHubExportLeads'

export const EnrichmentHubExport = () => {
  const {
    isExportOpen,
    toggleExport,
    enrichmentHubId,
    leadImportId,
    isOriginalColumnsChoice,
  } = useLeadExport()
  const currentOrganization = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const { t } = useTranslation('export')

  const { data: hubDetails } = useGetOneEnrichmentHubQuery(
    {
      organizationId: currentOrganization?.id,
      id: enrichmentHubId,
    },
    { skip: !currentOrganization?.id || !enrichmentHubId }
  )

  const { data: contactExportableFields } = useGetContactExportableFieldsQuery(
    {
      organizationId: currentOrganization?.id,
      filters: [
        {
          field: CommonLeadFieldEnum.LEAD_IMPORT_IDS,
          operator: OperatorFilterType.EQUALS,
          value: leadImportId,
        },
      ],
    },
    {
      skip: !currentOrganization || (!enrichmentHubId && !leadImportId),
    }
  )

  const {
    isLoading,
    isItemExpanded,
    handleAccordionChange,
    handleSubmit,
    getPreExpanded,
  } = useEnrichmentHubExportForm({
    contactExportableFields,
  })

  return (
    <Drawer
      classNamePanel="!max-w-[800px] h-screen flex flex-col"
      onClose={toggleExport}
      open={isExportOpen}
    >
      <form className="flex flex-col flex-1 h-full" onSubmit={handleSubmit}>
        <div className="flex-1 flex flex-col overflow-y-auto gap-8 pt-8 px-6 pb-8">
          <EnrichmentHubExportLeads />
          {contactExportableFields ? (
            <div className={'flex flex-col h-full gap-4'}>
              <Typography variant="heading" size="s">
                {t('Which fields would you like to export?')}
              </Typography>
              {hubDetails &&
              [
                LeadImportSourceEnum.CSV_FILE,
                // LeadImportSourceEnum.HUBSPOT,
              ].includes(hubDetails.source) ? (
                <Accordion
                  isMultiple
                  preExpanded={[
                    !isOriginalColumnsChoice ? 'item-2' : 'item-1',
                    ...getPreExpanded(),
                  ]}
                  onChange={handleAccordionChange}
                >
                  <EnrichmentHubExportAddOriginalColumns
                    filename={hubDetails.sourceDescription as string}
                    fields={contactExportableFields?.originalFields}
                    isExpanded={isItemExpanded('item-1')}
                  />
                  <EnrichmentHubExportColumns
                    categories={contactExportableFields?.exportableCategories}
                    fields={contactExportableFields?.exportableFields}
                    isChoice
                    isExpanded={isItemExpanded('item-2')}
                  />
                </Accordion>
              ) : (
                <Accordion
                  onChange={handleAccordionChange}
                  preExpanded={['item-2']}
                >
                  <EnrichmentHubExportColumns
                    categories={contactExportableFields?.exportableCategories}
                    fields={contactExportableFields?.exportableFields}
                    isExpanded={isItemExpanded('item-2')}
                  />
                </Accordion>
              )}
            </div>
          ) : (
            <Card>
              <div className="flex flex-col gap-4">
                <Skeleton
                  height={1}
                  className="w-full !m-0 !p-0"
                  hasAnimation
                />
                <Divider />
                <SkeletonList hasBorder={false} />
              </div>
            </Card>
          )}
        </div>
        <LeadExportFooter isLoading={isLoading} isDisabled={isLoading} />
      </form>
    </Drawer>
  )
}
