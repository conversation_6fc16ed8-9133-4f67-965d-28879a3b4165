import { useEffect, useMemo } from 'react'
import { useParams } from 'react-router-dom'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { useGetOneEnrichmentHubQuery } from '@internals/features/enrichmentHub/api/enrichmentHubImportApi'
import { useEnrichmentHubImportWebsocket } from '@internals/features/enrichmentHub/hooks/useEnrichmentHubImportWebsocket'
import {
  useGetContactTableColumnsQuery,
  useSearchContactsQuery,
} from '@internals/features/lead/api/contactApi'
import { OperatorFilterType } from '@internals/features/lead/types/leadFilterType'
import { useTypedSelector } from '@internals/store/store'

type UseEnrichmentHubDetailRequestType = {
  data: any
  refetch: () => void
  isLoading: boolean
  isError: boolean
  isFetching: boolean
  isUninitialized: boolean
  isSuccess: boolean
  error: any
}

type UseEnrichmentHubDetailReturnType = {
  searchHubContactList: UseEnrichmentHubDetailRequestType
  hubDetail: UseEnrichmentHubDetailRequestType
}

/**
 * Retrieves enrichment hub leads data.
 * @returns {UseEnrichmentHubDetailReturnType} The result object containing leads data and query status.
 */
export const useEnrichmentHubDetail = (): UseEnrichmentHubDetailReturnType => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const enrichmentHubWebsocketPayload = useEnrichmentHubImportWebsocket()
  const { hubId } = useParams()
  const { currentPage, pageSize, debouncedSearchText } = useGridContext()

  // Get fields for the contact
  useGetContactTableColumnsQuery({
    organizationId,
  })

  // Get the enrichment hub detail
  const {
    data: dataHubDetail,
    isLoading: isLoadingHubDetail,
    isError: isErrorHubDetail,
    isFetching: isFetchingHubDetail,
    isUninitialized: isUninitializedHubDetail,
    error: errorHubDetail,
    isSuccess: isSuccessHubDetail,
    refetch: refetchHubDetail,
  } = useGetOneEnrichmentHubQuery(
    {
      organizationId,
      id: hubId as string,
    },
    { skip: !hubId || hubId === 'undefined' }
  )

  // Get the enrichment hub leads
  const {
    data,
    refetch,
    isLoading,
    isFetching,
    isError,
    isUninitialized,
    isSuccess,
    error,
  } = useSearchContactsQuery(
    {
      organizationId,
      searchText: debouncedSearchText,
      page: currentPage,
      limitPerPage: pageSize,
      filters: dataHubDetail?.leadImportId
        ? [
            {
              field: 'leadImportIds',
              operator: OperatorFilterType.EQUALS,
              value: dataHubDetail?.leadImportId,
            },
          ]
        : [],
    },
    { skip: !dataHubDetail?.leadImportId || false }
  )

  useEffect(() => {
    // Refetch the hub detail when a property nbLeadsEnriched or status is update from the websocket
    // We need the statistics to be updated and update show the correct banner helper
    if (
      enrichmentHubWebsocketPayload?.payload?.nbLeadsEnriched ||
      enrichmentHubWebsocketPayload?.payload?.status
    ) {
      !isUninitializedHubDetail && refetchHubDetail()
    }
  }, [
    enrichmentHubWebsocketPayload?.payload?.nbLeadsEnriched,
    enrichmentHubWebsocketPayload?.payload?.status,
    refetchHubDetail,
    isUninitializedHubDetail,
  ])

  return useMemo(
    () => ({
      searchHubContactList: {
        data,
        refetch,
        isLoading,
        isFetching,
        isError,
        error,
        isUninitialized,
        isSuccess,
      },
      hubDetail: {
        data: dataHubDetail,
        refetch: refetchHubDetail,
        isLoading: isLoadingHubDetail,
        isError: isErrorHubDetail,
        isFetching: isFetchingHubDetail,
        isUninitialized: isUninitializedHubDetail,
        isSuccess: isSuccessHubDetail,
        error: errorHubDetail,
      },
    }),
    [
      data,
      refetch,
      isLoading,
      isFetching,
      isError,
      error,
      isUninitialized,
      isSuccess,
      dataHubDetail,
      refetchHubDetail,
      isLoadingHubDetail,
      isErrorHubDetail,
      isFetchingHubDetail,
      isUninitializedHubDetail,
      isSuccessHubDetail,
      errorHubDetail,
    ]
  )
}
