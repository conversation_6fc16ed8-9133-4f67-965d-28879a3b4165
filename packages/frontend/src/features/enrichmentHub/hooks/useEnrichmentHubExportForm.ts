import type { FormEvent } from 'react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type {
  GetContactExportableFieldsResponseDTO,
  LeadExportableFieldDTO,
  Organization,
} from '@getheroes/shared'
import { LeadCategory } from '@getheroes/shared'
import { useExportLeadsAsyncMutation } from '@internals/features/lead/api/csvApi'
import type { ExportLeadsTableRequestType } from '@internals/features/lead/types/csvType'
import type { LeadExportOption } from '@internals/features/lead-export/enum/LeadExportOption.enum'
import { useLeadExport } from '@internals/features/lead-export/hooks/useLeadExport'
import { useTypedSelector } from '@internals/store/store'

import type { EnrichmentHubExportFilters } from '../components/EnrichmentHubExport/EnrichmentHubExportFilters.enum'
import {
  getEnrichmentEnrichmentHubExportFilters,
  getEnrichmentHubExportFields,
} from '../utils/enrichmentHubExport.utils'
import { useToast } from '@getheroes/ui'

interface EnrichmentHubExportFormProps {
  contactExportableFields: GetContactExportableFieldsResponseDTO | undefined
}

export const useEnrichmentHubExportForm = ({
  contactExportableFields,
}: EnrichmentHubExportFormProps) => {
  const currentOrganization = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const { createToast } = useToast()
  const { t } = useTranslation('export')
  const [isLoading, setIsLoading] = useState(false)
  const [accordionOpen, setAccordionOpen] = useState<string[]>([])

  const {
    toggleExport,
    enrichmentHubId,
    addExportsInProgress,
    isOriginalColumnsChoice,
  } = useLeadExport()

  const [exportCsvMutationV2] = useExportLeadsAsyncMutation()

  const getPreExpanded = () => {
    const expandedItems = [...accordionOpen]
    if (!isOriginalColumnsChoice) {
      expandedItems.push('item-2')
    } else {
      expandedItems.push('item-1')
    }
    return expandedItems
  }

  const isItemExpanded = (itemId: string) => {
    return getPreExpanded().includes(itemId)
  }

  const handleAccordionChange = (uuids: string[]) => {
    setAccordionOpen(uuids)
  }

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)
    const formData = new FormData(e.currentTarget)
    const formObj: Record<string, string> = {}

    for (const [key, value] of Array.from(formData.entries())) {
      formObj[key] = value.toString()
    }

    const filters = formObj['leads-export'] as EnrichmentHubExportFilters
    const selectValue = formObj['select-fields'] as LeadExportOption
    const choice = isOriginalColumnsChoice ? 'original-column' : selectValue

    const payload: ExportLeadsTableRequestType = {
      exportFields: getEnrichmentHubExportFields(
        choice,
        isOriginalColumnsChoice
          ? (contactExportableFields?.originalFields as LeadExportableFieldDTO[])
          : (contactExportableFields?.exportableFields as LeadExportableFieldDTO[]),
        Object.keys(formObj).filter(
          f =>
            !['leads-export', 'select-fields', 'add-columns'].includes(f) ||
            f.startsWith('all')
        )
      ),
      filters: getEnrichmentEnrichmentHubExportFilters(filters),
      enrichmentHubImportId: enrichmentHubId,
      leadCategory: LeadCategory.CONTACT,
      organizationId: currentOrganization?.id,
      exportType: isOriginalColumnsChoice
        ? 'original-columns'
        : 'default-columns',
    }

    exportCsvMutationV2(payload)
      .unwrap()
      .then(data => {
        addExportsInProgress(
          data.searchQueryObject.filters.find(
            (filter: { field: 'leadImportIds' | 'enrichmentHubId' }) =>
              filter.field === 'leadImportIds' ||
              filter.field === 'enrichmentHubId'
          ).value
        )
        toggleExport()
        createToast({
          type: 'main',
          message: t("CSV export is in progress, you'll receive an email"),
          isUniq: false,
        })
      })
      .catch(error => {
        createToast({
          type: 'error',
          message: t(
            error.data?.message ?? 'An error occurred while exporting your data'
          ),
        })
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  return {
    isLoading,
    accordionOpen,
    getPreExpanded,
    isItemExpanded,
    handleAccordionChange,
    handleSubmit,
  }
}
