import isEmpty from 'lodash/isEmpty'
import { useCallback, useMemo } from 'react'

import type { EnrichmentHubImportType } from '@getheroes/shared'
import { EnrichmentType, getEnrichmentCost } from '@getheroes/shared'
import { useSubscriptions } from '@internals/features/subscription/hooks/useSubscriptions'

/**
 * Returns a function to estimate the credit cost of the Enrichment Hub.
 * The estimated credit cost is calculated based on the number of contacts to enrich and the types of enrichment required.
 *
 * @returns {function} - A function to estimate the credit cost.
 */
export const useEnrichmentHubEstimatedCreditCost = () => {
  const subscriptions = useSubscriptions()

  const estimateEnrichmentHubCreditCost = useCallback(
    (enrichmentTypes: EnrichmentHubImportType[], nbEnrichment = 1) => {
      if (isEmpty(enrichmentTypes)) {
        return {
          estimateTotalCost: 0,
          isInsufficientCredit: false,
        }
      }

      const isEnrichEmail = enrichmentTypes?.includes(EnrichmentType.EMAIL)
      const isEnrichPhone = enrichmentTypes?.includes(EnrichmentType.PHONE)
      const enrichmentType =
        isEnrichEmail && isEnrichPhone
          ? EnrichmentType.ADVANCED
          : isEnrichPhone
            ? EnrichmentType.PHONE
            : EnrichmentType.EMAIL

      const enrichmentCost = getEnrichmentCost(
        enrichmentType,
        subscriptions?.currentPlan
      )
      return {
        estimateTotalCost: enrichmentCost * nbEnrichment,
        isInsufficientCredit:
          enrichmentCost * nbEnrichment > (subscriptions?.creditBalance || 0),
      }
    },
    [subscriptions?.creditBalance, subscriptions?.currentPlan]
  )

  return useMemo(
    () => ({
      estimateEnrichmentHubCreditCost,
    }),
    [estimateEnrichmentHubCreditCost]
  )
}
