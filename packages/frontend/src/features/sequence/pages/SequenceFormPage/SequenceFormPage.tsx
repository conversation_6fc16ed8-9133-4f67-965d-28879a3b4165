import { useContext, useEffect, useMemo } from 'react'
import { useParams } from 'react-router-dom'

import { setSequenceId } from '@getheroes/frontend/config/store/slices/sequenceSlice'
import { GridProvider } from '@internals/components/common/dataDisplay/Grid/GridProvider'
import { PrivateLayout } from '@internals/components/common/layout/PrivateLayout/PrivateLayout'
import { SequenceHeader } from '@internals/features/sequence/components/SequenceFormPageHeader/SequenceFormPageHeader'
import { SequenceFormPageModals } from '@internals/features/sequence/components/SequenceFormPageModals/SequenceFormPageModals'
import { useGetSequence } from '@internals/features/sequence/hooks/useGetSequence'
import { SequenceFormPageStepContext } from '@internals/features/sequence/pages/SequenceFormPage/context/SequenceFormPageStepContext'
import { SequenceFormStepAddLeadAssignPage } from '@internals/features/sequence/pages/SequenceFormPage/steps/AddLeadAssignPage'
import { SequenceFormStepAddStepPage } from '@internals/features/sequence/pages/SequenceFormPage/steps/AddStepsPage/AddStepsPage'
import { SequenceFormStepEnrichPage } from '@internals/features/sequence/pages/SequenceFormPage/steps/EnrichPage/EnrichPage'
import { SequenceFormStepReviewPage } from '@internals/features/sequence/pages/SequenceFormPage/steps/ReviewPage/ReviewPage'
import { SequenceFormStepSettingsPage } from '@internals/features/sequence/pages/SequenceFormPage/steps/SettingsPage'
import { ErrorPage } from '@internals/pages/ErrorPage/ErrorPage'
import { useAppDispatch } from '@internals/store/store'

export const SequenceFormPage = () => {
  /* Vars */

  const { sequenceId = '' } = useParams<{
    sequenceId: string
  }>()
  const { sequence, isError } = useGetSequence({ sequenceId })
  const { currentStepKey, isEditMode } = useContext(SequenceFormPageStepContext)
  const dispatch = useAppDispatch()

  /* Effects */

  useEffect(() => {
    dispatch(setSequenceId(sequenceId))
    return () => {
      dispatch(setSequenceId(''))
    }
  }, [dispatch, sequenceId])

  /* Memos */

  const stepComponent = useMemo(() => {
    switch (currentStepKey) {
      case 'add-leads-assign':
        return (
          <GridProvider>
            <SequenceFormStepAddLeadAssignPage
              sequenceId={sequence?.id || ''}
            />
          </GridProvider>
        )

      case 'add-steps':
        return (
          <SequenceFormStepAddStepPage
            sequenceId={sequence?.id || ''}
            isEditSequence={isEditMode}
          />
        )

      case 'enrich':
        return (
          <GridProvider>
            <SequenceFormStepEnrichPage sequenceId={sequence?.id || ''} />
          </GridProvider>
        )

      case 'settings':
        return <SequenceFormStepSettingsPage />

      case 'review':
        return <SequenceFormStepReviewPage sequenceId={sequence?.id || ''} />

      default:
        return (
          <SequenceFormStepAddLeadAssignPage sequenceId={sequence?.id || ''} />
        )
    }
  }, [currentStepKey, isEditMode, sequence?.id])

  if (isError) {
    return <ErrorPage />
  }

  return (
    <PrivateLayout>
      <div className="flex flex-col h-full gap-2">
        <SequenceHeader sequence={sequence} />

        {stepComponent}

        <SequenceFormPageModals />
      </div>
    </PrivateLayout>
  )
}
