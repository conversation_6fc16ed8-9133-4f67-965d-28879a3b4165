import { isEmpty } from 'lodash'
import type { FC, ReactNode } from 'react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams, useSearchParams } from 'react-router-dom'

import type { Step } from '@internals/components/common/navigation/Stepper/Stepper.type'
import { useGetLeadsBySequence } from '@internals/features/sequence/hooks/useGetLeadsBySequence'
import { useGetListStepsSequence } from '@internals/features/sequence/hooks/useGetListStepsSequence'
import type { SequencePageStepType } from '@internals/features/sequence/types/sequence'
import { checkIsInvalidSequenceStep } from '@internals/features/sequence/util/checkIsInvalidSequenceStep'

import { SequenceFormPageStepContext } from './SequenceFormPageStepContext'

interface SequenceFormPageStepProviderProps {
  children: ReactNode
}

const getInitialStep = (
  t: (key: string) => string
): Step<SequencePageStepType>[] => [
  {
    key: 'add-leads-assign',
    label: t('Add leads & assign'),
    isActive: false,
    isDisabled: false,
    isCompleted: false,
  },
  {
    key: 'add-steps',
    label: t('Add steps'),
    isActive: false,
    isDisabled: false,
    isCompleted: false,
  },

  {
    key: 'settings',
    label: t('Settings'),
    isActive: false,
    isDisabled: true,
    isCompleted: false,
  },
  {
    key: 'enrich',
    label: t('Enrich'),
    isActive: false,
    isDisabled: true,
    isCompleted: false,
  },
  {
    key: 'review',
    label: t('Review and launch'),
    isActive: false,
    isDisabled: true,
    isCompleted: false,
  },
]

export const SequenceFormPageStepProvider: FC<
  SequenceFormPageStepProviderProps
> = ({ children }) => {
  /* Vars */

  const { t } = useTranslation('sequence')
  const { stepId = 'add-leads-assign', sequenceId = '' } = useParams<{
    stepId: SequencePageStepType
    sequenceId: string
  }>()
  const [searchParams] = useSearchParams()
  const isEdit = !!searchParams.get('edit')
  const [formSteps, setFormSteps] = useState<Step<SequencePageStepType>[]>(
    getInitialStep(t)
  )

  /* Queries */

  const { leads, isLoading: isGetLeadsLoading } = useGetLeadsBySequence({
    sequenceId,
  })

  const { steps, isLoading: isGetListStepsLoading } = useGetListStepsSequence({
    sequenceId,
  })

  const isLoading = isGetLeadsLoading || isGetListStepsLoading

  /* Memos */

  const currentStepIndex = useMemo(() => {
    return formSteps.findIndex(step => step.key === stepId)
  }, [stepId, formSteps])

  const emptySteps = useMemo(() => {
    if (!steps || isEmpty(steps)) {
      return []
    }
    return steps.filter(checkIsInvalidSequenceStep)
  }, [steps])

  /* Functions */

  const updateStep = useCallback(
    (
      stepKey: SequencePageStepType,
      param: { isDisabled?: boolean; isCompleted?: boolean }
    ) => {
      setFormSteps(prevFormSteps =>
        prevFormSteps.map(step => ({
          ...step,
          ...(step.key === stepKey ? param : {}),
        }))
      )
    },
    []
  )

  const toggleDisabledStep = useCallback(
    (stepKey: SequencePageStepType, isDisabled: boolean) => {
      updateStep(stepKey, { isDisabled })
    },
    [updateStep]
  )

  const toggleCompleteStep = useCallback(
    (stepKey: SequencePageStepType, isCompleted: boolean) => {
      updateStep(stepKey, { isCompleted })
    },
    [updateStep]
  )

  /* Effects */

  /**
   * Pass the step form when the user navigates to it
   */
  useEffect(() => {
    setFormSteps(prevFormSteps =>
      prevFormSteps.map(stepForm => ({
        ...stepForm,
        isActive: stepForm.key === stepId,
      }))
    )
  }, [stepId])

  /**
   * Pass the add-leads-assign step to complete if leads have been recorded
   */
  useEffect(() => {
    if (leads.length > 0) {
      toggleCompleteStep('add-leads-assign', true)
    } else {
      toggleCompleteStep('add-leads-assign', false)
      toggleDisabledStep('settings', true)
      toggleDisabledStep('enrich', true)
      toggleDisabledStep('review', true)
    }
  }, [leads, toggleCompleteStep, toggleDisabledStep])

  /**
   * Skip the step add-steps if steps have been recorded
   */
  useEffect(() => {
    if (steps && !isEmpty(steps) && emptySteps.length === 0) {
      toggleCompleteStep('add-leads-assign', true)
      toggleCompleteStep('add-steps', true)
      toggleCompleteStep('settings', true)
      toggleCompleteStep('enrich', true)
      toggleCompleteStep('review', true)
      toggleDisabledStep('settings', false)
      toggleDisabledStep('enrich', false)
      toggleDisabledStep('review', false)
    } else {
      toggleCompleteStep('add-steps', false)
      toggleDisabledStep('settings', true)
      toggleDisabledStep('enrich', true)
      toggleDisabledStep('review', true)
    }
  }, [steps, emptySteps, toggleDisabledStep, toggleCompleteStep])

  return (
    <SequenceFormPageStepContext.Provider
      value={{
        steps: formSteps,
        currentStepKey: stepId,
        currentStepIndex,
        isLastStep: currentStepIndex >= formSteps.length - 1,
        isEditMode: isEdit,
        isLoading,
        toggleDisabledStep,
        toggleCompleteStep,
      }}
    >
      {children}
    </SequenceFormPageStepContext.Provider>
  )
}
