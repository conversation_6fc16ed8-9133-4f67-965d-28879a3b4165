import { createContext } from 'react'

import type { Step } from '@internals/components/common/navigation/Stepper/Stepper.type'
import type { SequencePageStepType } from '@internals/features/sequence/types/sequence'

interface SequenceFormPageStepContextType {
  steps: Step<SequencePageStepType>[]
  currentStepKey: SequencePageStepType
  currentStepIndex: number
  isLastStep: boolean
  isEditMode: boolean
  isLoading: boolean
  toggleCompleteStep: (
    stepKey: SequencePageStepType,
    isCompleted: boolean
  ) => void
  toggleDisabledStep: (
    stepKey: SequencePageStepType,
    isDisabled: boolean
  ) => void
}

export const SequenceFormPageStepContext =
  createContext<SequenceFormPageStepContextType | null>(null)
