import { clsx } from 'clsx'
import type { ReactNode } from 'react'

import { Tooltip } from '@getheroes/ui'
import { RenderAs } from '@internals/components/technical/RenderAs/RenderAs'

export const Item = ({
  children,
  className,
  onClick,
  tooltipText,
}: {
  children: ReactNode
  className?: string
  onClick?: () => void
  tooltipText?: string | JSX.Element
}) => {
  return (
    <Tooltip content={tooltipText} placement="bottom">
      <RenderAs
        as={onClick ? 'button' : 'div'}
        onClick={onClick}
        className={clsx(
          'px-6 py-4 bg-backgroundSecondary rounded-lg w-full h-full flex flex-col justify-center',
          className
        )}
      >
        {children}
      </RenderAs>
    </Tooltip>
  )
}
