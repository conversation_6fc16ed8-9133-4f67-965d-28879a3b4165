import { clsx } from 'clsx'
import isEmpty from 'lodash/isEmpty'
import type { FC } from 'react'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import {
  useTracking,
  EnrichEvents,
  SequenceEvents,
} from '@getheroes/frontend/hooks'
import type { EnrichmentType } from '@getheroes/shared'
import { Card, Helper, Tag } from '@getheroes/ui'
import { Grid } from '@internals/components/common/dataDisplay/Grid/Grid'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { CompanyNameCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/CompanyNameCell'
import { ContactNameCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/ContactNameCell'
import { SocialCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/SocialCell'
import { StringCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/StringCell'
import type { GridColDef } from '@internals/components/common/dataDisplay/Grid/types/grid'
import {
  AutoSizeStrategyEnum,
  GridDefaultOptions,
  GridRowSelectionEnum,
} from '@internals/components/common/dataDisplay/Grid/types/grid'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import { ButtonVariant } from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { EnrichLeadsModal } from '@internals/features/lead/components/LeadsModals/EnrichLeadsModal/EnrichLeadsModal'
import { useEnrichPhoneOrEmail } from '@internals/features/lead/hooks/enrichment/useEnrichPhoneOrEmail'
import { ModalDeleteLeads } from '@internals/features/sequence/components/ModalDeleteLeads/ModalDeleteLeads'
import { useGetLeadsBySequence } from '@internals/features/sequence/hooks/useGetLeadsBySequence'
import type {
  LeadBySequence,
  UserMissingData,
} from '@internals/features/sequence/types/sequence'

import { FloatingEnrichLeadsSequence } from './components/FloatingEnrichLeadsSequence'

interface SequenceFormStepEnrichPageProps {
  sequenceId: string
}

const HIDDEN = true // TODO: Wait backend endpoint for get count emails/phones at enrich

export const SequenceFormStepEnrichPage: FC<
  SequenceFormStepEnrichPageProps
> = ({ sequenceId }) => {
  /* Vars */

  const { t } = useTranslation('sequence')
  const missRef = useRef<UserMissingData>({})
  const [isOpenEnrichModal, setIsOpenEnrichModal] = useState(false)
  const [init, setInit] = useState(false)
  const [isEmailPhone, setIsEmailPhone] = useState<{
    emails?: boolean
    phones?: boolean
  }>({
    emails: false,
    phones: false,
  })
  const [countPhones, setCountPhones] = useState(0)
  const [countEmails, setCountEmails] = useState(0)
  const [allContactsLoaded, setAllContactsLoaded] = useState<LeadBySequence[]>(
    []
  )
  const [isOpenModalDeleteLeads, setIsOpenModalDeleteLeads] = useState(false)
  const {
    selections,
    selectedRowsData,
    setSelections,
    gridApi,
    setCurrentPage,
  } = useGridContext()

  /* Queries */

  const { meta, leads, isFetching, refetch, isUninitialized } =
    useGetLeadsBySequence({
      sequenceId,
      filters: isEmailPhone,
    })

  /* Hooks */

  const enrichPhoneOrEmail = useEnrichPhoneOrEmail()
  const { sendEvent } = useTracking()

  /* Memos */

  const leadsBySequence = useMemo(() => {
    if (!leads) {
      return []
    }
    let missingPrepareData = {}
    leads.forEach(({ contact }) => {
      missingPrepareData = {
        ...missingPrepareData,
        [contact.id]: {
          emails: isEmpty(contact.emails),
          phones: isEmpty(contact.phones),
        },
      }
    })
    missRef.current = missingPrepareData
    if (!init) {
      setInit(true)
      setCountPhones(
        Object.values(missingPrepareData)?.filter(
          (contact: any) => contact?.phones
        )?.length || 0
      )
      setCountEmails(
        Object.values(missingPrepareData)?.filter(
          (contact: any) => contact?.emails
        )?.length || 0
      )
    }
    setAllContactsLoaded(prev => [...prev, ...leads])
    return leads
  }, [leads]) // eslint-disable-line react-hooks/exhaustive-deps

  const columns = useMemo(() => {
    return [
      {
        field: 'name',
        headerName: t('Name', { ns: 'lead' }),
        cellRenderer: ({ data }: { data: LeadBySequence }) => (
          <ContactNameCell contact={data.contact} />
        ),
      },
      {
        field: 'title',
        headerName: t('Job title', { ns: 'lead' }),
        cellRenderer: ({ data }: { data: LeadBySequence }) => {
          return (
            <StringCell
              lead={data.contact}
              value={data.contact.title}
              isEnrichmentColumn={false}
            />
          )
        },
      },
      {
        field: 'linkedinUrl',
        headerName: t('LinkedIn', { ns: 'lead' }),
        cellRenderer: ({ data }: { data: LeadBySequence }) => (
          <SocialCell
            isEnrichmentColumn={false}
            lead={data.contact}
            icon={'Linkedin'}
            value={data.contact.linkedinUrl}
          />
        ),
      },
      {
        field: 'company',
        headerName: t('company', { ns: 'lead' }),
        cellRenderer: ({ data }: { data: LeadBySequence }) => (
          <CompanyNameCell company={data.contact.company} />
        ),
      },
      {
        field: 'missing',
        headerName: t('missing', { ns: 'lead' }),
        cellRenderer: ({ data }: { data: LeadBySequence }) => {
          return (
            <div className="flex gap-2">
              {missRef.current[data.id]?.emails &&
                isEmailPhone?.emails !== undefined && (
                  <Tag
                    label={t('Missing Email')}
                    color={'red'}
                    icon={'WarningCircle'}
                  />
                )}

              {missRef.current[data.id]?.phones &&
                isEmailPhone?.phones !== undefined && (
                  <Tag
                    label={t('Missing Phone Number')}
                    color={'red'}
                    icon={'WarningCircle'}
                  />
                )}
            </div>
          )
        },
      },
    ]
  }, [isEmailPhone?.emails, isEmailPhone?.phones, t])

  /* Effects */

  useEffect(() => {
    if (!isOpenEnrichModal) {
      return
    }
    const filteredContacts: LeadBySequence[] = []
    selections.forEach(id => {
      if (
        allContactsLoaded.find((data: LeadBySequence) => data.contact.id === id)
      ) {
        filteredContacts.push(
          allContactsLoaded.find(
            (data: LeadBySequence) => data.contact.id === id
          ) as LeadBySequence
        )
      }
    })
  }, [
    allContactsLoaded,
    leadsBySequence,
    gridApi,
    isOpenEnrichModal,
    selections,
  ])

  useEffect(() => {
    if (!isUninitialized && !isEmpty(gridApi)) {
      setCurrentPage(GridDefaultOptions.PAGE)
      setSelections([])
      gridApi.deselectAll()
      refetch()
    }
  }, [
    isEmailPhone,
    refetch,
    isUninitialized,
    setCurrentPage,
    setSelections,
    gridApi,
  ])

  /* Functions */

  const handleShowMissingPhone = () => {
    if (isEmailPhone.emails === undefined) {
      setIsEmailPhone({
        phones: false,
        emails: false,
      })
    } else {
      setIsEmailPhone({
        phones: false,
      })
    }

    sendEvent(
      SequenceEvents.SEQUENCE_ENRICH_PAGE_CLICK_ENRICH_TAB_EMAIL_PHONE,
      {
        option: 'phone',
      }
    )
  }

  const handleShowMissingMail = () => {
    if (isEmailPhone.phones === undefined) {
      setIsEmailPhone({
        phones: false,
        emails: false,
      })
    } else {
      setIsEmailPhone({
        emails: false,
      })
    }

    sendEvent(
      SequenceEvents.SEQUENCE_ENRICH_PAGE_CLICK_ENRICH_TAB_EMAIL_PHONE,
      {
        option: 'mail',
      }
    )
  }

  const handleEnrichLeads = (enrichmentType?: EnrichmentType) => {
    // noinspection JSIgnoredPromiseFromCall
    enrichPhoneOrEmail(Object.values(selectedRowsData), enrichmentType)
    setSelections([])

    sendEvent(EnrichEvents.ENRICH_MODAL_CLICK_LAUNCH_ENRICHMENT)
  }

  const handleSubmitDeleteLeads = () => {
    setIsOpenModalDeleteLeads(false)
    setSelections([])
  }

  return (
    <Card fullHeight>
      <div className="w-full h-full flex flex-col flex-grow">
        <Helper
          color="blue-secondary"
          icon="Sparks"
          title={t('Enrich your leads')}
          description={t(
            'Improve your prospecting by having 7x more direct conversations with qualified leads.'
          )}
        />

        <div className="flex flex-col overflow-hidden mt-2 gap-y-2 flex-grow relative">
          <div className={'flex'}>
            <Button
              dataTestId={'lexical-editor-image-button'}
              variant={
                isEmailPhone.emails === undefined
                  ? ButtonVariant.SECONDARY
                  : ButtonVariant.TERTIARY
              }
              onClick={handleShowMissingPhone}
              className={clsx('toolbar-item spaced border', {
                'border-borderAccent': isEmailPhone.emails === undefined,
              })}
              aria-label="Insert image"
              title="Insert image"
              type="button"
            >
              {/* TODO: We can't count total phones missing, we need a endpoint for this information */}
              {!HIDDEN && (
                <Tag
                  label={`${countPhones}`}
                  color={
                    isEmailPhone.emails === undefined ? 'brand-primary' : 'grey'
                  }
                />
              )}

              {t('Missing phone numbers')}
            </Button>

            <Button
              dataTestId={'lexical-editor-image-button'}
              variant={
                isEmailPhone.phones === undefined
                  ? ButtonVariant.SECONDARY
                  : ButtonVariant.TERTIARY
              }
              onClick={handleShowMissingMail}
              className={clsx('toolbar-item spaced border ml-4', {
                'border-borderAccent': isEmailPhone.phones === undefined,
              })}
            >
              {/* TODO: We can't count total email missing, we need a endpoint for this information */}
              {!HIDDEN && (
                <Tag
                  label={`${countEmails}`}
                  color={
                    isEmailPhone.phones === undefined ? 'brand-primary' : 'grey'
                  }
                />
              )}

              {t('Missing emails')}
            </Button>
          </div>

          <Grid
            name={`sequence-enrich-contacts-table`}
            data={leadsBySequence || []}
            columns={columns as GridColDef[]}
            checkboxSelection
            rowSelection={GridRowSelectionEnum.MULTIPLE}
            paginationMeta={meta}
            isLoading={isFetching}
            hasWindowResize
            autoSizeColumnStrategy={{
              type: AutoSizeStrategyEnum.FILL_GRID_WIDTH,
            }}
            suppressDragLeaveHidesColumns
            suppressRowClickSelection={false}
            saveToLocalStorage
          />

          {!isOpenEnrichModal && selections.length !== 0 && (
            <FloatingEnrichLeadsSequence
              openDrawerEnrichLeads={setIsOpenEnrichModal}
              setIsOpenModalDeleteLeads={setIsOpenModalDeleteLeads}
            />
          )}
        </div>

        <ModalDeleteLeads
          open={isOpenModalDeleteLeads}
          onSubmit={handleSubmitDeleteLeads}
          onClose={() => setIsOpenModalDeleteLeads(false)}
          contactIds={selections}
          sequenceId={sequenceId}
        />

        <EnrichLeadsModal
          isOpen={isOpenEnrichModal}
          onClose={() => setIsOpenEnrichModal(false)}
          onClickEnrich={handleEnrichLeads}
          nbContacts={selections.length}
        />
      </div>
    </Card>
  )
}
