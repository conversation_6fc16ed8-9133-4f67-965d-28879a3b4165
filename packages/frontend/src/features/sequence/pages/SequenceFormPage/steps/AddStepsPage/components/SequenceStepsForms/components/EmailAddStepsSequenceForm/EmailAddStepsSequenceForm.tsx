import { clsx } from 'clsx'
import { isNull } from 'lodash'
import isEmpty from 'lodash/isEmpty'
import isUndefined from 'lodash/isUndefined'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { useDebounce } from 'usehooks-ts'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { EmailAttachment, RegexClass, toMo } from '@getheroes/shared'
import { IconButton, Spinner, Tooltip, Typography } from '@getheroes/ui'
import { Lexical } from '@internals/components/common/dataEntry/Lexical/Lexical'
import { useValidator } from '@internals/components/common/dataEntry/Lexical/hooks/useValidator'
import { useVariableValidator } from '@internals/components/common/dataEntry/Lexical/hooks/useVariableValidator'
import type {
  EditorOnChangeType,
  LexicalImperativeHandle,
} from '@internals/components/common/dataEntry/Lexical/lexical.type'
import { LEXICAL_EMPTY_CONTENT } from '@internals/components/common/dataEntry/Lexical/utils/isEmptyLegacyContent'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { useUpdateSequenceStep } from '@internals/features/sequence/hooks/useUpdateSequenceStep'
import { useSequenceStepValidation } from '@internals/features/sequence/pages/SequenceFormPage/steps/AddStepsPage/components/SequenceStepsForms/hooks/useSequenceStepValidation'
import type { SequenceStepperMode } from '@internals/features/sequence/types/sequence'
import type {
  EmailStepContent,
  SequenceStepApi,
} from '@internals/features/sequence/types/sequenceStep'
import { DrawerTaskSelectTemplate } from '@internals/features/task/components/PanelRight/DrawerTaskSelectTemplate/DrawerTaskSelectTemplate'
import { TemplateType } from '@internals/models/template'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

import './EmailAddStepsSequenceForm.scoped.scss'

type EmailAddStepsSequenceFormProps = {
  step: SequenceStepApi
  stepperMode: SequenceStepperMode
}

const EMAIL_SUBJECT_MAX_LENGTH = 77

export const EmailAddStepsSequenceForm = ({
  step,
  stepperMode,
}: EmailAddStepsSequenceFormProps) => {
  /* Vars */

  const stepContent = step.content as EmailStepContent
  const { t } = useTranslation('sequence')
  const { handleFormValidation } = useSequenceStepValidation({ stepperMode })
  const lexicalRef = useRef<LexicalImperativeHandle>(null)
  const lexicalSubjectRef = useRef<LexicalImperativeHandle>(null)
  const [open, setOpen] = useState(false)
  const [isWriting, setIsWriting] = useState(false)
  const [attachments, setAttachments] = useState(stepContent?.attachments || [])
  const [isSignature, setIsSignature] = useState(stepContent?.addSignature)
  const [isInit, setIsInit] = useState(false)
  const [writingType, setWritingType] = useState<
    'attachments' | 'signature' | null
  >(null)
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  /* Queries */

  const { checkVariableError } = useVariableValidator({ organizationId })
  const { checkEmptyError } = useValidator()

  const toolbarOptionsBody = ['variable', 'signature', 'insertAttachmentBtn']

  /* Form */

  const {
    control,
    setValue,
    getValues,
    watch,
    handleSubmit,
    formState: { errors },
  } = useForm<EmailStepContent>({
    defaultValues: {
      body: stepContent?.body || LEXICAL_EMPTY_CONTENT,
      subject: stepContent?.subject || '',
      attachments: stepContent?.attachments || [],
      addSignature: stepContent?.addSignature || true,
    },
    values: {
      body: stepContent?.body || LEXICAL_EMPTY_CONTENT,
      subject: stepContent?.subject || '',
      attachments: stepContent?.attachments || [],
      addSignature: stepContent?.addSignature || true,
    },
  })

  const { updateSequenceStep } = useUpdateSequenceStep()
  const debounceSubject = useDebounce(watch('subject'), 500)
  const debounceBody = useDebounce(watch('body'), 500)

  // This is a watcher for the body field, without we have a warning :
  // "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked."
  const staticBodyWatcher = watch('body')
  const staticSubjectWatcher = watch('subject')

  /* Effects */

  useEffect(() => {
    setTimeout(() => {
      handleSubmit(() => {
        return
      })()
    }, 1)
  }, [handleSubmit])

  useEffect(() => {
    // Force to stop writing after 3 seconds
    // Sometimes the user is blocked by loader infinite, i can't reproduce it
    // This code ensure that the user can continue
    let interval: NodeJS.Timeout | null = null

    if (isWriting) {
      interval = setInterval(() => {
        setIsWriting(false)
      }, 3000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [setIsWriting, isWriting, staticBodyWatcher, staticSubjectWatcher])

  useEffect(() => {
    if (!stepContent) {
      return
    }
    lexicalRef.current?.setHasSignature(stepContent?.addSignature || false)
    lexicalRef.current?.setAttachments(stepContent?.attachments || [])
    lexicalSubjectRef.current?.setHtmlContent(
      stepContent.subject || LEXICAL_EMPTY_CONTENT
    )
    lexicalRef.current?.setHtmlContent(
      stepContent.body || LEXICAL_EMPTY_CONTENT
    )
    // Auto focus on the first empty field
    setTimeout(() => {
      if (
        stepContent.subject !== LEXICAL_EMPTY_CONTENT &&
        !isEmpty(stepContent.subject)
      ) {
        const editor = lexicalRef.current?.getEditor()
        editor?.focus()
      } else {
        const editor = lexicalSubjectRef.current?.getEditor()
        editor?.focus()
      }
    }, 10)
  }, [stepContent])

  useEffect(() => {
    const updateSubject = async () => {
      await updateSequenceStep({
        id: step.id,
        content: {
          ...getValues(),
          attachments,
          addSignature: isSignature,
        },
      })
      setIsWriting(false)
    }
    isInit && handleFormValidation(debounceSubject, updateSubject)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceSubject])

  useEffect(() => {
    const updateBody = async () => {
      await updateSequenceStep({
        id: step.id,
        content: {
          ...getValues(),
          attachments,
          addSignature: isSignature,
        },
      })
      setIsWriting(false)
    }

    isInit && handleFormValidation(debounceBody, updateBody)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceBody])

  useEffect(() => {
    setTimeout(() => {
      // Wait for the editor to be initialized with data, limit request api
      setIsInit(true)
    }, 1000)
  }, [setIsInit])

  /* Callbacks */

  const updateSignature = useCallback(
    async addSignature => {
      await updateSequenceStep({
        id: step.id,
        content: {
          ...getValues(),
          attachments,
          addSignature,
        },
      })
      setIsSignature(addSignature)
      setValue('addSignature', addSignature)
      setIsWriting(false)
      setWritingType(null)
    },
    [
      attachments,
      getValues,
      setIsWriting,
      setValue,
      step.id,
      updateSequenceStep,
    ]
  )

  const updateAttachments = useCallback(
    async fileAttachments => {
      await updateSequenceStep({
        id: step.id,
        content: {
          ...getValues(),
          addSignature: isSignature,
          attachments: fileAttachments,
        },
      })
      setAttachments(fileAttachments)
      setValue('attachments', fileAttachments)
      setIsWriting(false)
      setWritingType(null)
    },
    [
      getValues,
      isSignature,
      setIsWriting,
      setValue,
      step.id,
      updateSequenceStep,
    ]
  )

  return (
    <div
      className={clsx('flex h-full flex-col gap-2', {
        'pointer-events-none': writingType !== null,
      })}
    >
      <div className="flex items-center justify-between">
        <h6 className={'heading-xs'}>{t('Subject')}</h6>
        <div className={'flex gap-2 items-center'}>
          {isWriting && (
            <div className="flex items-center body-xs-regular text-textBrand gap-1">
              <Spinner />
              {t('Save loading...')}
            </div>
          )}

          <Typography size="xxs">
            {watch('subject').length}/{EMAIL_SUBJECT_MAX_LENGTH}
          </Typography>
        </div>
      </div>

      <Controller
        control={control}
        name="subject"
        rules={{
          required: t('You should add an email subject'),
          validate: value => checkVariableError(value),
        }}
        render={({ field: { onChange } }) => (
          <Lexical
            maxLength={EMAIL_SUBJECT_MAX_LENGTH}
            name={idReferentials.sequence.components.addSteps.email.subject}
            dataTestId={
              idReferentials.sequence.components.addSteps.email.subject
            }
            ref={lexicalSubjectRef}
            toolbarOptions={['singleLine', 'variable']}
            placeholder={t('Subject') as string}
            onChange={() => {
              const editor = lexicalSubjectRef?.current?.getEditor()
              const rootElement = editor?.getRootElement()
              const cleanText = rootElement?.innerText.replace(
                RegexClass.NEWLINES,
                ''
              )
              isInit && setIsWriting(true)
              onChange(cleanText)
            }}
            error={errors.subject?.message}
            variant="input"
            showCharacterCounter={false}
          />
        )}
      />

      <h6 className={'heading-xs'}>{t('Email content')}</h6>

      <div className="h-full overflow-y-hidden">
        <Controller
          control={control}
          name="body"
          rules={{
            validate: {
              variable: value => checkVariableError(value),
              empty: value =>
                checkEmptyError(value, t('You should write a message')),
            },
          }}
          render={({ field: { name, onChange } }) => (
            <Lexical
              dataTestId={
                idReferentials.sequence.components.addSteps.email.body
              }
              ref={lexicalRef}
              name={name}
              error={errors.body?.message}
              toolbarOptions={toolbarOptionsBody}
              floatingToolbarOptions={[
                'textAlign',
                'familyFont',
                'blockTypeFormat',
              ]}
              onChange={({ html, addSignature }: EditorOnChangeType) => {
                // Signature
                if (
                  !isUndefined(addSignature) &&
                  isUndefined(html) &&
                  (getValues('addSignature') !== addSignature ||
                    stepContent.addSignature !== addSignature)
                ) {
                  isInit && setIsWriting(true)
                  setWritingType('signature')
                  updateSignature(addSignature)
                  return
                }
                isInit && setIsWriting(true)

                if (html) {
                  onChange(html)
                }
              }}
              toolbarClassName="h-auto overflow-hidden"
              attachmentOptions={{
                maxTotalSize: toMo(EmailAttachment.MAX_TOTAL_SIZE),
                maxFiles: EmailAttachment.MAX_FILE,
              }}
              getAttachments={fileAttachments => {
                if (
                  stepContent?.attachments?.join(',') !==
                    fileAttachments.join(',') &&
                  isInit
                ) {
                  isInit && setIsWriting(true)
                  setWritingType('attachments')
                  updateAttachments(fileAttachments)
                }
              }}
            >
              <div className={'!mr-14 flex items-center'}>
                <Tooltip content={t('Templates', { ns: 'template' }) as string}>
                  <IconButton
                    icon={'Page'}
                    size={'small'}
                    dataTestId={
                      idReferentials.sequence.components.StepAddStepsSequence
                        .EmailAddStepsSequence.templateSelectionButton
                    }
                    variant={'tertiary-outlined'}
                    onClick={() => setOpen(true)}
                  />
                </Tooltip>
              </div>
            </Lexical>
          )}
        />
      </div>

      <Drawer
        classNamePanel="!max-w-7xl"
        open={open}
        onClose={() => {
          setOpen(false)
        }}
      >
        <DrawerTaskSelectTemplate
          setOpen={setOpen}
          setSubject={subject => {
            setValue('subject', subject)
          }}
          setTemplate={({ lexical, content, subject }) => {
            if (isEmpty(lexicalRef.current)) {
              return
            }
            if (!isNull(lexical) && !isEmpty(lexical)) {
              lexicalRef.current.setEditorState(lexical as string)
            } else {
              lexicalRef.current.setHtmlContent(content)
            }
            lexicalSubjectRef?.current?.setHtmlContent(subject as string)
          }}
          type={TemplateType.EMAIL}
        />
      </Drawer>
    </div>
  )
}
