import { useTranslation } from 'react-i18next'

import { LinkedInMessageForm } from '@internals/features/sequence/pages/SequenceFormPage/steps/AddStepsPage/components/SequenceStepsForms/components/LinkedInMessageForm'
import type { SequenceStepperMode } from '@internals/features/sequence/types/sequence'
import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'

type StepLinkedInInvitationFormProps = {
  step: SequenceStepApi
  stepperMode: SequenceStepperMode
}

const LINKEDIN_INVITATION_MESSAGE_MAX_LENGTH = 190

export const StepLinkedInInvitationForm = ({
  step,
  stepperMode,
}: StepLinkedInInvitationFormProps) => {
  const { t } = useTranslation('sequence')

  return (
    <LinkedInMessageForm
      placeholder={t(
        'As a free LinkedIn user, you can send up to 5 connection requests with personalized notes each month. Once you exceed this limit, your invitations will be sent without notes.'
      )}
      isMessageMandatory={false}
      step={step}
      maxLengthMessage={LINKEDIN_INVITATION_MESSAGE_MAX_LENGTH}
      stepperMode={stepperMode}
    />
  )
}
