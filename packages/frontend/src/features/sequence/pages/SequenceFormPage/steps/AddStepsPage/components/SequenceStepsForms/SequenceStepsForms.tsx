import { SequenceStepType } from '@getheroes/shared'
import { EmailAddStepsSequenceForm } from '@internals/features/sequence/pages/SequenceFormPage/steps/AddStepsPage/components/SequenceStepsForms/components/EmailAddStepsSequenceForm/EmailAddStepsSequenceForm'
import { StepLinkedInInvitationForm } from '@internals/features/sequence/pages/SequenceFormPage/steps/AddStepsPage/components/SequenceStepsForms/components/StepLinkedInInvitationForm/StepLinkedInInvitationForm'
import { StepLinkedInMessageForm } from '@internals/features/sequence/pages/SequenceFormPage/steps/AddStepsPage/components/SequenceStepsForms/components/StepLinkedInMessageForm/StepLinkedInMessageForm'
import { StepLinkedInVisitForm } from '@internals/features/sequence/pages/SequenceFormPage/steps/AddStepsPage/components/SequenceStepsForms/components/StepLinkedInVisitForm/StepLinkedInVisitForm'
import type { SequenceStepperMode } from '@internals/features/sequence/types/sequence'
import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'

type SequenceStepsFormsProps = {
  step: SequenceStepApi
  showError: boolean
  stepperMode: SequenceStepperMode
}

export const SequenceStepsForms = ({
  step,
  showError,
  stepperMode,
}: SequenceStepsFormsProps) => {
  const COMMON_PROPS = {
    step,
    showError,
    stepperMode,
  }

  switch (step.type) {
    case SequenceStepType.EMAIL:
      return <EmailAddStepsSequenceForm {...COMMON_PROPS} key={step.id} />

    case SequenceStepType.LINKEDIN_SEND_MESSAGE:
      return <StepLinkedInMessageForm {...COMMON_PROPS} key={step.id} />

    case SequenceStepType.LINKEDIN_SEND_INVITATION:
      return <StepLinkedInInvitationForm {...COMMON_PROPS} key={step.id} />

    case SequenceStepType.LINKEDIN_VISIT_PROFILE:
      return <StepLinkedInVisitForm {...COMMON_PROPS} key={step.id} />

    default:
      return null
  }
}
