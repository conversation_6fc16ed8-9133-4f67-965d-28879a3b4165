import { useEffect, useMemo, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import sanitizeHtml from 'sanitize-html'
import { useDebounce } from 'usehooks-ts'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { Typography } from '@getheroes/ui'
import type {
  EditorOnChangeType,
  LexicalImperativeHandle,
} from '@internals/components/common/dataEntry/Lexical/Editor'
import { Lexical } from '@internals/components/common/dataEntry/Lexical/Lexical'
import { useValidator } from '@internals/components/common/dataEntry/Lexical/hooks/useValidator'
import { useVariableValidator } from '@internals/components/common/dataEntry/Lexical/hooks/useVariableValidator'
import { LEXICAL_EMPTY_CONTENT } from '@internals/components/common/dataEntry/Lexical/utils/isEmptyLegacyContent'
import { Spinner } from '@internals/components/common/feedback/Spinner/Spinner'
import { useUpdateSequenceStep } from '@internals/features/sequence/hooks/useUpdateSequenceStep'
import { useSequenceStepValidation } from '@internals/features/sequence/pages/SequenceFormPage/steps/AddStepsPage/components/SequenceStepsForms/hooks/useSequenceStepValidation'
import type { SequenceStepperMode } from '@internals/features/sequence/types/sequence'
import type {
  LinkedInMessage,
  SequenceStepApi,
} from '@internals/features/sequence/types/sequenceStep'
import { useTypedSelector } from '@internals/store/store'
import { DEFAULT_ICON_SIZE } from '@internals/utils/design'
import { idReferentials } from '@internals/utils/idReferentials'

type LinkedInMessageFormProps = {
  step: SequenceStepApi
  maxLengthMessage: number
  isMessageMandatory: boolean
  placeholder?: string
  stepperMode: SequenceStepperMode
}
export const LinkedInMessageForm = ({
  step,
  maxLengthMessage,
  isMessageMandatory,
  placeholder = 'The message will be sent to friends only (1st relation)',
  stepperMode,
}: LinkedInMessageFormProps) => {
  /* Vars */

  const { t } = useTranslation('sequence')
  const lexicalRef = useRef<LexicalImperativeHandle>(null)
  const [isWriting, setIsWriting] = useState(false)
  const [isInit, setIsInit] = useState(false)
  const { id: organizationId } = useTypedSelector(selectCurrentUserOrganization)

  const stepContent = step.content as LinkedInMessage
  const stepMessage = stepContent.body

  /* Queries */

  const { checkVariableError } = useVariableValidator({ organizationId })
  const { checkEmptyError } = useValidator()
  const { updateSequenceStep } = useUpdateSequenceStep()
  const { handleFormValidation } = useSequenceStepValidation({ stepperMode })

  /* Form */

  const {
    control,
    getValues,
    watch,
    handleSubmit,
    formState: { errors },
  } = useForm<LinkedInMessage>({
    defaultValues: {
      body: stepMessage || LEXICAL_EMPTY_CONTENT,
    },
    values: {
      body: stepMessage || LEXICAL_EMPTY_CONTENT,
    },
  })

  const debounceMessage = useDebounce(watch('body'), 500)
  const bodyValue = watch('body')

  /* Effects */

  useEffect(() => {
    setTimeout(() => {
      handleSubmit(() => {
        return
      })()
    }, 1)
  }, [handleSubmit])

  useEffect(() => {
    setTimeout(() => {
      // Wait for the editor to be initialized with data, limit request api
      setIsInit(true)
    }, 1000)
  }, [setIsInit])

  useEffect(() => {
    // Force to stop writing after 3 seconds
    // Sometimes the user is blocked by loader infinite, i can't reproduce it
    // This code ensure that the user can continue
    let interval: NodeJS.Timeout | null = null

    if (isWriting) {
      interval = setInterval(() => {
        setIsWriting(false)
      }, 3000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [setIsWriting, isWriting, debounceMessage])

  useEffect(() => {
    const updateMessage = async () => {
      await updateSequenceStep({
        id: step.id,
        content: {
          ...getValues(),
        },
      })
      setIsWriting(false)
    }

    debounceMessage &&
      isInit &&
      handleFormValidation(debounceMessage, updateMessage)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceMessage])

  useEffect(() => {
    if (!stepContent) {
      return
    }
    lexicalRef.current?.setHtmlContent(
      stepContent.body || LEXICAL_EMPTY_CONTENT
    )
  }, [stepContent])

  /* Memos */

  const currentTextLength = useMemo(() => {
    // We should clean the text from html to get the real length
    if (bodyValue === LEXICAL_EMPTY_CONTENT) {
      return 0
    }
    return bodyValue
      ? sanitizeHtml(bodyValue, {
          allowedTags: [],
          allowedAttributes: {},
        }).length
      : 0
  }, [bodyValue])

  return (
    <div className="flex flex-col h-full gap-2">
      <div className="flex items-center justify-between">
        <h6 className={'heading-xs'}>{t('Message')}</h6>

        <div className={'flex gap-2 items-center'}>
          {isWriting && (
            <div className="flex items-center body-xs-regular text-textBrand gap-1">
              <Spinner size={DEFAULT_ICON_SIZE} className="h-4" />
              {t('Save loading...')}
            </div>
          )}

          <Typography size="xxs">
            {currentTextLength}/{maxLengthMessage}
          </Typography>
        </div>
      </div>

      <Controller
        control={control}
        name="body"
        rules={{
          validate: {
            variable: value => checkVariableError(value),
            empty: value =>
              isMessageMandatory
                ? checkEmptyError(value, t('You should write a message'))
                : true,
          },
        }}
        render={({ field: { name, onChange } }) => (
          <div className="flex items-center h-full">
            <Lexical
              maxLength={maxLengthMessage}
              name={name}
              dataTestId={
                idReferentials.sequence.components.addSteps.linkedInMessage
                  .message
              }
              ref={lexicalRef}
              toolbarOptions={['variable']}
              showFloatingToolbar={false}
              placeholder={t(placeholder) as string}
              isPlaceholderSingleLine={false}
              onChange={({ html }: EditorOnChangeType): void => {
                isInit && setIsWriting(true)
                onChange(html)
              }}
              error={errors.body?.message}
            />
          </div>
        )}
      />
    </div>
  )
}
