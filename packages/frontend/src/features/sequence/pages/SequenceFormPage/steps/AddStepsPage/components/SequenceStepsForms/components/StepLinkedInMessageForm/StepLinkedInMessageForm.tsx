import type { SequenceStepperMode } from '@internals/features/sequence/types/sequence'
import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'

import { LinkedInMessageForm } from '../LinkedInMessageForm'

type StepLinkedInMessageFormProps = {
  step: SequenceStepApi
  stepperMode: SequenceStepperMode
}

const LINKEDIN_MESSAGE_MAX_LENGTH = 8000

export const StepLinkedInMessageForm = ({
  step,
  stepperMode,
}: StepLinkedInMessageFormProps) => {
  return (
    <LinkedInMessageForm
      isMessageMandatory={true}
      step={step}
      maxLengthMessage={LINKEDIN_MESSAGE_MAX_LENGTH}
      stepperMode={stepperMode}
    />
  )
}
