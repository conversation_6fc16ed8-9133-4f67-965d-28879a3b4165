import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { useTracking, EnrichEvents } from '@getheroes/frontend/hooks'
import { Button, FloatingBar } from '@getheroes/ui'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'

type FloatingEnrichLeadsSequenceProps = {
  openDrawerEnrichLeads: (open: boolean) => void
  setIsOpenModalDeleteLeads: (open: boolean) => void
}
export const FloatingEnrichLeadsSequence = ({
  openDrawerEnrichLeads,
  setIsOpenModalDeleteLeads,
}: FloatingEnrichLeadsSequenceProps) => {
  /* Vars */

  const { t } = useTranslation('sequence')
  const { selections } = useGridContext()
  const count = useMemo(() => selections.length, [selections])
  const { sendEvent } = useTracking()

  /* Functions */

  const handleEnrichLeads = () => {
    openDrawerEnrichLeads(true)
    sendEvent(EnrichEvents.ENRICH_PAGE_FLOATING_BAR_CLICK_ENRICH_LEADS)
  }

  return (
    <FloatingBar title={t('Leads selected', { count })}>
      <div className="flex gap-2">
        <Button
          size={'small'}
          iconLeft={'UserPlus'}
          variant={'tertiary-outlined'}
          onClick={handleEnrichLeads}
        >
          {t('Enrich leads')}
        </Button>

        <Button
          size={'small'}
          iconLeft={'Trash'}
          variant={'danger'}
          onClick={() => {
            setIsOpenModalDeleteLeads(true)
          }}
        >
          {t('Remove from sequence', { count })}
        </Button>
      </div>
    </FloatingBar>
  )
}
