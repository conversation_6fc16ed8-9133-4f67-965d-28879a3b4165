import { useEffect, useState } from 'react'
import { Virtuoso } from 'react-virtuoso'

import { api } from '@getheroes/frontend/config/api'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import {
  removeAllSequenceContactErrors,
  removeOneSequenceContactError,
  selectAllSequenceContactErrors,
  setManySequenceContactErrors,
} from '@getheroes/frontend/config/store/slices/sequenceContactErrorsSlice'
import type { Organization } from '@getheroes/shared'
import { Spinner } from '@internals/components/common/feedback/Spinner/Spinner'
import { ErrorBoundary } from '@internals/components/technical/ErrorBoundary/ErrorBoundary'
import { useGetSequenceLeadsErrorsQuery } from '@internals/features/sequence/api/sequenceApi'
import { ModalDeleteLeads } from '@internals/features/sequence/components/ModalDeleteLeads/ModalDeleteLeads'
import type { SequenceContactErrorListProps } from '@internals/features/sequence/pages/SequenceFormPage/steps/ReviewPage/components/SequenceContactErrorList/SequenceContactErrorList-export'
import { ErrorPage } from '@internals/pages/ErrorPage/ErrorPage'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'

import { SequenceContactError } from '../SequenceContactError'

const FooterVirtuoso = ({
  context: { noMorePage = false },
}: {
  context: {
    noMorePage: boolean
  }
}) => {
  if (noMorePage) {
    return null
  }

  return (
    <div className="flex justify-center items-center">
      <Spinner />
    </div>
  )
}

const INITIAL_PAGE = 1
const LIMIT_PER_PAGE = 10
const NO_LEAD_TO_DELETE = ''
const LEAD_ERROR_LIST_OVERSCAN = 20

export const SequenceContactErrorList = ({
  sequenceId,
}: SequenceContactErrorListProps) => {
  /* Vars */

  const dispatch = useAppDispatch()
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const leadsErrors = useTypedSelector(selectAllSequenceContactErrors)

  const [page, setPage] = useState(INITIAL_PAGE)
  const [leadToDelete, setLeadToDelete] = useState(NO_LEAD_TO_DELETE)

  const { data, isError: isErrorGetSequenceLeadsErrors } =
    useGetSequenceLeadsErrorsQuery({
      sequenceId,
      organizationId,
      pagination: {
        page,
        limitPerPage: LIMIT_PER_PAGE,
      },
    })

  const { totalPages = 0 } = data?.meta || {}

  /* Effects */
  useEffect(() => {
    dispatch(setManySequenceContactErrors(data?.items || []))
  }, [data, dispatch])

  useEffect(() => {
    return () => {
      dispatch(removeAllSequenceContactErrors())
    }
  }, [dispatch])

  /* Functions */

  const handleUpdateLeadErrors = (contactId: string) => {
    dispatch(removeOneSequenceContactError(contactId))
    dispatch(
      api.util.invalidateTags([
        {
          type: 'Sequence',
          id: `SEQUENCE_${sequenceId}_LEADS_ERRORS_LIST`,
        },
      ])
    )
  }

  if (isErrorGetSequenceLeadsErrors) {
    return <ErrorPage />
  }

  return (
    <ErrorBoundary fallback={<ErrorPage />}>
      <Virtuoso
        data={leadsErrors}
        itemContent={(i, { contactId, errorsFields }) => (
          <div className="p-4">
            <SequenceContactError
              contactId={contactId}
              errorFieldIdList={errorsFields}
              sequenceId={sequenceId}
            />
          </div>
        )}
        endReached={() => {
          if (page < totalPages) {
            setPage(page + 1)
          }
        }}
        overscan={LEAD_ERROR_LIST_OVERSCAN}
        computeItemKey={(index, item) => item.contactId}
        components={{ Footer: FooterVirtuoso }}
        context={{ noMorePage: page >= totalPages }}
      />

      <ModalDeleteLeads
        open={Boolean(leadToDelete)}
        onSubmit={() => {
          handleUpdateLeadErrors(leadToDelete)
          setLeadToDelete(NO_LEAD_TO_DELETE)
        }}
        onClose={() => {
          setLeadToDelete(NO_LEAD_TO_DELETE)
        }}
        contactIds={[leadToDelete]}
        sequenceId={sequenceId}
      />
    </ErrorBoundary>
  )
}
