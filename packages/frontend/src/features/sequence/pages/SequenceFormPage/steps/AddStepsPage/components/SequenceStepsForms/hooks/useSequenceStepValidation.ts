import { useCallback } from 'react'
import { useTranslation } from 'react-i18next'

import { isEnableSequence } from '@getheroes/frontend/config/store/selectors/sequenceSelectors'
import { LEXICAL_EMPTY_CONTENT } from '@internals/components/common/dataEntry/Lexical/utils/isEmptyLegacyContent'
import { SequenceStepperMode } from '@internals/features/sequence/types/sequence'
import { useTypedSelector } from '@internals/store/store'
import { useToast } from '@getheroes/ui'

type UseSequenceStepValidationProps = {
  stepperMode: SequenceStepperMode
}

export const useSequenceStepValidation = ({
  stepperMode,
}: UseSequenceStepValidationProps) => {
  const { createToast } = useToast()
  const isEnable = useTypedSelector(isEnableSequence)
  const { t } = useTranslation('sequence')
  const handleFormValidation = useCallback(
    (value: string, updateApiFn: () => Promise<void>) => {
      switch (stepperMode) {
        case SequenceStepperMode.CREATE:
          updateApiFn()
          break
        case SequenceStepperMode.EDIT:
          if (isEnable && (value === LEXICAL_EMPTY_CONTENT || value === '')) {
            createToast({
              message: t(
                'Sequence is in progress. You are not allowed to update an empty value.'
              ),
              type: 'error',
            })
            return
          } else {
            updateApiFn()
          }
          break
        default:
          break
      }
    },
    [createToast, isEnable, stepperMode, t]
  )

  return {
    handleFormValidation,
  }
}
