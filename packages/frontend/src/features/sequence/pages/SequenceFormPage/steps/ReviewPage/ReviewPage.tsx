import { clsx } from 'clsx'
import isEmpty from 'lodash/isEmpty'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useTracking, SequenceEvents } from '@getheroes/frontend/hooks'
import { getDaysOfWeek, localizeTime } from '@getheroes/frontend/utils'
import type { Organization } from '@getheroes/shared'
import { Card, Icon } from '@getheroes/ui'
import { DoodledNetwork } from '@internals/assets/svg/icons/DoodledNetwork'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import {
  ButtonSize,
  ButtonVariant,
} from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { useGetSequenceLeadsErrorsQuery } from '@internals/features/sequence/api/sequenceApi'
import { useDeleteLeadsBySequence } from '@internals/features/sequence/hooks/useDeleteLeadsBySequence'
import { useGetLeadsBySequence } from '@internals/features/sequence/hooks/useGetLeadsBySequence'
import { useGetListStepsSequence } from '@internals/features/sequence/hooks/useGetListStepsSequence'
import { useGetSequence } from '@internals/features/sequence/hooks/useGetSequence'
import { Item } from '@internals/features/sequence/pages/SequenceFormPage/steps/ReviewPage/components/Item'
import { LoadingReviewStep } from '@internals/features/sequence/pages/SequenceFormPage/steps/ReviewPage/components/LoadingReviewStep'
import { SequenceContactErrorList } from '@internals/features/sequence/pages/SequenceFormPage/steps/ReviewPage/components/SequenceContactErrorList/SequenceContactErrorList'
import { LEAD_ERROR_LIST_CONTAINER } from '@internals/features/sequence/pages/SequenceFormPage/steps/ReviewPage/components/SequenceContactErrorList/SequenceContactErrorList-export'
import type { SequencePageStepType } from '@internals/features/sequence/types/sequence'
import { getSequenceRoute } from '@internals/features/sequence/util/getSequenceRoute'
import { useRoute } from '@internals/hooks/useRoute'
import { ErrorPage } from '@internals/pages/ErrorPage/ErrorPage'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'
import { joinStringAndSpecificLastCharacter } from '@internals/utils/string'

interface SequenceFormStepReviewPageProps {
  sequenceId: string
}

const weekdays = getDaysOfWeek()
const INITIAL_PAGE_SEQUENCE_ERRORS = 1
const LIMIT_PER_PAGE_SEQUENCE_ERRORS = 10
const NB_SEQUENCE_ERRORS_NOT_SET = null

export const SequenceFormStepReviewPage = ({
  sequenceId,
}: SequenceFormStepReviewPageProps) => {
  /* Vars */

  const { t } = useTranslation(['sequence', 'common'])
  const navigate = useNavigate()
  const { sendEvent } = useTracking()
  const [openDrawer, setOpenDrawer] = useState(false)
  const [nbSequenceErrors, setNbSequenceErrors] = useState<
    typeof NB_SEQUENCE_ERRORS_NOT_SET | number
  >(NB_SEQUENCE_ERRORS_NOT_SET)

  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const { privateRoutes } = useRoute()

  /* Queries */

  const {
    sequence,
    isLoading: isLoadingSequence,
    isError: isErrorSequence,
  } = useGetSequence({ sequenceId })

  const {
    leads,
    meta,
    isLoading: isLoadingLeads,
    isError: isErrorLeads,
  } = useGetLeadsBySequence({ sequenceId })

  const {
    steps,
    isError: isErrorSteps,
    isLoading: isLoadingSteps,
  } = useGetListStepsSequence({ sequenceId })

  const {
    data: dataSequenceLeadsErrors,
    isSuccess: isSuccessSequenceErrors,
    isError: isErrorSequenceErrors,
  } = useGetSequenceLeadsErrorsQuery(
    {
      sequenceId,
      organizationId,
      pagination: {
        page: INITIAL_PAGE_SEQUENCE_ERRORS,
        limitPerPage: LIMIT_PER_PAGE_SEQUENCE_ERRORS,
      },
    },
    {
      refetchOnMountOrArgChange: true,
    }
  )

  const { deleteLeads } = useDeleteLeadsBySequence({ sequenceId })

  /* Functions */

  useEffect(() => {
    if (isSuccessSequenceErrors) {
      const errors = dataSequenceLeadsErrors?.meta?.totalItems
      setNbSequenceErrors(errors ?? 0)
      if (errors === 0) {
        setOpenDrawer(false)
      }
    }
  }, [dataSequenceLeadsErrors?.meta?.totalItems, isSuccessSequenceErrors])

  /* useEffect(() => {
     if (nbSequenceErrors && nbSequenceErrors > 0) {
      shouldBlockByErrorsInSequence(true)
    }
    if (isBlockedByErrorsInSequence && nbSequenceErrors === 0) {
      shouldBlockByErrorsInSequence(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nbSequenceErrors]) */

  /* Functions */

  const navigateToStep = (stepKey: SequencePageStepType) => () => {
    // We force the stepId to be "leads" because it's a legacy code
    navigate(getSequenceRoute({ sequenceId, stepId: 'leads' }))
  }

  const handleReviewErrors = () => {
    setOpenDrawer(true)
    sendEvent(SequenceEvents.REVIEW_ERRORS_PAGE_CLICK_REVIEW_ERRORS)
  }

  const handleDeleteAllLeads = () => {
    deleteLeads([], true)
    sendEvent(SequenceEvents.REVIEW_ERRORS_DRAWER_CLICK_REMOVE_ALL)
  }

  const isError =
    isErrorLeads || isErrorSequence || isErrorSteps || isErrorSequenceErrors
  const isLoading =
    isLoadingLeads ||
    isLoadingSequence ||
    isLoadingSteps ||
    !leads ||
    !sequence ||
    !steps

  if (isError) {
    return <ErrorPage />
  }

  return (
    <Card fullHeight>
      <div className="bg-backgroundPrimary flex p-2 gap-8 w-full h-full flex-grow flex-col">
        <div className="flex items-center gap-4">
          <DoodledNetwork />
          <h1 className="heading-l font-bold">
            {t("You're ready to launch!")}
          </h1>
        </div>

        {!isLoading && (
          <Item
            tooltipText={t(
              'You will be redirected to the screen to modify settings'
            )}
            onClick={navigateToStep('settings')}
            className="gap-2"
          >
            <Icon name={'Clock'} />

            <div className="flex items-center justify-between gap-2 text-sm">
              <p className="text-left  w-5/6 body-s-regular">
                {t('Your sequence will be launch', {
                  start: localizeTime(sequence.scheduleTimeStart),
                  end: localizeTime(sequence.scheduleTimeEnd),
                  days: joinStringAndSpecificLastCharacter(
                    sequence.scheduleDays
                      .map(day => {
                        const weekday = weekdays.find(
                          weekday => weekday.raw.toLocaleLowerCase() === day
                        )
                        if (!weekday) {
                          return ''
                        }
                        return weekday.localized
                      })
                      .filter(day => !isEmpty(day)),
                    ',',
                    t('And', { ns: 'common' }).toLocaleLowerCase()
                  ),
                })}
              </p>

              <Icon name={'EditPencil'} />
            </div>
          </Item>
        )}

        <div className="flex gap-2">
          {isLoading || nbSequenceErrors === NB_SEQUENCE_ERRORS_NOT_SET ? (
            <LoadingReviewStep />
          ) : (
            <>
              <Item
                tooltipText={t(
                  'Please review all errors or delete lead with error before launching the sequence'
                )}
                className={clsx({
                  '!bg-backgroundError': nbSequenceErrors > 0,
                  '!bg-backgroundSuccess': nbSequenceErrors === 0,
                })}
              >
                <p
                  className={clsx({
                    'heading-l !font-bold text-textError': nbSequenceErrors > 0,
                    'heading-xs font-bold': nbSequenceErrors === 0,
                  })}
                >
                  {nbSequenceErrors > 0 ? nbSequenceErrors : t('Well done!')}
                </p>

                <div className="flex items-center justify-between gap-2 text-size04 font-medium">
                  <p
                    className={clsx('body-m-medium', {
                      'text-textSubtle': nbSequenceErrors === 0,
                    })}
                  >
                    {nbSequenceErrors > 0
                      ? t('Leads are in error', { count: nbSequenceErrors })
                      : t('There are no errors')}
                  </p>

                  {nbSequenceErrors > 0 && (
                    <Button
                      dataTestId={
                        idReferentials.sequence.components.stepReviewSequence
                          .reviewErrorsButton
                      }
                      variant={ButtonVariant.PRIMARY}
                      size={ButtonSize.SMALL}
                      onClick={handleReviewErrors}
                    >
                      {t('Review errors')}
                    </Button>
                  )}
                </div>
              </Item>

              <Item
                tooltipText={t(
                  'You will be redirected to the screen to add leads'
                )}
                onClick={navigateToStep('add-leads-assign')}
              >
                <p className="heading-l font-bold">
                  {meta?.totalItems ?? 'N/A'}
                </p>

                <div className="flex items-center gap-2 text-size04 font-medium">
                  <p>{t('Leads', { count: leads.length })}</p>

                  <Icon name={'EditPencil'} color={'decorative-brand'} />
                </div>
              </Item>

              <Item
                tooltipText={t(
                  'You will be redirected to the screen to modify steps'
                )}
                onClick={navigateToStep('add-steps')}
              >
                <p className="heading-l font-bold">{steps.length}</p>

                <div className="flex items-center gap-2 text-size04 font-medium">
                  <p>{t('Steps', { count: steps.length })}</p>

                  <Icon name={'EditPencil'} color={'decorative-brand'} />
                </div>
              </Item>
            </>
          )}
        </div>

        <Drawer
          classNamePanel="!w-drawer-m !max-w-none"
          open={openDrawer}
          onClose={() => {
            setOpenDrawer(false)
          }}
        >
          <div className="h-full flex flex-col" id={LEAD_ERROR_LIST_CONTAINER}>
            <div className="pt-8 pr-4 pl-8 mb-6 sticky top-0 flex justify-between gap-4 bg-white z-10">
              <div className="flex-1">
                <h1 className="heading-m">{t('Review Errors')}</h1>

                <div className="body-m-regular text-textSubtle">
                  {t(
                    'Review all errors that zeliq found. Any lead with errors, will not be included in your sequence'
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  dataTestId={
                    idReferentials.sequence.components.stepReviewSequence
                      .removeAllLeadsErrorButton
                  }
                  variant={ButtonVariant.DANGER}
                  size={ButtonSize.LARGE}
                  onClick={handleDeleteAllLeads}
                  className="shrink-0"
                >
                  {t('Remove all', { ns: 'sequence' })}
                </Button>

                <Button
                  dataTestId={
                    idReferentials.sequence.components.stepReviewSequence
                      .closeDrawerButton
                  }
                  variant={ButtonVariant.TERTIARY}
                  size={ButtonSize.LARGE}
                  onClick={() => {
                    setOpenDrawer(false)
                  }}
                  className="shrink-0"
                >
                  {t('Close', { ns: 'common' })}
                </Button>
              </div>
            </div>

            {nbSequenceErrors && (
              <div className="pr-4 pl-8 mb-6 heading-xs">
                {nbSequenceErrors}{' '}
                {t('Leads are in error', {
                  count: nbSequenceErrors,
                })}
              </div>
            )}

            <div className="pl-4 h-full">
              <SequenceContactErrorList sequenceId={sequenceId} />
            </div>
          </div>
        </Drawer>
      </div>
    </Card>
  )
}
