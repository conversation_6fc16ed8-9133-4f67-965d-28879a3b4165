import { useMemo } from 'react'

import { api } from '@getheroes/frontend/config/api'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { removeOneSequenceContactError } from '@getheroes/frontend/config/store/slices/sequenceContactErrorsSlice'
import { SequenceEvents, useTracking } from '@getheroes/frontend/hooks'
import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import { getEnrichmentTypeFromSelectedFields } from '@getheroes/frontend/utils'
import type { Contact, Organization } from '@getheroes/shared'
import { ContactTitle } from '@internals/components/business/lead/contact/ContactTitle/ContactTitle'
import { useGetContactQuery } from '@internals/features/lead/api/contactApi'
import { useEnrichPhoneOrEmail } from '@internals/features/lead/hooks/enrichment/useEnrichPhoneOrEmail'
import type { LeadsField } from '@internals/features/lead/types/leadsTableColumn'
import { ErrorVariables } from '@internals/features/sequence/components/SequenceErrors/components/ZeliqBusinessErrors/ErrorVariables'
import { useUpdateVariables } from '@internals/features/sequence/components/SequenceErrors/hooks/useUpdateVariables'
import { useGetContactFields } from '@internals/hooks/useGetContactFields'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'

export type ReviewSequenceContactErrorVariablesProps = {
  contactId: string
  errorFieldIdList: string[]
  sequenceId: string
}

export const SequenceContactError = ({
  contactId,
  errorFieldIdList,
  sequenceId,
}: ReviewSequenceContactErrorVariablesProps) => {
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const { sendEvent } = useTracking()
  const dispatch = useAppDispatch()

  const { data: contact } = useGetContactQuery({
    contactId,
    organizationId,
  })

  const { data: fields = [] } = useGetContactFields()
  const enrichPhoneOrEmail = useEnrichPhoneOrEmail()

  const errorsFields = useMemo(() => {
    return fields.filter((field: LeadsField) =>
      errorFieldIdList.includes(field.id)
    )
  }, [errorFieldIdList, fields])

  const [updateVariables, { isLoading: isLoadingUpdateVariables }] =
    useUpdateVariables()

  const handleSubmit = async (partialContact: Partial<Contact>) => {
    if (!contact) {
      return
    }

    await updateVariables(contact, partialContact)

    dispatch(removeOneSequenceContactError(contactId))
    dispatch(
      api.util.invalidateTags([
        {
          type: 'Sequence',
          id: `SEQUENCE_${sequenceId}_LEADS_ERRORS_LIST`,
        },
      ])
    )

    sendEvent(SequenceEvents.REVIEW_ERRORS_DRAWER_CLICK_SAVE)
  }

  if (!contact) {
    return null
  }

  return (
    <div className="p-2 border border-borderError rounded-lg">
      <ErrorVariables
        isLoading={isLoadingUpdateVariables}
        fieldList={errorsFields}
        onSubmit={handleSubmit}
        onEnrich={fieldId => {
          const fieldNameProxy =
            fieldId === ExclusiveContactLeadFieldEnum.PHONES ? 'phone' : 'email'
          return enrichPhoneOrEmail(
            [contact],
            getEnrichmentTypeFromSelectedFields({ [fieldNameProxy]: true })
          )
        }}
        contact={contact}
        title={
          <div className="pb-2">
            <ContactTitle contact={contact} />
          </div>
        }
      />
    </div>
  )
}
