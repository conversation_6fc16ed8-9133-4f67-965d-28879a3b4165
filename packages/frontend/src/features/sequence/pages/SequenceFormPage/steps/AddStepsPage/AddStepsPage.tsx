import { clsx } from 'clsx'
import isEmpty from 'lodash/isEmpty'
import isUndefined from 'lodash/isUndefined'
import { useContext, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { isEnableSequence } from '@getheroes/frontend/config/store/selectors/sequenceSelectors'
import { SequenceEvents } from '@getheroes/frontend/hooks'
import { getZeliqBlogAddress } from '@getheroes/frontend/utils'
import { SequenceStepType, ONE_DAY_IN_MINUTES } from '@getheroes/shared'
import {
  Card,
  Divider,
  Helper,
  IconButton,
  Spinner,
  Tag,
  Typography,
  useToast,
} from '@getheroes/ui'
import { SelectInput } from '@internals/components/common/dataEntry/Select/SelectInput'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import {
  ButtonSize,
  ButtonVariant,
} from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { useMail } from '@internals/features/gmail/hook/useMail'
import { LinkedinConnectModal } from '@internals/features/integration/components/LinkedIn/LinkedinConnectModal/LinkedinConnectModal'
import { useIntegration } from '@internals/features/integration/hooks/useIntegration'
import { useLinkedin } from '@internals/features/integration/hooks/useLinkedin'
import { DrawerSelectStepSequence } from '@internals/features/sequence/components/DrawerSelectStepSequence/DrawerSelectStepSequence'
import { StepCardSequence } from '@internals/features/sequence/components/StepCardSequence/StepCardSequence'
import { useGetListStepsSequence } from '@internals/features/sequence/hooks/useGetListStepsSequence'
import { useGetSequenceStepsConfig } from '@internals/features/sequence/hooks/useGetSequenceStepsConfig'
import { useUpdateSequenceStep } from '@internals/features/sequence/hooks/useUpdateSequenceStep'
import { SequenceFormPageStepContext } from '@internals/features/sequence/pages/SequenceFormPage/context/SequenceFormPageStepContext'
import { SequenceStepsForms } from '@internals/features/sequence/pages/SequenceFormPage/steps/AddStepsPage/components/SequenceStepsForms/SequenceStepsForms'
import { SequenceStepperMode } from '@internals/features/sequence/types/sequence'
import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'
import { checkIsInvalidSequenceStep } from '@internals/features/sequence/util/checkIsInvalidSequenceStep'
import { EmailConnectModal } from '@internals/features/settings/pages/EmailAccount/EmailConnectModal'
import type { FormInputValue } from '@internals/models/form'
import { EmptyCollectionPage } from '@internals/pages/EmptyCollectionPage/EmptyCollectionPage'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

const MAX_DAYS_BETWEEN_STEPS = 100

type StepAddSequenceStepsProps = {
  isEditSequence: boolean
  sequenceId: string
}

const EmptyAddStepsSequence = ({
  setOpen,
}: {
  setOpen: (open: boolean) => void
}) => {
  /* Vars */

  const isEnable = useTypedSelector(isEnableSequence)
  const { t } = useTranslation('sequence')

  const handleOpenPanel = () => {
    if (isEnable) {
      return
    }

    setOpen(true)
  }

  return (
    <Card fullHeight>
      <div className="flex flex-col gap-8 items-center justify-center h-full">
        <Typography variant="heading" size="l">
          {t('Start building your sequence')}
        </Typography>

        <div className="max-w-72">
          <Card onClick={handleOpenPanel} isCondense isHoverable>
            <div className="flex flex-col items-center gap-2">
              <Typography variant="heading" size="xs" weight="bold">
                {t('Build your sequence manually')}
              </Typography>

              <Typography variant="body" size="xs" color="base-subtle">
                {t('Start by choosing your sequence’s first step')}
              </Typography>
            </div>
          </Card>
        </div>
      </div>
    </Card>
  )
}

export const SequenceFormStepAddStepPage = ({
  isEditSequence,
  sequenceId,
}: StepAddSequenceStepsProps) => {
  /* Vars */

  const { t, i18n } = useTranslation(['sequence', 'mail'])
  const { createToast } = useToast()
  const { sendEvent } = useTrackingContext()
  const { SINGLE_SEQUENCE_STEP } = useGetSequenceStepsConfig()
  const isEnable = useTypedSelector(isEnableSequence)
  const { toggleCompleteStep } = useContext(SequenceFormPageStepContext) ?? {}

  /* States */

  const lastStepRef = useRef<HTMLDivElement | null>(null)
  const [isDrawerSelectStepOpen, setIsDrawerSelectStepOpen] = useState(false)
  const [isOpenLinkedinConnectModal, setIsOpenLinkedinConnectModal] =
    useState<boolean>(false)
  const [shouldFocusLastStep, setShouldFocusLastStep] = useState(true)
  const [stepsCount, setStepsCount] = useState(0)
  const [selectedStep, setSelectedStep] = useState<SequenceStepApi | undefined>(
    undefined
  )
  const [isCurrentStepNew, setIsCurrentStepNew] = useState(false)
  const [
    hasModalLinkedInAlreadyBeenShown,
    setHasModalLinkedInAlreadyBeenShown,
  ] = useState(false)
  const [showConnectMailModal, setShowConnectMailModal] = useState(false)
  const [hasModalEmailAlreadyBeenShown, setHasModalEmailAlreadyBeenShown] =
    useState(false)

  /* Custom hooks */
  const { steps, isLoading, isFetching } = useGetListStepsSequence({
    sequenceId,
  })
  // TODO use useLinkedIn instead of useIntegration ?
  const { isSuccess, isError: isConnectEmailError, message } = useMail()
  const { updateSequenceStep } = useUpdateSequenceStep()
  const {
    isAlreadyConnectedToLinkedin,
    isAlreadyConnectedToEmail,
    isFetching: isFetchingIntegration,
    isLoading: isLoadingIntegration,
  } = useIntegration()

  const {
    checkUserConnectedToLinkedin,
    isExtensionInstalled,
    checkExtensionIsInstalled,
    isLoading: isLoadingLinkedin,
    isErrorPatchLinkedinCookie,
    setIsErrorPatchLinkedinCookie,
    setIsLoading,
  } = useLinkedin()

  /* Memos */

  const emptySteps = useMemo(() => {
    if (!steps || isEmpty(steps)) {
      return []
    }
    return steps.filter(checkIsInvalidSequenceStep)
  }, [steps])

  const linkedInSteps = useMemo(() => {
    return steps?.filter(step => step.type !== SequenceStepType.EMAIL) ?? []
  }, [steps])

  const zeliqBlogAddress = useMemo(() => {
    return getZeliqBlogAddress(i18n.language)
  }, [i18n.language])

  /* Effects */

  useEffect(() => {
    if (steps && isEmpty(steps)) {
      setIsDrawerSelectStepOpen(true)
    }
  }, [steps])

  useEffect(() => {
    if (steps && isEmpty(steps) && !isLoading) {
      setSelectedStep(undefined)
      if (steps?.length !== stepsCount) {
        setStepsCount(steps.length)
      }
      return
    }
    if (steps && steps.length !== stepsCount) {
      setIsDrawerSelectStepOpen(false)
      setStepsCount(steps.length)
      if (shouldFocusLastStep && !isLoading && !isFetching) {
        setSelectedStep(steps.slice(-1)[0])
        setShouldFocusLastStep(false)
        lastStepRef.current?.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }, [
    isDrawerSelectStepOpen,
    isFetching,
    isLoading,
    shouldFocusLastStep,
    steps,
    stepsCount,
  ])

  useEffect(() => {
    // if there are steps and the user is not already connected to LinkedIn
    if (
      steps &&
      !isEmpty(steps) &&
      !isFetchingIntegration &&
      !isLoadingIntegration
    ) {
      if (
        !isEmpty(linkedInSteps) &&
        !isAlreadyConnectedToLinkedin &&
        !hasModalLinkedInAlreadyBeenShown
      ) {
        setIsOpenLinkedinConnectModal(true)
        setHasModalLinkedInAlreadyBeenShown(true)
      }
    }
  }, [
    isAlreadyConnectedToLinkedin,
    steps,
    isFetchingIntegration,
    isLoadingIntegration,
    linkedInSteps,
    hasModalLinkedInAlreadyBeenShown,
  ])

  useEffect(() => {
    if (!toggleCompleteStep) {
      return
    }

    toggleCompleteStep(
      'add-steps',
      isEmpty(steps) === false && emptySteps.length === 0
    )
    toggleCompleteStep(
      'settings',
      isEmpty(steps) === false && emptySteps.length === 0
    )
    toggleCompleteStep(
      'enrich',
      isEmpty(steps) === false && emptySteps.length === 0
    )
    toggleCompleteStep(
      'review',
      isEmpty(steps) === false && emptySteps.length === 0
    )
  }, [emptySteps, steps, toggleCompleteStep])

  /**
   * Display a success message, once the Oauth redirection has been succeeded or failed
   */
  useEffect(() => {
    const translatedMessage = t(message, {
      ns: 'mail',
    })

    if (isSuccess) {
      createToast({
        type: 'main',
        message: translatedMessage,
      })
    }

    if (isConnectEmailError) {
      createToast({
        type: 'error',
        message: translatedMessage,
      })
    }
  }, [createToast, isConnectEmailError, isSuccess, message])

  /* Functions */

  const handleAddStep = () => {
    setIsDrawerSelectStepOpen(true)
    sendEvent(SequenceEvents.SEQUENCE_ADD_STEP_PAGE_CLICK_ADD_STEP_BUTTON)
  }

  const handleCloseSelectStepSequence = (type?: SequenceStepType) => {
    if (
      type &&
      type !== SequenceStepType.EMAIL &&
      !isAlreadyConnectedToLinkedin &&
      !isEmpty(linkedInSteps) &&
      !hasModalLinkedInAlreadyBeenShown
    ) {
      setIsOpenLinkedinConnectModal(true)
      setHasModalLinkedInAlreadyBeenShown(true)
    }

    if (
      type === SequenceStepType.EMAIL &&
      !isAlreadyConnectedToEmail &&
      !hasModalEmailAlreadyBeenShown
    ) {
      setShowConnectMailModal(true)
      setHasModalEmailAlreadyBeenShown(true)
    }

    setIsDrawerSelectStepOpen(false)
    setIsCurrentStepNew(true)
    setShouldFocusLastStep(true)
  }

  return (
    <>
      {isLoading && (
        <div className="w-full h-full absolute flex items-center justify-center bg-white bg-opacity-70">
          <Spinner />
        </div>
      )}

      {isEmpty(steps) || isUndefined(steps) ? (
        <EmptyAddStepsSequence setOpen={setIsDrawerSelectStepOpen} />
      ) : (
        <div className="flex gap-2 h-full w-full overflow-hidden">
          {/***************/}
          {/* Left Panel */}
          {/***************/}
          <Card fullHeight>
            <div className="flex flex-col items-center justify-between w-full h-full overflow-auto gap-4">
              <div className="w-full flex flex-col items-center overflow-auto">
                <Tag
                  dataTestId="start-timeline"
                  label={t('Start')}
                  color={'grey'}
                />

                {steps?.map((step, index) => {
                  const isLastStep = index === steps.length - 1
                  const isFirstStep = index === 0
                  const previousStep = steps[index - 1]
                  const waitingBetweenStep = previousStep?.waitingBetweenStep
                    ? previousStep.waitingBetweenStep / ONE_DAY_IN_MINUTES
                    : ONE_DAY_IN_MINUTES

                  return (
                    <span
                      key={step.id}
                      className="flex flex-col w-full items-center"
                      ref={isLastStep ? lastStepRef : null}
                    >
                      <div
                        className={clsx(
                          'relative flex items-center w-full justify-center',
                          {
                            'h-10': isFirstStep,
                            'h-16': !isFirstStep,
                          }
                        )}
                      >
                        <div
                          className={clsx(
                            'absolute h-full w-px bg-backgroundPrimary'
                          )}
                        >
                          <div
                            className={clsx(
                              'w-px h-full bg-backgroundDecorativeBeige'
                            )}
                          />
                        </div>

                        {index > 0 && (
                          <div className="left-0 flex items-center">
                            <div className="mr-2 text-xs">{t('Wait for')}</div>
                            {/* @ts-expect-error-next-line */}
                            <SelectInput
                              id={`waiting-between-step-${previousStep.id}`}
                              dataTestId="select-waiting-between-step"
                              disabled={isEnable}
                              defaultValue={{
                                label: waitingBetweenStep.toString(),
                                value: waitingBetweenStep,
                              }}
                              className="flex !p-0 mr-2"
                              name={'test'}
                              onChange={(select: FormInputValue<number>) => {
                                updateSequenceStep({
                                  id: previousStep.id,
                                  waitingBetweenStep:
                                    Number(select?.target?.value) *
                                      ONE_DAY_IN_MINUTES || ONE_DAY_IN_MINUTES,
                                })
                              }}
                              options={[
                                ...Array(MAX_DAYS_BETWEEN_STEPS).keys(),
                              ].map((value: string | number) => {
                                const newValue = Number(value) + 1
                                return {
                                  label: newValue?.toString(),
                                  value: newValue,
                                }
                              })}
                            />

                            <div className="mr-2 text-xs">{t('days(s)')}</div>
                          </div>
                        )}
                      </div>

                      <StepCardSequence
                        stepId={step.id}
                        stepConfig={SINGLE_SEQUENCE_STEP[step.type]}
                        isRemovable
                        isDuplicable
                        order={index + 1}
                        content={step.content}
                        onClick={() => {
                          setSelectedStep(step)
                          setIsCurrentStepNew(false)
                          setShouldFocusLastStep(false)
                        }}
                        onDuplicateCallback={() => {
                          setSelectedStep(step)
                          setShouldFocusLastStep(true)
                          lastStepRef.current?.scrollIntoView({
                            behavior: 'smooth',
                          })
                        }}
                        isSelected={selectedStep?.id === step.id}
                        showError={Boolean(
                          emptySteps.find(emptyStep => emptyStep.id === step.id)
                        )}
                      />
                    </span>
                  )
                })}

                {!isEnable ? (
                  <div className="relative">
                    <div className={clsx('w-px bg-backgroundPrimary')}>
                      <div
                        className={clsx(
                          'w-px h-[90px] bg-backgroundDecorativeBeige'
                        )}
                      />
                    </div>

                    <div
                      className={
                        'flex items-center justify-center size-12 absolute top-6 -left-6'
                      }
                    >
                      <IconButton
                        icon={'PlusCircle'}
                        variant={'tertiary-outlined'}
                        size={'large'}
                        onClick={handleAddStep}
                        dataTestId={'add-step-button'}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="h-16">
                    <Divider orientation="vertical" />
                  </div>
                )}

                <Tag label={t('End of sequence')} color={'grey'} />
              </div>

              <Helper
                icon="QuestionMark"
                title={t('Feeling lost?')}
                description={t(
                  'Check out our guides on our Help Center to learn more about automation and sequences in Zeliq.'
                )}
                endComponent={
                  <Button
                    dataTestId={idReferentials.common.dataDisplay.helper.button}
                    variant={ButtonVariant.TERTIARY}
                    link={zeliqBlogAddress}
                    size={ButtonSize.SMALL}
                    className={'shrink-0'}
                  >
                    {t('Visit help center')}
                  </Button>
                }
              />
            </div>
          </Card>

          {/***************/}
          {/* Right Panel */}
          {/***************/}
          <Card fullHeight>
            {selectedStep &&
            steps.some(step => step.id === selectedStep.id) && (
              <EmptyCollectionPage label={t('Select a step at configure.')} />
            ) ? (
              <SequenceStepsForms
                stepperMode={
                  isEditSequence
                    ? SequenceStepperMode.EDIT
                    : SequenceStepperMode.CREATE
                }
                key={selectedStep.id}
                step={selectedStep}
                showError={!isCurrentStepNew}
              />
            ) : (
              <EmptyCollectionPage label={t('Select a step at configure.')} />
            )}
          </Card>
        </div>
      )}

      <DrawerSelectStepSequence
        isOpen={isDrawerSelectStepOpen}
        setIsOpen={setIsDrawerSelectStepOpen}
        handleClose={handleCloseSelectStepSequence}
      />

      {/*
          This modal need to be wrapped with the boolean
          because it's calling checkExtensionIsInstalled from the useLinkedIn hook
          and it's causing the opening of the Linkedin page in another tab
      */}
      {isOpenLinkedinConnectModal && (
        <LinkedinConnectModal
          isOpen={isOpenLinkedinConnectModal}
          onClose={() => setIsOpenLinkedinConnectModal(false)}
          isExtensionInstalled={isExtensionInstalled}
          isConnectedToLinkedin={isAlreadyConnectedToLinkedin}
          isLoading={isLoadingLinkedin}
          checkExtensionIsInstalled={checkExtensionIsInstalled}
          checkUserConnectedToLinkedin={checkUserConnectedToLinkedin}
          setIsLoading={setIsLoading}
          isErrorPatchLinkedinCookie={isErrorPatchLinkedinCookie}
          setIsErrorPatchLinkedinCookie={setIsErrorPatchLinkedinCookie}
        />
      )}

      <EmailConnectModal
        open={showConnectMailModal}
        onClose={() => setShowConnectMailModal(false)}
      />
    </>
  )
}
