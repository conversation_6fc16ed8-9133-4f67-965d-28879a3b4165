import React, { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { useTracking, SequenceEvents } from '@getheroes/frontend/hooks'
import type { IconName } from '@getheroes/ui'
import { Accordion, Card, Icon, Tooltip, Typography } from '@getheroes/ui'
import { Heart } from '@internals/assets/svg/icons/Heart'
import { SwitchButton } from '@internals/components/common/dataEntry/Switch/SwitchButton'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import { ButtonVariant } from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { DrawerScheduleSequenceSettings } from '@internals/features/sequence/components/DrawerScheduleSequenceSettings/DrawerScheduleSequenceSettings'
import { idReferentials } from '@internals/utils/idReferentials'

enum Settings {
  SCHEDULE = 'schedule',
  SETTINGS = 'settings',
}

export const SequenceFormStepSettingsPage = () => {
  /* Vars */

  const { t } = useTranslation(['sequence', 'common'])
  const [openSchedule, setOpenSchedule] = useState(false)
  const { sendEvent } = useTracking()

  /* Functions */

  const handleEditSchedule = useCallback(() => {
    setOpenSchedule(true)
    sendEvent(SequenceEvents.SEQUENCE_SETTINGS_PAGE_CLICK_EDIT_SCHEDULE)
  }, [sendEvent])

  /* Memos */

  const items = useMemo(() => {
    return [
      {
        id: Settings.SCHEDULE,
        icon: 'ChatBubble' as IconName,
        header: t('Schedule', { ns: 'common' }),
        content: (
          <div className="flex w-full bg-backgroundDecorativeBeige rounded-xl p-6 items-center">
            <div className="flex items-center w-[96px] mr-7 h-[96px]">
              <Heart />
            </div>
            <div className="flex flex-col justify-between">
              <div className="heading-s">
                {t('Everything’s already setup for you!', { ns: 'common' })}
              </div>
              <div className="text-textSubtle body-s-regular">
                {t(
                  'Zeliq’s accurate data and smart insights automatically schedule your sequence so that you get the best results. If you need something very specific, you can open and update the advanced settings.'
                )}
              </div>

              <div className={'min-w-[128px] shrink-0 mt-4'}>
                <Button
                  dataTestId={
                    idReferentials.sequence.components.StepSequenceSetting
                      .openAdvancedSettingsButton
                  }
                  variant={ButtonVariant.LINK}
                  onClick={handleEditSchedule}
                >
                  {t('Edit schedule', { ns: 'common' })}
                </Button>
              </div>
            </div>
          </div>
        ),
      },
      {
        id: Settings.SETTINGS,
        icon: 'Network' as IconName,
        header: t('Settings', { ns: 'common' }),
        content: (
          <div className="flex flex-col gap-8 p-6 bg-backgroundSecondary rounded-base">
            <Tooltip
              content={t('These settings can not be edited for the moment')}
              placement="left-start"
            >
              <div className="flex flex-col gap-4">
                <div className="heading-xs !font-bold">
                  {t('Stop the campaign for a lead when')}
                </div>

                <SwitchButton
                  name={
                    idReferentials.sequence.components.StepSequenceSetting
                      .leadRepliedSwitchButton
                  }
                  dataTestId={
                    idReferentials.sequence.components.StepSequenceSetting
                      .leadRepliedSwitchButton
                  }
                  labelPosition="end"
                  onChange={() => void 0}
                  isChecked={true}
                  label={t('The lead replied to an email')}
                  classNameLabel="body-s-medium !ml-2"
                />
                <SwitchButton
                  name={
                    idReferentials.sequence.components.StepSequenceSetting
                      .leadUnsubscridedSwitchButton
                  }
                  dataTestId={
                    idReferentials.sequence.components.StepSequenceSetting
                      .leadUnsubscridedSwitchButton
                  }
                  labelPosition="end"
                  onChange={() => void 0}
                  isChecked={true}
                  label={t('The lead unsubscribed to a sequence')}
                  classNameLabel="body-s-medium !ml-2"
                />

                <SwitchButton
                  name={
                    idReferentials.sequence.components.StepSequenceSetting
                      .leadBouncedSwitchButton
                  }
                  dataTestId={
                    idReferentials.sequence.components.StepSequenceSetting
                      .leadBouncedSwitchButton
                  }
                  labelPosition="end"
                  onChange={() => void 0}
                  isChecked={true}
                  label={t('The leads email adresse bounced')}
                  classNameLabel="body-s-medium !ml-2"
                />
              </div>
            </Tooltip>
          </div>
        ),
      },
    ]
  }, [handleEditSchedule, t])

  return (
    <Card fullHeight>
      <Accordion
        isMultiple
        dataTestId={
          idReferentials.sequence.components.StepSequenceSetting
            .settingsAccordion
        }
      >
        {items.map(({ id, content, header, icon }) => (
          <Accordion.Item key={id} id={id}>
            <Accordion.Header>
              <div className={'flex items-center gap-2'}>
                <Icon name={icon} color={'decorative-blue'} />
                <p>
                  <Typography variant={'label'} size={'m'}>
                    {header}
                  </Typography>
                </p>
              </div>
            </Accordion.Header>
            <Accordion.Body>{content}</Accordion.Body>
          </Accordion.Item>
        ))}
      </Accordion>

      <Drawer
        classNamePanel="!max-w-[95rem] c-sequence-form-drawer"
        open={openSchedule}
        onClose={() => {
          setOpenSchedule(false)
        }}
      >
        {openSchedule && (
          <DrawerScheduleSequenceSettings
            onClose={() => {
              setOpenSchedule(false)
            }}
          />
        )}
      </Drawer>
    </Card>
  )
}
