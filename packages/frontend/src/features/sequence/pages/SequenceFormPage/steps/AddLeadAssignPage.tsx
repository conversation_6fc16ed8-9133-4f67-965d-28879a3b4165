import { clsx } from 'clsx'
import isEmpty from 'lodash/isEmpty'
import type { FC } from 'react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

import { api } from '@getheroes/frontend/config/api'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useTracking } from '@getheroes/frontend/hooks'
import type { Contact, Organization } from '@getheroes/shared'
import {
  getEnrichmentCost,
  LeadCategory,
  EnrichmentType,
} from '@getheroes/shared'
import { Button, Card, Icon, IconButton, Spinner } from '@getheroes/ui'
import { EnrichLeadsModal } from '@getheroes/ui-business'
import { GridProvider } from '@internals/components/common/dataDisplay/Grid/GridProvider'
import { Paper } from '@internals/components/common/dataDisplay/Paper/Paper'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { DropdownMenu } from '@internals/components/common/navigation/DropdownMenu/DropdownMenu'
import type { MenuItem } from '@internals/components/common/navigation/DropdownMenu/DropdownMenu-export'
import { LeadSidePanel } from '@internals/features/lead/components/LeadSidePanel/LeadSidePanel'
import { useEnrichPhoneOrEmail } from '@internals/features/lead/hooks/enrichment/useEnrichPhoneOrEmail'
import { useFetchLeadQuery } from '@internals/features/lead/hooks/fetchLead/useFetchLeadQuery'
import { LeadPageContext } from '@internals/features/lead/types/genericLeadType'
import { SelectLeadsSequenceStepper } from '@internals/features/sequence/components/SelectLeadsSequence/SelectLeadsSequenceStepper'
import { SelectLeadsSequenceProcessProvider } from '@internals/features/sequence/components/SelectLeadsSequence/hooks/useSelectLeadsSequenceStepper'
import { TableShowAddedLeads } from '@internals/features/sequence/components/TableShowAddedLeads/TableShowAddedLeads'
import { useGetLeadsBySequence } from '@internals/features/sequence/hooks/useGetLeadsBySequence'
import { useCurrentPlan } from '@internals/features/subscription/hooks/useCurrentPlan'
import { useCurrentUser } from '@internals/hooks/useCurrentUser'
import { privateRoutes } from '@internals/hooks/useRoute'
import { EmptyCollectionPage } from '@internals/pages/EmptyCollectionPage/EmptyCollectionPage'
import { ErrorPage } from '@internals/pages/ErrorPage/ErrorPage'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

interface SequenceFormStepAddLeadAssignPageProps {
  sequenceId: string
}

const DropdownButton = () => {
  const { t } = useTranslation('sequence')

  return (
    <Button
      dataTestId={
        idReferentials.sequence.pages.SequenceFormPage.addMoreLeadsButton
      }
      iconLeft={'Plus'}
      variant={'primary'}
    >
      {t('Add more leads')}
    </Button>
  )
}

const SELECTED_CONTACT_ID_EMPTY = ''

export const SequenceFormStepAddLeadAssignPage: FC<
  SequenceFormStepAddLeadAssignPageProps
> = ({ sequenceId }) => {
  /* #region Vars */

  const dispatch = useAppDispatch()
  const { t } = useTranslation('sequence')
  const navigate = useNavigate()
  const { creditBalance, plan } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const phoneCreditValue = getEnrichmentCost(EnrichmentType.PHONE, plan)
  const emailCreditValue = getEnrichmentCost(EnrichmentType.EMAIL, plan)

  const [isSelectLeadsDrawerOpen, setIsSelectLeadsDrawerOpen] = useState(false)
  const [isOpenEnrichLeadModal, setIsOpenEnrichLeadModal] = useState(false)
  const [typeDrawer, setTypeDrawer] = useState<LeadPageContext>(
    LeadPageContext.ALL_LEADS
  )
  const [selectedContactId, setSelectedContactId] = useState(
    SELECTED_CONTACT_ID_EMPTY
  )
  const [contactIdsAdded, setContactIdsAdded] = useState<string[]>([])

  /* #region Hooks */

  const { leads, meta, isLoading, isError, isUninitialized, isFetching } =
    useGetLeadsBySequence({
      sequenceId,
    })
  const { isCurrentUserAdminOrManager } = useCurrentUser()
  const { isEssentialAdvancedEnterprisePlan } = useCurrentPlan()
  const { sendEvent } = useTracking()
  const enrichPhoneOrEmail = useEnrichPhoneOrEmail()

  /* #region Queries */

  const [fetchLead, { isSuccess: isSuccessFetchContact }] = useFetchLeadQuery({
    leadCategory: LeadCategory.CONTACT,
  })

  /* #region Effects */

  useEffect(() => {
    if (!selectedContactId) {
      return
    }

    fetchLead({ leadId: selectedContactId })
  }, [fetchLead, selectedContactId])

  /* #region Memos */

  const dropdownItems: MenuItem[] = useMemo(() => {
    const items: MenuItem[] = [
      {
        key: LeadPageContext.MY_LEADS,
        label: t('Add from My Leads'),
        action: () => {
          setTypeDrawer(LeadPageContext.MY_LEADS)
          setIsSelectLeadsDrawerOpen(true)
        },
        startIcon: <Icon name={'Community'} />,
      },
    ]

    if (isEssentialAdvancedEnterprisePlan && isCurrentUserAdminOrManager) {
      items.push({
        key: LeadPageContext.ALL_LEADS,
        label: t('Add from All Leads'),
        action: () => {
          setTypeDrawer(LeadPageContext.ALL_LEADS)
          setIsSelectLeadsDrawerOpen(true)
        },
        startIcon: <Icon name={'Community'} />,
      })
    }

    return items
  }, [isCurrentUserAdminOrManager, isEssentialAdvancedEnterprisePlan, t])

  /* #region Functions */

  const addLeads = (mode: LeadPageContext) => () => {
    setTypeDrawer(mode)
    setIsSelectLeadsDrawerOpen(true)
    sendEvent(
      `SequenceAddLeads_click_${mode === LeadPageContext.ALL_LEADS ? 'AllLeads' : 'MyLeads'}`
    )
  }

  const handleClickRefresh = () => {
    dispatch(
      api.util.invalidateTags([
        {
          type: 'Sequence',
          id: `SEQUENCE_${sequenceId}_LEADS_LIST`,
        },
        {
          type: 'Sequence',
          id: `SEQUENCE_CONTACT_STEP_LIST`,
        },
      ])
    )
  }

  const handleAddLeadsSuccess = (_contactIdsAdded: string[]) => {
    setIsOpenEnrichLeadModal(_contactIdsAdded.length > 0)
    setContactIdsAdded(_contactIdsAdded)
  }

  const handleClickEnrich = useCallback(
    (enrichmentType: EnrichmentType | undefined) =>
      enrichPhoneOrEmail(
        contactIdsAdded.map(id => ({ id })) as unknown as Contact[],
        enrichmentType
      ),
    [contactIdsAdded, enrichPhoneOrEmail]
  )

  /* #region Renders */

  if (isError) {
    return <ErrorPage />
  }

  if (isLoading || isUninitialized) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Spinner />
      </div>
    )
  }

  return (
    <>
      <div className="w-full h-full flex gap-2 background-decorative-red overflow-y-hidden">
        <Card fullHeight fullWidth>
          {isEmpty(leads) ? (
            <EmptyCollectionPage
              label={t('First, you need to add leads to your sequence')}
              illustration={false}
            >
              <div className="flex gap-4 mt-4">
                <Paper onClick={addLeads(LeadPageContext.ALL_LEADS)}>
                  <div className="flex items-center gap-2 w-80">
                    <div
                      className={clsx(
                        'flex w-12 h-12 justify-center items-center rounded-m mr-4 bg-backgroundDecorativeBrand text-textBrand'
                      )}
                    >
                      <Icon name={'Community'} />
                    </div>

                    <div className={clsx('mr-4 flex flex-col-reverse')}>
                      <div className="heading-xs font-bold">
                        {t('All Leads')}
                      </div>

                      <div className="body-xs-regular text-textSubtle">
                        {t('Select from')}
                      </div>
                    </div>
                  </div>
                </Paper>

                <Paper onClick={addLeads(LeadPageContext.MY_LEADS)}>
                  <div className="flex items-center gap-2 w-80">
                    <div
                      className={clsx(
                        'flex w-12 h-12 justify-center items-center rounded-m mr-4 bg-backgroundDecorativeBrand text-textBrand'
                      )}
                    >
                      <Icon name={'Group'} />
                    </div>

                    <div className={clsx('mr-4 flex flex-col-reverse')}>
                      <div className="heading-xs font-bold">
                        {t('My Leads')}
                      </div>

                      <div className="body-xs-regular text-textSubtle">
                        {t('Select from')}
                      </div>
                    </div>
                  </div>
                </Paper>
              </div>
            </EmptyCollectionPage>
          ) : (
            <div className="flex flex-col w-full h-full gap-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <p className="heading-s">
                    {t('Leads', { count: meta?.totalItems ?? 0 })}
                  </p>

                  <IconButton
                    size="small"
                    icon="Refresh"
                    onClick={handleClickRefresh}
                  />
                </div>

                <DropdownMenu
                  dataTestId={
                    idReferentials.sequence.components.StepAddLeadsScreen
                      .addMoreLeadsButton
                  }
                  openedButton={<DropdownButton />}
                  closedButton={<DropdownButton />}
                  items={dropdownItems}
                />
              </div>

              <TableShowAddedLeads
                onClickContact={setSelectedContactId}
                leads={leads}
                sequenceId={sequenceId}
                paginationMeta={meta}
                isLoading={isLoading || isFetching}
                context="SequenceV1SelectLeads"
              />
            </div>
          )}
        </Card>

        <LeadSidePanel
          leadId={selectedContactId}
          leadCategory={LeadCategory.CONTACT}
          isOpen={Boolean(selectedContactId && isSuccessFetchContact)}
          onClose={() => setSelectedContactId(SELECTED_CONTACT_ID_EMPTY)}
        />
      </div>

      <Drawer
        classNamePanel="!max-w-[95rem] c-sequence-form-drawer"
        open={isSelectLeadsDrawerOpen}
        onClose={() => setIsSelectLeadsDrawerOpen(false)}
      >
        {isSelectLeadsDrawerOpen && (
          <SelectLeadsSequenceProcessProvider>
            <GridProvider>
              <SelectLeadsSequenceStepper
                typeDrawer={typeDrawer}
                onClose={() => setIsSelectLeadsDrawerOpen(false)}
                onAddLeadsSuccess={handleAddLeadsSuccess}
                leadCategory={LeadCategory.CONTACT}
              />
            </GridProvider>
          </SelectLeadsSequenceProcessProvider>
        )}
      </Drawer>

      {/*  Enrichment Modal */}
      <EnrichLeadsModal
        isOpen={isOpenEnrichLeadModal}
        onClickEnrich={handleClickEnrich}
        onClose={() => setIsOpenEnrichLeadModal(false)}
        numberOfLeads={contactIdsAdded.length}
        credits={creditBalance}
        costEmailCredits={emailCreditValue}
        costPhoneCredits={phoneCreditValue}
        onClickBuyCredits={() => navigate(privateRoutes.creditsAndPlans.path)}
      />
    </>
  )
}
