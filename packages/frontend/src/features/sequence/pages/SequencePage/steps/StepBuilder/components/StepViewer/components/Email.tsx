import { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import type { FieldValues, UseFormReturn } from 'react-hook-form'
import { Controller, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { SequenceEvents } from '@getheroes/frontend/hooks'
import {
  EmailAttachment,
  LocaleEnum,
  SequenceStepType,
  toMo,
} from '@getheroes/shared'
import { Button, CardV2, IconButton, Select, Tooltip } from '@getheroes/ui'
import { ConfirmationModal } from '@getheroes/ui-business'
import { AutoSave } from '@internals/components/common/dataEntry/AutoSave/AutoSave'
import { Lexical } from '@internals/components/common/dataEntry/Lexical/Lexical'
import { useValidator } from '@internals/components/common/dataEntry/Lexical/hooks/useValidator'
import { useVariableValidator } from '@internals/components/common/dataEntry/Lexical/hooks/useVariableValidator'
import type {
  EditorOnChangeType,
  LexicalImperativeHandle,
} from '@internals/components/common/dataEntry/Lexical/lexical.type'
import type { AttachmentModel } from '@internals/components/common/dataEntry/Lexical/ui/AttachmentBar'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { useUpdateSequenceStep } from '@internals/features/sequence/hooks/useUpdateSequenceStep'
import { useFormConfirmationModal } from '@internals/features/sequence/pages/SequencePage/steps/StepBuilder/components/StepViewer/hooks/useFormConfirmationModal'
import type {
  EmailStepContent,
  SequenceStepApi,
} from '@internals/features/sequence/types/sequenceStep'
import { DrawerTaskSelectTemplate } from '@internals/features/task/components/PanelRight/DrawerTaskSelectTemplate/DrawerTaskSelectTemplate'
import { TemplateType } from '@internals/models/template'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

interface FormValues {
  content: {
    subject: string
    body: string
    attachments: AttachmentModel[]
    addSignature: boolean
  }
  type: SequenceStepType
}

interface EmailProps {
  step: SequenceStepApi
  isSequenceLive: boolean
  isFirstEmailStep: boolean
}

const EMAIL_SUBJECT_MAX_LENGTH = 220

// Email component needs to be used with a "key" prop to force a remount
// when the step is changed
// This is because the Lexical editor doesn't update its content properly
export const Email = ({
  step,
  isSequenceLive,
  isFirstEmailStep,
}: EmailProps) => {
  /* Vars */

  const { t, i18n } = useTranslation('sequence')
  const organization = useTypedSelector(selectCurrentUserOrganization)
  const organizationId = organization?.id ?? ''
  const { sendEvent } = useTrackingContext()

  const lexicalSubjectRef = useRef<LexicalImperativeHandle>(null)
  const lexicalBodyRef = useRef<LexicalImperativeHandle>(null)

  const [isOpenTemplateDrawer, setIsOpenTemplateDrawer] = useState(false)

  const content = step.content as EmailStepContent

  const isFirstRender = useRef(true)

  const defaultValues = {
    content: {
      subject: content.subject,
      body: content.body,
      attachments: content?.attachments || [],
      addSignature: content?.addSignature || false,
    },
    type: step.type,
  }

  const methods = useForm<FormValues>({
    values: defaultValues,
    mode: 'onChange',
  })

  const {
    control,
    formState: { errors, isDirty, isValid },
    setValue,
  } = methods

  const { containerRef, modalProps } = useFormConfirmationModal({
    isDirty: isDirty && isSequenceLive,
    formMethods: methods,
    onAfterConfirm: () => {
      lexicalBodyRef?.current?.setHtmlContent(content.body)
      lexicalSubjectRef.current?.setHtmlContent(content.subject)
    },
    onAfterCancel: () => {
      // Needed to let lexical ref to be setted
      // Should no longer be needed after lexical refactoring
      setTimeout(() => {
        lexicalBodyRef.current?.focus()
      }, 100)
    },
  })

  /* Queries */

  const { checkVariableError } = useVariableValidator({ organizationId })
  const { checkEmptyError } = useValidator()
  const { updateSequenceStep } = useUpdateSequenceStep({
    showSuccessToast: false,
  })

  /* Effects */

  useEffect(() => {
    if (lexicalSubjectRef.current) {
      lexicalSubjectRef.current.setHtmlContent(content.subject)
    }
  }, [content.subject])

  useEffect(() => {
    // necessary to keep cursor position when editing the body
    if (!isFirstRender.current) {
      return
    }

    isFirstRender.current = false

    if (lexicalBodyRef.current) {
      lexicalBodyRef.current.setHtmlContent(content.body)
      lexicalBodyRef.current.setHasSignature(!!content.addSignature)
    }
  }, [content.body, content.addSignature])

  // Attachement updates needs to be isolated from the body updates
  // When there are attachments, content.attachments is always re-defined
  // If the body where to be updated at the same time, it will take the focus
  // Ex: Mail with attachments + edit subject => focus body
  useEffect(() => {
    if (lexicalBodyRef.current) {
      lexicalBodyRef.current.setAttachments(content.attachments || [])
    }
  }, [content.attachments])

  useLayoutEffect(() => {
    // Trigger the form validation when step is created,
    // to generate error on StepBuilder and disable the "Next" button
    methods.trigger()

    // step.id is mandatory to trigger the form validation when changing step
  }, [methods, step.id])

  /* Memos */

  const EMAIL_OPTIONS = useMemo(() => {
    return [
      { label: t('New'), value: SequenceStepType.EMAIL },
      {
        label: t('Reply in Thread'),
        value: SequenceStepType.REPLY_IN_THREAD,
        disabled: isFirstEmailStep,
      },
      { label: t('Manual'), value: SequenceStepType.MANUAL_EMAIL },
    ]
  }, [t, isFirstEmailStep])

  /* Functions */

  const onSubmit = (data: FormValues) => {
    updateSequenceStep({
      id: step.id,
      ...data,
    })
  }

  const handleClickSave = () => {
    sendEvent(SequenceEvents.SEQUENCE_V2_STEP_BUILDER_BUTTON_CLICK_SAVE_EDIT)
    methods.handleSubmit(onSubmit)()
  }

  const handleChangeBody =
    (callback: (value: string) => void) =>
    ({ html, addSignature }: EditorOnChangeType) => {
      if (addSignature !== undefined) {
        setValue('content.addSignature', addSignature, { shouldDirty: true })
        return
      }

      if (html) {
        callback(html)
      }
    }

  const handleChangeType = (type: SequenceStepType) => {
    sendEvent(
      SequenceEvents.SEQUENCE_V2_STEP_BUILDER_DROPDOWN_CLICK_EMAIL_TYPES,
      {
        type,
      }
    )

    updateSequenceStep({
      id: step.id,
      type,
    })
  }

  return (
    <div ref={containerRef} className={'h-full'}>
      <div className="h-full flex flex-col gap-2">
        {/* Email subject */}
        <div className="flex items-start gap-2">
          <Tooltip
            content={
              step.progression !== 'NOTSTARTED'
                ? t(`Step type can't be changed once the step has started.`)
                : undefined
            }
            placement="bottom"
          >
            <Select
              options={EMAIL_OPTIONS}
              value={step.type}
              size="medium"
              isStopPropagationPopoverContentMouseDown
              onChange={value => handleChangeType(value as SequenceStepType)}
              label={t('Subject')}
              disabled={step.progression !== 'NOTSTARTED'}
            />
          </Tooltip>

          <Controller
            control={control}
            name="content.subject"
            rules={{
              validate: {
                variable: value => checkVariableError(value),
                empty: value =>
                  step.type !== SequenceStepType.MANUAL_EMAIL &&
                  checkEmptyError(value, t('You should add a subject content')),
              },
              maxLength: {
                value: EMAIL_SUBJECT_MAX_LENGTH,
                message: t('{{field}} must not exceed {{number}} characters', {
                  field: 'Message',
                  number: EMAIL_SUBJECT_MAX_LENGTH,
                  ns: 'validation',
                }),
              },
            }}
            disabled={
              step.type === SequenceStepType.REPLY_IN_THREAD ||
              step.type === SequenceStepType.MANUAL_EMAIL
            }
            render={({ field: { name, onChange, disabled } }) => (
              <div className="mt-5 flex-grow">
                <Lexical
                  name={name}
                  ref={lexicalSubjectRef}
                  dataTestId={
                    idReferentials.sequence.components.addSteps.email.subject
                  }
                  toolbarOptions={['singleLine', 'variable']}
                  placeholder={t('Set the email subject')}
                  onChange={({ raw }: EditorOnChangeType) => onChange(raw)}
                  error={errors.content?.subject?.message}
                  editable={!disabled}
                  variant="input"
                />
              </div>
            )}
          />
        </div>

        {/* Email content */}

        <div className="h-full overflow-y-hidden">
          {step.type !== SequenceStepType.MANUAL_EMAIL && (
            <Controller
              control={control}
              name="content.body"
              rules={{
                validate: {
                  variable: value => checkVariableError(value),
                  empty: value =>
                    checkEmptyError(value, t('You must add an email content')),
                },
              }}
              render={({ field: { name, onChange, disabled } }) => (
                <Lexical
                  dataTestId={
                    idReferentials.sequence.components.addSteps.email.body
                  }
                  ref={lexicalBodyRef}
                  name={name}
                  error={errors.content?.body?.message}
                  toolbarOptions={[
                    'variable',
                    'signature',
                    'insertAttachmentBtn',
                    'clearFormatting',
                    'preview',
                  ]}
                  floatingToolbarOptions={[
                    'textAlign',
                    'familyFont',
                    'blockTypeFormat',
                  ]}
                  onChange={handleChangeBody(onChange)}
                  attachmentOptions={{
                    maxTotalSize: toMo(EmailAttachment.MAX_TOTAL_SIZE),
                    maxFiles: EmailAttachment.MAX_FILE,
                  }}
                  getAttachments={fileAttachments =>
                    setValue('content.attachments', fileAttachments, {
                      shouldDirty: true,
                    })
                  }
                  editable={!disabled}
                  label={t('Email content')}
                >
                  <Tooltip content={t('Templates')} placement="bottom">
                    <IconButton
                      dataTestId={
                        idReferentials.sequence.components.StepAddStepsSequence
                          .EmailAddStepsSequence.templateSelectionButton
                      }
                      variant="tertiary-outlined"
                      size="medium"
                      onClick={() => setIsOpenTemplateDrawer(true)}
                      icon="Page"
                    />
                  </Tooltip>
                </Lexical>
              )}
            />
          )}

          <CardV2
            backgroundImage={
              i18n.language === LocaleEnum.FRENCH
                ? 'https://zeliq-dev-public.s3.eu-west-3.amazonaws.com/Large_Illustration_Manual_Email_fr.png'
                : 'https://zeliq-dev-public.s3.eu-west-3.amazonaws.com/Step+Builder-+Manual+email-min.png'
            }
            isFullHeight
          />
        </div>

        {isSequenceLive ? (
          isDirty && (
            <Button
              variant="primary"
              onClick={handleClickSave}
              disabled={!isValid}
            >
              {t('Save')}
            </Button>
          )
        ) : (
          <AutoSave
            defaultValues={defaultValues}
            onSubmit={data => onSubmit(data as FormValues)}
            methods={methods as unknown as UseFormReturn<FieldValues>}
            options={{ forceSubmit: true }}
          />
        )}
      </div>

      <Drawer
        classNamePanel="!max-w-7xl"
        open={isOpenTemplateDrawer}
        onClose={() => setIsOpenTemplateDrawer(false)}
      >
        <DrawerTaskSelectTemplate
          setOpen={setIsOpenTemplateDrawer}
          setSubject={subject =>
            lexicalSubjectRef.current?.setHtmlContent(subject)
          }
          setTemplate={({ content }) => {
            lexicalBodyRef.current?.setHtmlContent(content)
          }}
          type={TemplateType.EMAIL}
        />
      </Drawer>

      <ConfirmationModal {...modalProps} />
    </div>
  )
}
