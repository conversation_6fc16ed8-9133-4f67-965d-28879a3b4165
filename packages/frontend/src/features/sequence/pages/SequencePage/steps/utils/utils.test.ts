import { describe, it, expect } from 'vitest'

import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'

import { sortStepsByOrder } from './utils'

describe('sortStepsByOrder', () => {
  it('should return empty Array if list is empty', () => {
    const steps: SequenceStepApi[] = []
    const sortedSteps = sortStepsByOrder(steps)

    expect(sortedSteps).toEqual([])
    expect(sortedSteps).not.toBe(steps)
  })

  it('Should sort steps by increasing order', () => {
    const steps = [
      { id: 'step-2', order: 2 },
      { id: 'step-3', order: 3 },
      { id: 'step-1', order: 1 },
    ] as SequenceStepApi[]

    const sortedSteps = sortStepsByOrder(steps)

    expect(sortedSteps).toEqual([
      { id: 'step-1', order: 1 },
      { id: 'step-2', order: 2 },
      { id: 'step-3', order: 3 },
    ])
  })
})
