import { useCallback } from 'react'
import { useNavigate } from 'react-router-dom'

import { useTracking } from '@getheroes/frontend/hooks'
import type { SequencePageStep } from '@internals/features/sequence/pages/SequencePage/sequence-page.type'
import { getSequenceRoute } from '@internals/features/sequence/util/getSequenceRoute'

export const useNavigateToStep = (sequenceId: string) => {
  const navigate = useNavigate()
  const { sendEvent } = useTracking()

  return useCallback(
    (newStepId: SequencePageStep) => {
      sendEvent('SequenceV2Header_click_Stepper', {
        context: 'SequenceV2SelectLeads',
        sequenceId,
        stepId: newStepId,
      })

      const searchParams = new URLSearchParams(location.search)

      navigate({
        pathname: getSequenceRoute({
          sequenceId: sequenceId,
          stepId: newStepId,
        }),
        search: searchParams.toString(),
      })
    },
    [navigate, sendEvent, sequenceId]
  )
}
