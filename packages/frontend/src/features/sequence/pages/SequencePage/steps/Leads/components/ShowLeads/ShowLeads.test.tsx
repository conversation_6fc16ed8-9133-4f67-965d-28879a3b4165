import { fireEvent, render, screen } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { ShowLeads } from './ShowLeads'

vi.mock('react-i18next', async () => ({
  ...(await vi.importActual('react-i18next')),
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}))

vi.mock('@internals/hooks/useCurrentUser', async () => ({
  useCurrentUser: () => ({
    isCurrentUserAdminOrManager: true,
  }),
}))

vi.mock('@internals/features/subscription/hooks/useCurrentPlan', async () => ({
  useCurrentPlan: () => ({
    isEssentialAdvancedEnterprisePlan: true,
  }),
}))

vi.mock(
  '@internals/features/sequence/components/TableShowAddedLeads/TableShowAddedLeads',
  async () => ({
    TableShowAddedLeads: ({
      onClickContact,
    }: {
      onClickContact: (id: string) => void
    }) => (
      <div data-testid="table-show-added-leads">
        <button onClick={() => onClickContact('contact-1')}>Contact 1</button>
      </div>
    ),
  })
)

vi.mock(
  '@internals/components/common/dataDisplay/Grid/GridProvider',
  async () => ({
    GridProvider: ({ children }: { children: never }) => (
      <div data-testid="grid-provider">{children}</div>
    ),
  })
)

vi.mock(
  '@internals/features/lead/components/LeadSidePanel/LeadSidePanel',
  async () => ({
    LeadSidePanel: ({ isOpen }: { isOpen: boolean }) => (
      <div data-testid="lead-side-panel">{isOpen ? 'Open' : 'Closed'}</div>
    ),
  })
)

describe('ShowLeads', () => {
  const onClickAddLeads = vi.fn()

  it('should render the grid', () => {
    render(
      <ShowLeads
        sequenceId="1"
        leads={[]}
        isFetching={false}
        onClickAddLeads={onClickAddLeads}
      />
    )
    expect(screen.getByTestId('grid-provider')).toBeInTheDocument()
  })

  it('should render the "Add leads" button', () => {
    render(
      <ShowLeads
        sequenceId="1"
        leads={[]}
        isFetching={false}
        onClickAddLeads={onClickAddLeads}
      />
    )
    expect(screen.getByText('Add leads')).toBeInTheDocument()
    fireEvent.click(screen.getByText('Add leads'))
    expect(screen.getByText('Add from My Leads')).toBeInTheDocument()
    expect(screen.getByText('Add from All Leads')).toBeInTheDocument()
  })

  it('should call onClickAddLeads select from My Leads', () => {
    render(
      <ShowLeads
        sequenceId="1"
        leads={[]}
        isFetching={false}
        onClickAddLeads={onClickAddLeads}
      />
    )

    fireEvent.click(screen.getByText('Add leads'))
    fireEvent.click(screen.getByText('Add from My Leads'))
    expect(onClickAddLeads).toHaveBeenCalledWith('my-leads')
  })

  it('should call onClickAddLeads select from All Leads', () => {
    render(
      <ShowLeads
        sequenceId="1"
        leads={[]}
        isFetching={false}
        onClickAddLeads={onClickAddLeads}
      />
    )

    fireEvent.click(screen.getByText('Add leads'))
    fireEvent.click(screen.getByText('Add from All Leads'))
    expect(onClickAddLeads).toHaveBeenCalledWith('all-leads')
  })

  it('should update selectedContactId state when a contact is clicked', () => {
    render(
      <ShowLeads
        sequenceId="1"
        leads={[]}
        isFetching={false}
        onClickAddLeads={onClickAddLeads}
      />
    )

    fireEvent.click(screen.getByText('Contact 1'))
    expect(screen.getByTestId('lead-side-panel')).toHaveTextContent('Open')
  })
})
