import { type KeyboardEvent } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { Input, Typography } from '@getheroes/ui'
import { useUpdateSequenceStep } from '@internals/features/sequence/hooks/useUpdateSequenceStep'
import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'
import { getStepNameFromStep } from '@internals/utils/sequence/getStepName'

export const StepItemFormName = ({
  isEditing,
  handleToogleEditingMode,
  step,
}: {
  isEditing: boolean
  handleToogleEditingMode: (isEditing: boolean) => void
  step: SequenceStepApi
}) => {
  /* #region Vars */

  const { t } = useTranslation('sequence')

  const stepName = getStepNameFromStep(step)

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<{ name?: string | null }>({
    values: {
      name: step.name,
    },
    mode: 'onBlur',
  })

  /* #region Queries */

  const { updateSequenceStep } = useUpdateSequenceStep()

  /* #region Functions */

  const onSubmit = (data: { name: string }) => {
    if (data.name !== step.name) {
      updateSequenceStep({
        id: step.id,
        name: data.name || null,
      })
    }

    handleToogleEditingMode(false)
  }

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSubmit(onSubmit)()
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {!isEditing && (
        <div role="button" onClick={() => handleToogleEditingMode(true)}>
          <Typography variant="label" size="s" weight="medium" isTruncate>
            {t(stepName)}
          </Typography>
        </div>
      )}

      {isEditing && (
        <Controller
          control={control}
          name="name"
          render={({ field }) => (
            <Input
              {...field}
              // step name is translated, so we need to display the translated value
              value={t(field.value)}
              autoFocus
              size="small"
              onBlur={() => handleSubmit(onSubmit)()}
              onKeyDown={handleKeyDown}
              error={errors.name?.message}
            />
          )}
        />
      )}
    </form>
  )
}
