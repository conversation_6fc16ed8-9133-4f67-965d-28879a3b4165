import { useEffect, useRef } from 'react'
import type { FieldValues, UseFormReturn } from 'react-hook-form'
import { Controller, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import sanitizeHtml from 'sanitize-html'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { SequenceEvents } from '@getheroes/frontend/hooks'
import { Button } from '@getheroes/ui'
import { ConfirmationModal } from '@getheroes/ui-business'
import { AutoSave } from '@internals/components/common/dataEntry/AutoSave/AutoSave'
import { Lexical } from '@internals/components/common/dataEntry/Lexical/Lexical'
import { useVariableValidator } from '@internals/components/common/dataEntry/Lexical/hooks/useVariableValidator'
import type {
  EditorOnChangeType,
  LexicalImperativeHandle,
} from '@internals/components/common/dataEntry/Lexical/lexical.type'
import { useUpdateSequenceStep } from '@internals/features/sequence/hooks/useUpdateSequenceStep'
import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'
import { useTypedSelector } from '@internals/store/store'

import { useFormConfirmationModal } from '../hooks/useFormConfirmationModal'

const MAX_LENGTH_MESSAGE = 190

interface FormValues {
  body: string
}

interface LinkedInRequestConnectionProps {
  step: SequenceStepApi
  isSequenceLive: boolean
}

// LinkedInRequestConnection component needs to be used with a "key" prop to force a remount
// when the step is changed
// This is because the Lexical editor doesn't update its content properly
export const LinkedInRequestConnection = ({
  step,
  isSequenceLive,
}: LinkedInRequestConnectionProps) => {
  /* Vars */
  const { t } = useTranslation('sequence')
  const organization = useTypedSelector(selectCurrentUserOrganization)
  const organizationId = organization?.id ?? ''

  const { sendEvent } = useTrackingContext()

  const lexicalRef = useRef<LexicalImperativeHandle>(null)

  const defaultValues = { body: step.content.body ?? '' }

  const methods = useForm<FormValues>({
    values: defaultValues,
  })

  const {
    control,
    formState: { errors, isValid, isDirty },
  } = methods

  const { containerRef, modalProps } = useFormConfirmationModal({
    isDirty: isDirty && isSequenceLive,
    formMethods: methods,
    onAfterConfirm: () => {
      lexicalRef?.current?.setHtmlContent(step.content?.body ?? '')
    },
    onAfterCancel: () => {
      // Needed to let lexical ref to be setted
      // Should no longer be needed after lexical refactoring
      setTimeout(() => {
        lexicalRef.current?.focus()
      }, 100)
    },
  })

  /* Queries */

  const { checkVariableError } = useVariableValidator({ organizationId })
  const { updateSequenceStep } = useUpdateSequenceStep({
    showSuccessToast: false,
  })

  /* Effects */

  useEffect(() => {
    if (lexicalRef.current) {
      lexicalRef.current.setHtmlContent(step.content?.body ?? '')
    }
  }, [step.content.body])

  /* Functions */

  const onSubmit = (data: FieldValues) => {
    updateSequenceStep({
      id: step.id,
      content: { body: (data as FormValues).body },
    })
  }

  const handleClickSave = () => {
    sendEvent(SequenceEvents.SEQUENCE_V2_STEP_BUILDER_BUTTON_CLICK_SAVE_EDIT)
    methods.handleSubmit(onSubmit)()
  }

  const handleChangeBody =
    (callback: (value: string) => void) =>
    ({ html }: EditorOnChangeType) => {
      if (html) {
        callback(html)
      }
    }

  const checkMaxLength = (value: string) => {
    const sanitizedValue = sanitizeHtml(value, { allowedTags: [] })

    if (sanitizedValue.length > MAX_LENGTH_MESSAGE) {
      return t('{{field}} must not exceed {{number}} characters', {
        field: 'Message',
        number: MAX_LENGTH_MESSAGE,
        ns: 'validation',
      })
    }
  }

  return (
    <div ref={containerRef} className={'h-full flex flex-col gap-2'}>
      <Controller
        control={control}
        name="body"
        rules={{
          validate: {
            variable: value => checkVariableError(value),
            maxLength: checkMaxLength,
          },
        }}
        render={({ field: { name, onChange } }) => (
          <div className="flex items-center h-full">
            <Lexical
              name={name}
              ref={lexicalRef}
              label={t('Connection Request content')}
              showCharacterCounter
              maxLength={MAX_LENGTH_MESSAGE}
              toolbarOptions={['variable', 'speechToText', 'aiAssistant']}
              showFloatingToolbar={false}
              placeholder={t(
                'As a free LinkedIn user, you can send up to 5 connection requests with personalized notes each month. Once this quota is exceeded, your invitations will be sent without notes.'
              )}
              isPlaceholderSingleLine={false}
              onChange={handleChangeBody(onChange)}
              error={errors.body?.message}
            />
          </div>
        )}
      />

      {isSequenceLive ? (
        isDirty && (
          <Button
            variant="primary"
            onClick={handleClickSave}
            disabled={!isValid}
          >
            {t('Save')}
          </Button>
        )
      ) : (
        <AutoSave
          defaultValues={defaultValues}
          onSubmit={methods.handleSubmit(onSubmit)}
          methods={methods as unknown as UseFormReturn<FieldValues>}
          options={{ forceSubmit: true }}
        />
      )}

      <ConfirmationModal {...modalProps} />
    </div>
  )
}
