import { useState } from 'react'

import { StepItemFormName } from '@internals/features/sequence/pages/SequencePage/steps/StepBuilder/components/StepItemFormName'
import { StepItemIcon } from '@internals/features/sequence/pages/SequencePage/steps/StepBuilder/components/StepItemIcon'
import { checkIfStepHasError } from '@internals/features/sequence/pages/SequencePage/utils/checkIfStepHasError'
import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'

import { StepItemActions } from './StepItemActions'

interface StepItemBodyProps {
  step: SequenceStepApi
  disabledActions?: boolean
}

export const StepItemBody = ({ step, disabledActions }: StepItemBodyProps) => {
  const [isEditing, setIsEditing] = useState(false)
  const hasError = checkIfStepHasError(step)

  return (
    <div className="flex justify-between items-center gap-4 w-ful">
      <div className="flex items-center gap-2 w-full relative overflow-y-auto">
        <StepItemIcon
          type={step.type}
          color={hasError ? 'base-error' : 'base-default'}
        />

        <div className="flex-grow">
          <StepItemFormName
            isEditing={isEditing}
            handleToogleEditingMode={setIsEditing}
            step={step}
          />
        </div>
      </div>

      {!isEditing && (
        <StepItemActions
          step={step}
          disabledActions={disabledActions}
          onEditClick={() => setIsEditing(true)}
        />
      )}
    </div>
  )
}
