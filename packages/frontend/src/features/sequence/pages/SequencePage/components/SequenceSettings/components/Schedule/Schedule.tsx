import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import {
  Button,
  CardV2,
  Checkbox,
  Helper,
  Icon,
  Input,
  Typography,
} from '@getheroes/ui'
import {
  ClickListener,
  ConfirmationModal,
  SelectTime,
  SelectTimezone,
} from '@getheroes/ui-business'
import { DatePicker } from '@internals/components/common/dataEntry/DatePicker/DatePicker'
import type { Sequence } from '@internals/features/sequence/types/sequence'
import { SequenceDays } from '@internals/features/sequence/types/sequence'
import { idReferentials } from '@internals/utils/idReferentials'

import { useScheduleSettings } from './hooks/useScheduleSettings'

interface ScheduleProps {
  sequence: Sequence
}

export const Schedule = ({ sequence }: ScheduleProps) => {
  /* Vars */
  const { t } = useTranslation(['sequence', 'common'])
  const {
    formMethods: { formState, control },
    onSubmit,
    isSequenceAlreadyInProgress,
    isLoadingUpdateSequence,
    isUnsavedChangesModalOpen,
    handleCheckboxChange,
    handleClickOutsideForm,
    handleUnsavedChangesConfirm,
    setIsUnsavedChangesModalOpen,
    handleScheduleTimeStartChange,
    handleScheduleTimeEndChange,
    handleStartingDateChange,
    handleStartingTimeChange,
  } = useScheduleSettings({ sequence })

  return (
    <ClickListener
      className="flex-grow flex flex-col min-h-0"
      onClickOutside={handleClickOutsideForm}
    >
      <form onSubmit={onSubmit} className="flex flex-col h-full">
        <div className="flex-grow overflow-y-auto min-h-0">
          <div className="flex flex-col gap-4">
            <CardV2 padding="p-4" isShadow={false}>
              <div className="flex flex-col gap-2">
                {isSequenceAlreadyInProgress && (
                  <Helper
                    color="orange-secondary"
                    isFullWidth
                    description={t(
                      'Starting date and time are disabled because the sequence is already launched.'
                    )}
                  />
                )}

                <div className="flex gap-2">
                  <Controller
                    name="startingDate"
                    control={control}
                    render={({ field: { onChange, ...field } }) => (
                      <DatePicker
                        {...field}
                        value={field.value}
                        minDate={new Date()}
                        disabled={isSequenceAlreadyInProgress}
                        onChange={handleStartingDateChange(onChange)}
                        id={
                          idReferentials.sequence.components.settingsDrawer
                            .nextExecutionDate.datePicker
                        }
                        dateFormat={'dd/MM/yyyy'}
                        dataTestId={
                          idReferentials.sequence.components.settingsDrawer
                            .nextExecutionDate.datePicker
                        }
                      >
                        <Input
                          type="text"
                          fullWidth
                          readOnly
                          placeholder="DD/MM/YYYY"
                          suffixContent={<Icon name="Calendar" />}
                          label={t('Starting Date', { ns: 'sequence' })}
                        />
                      </DatePicker>
                    )}
                  />
                  <Controller
                    name="startingTime"
                    control={control}
                    render={({ field: { onChange, ...field } }) => (
                      <SelectTime
                        {...field}
                        isStopPropagationPopoverContentMouseDown
                        fullWidth
                        onChange={handleStartingTimeChange(onChange)}
                        disabled={isSequenceAlreadyInProgress}
                        size="medium"
                        label={t('Starting Time', { ns: 'sequence' })}
                        inputProps={{ placeholder: t('Search time') }}
                      />
                    )}
                  />
                </div>

                <Controller
                  name="scheduleTimezone"
                  control={control}
                  disabled={isSequenceAlreadyInProgress}
                  render={({ field }) => (
                    <SelectTimezone
                      {...field}
                      isStopPropagationPopoverContentMouseDown
                      size="medium"
                      label={t('Time zone')}
                      inputProps={{ placeholder: t('Search time zone') }}
                    />
                  )}
                />
              </div>
            </CardV2>
            <CardV2 padding="p-4" isShadow={false}>
              <div className="flex flex-col gap-2">
                <div className="flex gap-2">
                  <Controller
                    name="scheduleTimeStart"
                    control={control}
                    render={({ field: { onChange, ...field } }) => (
                      <SelectTime
                        {...field}
                        isStopPropagationPopoverContentMouseDown
                        fullWidth
                        onChange={handleScheduleTimeStartChange(onChange)}
                        size="medium"
                        label={t('Between', { ns: 'common' })}
                        inputProps={{ placeholder: t('Search time') }}
                      />
                    )}
                  />
                  <Controller
                    name="scheduleTimeEnd"
                    control={control}
                    render={({ field: { onChange, ...field } }) => (
                      <SelectTime
                        {...field}
                        isStopPropagationPopoverContentMouseDown
                        fullWidth
                        onChange={handleScheduleTimeEndChange(onChange)}
                        size="medium"
                        label={t('And', { ns: 'common' })}
                        inputProps={{ placeholder: t('Search time') }}
                      />
                    )}
                  />
                </div>
                <div className="flex flex-col gap-1">
                  <Typography weight="medium" color="base-placeholder">
                    {t('Only allow steps on :')}
                  </Typography>

                  <Controller
                    name="scheduleDays"
                    control={control}
                    render={({ field }) => (
                      <div className="flex flex-col gap-2">
                        <Checkbox
                          {...field}
                          label={t('Monday')}
                          checked={field.value.includes(SequenceDays.MONDAY)}
                          onChange={checked =>
                            handleCheckboxChange(SequenceDays.MONDAY, checked)
                          }
                        />

                        <Checkbox
                          {...field}
                          label={t('Tuesday')}
                          checked={field.value.includes(SequenceDays.TUESDAY)}
                          onChange={checked =>
                            handleCheckboxChange(SequenceDays.TUESDAY, checked)
                          }
                        />

                        <Checkbox
                          {...field}
                          label={t('Wednesday')}
                          checked={field.value.includes(SequenceDays.WEDNESDAY)}
                          onChange={checked =>
                            handleCheckboxChange(
                              SequenceDays.WEDNESDAY,
                              checked
                            )
                          }
                        />

                        <Checkbox
                          {...field}
                          label={t('Thursday')}
                          checked={field.value.includes(SequenceDays.THURSDAY)}
                          onChange={checked =>
                            handleCheckboxChange(SequenceDays.THURSDAY, checked)
                          }
                        />

                        <Checkbox
                          {...field}
                          label={t('Friday')}
                          checked={field.value.includes(SequenceDays.FRIDAY)}
                          onChange={checked =>
                            handleCheckboxChange(SequenceDays.FRIDAY, checked)
                          }
                        />

                        <Checkbox
                          {...field}
                          label={t('Saturday')}
                          checked={field.value.includes(SequenceDays.SATURDAY)}
                          onChange={checked =>
                            handleCheckboxChange(SequenceDays.SATURDAY, checked)
                          }
                        />

                        <Checkbox
                          {...field}
                          label={t('Sunday')}
                          checked={field.value.includes(SequenceDays.SUNDAY)}
                          onChange={checked =>
                            handleCheckboxChange(SequenceDays.SUNDAY, checked)
                          }
                        />
                      </div>
                    )}
                  />
                </div>
              </div>
            </CardV2>
          </div>
        </div>

        <div className="flex flex-shrink-0 mt-4">
          <div className="grow">{/* "save as default settings" here */}</div>
          <Button
            type="submit"
            variant="primary"
            size="medium"
            disabled={!formState.isDirty || isLoadingUpdateSequence}
          >
            {t('Save')}
          </Button>
        </div>
        <ConfirmationModal
          open={isUnsavedChangesModalOpen}
          onOpenChange={() => setIsUnsavedChangesModalOpen(false)}
          title={t('Are you sure you want to leave without saving?')}
          confirmButtonText={t('Yes I want to leave')}
          cancelButtonText={t('No I want to stay')}
          confirmButtonVariantColor="primary"
          onConfirm={handleUnsavedChangesConfirm}
        />
      </form>
    </ClickListener>
  )
}
