import { format } from 'date-fns'
import { utcToZonedTime, zonedTimeToUtc } from 'date-fns-tz'
import isEqual from 'lodash/isEqual'
import { ChangeEvent, useState } from 'react'
import { useForm } from 'react-hook-form'

import { useUpdateSequence } from '@internals/features/sequence/hooks/useUpdateSequence'
import type { SequenceSettingsFormValues } from '@internals/features/sequence/pages/SequencePage/components/SequenceSettings/components/Schedule/types/types'
import {
  DEFAULT_SCHEDULE_DAYS,
  DEFAULT_SCHEDULE_TIME_END,
  DEFAULT_SCHEDULE_TIME_START,
  DEFAULT_SCHEDULE_TIMEZONE,
  DEFAULT_STARTING_DATE_TIME,
} from '@internals/features/sequence/pages/SequencePage/steps/StepBuilder/constants'
import type {
  SequenceDays,
  Sequence,
} from '@internals/features/sequence/types/sequence'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'
import { SequenceEvents } from '@getheroes/frontend/hooks'
import { SelectProps } from '@getheroes/ui'

interface UseSequenceSettingsProps {
  sequence: Sequence
}

export const useScheduleSettings = ({ sequence }: UseSequenceSettingsProps) => {
  const { sendEvent } = useTrackingContext()

  const scheduleTimezone =
    sequence.scheduleTimezone ?? DEFAULT_SCHEDULE_TIMEZONE

  const startingDateTime = utcToZonedTime(
    sequence.nextExecutionDate ?? DEFAULT_STARTING_DATE_TIME,
    scheduleTimezone
  )

  const formMethods = useForm<SequenceSettingsFormValues>({
    values: {
      scheduleDays: sequence.scheduleDays ?? DEFAULT_SCHEDULE_DAYS,
      scheduleTimeStart:
        sequence.scheduleTimeStart ?? DEFAULT_SCHEDULE_TIME_START,
      scheduleTimeEnd: sequence.scheduleTimeEnd ?? DEFAULT_SCHEDULE_TIME_END,
      scheduleTimezone,
      startingDate: startingDateTime,
      startingTime: format(startingDateTime, 'HH:mm'),
    },
  })

  const { setValue, getValues, formState } = formMethods

  const isSequenceAlreadyInProgress = sequence.status === 'in-progress'

  const [isUnsavedChangesModalOpen, setIsUnsavedChangesModalOpen] =
    useState<boolean>(false)

  const { updateSequence, isLoading } = useUpdateSequence()

  /* Functions */
  const handleCheckboxChange = (day: SequenceDays, checked: boolean) => {
    // Get current value
    const currentScheduleDays = getValues('scheduleDays')

    // Calculate new value
    const scheduleDays = checked
      ? [...currentScheduleDays, day]
      : currentScheduleDays.filter((d: SequenceDays) => d !== day)

    if (!isEqual(currentScheduleDays, scheduleDays)) {
      setValue('scheduleDays', scheduleDays, { shouldDirty: true })
    }

    if (scheduleDays.length > 0) {
      sendEvent(
        SequenceEvents.SEQUENCE_SETTINGS_PANEL_BUTTON_CLICK_ACTIVE_DAY,
        {
          page: 'SequenceSettingsPanel',
          component: 'Checkbox',
          feature: 'ActiveDay',
          action: 'Click',
        }
      )
    }
  }

  const handleStartingDateChange =
    (callback: (value: Date) => void) => (value: Date) => {
      callback(value)
      sendEvent(
        SequenceEvents.SEQUENCE_SETTINGS_PANEL_BUTTON_CLICK_DATE_PICKER,
        {
          page: 'SequenceSettingsPanel',
          component: 'DatePicker',
          feature: 'StartingDate',
          action: 'Change',
        }
      )
    }

  const handleStartingTimeChange =
    (callback: (value: string) => void) => (value: string) => {
      callback(value)
      sendEvent(
        SequenceEvents.SEQUENCE_SETTINGS_PANEL_BUTTON_CLICK_TIME_PICKER,
        {
          page: 'SequenceSettingsPanel',
          component: 'TimePicker',
          feature: 'StartingTime',
          action: 'Change',
        }
      )
    }

  const handleScheduleTimeStartChange =
    (callback: (value: string) => void) => (value: string) => {
      callback(value)
      sendEvent(
        SequenceEvents.SEQUENCE_SETTINGS_PANEL_BUTTON_CLICK_ACTIVE_HOUR,
        {
          page: 'SequenceSettingsPanel',
          component: 'Select',
          feature: 'Between',
          action: 'Change',
        }
      )
    }

  const handleScheduleTimeEndChange =
    (callback: (value: string) => void) => (value: string) => {
      callback(value)
      sendEvent(
        SequenceEvents.SEQUENCE_SETTINGS_PANEL_BUTTON_CLICK_ACTIVE_HOUR,
        {
          page: 'SequenceSettingsPanel',
          component: 'Select',
          feature: 'And',
          action: 'Change',
        }
      )
    }

  const onSubmit = async (data: SequenceSettingsFormValues) => {
    const { startingDate, startingTime, ...payload } = data

    const [hours, minutes] = startingTime.split(':').map(Number)

    const date = startingDate.setHours(hours, minutes, 0, 0)

    const zonedDate = zonedTimeToUtc(date, data.scheduleTimezone)

    updateSequence(sequence.id, {
      ...payload,
      nextExecutionDate: zonedDate.toUTCString(),
      scheduleTimezone: data.scheduleTimezone,
    })
  }

  const handleClickOutsideForm = () => {
    if (formState.isDirty) {
      setIsUnsavedChangesModalOpen(true)
    }
  }

  const handleUnsavedChangesConfirm = () => {
    formMethods.reset()
    setIsUnsavedChangesModalOpen(false)
  }

  return {
    /* Vars */
    formMethods,
    isLoadingUpdateSequence: isLoading,
    isUnsavedChangesModalOpen,
    isSequenceAlreadyInProgress,
    /* Methods */
    handleCheckboxChange,
    handleUnsavedChangesConfirm,
    handleClickOutsideForm,
    setIsUnsavedChangesModalOpen,
    onSubmit: formMethods.handleSubmit(onSubmit),
    handleScheduleTimeStartChange,
    handleScheduleTimeEndChange,
    handleStartingDateChange,
    handleStartingTimeChange,
  }
}
