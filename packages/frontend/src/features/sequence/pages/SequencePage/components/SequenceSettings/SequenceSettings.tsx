import { useTranslation } from 'react-i18next'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Spinner, Typography } from '@getheroes/ui'
import type { Sequence } from '@internals/features/sequence/types/sequence'

import { Schedule } from './components/Schedule/Schedule'

interface SequenceSettingsProps {
  sequence?: Sequence
  isLoading?: boolean
  onClose: () => void
}

export const SequenceSettings = ({
  sequence,
  isLoading = false,
  onClose,
}: SequenceSettingsProps) => {
  /* Vars */
  const { t } = useTranslation('sequence')

  /* Render */

  if (isLoading && !sequence) {
    return (
      <div className="h-full flex items-center justify-center">
        <Spinner size="small" />
      </div>
    )
  }

  if (!sequence) {
    return null
  }

  return (
    <div className="flex flex-col h-full gap-4">
      {/* Settings Header */}
      <div className="flex-shrink-0 flex items-center justify-between">
        <Typography variant="heading">{t('Settings')}</Typography>
        <IconButton
          icon="Xmark"
          variant="tertiary-outlined"
          size="small"
          onClick={onClose}
        />
      </div>
      {/* Settings body*/}
      <div className="flex-grow flex flex-col min-h-0">
        <Schedule sequence={sequence} />
        {/* Other tabs here */}
      </div>
    </div>
  )
}
