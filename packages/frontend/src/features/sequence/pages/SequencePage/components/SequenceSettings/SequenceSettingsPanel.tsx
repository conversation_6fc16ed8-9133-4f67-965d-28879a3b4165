import { CardV2 } from '@getheroes/ui'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { SequenceSettings } from '@internals/features/sequence/pages/SequencePage/components/SequenceSettings/SequenceSettings'
import { useSequenceSettingsPanel } from '@internals/features/sequence/pages/SequencePage/components/SequenceSettings/useSequenceSettingsPanel'

interface SequenceSettingsPanelProps {
  sequenceId: string
  isOpen: boolean
  onClose: () => void
}

export const SequenceSettingsPanel = ({
  sequenceId,
  isOpen,
  onClose,
}: SequenceSettingsPanelProps) => {
  const {
    sequence,
    isLoadingSequence,
    isOpenSettingsDrawer,
    isOpenSettingsSidePanel,
  } = useSequenceSettingsPanel({ sequenceId, isOpen })

  if (!sequence && !isLoadingSequence) {
    return null
  }

  return (
    <>
      {isOpenSettingsSidePanel && (
        <div className="h-full max-w-md min-w-md min-h-0">
          <CardV2 padding="p-4" isFullHeight>
            <SequenceSettings
              sequence={sequence}
              isLoading={isLoadingSequence}
              onClose={onClose}
            />
          </CardV2>
        </div>
      )}

      {isOpenSettingsDrawer && (
        <Drawer
          open={isOpenSettingsDrawer}
          onClose={onClose}
          classNamePanel="!max-w-fit"
        >
          <div className="h-full p-4 max-w-md min-w-md min-h-0">
            <SequenceSettings
              sequence={sequence}
              isLoading={isLoadingSequence}
              onClose={onClose}
            />
          </div>
        </Drawer>
      )}
    </>
  )
}
