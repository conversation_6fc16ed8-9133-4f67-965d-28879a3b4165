import { useCallback, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { useDeleteLeadsBySequenceMutation } from '@internals/features/sequence/api/sequenceApi'
import { useTypedSelector } from '@internals/store/store'
import { useToast } from '@getheroes/ui'

export const useDeleteLeadsBySequence = ({
  sequenceId,
}: {
  sequenceId: string
}) => {
  const [deleteLeadsBySequence, { isError, isSuccess, isLoading }] =
    useDeleteLeadsBySequenceMutation()
  const { t } = useTranslation('sequence')
  const { createToast } = useToast()

  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  useEffect(() => {
    if (isError) {
      createToast({
        type: 'error',
        message: t('Error deleting leads'),
      })
    }
  }, [createToast, isError, isSuccess, t])

  const deleteLeads = useCallback(
    async (contacts: string[], deleteAllContactsWithErrors = false) => {
      if (!sequenceId) {
        return
      }

      return deleteLeadsBySequence({
        organizationId,
        sequenceId,
        contacts,
        deleteAllContactsWithErrors,
      })
    },
    [deleteLeadsBySequence, organizationId, sequenceId]
  )

  return useMemo(
    () => ({
      deleteLeads,
      isError,
      isSuccess,
      isFetching: isLoading,
    }),
    [deleteLeads, isError, isSuccess, isLoading]
  )
}
