import { useMemo } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useGetListStepsSequenceQuery } from '@internals/features/sequence/api/sequenceStepApi'
import { useTypedSelector } from '@internals/store/store'

export const useGetListStepsSequence = ({
  sequenceId,
}: {
  sequenceId: string
}) => {
  /* Vars */

  const organization = useTypedSelector(selectCurrentUserOrganization)

  /* Queries */

  const { data, isLoading, isError, isFetching } = useGetListStepsSequenceQuery(
    {
      organizationId: organization?.id as string,
      sequenceId: sequenceId as string,
    },
    {
      skip: !sequenceId || !organization,
    }
  )

  return useMemo(
    () => ({
      steps: data,
      isFetching,
      isLoading,
      isError,
    }),
    [data, isError, isLoading, isFetching]
  )
}
