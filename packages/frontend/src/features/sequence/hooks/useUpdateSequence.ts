import { useCallback, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useUpdateSequenceMutation } from '@internals/features/sequence/api/sequenceApi'
import type { Sequence } from '@internals/features/sequence/types/sequence'
import { useTypedSelector } from '@internals/store/store'
import { useToast } from '@getheroes/ui'

export const useUpdateSequence = (
  shouldDisplaySuccessToast = true,
  isSequenceCreation = false
) => {
  const organization = useTypedSelector(selectCurrentUserOrganization)
  const { createToast } = useToast()

  const { t } = useTranslation('sequence')

  const [update, { isError, isSuccess, isLoading }] =
    useUpdateSequenceMutation()

  useEffect(() => {
    if (isSuccess && shouldDisplaySuccessToast) {
      createToast({
        type: 'main',
        message: isSequenceCreation
          ? t('Sequence has been created')
          : t('Sequence has been updated'),
      })
    }

    if (isError) {
      createToast({
        type: 'error',
        message: t('Error while updating the sequence. Please try again'),
      })
    }
  }, [
    createToast,
    isError,
    isSequenceCreation,
    isSuccess,
    shouldDisplaySuccessToast,
    t,
  ])

  const updateSequence = useCallback(
    (sequenceId: string, sequence: Partial<Sequence>) => {
      if (!organization) {
        return
      }

      return update({
        organizationId: organization.id,
        sequence: {
          ...sequence,
          id: sequenceId,
        },
      })
    },
    [organization, update]
  )

  return useMemo(
    () => ({
      updateSequence,
      isError,
      isSuccess,
      isLoading,
    }),
    [isError, isSuccess, updateSequence, isLoading]
  )
}
