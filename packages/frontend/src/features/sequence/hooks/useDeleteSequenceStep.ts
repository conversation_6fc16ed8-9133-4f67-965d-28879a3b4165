import { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { getSequenceId } from '@getheroes/frontend/config/store/selectors/sequenceSelectors'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useDeleteSequenceStepMutation } from '@internals/features/sequence/api/sequenceStepApi'
import { useTypedSelector } from '@internals/store/store'
import { useToast } from '@getheroes/ui'

export const useDeleteSequenceStep = () => {
  /* Vars */

  const sequenceId = useTypedSelector(getSequenceId)
  const organization = useTypedSelector(selectCurrentUserOrganization)
  const { t } = useTranslation('sequence')
  const { createToast } = useToast()

  /* Queries */

  const [deleteSequence, { isError, isSuccess, isLoading }] =
    useDeleteSequenceStepMutation()

  /* Functions */

  const handleDeleteSequenceStep = useCallback(
    async ({ stepId }: { stepId: string }) => {
      try {
        if (!organization) {
          return
        }

        await deleteSequence({
          organizationId: organization.id,
          sequenceId,
          stepId,
        }).unwrap()

        createToast({
          type: 'main',
          message: t('Sequence step has been deleted'),
        })
      } catch (error: unknown) {
        // @ts-expect-error - error is of type unknown
        if (error.status === 403) {
          createToast({
            type: 'error',
            message: t('User role can not modify sequences.'),
          })
          return
        }

        createToast({
          type: 'error',
          message: t(
            'Error while deleting the sequence step. Please try again'
          ),
        })
      }
    },
    [deleteSequence, organization, sequenceId, t, createToast]
  )

  return useMemo(
    () => ({
      deleteStepSequence: handleDeleteSequenceStep,
      isError,
      isSuccess,
      isLoading,
    }),
    [handleDeleteSequenceStep, isError, isSuccess, isLoading]
  )
}
