import { useCallback, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useAddLeadsAndAssignStepMutation } from '@internals/features/sequence/api/sequenceApi'
import { useTypedSelector } from '@internals/store/store'
import { useToast } from '@getheroes/ui'

export const useAddLeadsSequence = () => {
  const { t } = useTranslation('sequence')
  const { createToast } = useToast()
  const organization = useTypedSelector(selectCurrentUserOrganization)
  const [
    addLeadsToSequence,
    { isSuccess, isError, isLoading: isLoadingAddLeadsSequence },
  ] = useAddLeadsAndAssignStepMutation()

  useEffect(() => {
    if (isError) {
      createToast({
        type: 'error',
        message: t('Error while add the leads. Please try again') as string,
      })
    }
  }, [isError, isSuccess, t, createToast])

  const addLeadsSequence = useCallback(
    async ({
      contacts,
      views = [],
      sequenceId,
      onlyMine = false,
    }: {
      contacts: string[]
      views?: string[]
      sequenceId: string
      onlyMine?: boolean
    }) => {
      if (!organization || !sequenceId) {
        return
      }

      const response = await addLeadsToSequence({
        organizationId: organization?.id,
        sequenceId,
        contacts,
        deleteContactAlreadyExist: false,
        views,
        onlyMine,
      }).unwrap()

      if (response.nbExcludeDuplicatesAnotherSequence) {
        createToast({
          type: 'error',
          message: t(
            '{{count}} are already in another sequence. They have not been added.',
            {
              count: response.nbExcludeDuplicatesAnotherSequence,
            }
          ) as string,
        })
      }

      return response
    },
    [addLeadsToSequence, createToast, organization, t]
  )

  return useMemo(
    () => ({
      addLeadsSequence,
      isSuccess,
      isError,
      isLoadingAddLeadsSequence,
    }),
    [addLeadsSequence, isError, isSuccess, isLoadingAddLeadsSequence]
  )
}
