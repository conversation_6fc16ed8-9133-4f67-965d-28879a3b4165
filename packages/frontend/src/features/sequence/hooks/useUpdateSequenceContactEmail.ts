import { useCallback, useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { selectCurrentSequence } from '@getheroes/frontend/config/store/slices/sequenceSlice'
import type { Contact } from '@getheroes/shared'
import { useEditContactByIdMutation } from '@internals/features/lead/api/contactApi'
import { useUpdateContactSequenceMutation } from '@internals/features/sequence/api/sequenceApi'
import { useTypedSelector } from '@internals/store/store'
import { useToast } from '@getheroes/ui'

export const useUpdateSequenceContactEmail = (contact: Contact) => {
  const organization = useTypedSelector(selectCurrentUserOrganization)
  const sequence = useTypedSelector(selectCurrentSequence)
  const { t } = useTranslation('sequence')
  const { createToast } = useToast()

  const [updateContactSequence, resultsContactSequenceMutation] =
    useUpdateContactSequenceMutation()
  const [editContact, resultEditContactById] = useEditContactByIdMutation()

  useEffect(() => {
    if (
      resultEditContactById.isSuccess &&
      resultsContactSequenceMutation.isSuccess
    ) {
      createToast({
        type: 'main',
        message: t('Changes have been saved'),
      })
    }

    if (
      resultEditContactById.isError ||
      resultsContactSequenceMutation.isError
    ) {
      createToast({
        type: 'error',
        message: t('An error occurred while saving changes'),
      })
    }
  }, [
    createToast,
    resultEditContactById.isError,
    resultEditContactById.isSuccess,
    resultsContactSequenceMutation.isError,
    resultsContactSequenceMutation.isSuccess,
    t,
  ])

  const updateSequenceContactEmail = useCallback(
    async (customEmail: string, isNew: boolean) => {
      if (!sequence || !organization) {
        return
      }
      /*
        Temporary solution, we should handle this transaction in the backend instead
      */
      if (isNew) {
        await editContact({
          contactId: contact.id,
          organizationId: organization.id,
          emails: [...(contact.emails || []), customEmail],
        })
      }
      await updateContactSequence({
        organizationId: organization.id as string,
        sequenceId: sequence.id as string,
        customEmail,
        contactId: contact.id,
      })
    },
    [
      sequence,
      organization,
      updateContactSequence,
      contact.id,
      contact.emails,
      editContact,
    ]
  )

  return [
    updateSequenceContactEmail,
    {
      isError:
        resultsContactSequenceMutation.isError || resultEditContactById.isError,
      isSuccess:
        resultsContactSequenceMutation.isSuccess &&
        resultEditContactById.isSuccess,
      isLoading:
        resultsContactSequenceMutation.isLoading &&
        resultEditContactById.isLoading,
    },
  ] as const
}
