import { useCallback, useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { selectCurrentSequence } from '@getheroes/frontend/config/store/slices/sequenceSlice'
import { useUpdateSequencesContactStepMutation } from '@internals/features/sequence/api/sequenceApi'
import { useTypedSelector } from '@internals/store/store'
import { useToast } from '@getheroes/ui'

export interface UseUpdateSequenceContactStepArgs {
  contactId: string
  stepId: string
}

export const useUpdateSequenceContactStep = ({
  contactId,
  stepId,
}: UseUpdateSequenceContactStepArgs) => {
  const { createToast } = useToast()
  const { t } = useTranslation('sequence')

  const organization = useTypedSelector(selectCurrentUserOrganization)
  const sequence = useTypedSelector(selectCurrentSequence)

  const [updateSequenceContactStepMutation, { isError, isSuccess, isLoading }] =
    useUpdateSequencesContactStepMutation()

  const organizationId: string = organization?.id || ''
  const sequenceId: string = sequence?.id || ''

  useEffect(() => {
    if (isSuccess) {
      createToast({
        type: 'main',
        message: t('Changes have been saved'),
      })
    }

    if (isError) {
      createToast({
        type: 'error',
        message: t('An error occurred while saving changes'),
      })
    }
  }, [isError, isSuccess])

  const updateSequenceContactStep = useCallback(
    async (payload: { content: { subject?: string; body: string } }) => {
      if (!sequence) {
        return
      }
      await updateSequenceContactStepMutation({
        params: {
          organizationId,
          sequenceId,
          contactId,
          stepId,
        },
        body: payload,
      })
    },
    [
      updateSequenceContactStepMutation,
      sequence,
      sequenceId,
      contactId,
      stepId,
      organizationId,
    ]
  )

  return [updateSequenceContactStep, { isError, isSuccess, isLoading }] as const
}
