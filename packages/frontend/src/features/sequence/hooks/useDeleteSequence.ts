import { useCallback, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useDeleteSequenceMutation } from '@internals/features/sequence/api/sequenceApi'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'
import { useToast } from '@getheroes/ui'

export const useDeleteSequence = () => {
  const organization = useTypedSelector(selectCurrentUserOrganization)
  const params = useParams()

  const { t } = useTranslation('sequence')

  const dispatch = useAppDispatch()

  const { createToast } = useToast()
  const [deleteSequence, { isError, isSuccess, isLoading }] =
    useDeleteSequenceMutation()

  useEffect(() => {
    if (params?.sequenceType === 'new') {
      return
    }
    if (isSuccess) {
      createToast({
        type: 'main',
        message: t('Sequence has been deleted') as string,
      })
    }

    if (isError) {
      createToast({
        type: 'error',
        message: t(
          'Error while deleting the sequence. Please try again'
        ) as string,
      })
    }
  }, [createToast, dispatch, isError, isSuccess, params?.sequenceType, t])

  const handleDeleteSequence = useCallback(
    async (sequenceId: string) => {
      if (!organization) {
        return
      }
      await deleteSequence({
        organizationId: organization.id,
        sequenceId,
      })
    },
    [deleteSequence, organization]
  )

  return useMemo(
    () => ({
      deleteSequence: handleDeleteSequence,
      isError,
      isSuccess,
      isLoading,
    }),
    [handleDeleteSequence, isError, isSuccess, isLoading]
  )
}
