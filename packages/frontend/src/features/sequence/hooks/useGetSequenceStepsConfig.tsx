import { useTranslation } from 'react-i18next'

import { FeatureGateway, SequenceStepType } from '@getheroes/shared'
import { Icon } from '@getheroes/ui'
import { useHasAccessToFeature } from '@internals/features/featureGateway/hooks/useHasAccessToFeature'
import type {
  MultipleStep,
  SingleStep,
} from '@internals/features/sequence/types/sequence'
import { MultipleLinkedInStepEnum } from '@internals/features/sequence/types/sequence'

export const useGetSequenceStepsConfig = () => {
  const { t } = useTranslation('sequence')
  const { hasAccess: canUseLinkedInIntegration } = useHasAccessToFeature(
    FeatureGateway.CAN_USE_LINKEDIN_INTEGRATION
  )

  const EmailIcon = () => (
    <Icon
      name={'Mail'}
      backgroundColor={'decorative-brand-default'}
      containerSize={'x-large'}
    />
  )

  const LinkedinIcon = () => (
    <Icon
      name={'Linkedin'}
      backgroundColor={'decorative-blue-default'}
      containerSize={'x-large'}
    />
  )

  // ************************************************************
  // *************     SINGLE STEP CONFIGURATION     ************
  // ************************************************************

  const SINGLE_SEQUENCE_STEP: Record<string, SingleStep> = {
    [SequenceStepType.EMAIL]: {
      type: SequenceStepType.EMAIL,
      label: t('Send an email'),
      subLabel: t('Send an email to your leads'),
      isBeta: false,
      isFeatureFlag: false,
      icon: <EmailIcon />,
    },
    // NOT READY YET
    // --------------------------------
    // [SequenceStepType.CALL]: {
    //   type: SequenceStepType.CALL,
    //   label: t('Make a call', { ns: 'sequence' }),
    //   subLabel: t('This step will create a task to call the lead', {
    //     ns: 'sequence',
    //   }),
    //   icon: (
    //     <div
    //       className={clsx(
    //         COMMON_ICON_CLASSNAME,
    //         'bg-backgroundCall text-iconCall'
    //       )}
    //     >
    //       <Phone />
    //     </div>
    //   ),
    // },
    [SequenceStepType.LINKEDIN_VISIT_PROFILE]: {
      type: SequenceStepType.LINKEDIN_VISIT_PROFILE,
      label: t('LinkedIn visit profile'),
      subLabel: t('Stalk the lead’s linkedIn profile'),
      isBeta: true,
      isFeatureFlag: false,
      isDisabledByFeatureGateway: !canUseLinkedInIntegration,
      icon: <LinkedinIcon />,
    },
    [SequenceStepType.LINKEDIN_SEND_INVITATION]: {
      type: SequenceStepType.LINKEDIN_SEND_INVITATION,
      label: t('LinkedIn connection request'),
      subLabel: t('Send a connection request to the lead'),
      isBeta: true,
      isFeatureFlag: false,
      isDisabledByFeatureGateway: !canUseLinkedInIntegration,
      icon: <LinkedinIcon />,
    },
    [SequenceStepType.LINKEDIN_SEND_MESSAGE]: {
      type: SequenceStepType.LINKEDIN_SEND_MESSAGE,
      label: t('LinkedIn message', { ns: 'sequence' }),
      subLabel: t('Only available for your personal connections'),
      isBeta: true,
      isFeatureFlag: false,
      isDisabledByFeatureGateway: !canUseLinkedInIntegration,
      icon: <LinkedinIcon />,
    },
  }

  // ************************************************************
  // *************     MULTIPLE STEP CONFIGURATION     **********
  // ************************************************************

  const MULTIPLE_SEQUENCE_STEP: Record<string, MultipleStep> = {
    // ------ 1st config ------
    [MultipleLinkedInStepEnum.INVIT_MESSAGE]: {
      type: MultipleLinkedInStepEnum.INVIT_MESSAGE,
      label: t('LinkedIn connection request + message'),
      isBeta: true,
      isFeatureFlag: false,
      isDisabledByFeatureGateway: !canUseLinkedInIntegration,
      subLabel: t(
        'It will create {{count}} steps. If you and your lead are already connected, we will not send a request.',
        {
          count: 2,
        }
      ),
      steps: [
        SINGLE_SEQUENCE_STEP[SequenceStepType.LINKEDIN_SEND_INVITATION],
        SINGLE_SEQUENCE_STEP[SequenceStepType.LINKEDIN_SEND_MESSAGE],
      ],
      icon: <LinkedinIcon />,
    },

    // ------ 2nd config ------
    [MultipleLinkedInStepEnum.VISIT_INVIT_MESSAGE]: {
      type: MultipleLinkedInStepEnum.VISIT_INVIT_MESSAGE,
      label: t('LinkedIn visit profile + connection request + message'),
      isBeta: true,
      isDisabledByFeatureGateway: !canUseLinkedInIntegration,
      isFeatureFlag: false,
      subLabel: t(
        'It will create {{count}} steps. If you and your lead are already connected, we will not send a request.',
        {
          count: 3,
        }
      ),
      steps: [
        SINGLE_SEQUENCE_STEP[SequenceStepType.LINKEDIN_VISIT_PROFILE],
        SINGLE_SEQUENCE_STEP[SequenceStepType.LINKEDIN_SEND_INVITATION],
        SINGLE_SEQUENCE_STEP[SequenceStepType.LINKEDIN_SEND_MESSAGE],
      ],
      icon: <LinkedinIcon />,
    },
  }

  return {
    SINGLE_SEQUENCE_STEP,
    MULTIPLE_SEQUENCE_STEP,
  }
}
