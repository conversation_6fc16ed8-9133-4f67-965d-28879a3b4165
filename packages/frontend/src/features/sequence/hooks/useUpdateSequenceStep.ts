import { useCallback, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { getSequenceId } from '@getheroes/frontend/config/store/selectors/sequenceSelectors'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useUpdateSequenceStepMutation } from '@internals/features/sequence/api/sequenceStepApi'
import type { SequenceStepApi } from '@internals/features/sequence/types/sequenceStep'
import { useTypedSelector } from '@internals/store/store'
import { useToast } from '@getheroes/ui'

export const useUpdateSequenceStep = ({
  showSuccessToast = true,
}: {
  showSuccessToast?: boolean
} = {}) => {
  /* Vars */

  const organization = useTypedSelector(selectCurrentUserOrganization)
  const sequenceId = useTypedSelector(getSequenceId)
  const { createToast } = useToast()
  const { t } = useTranslation('sequence')

  /* Queries */

  const [update, { isError, isSuccess, isLoading }] =
    useUpdateSequenceStepMutation()

  /* Effects */

  useEffect(() => {
    if (isSuccess && showSuccessToast) {
      createToast({
        type: 'main',
        message: t('Sequence step has been updated') as string,
      })
    }

    if (isError) {
      createToast({
        type: 'error',
        message: t('Error while updating the sequence step. Please try again'),
      })
    }
  }, [createToast, isError, isSuccess, t, showSuccessToast])

  /* Functions */

  const updateSequenceStep = useCallback(
    async (step: Partial<SequenceStepApi> & Pick<SequenceStepApi, 'id'>) => {
      if (!organization) {
        return
      }
      await update({
        organizationId: organization.id,
        step,
        sequenceId,
      })
    },
    [organization, sequenceId, update]
  )

  return useMemo(
    () => ({
      updateSequenceStep,
      isError,
      isSuccess,
      isLoading,
    }),
    [isError, isSuccess, isLoading, updateSequenceStep]
  )
}
