import { describe } from 'vitest'

import { CurrentStatusIndicator } from '@getheroes/ui-business'
import { SequenceStatus } from '@internals/features/sequence/types/sequence'
import {
  getSequenceLabelBySequenceStatus,
  mapSequenceStatusToIndicatorStatus,
} from '@internals/features/sequence/util/sequenceStatusMapper'

describe('mapSequenceStatusToIndicatorStatus', () => {
  it('should return CurrentStatusIndicator.RUNNING when status is SequenceStatus.IN_PROGRESS', () => {
    expect(mapSequenceStatusToIndicatorStatus(SequenceStatus.IN_PROGRESS)).toBe(
      CurrentStatusIndicator.RUNNING
    )
  })

  it('should return CurrentStatusIndicator.PAUSED when status is SequenceStatus.PAUSE', () => {
    expect(mapSequenceStatusToIndicatorStatus(SequenceStatus.PAUSE)).toBe(
      CurrentStatusIndicator.PAUSED
    )
  })

  it('should return CurrentStatusIndicator.IN_QUEUE when status is SequenceStatus.DONE', () => {
    expect(mapSequenceStatusToIndicatorStatus(SequenceStatus.DONE)).toBe(
      CurrentStatusIndicator.IN_QUEUE
    )
  })

  it('should return CurrentStatusIndicator.ERROR when status is not SequenceStatus.IN_PROGRESS, SequenceStatus.PAUSE, SequenceStatus.DONE', () => {
    expect(
      mapSequenceStatusToIndicatorStatus('invalid' as SequenceStatus)
    ).toBe(CurrentStatusIndicator.ERROR)
  })
})

describe('getSequenceLabelBySequenceStatus', () => {
  it('should return "Running" when status is SequenceStatus.IN_PROGRESS', () => {
    expect(getSequenceLabelBySequenceStatus(SequenceStatus.IN_PROGRESS)).toBe(
      'Running'
    )
  })

  it('should return "Paused" when status is SequenceStatus.PAUSE', () => {
    expect(getSequenceLabelBySequenceStatus(SequenceStatus.PAUSE)).toBe(
      'Paused'
    )
  })

  it('should return "Done" when status is SequenceStatus.DONE', () => {
    expect(getSequenceLabelBySequenceStatus(SequenceStatus.DONE)).toBe('Done')
  })

  it('should return "Error" when status is not SequenceStatus.IN_PROGRESS, SequenceStatus.PAUSE, SequenceStatus.DONE', () => {
    expect(getSequenceLabelBySequenceStatus('invalid' as SequenceStatus)).toBe(
      'Error'
    )
  })
})
