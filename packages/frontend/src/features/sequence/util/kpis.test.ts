import { describe, it, expect } from 'vitest'
import { getSequenceKpis, getSequenceMacroKpis, ratioCalculation } from './kpis'

describe('ratioCalculation', () => {
  it('should return 0% or 0.0 %', () => {
    expect(ratioCalculation(0, 0, false)).toEqual('0 %')
    expect(ratioCalculation(null, null, true)).toEqual('0.0 %')
  })

  it('should return 50% or 50.0 %', () => {
    expect(ratioCalculation(50, 100, false)).toEqual('50 %')
    expect(ratioCalculation(50, 100, true)).toEqual('50.0 %')
  })

  it('should return 33% or 33.3 %', () => {
    expect(ratioCalculation(1, 3, false)).toEqual('33 %')
    expect(ratioCalculation(1, 3, true)).toEqual('33.3 %')
  })
})

describe('getSequenceKpis', () => {
  it('should return the kpi list', () => {
    expect(
      getSequenceKpis({
        mailsTotal: 0,
        mailsOpened: 0,
        linksClicked: 0,
        mailsReplied: 0,
      })
    ).toEqual([
      {
        name: 'Email sent',
        value: 0,
        ratio: 0,
      },
      {
        name: 'Opened',
        value: 0,
        ratio: '0.0 %',
      },
      {
        name: 'Clicked',
        value: 0,
        ratio: '0.0 %',
      },
      {
        name: 'Replied',
        value: 0,
        ratio: '0.0 %',
      },
      {
        name: 'Unsubscribed',
        value: 0,
        ratio: '0.0 %',
      },
      {
        name: 'Bounced',
        value: 0,
        ratio: '0.0 %',
      },
    ])
  })
})

describe('getSequenceMacroKpis', () => {
  it('should return the kpi list', () => {
    expect(
      getSequenceMacroKpis({
        sumLinksClicked: 0,
        sumMailsBounced: 0,
        sumMailsOpened: 0,
        sumMailsReplied: 0,
        sumMailsTotal: 0,
        sumMailsUnsubscribed: 0,
      })
    ).toEqual([
      {
        name: 'Email sent',
        value: 0,
        ratio: 0,
      },
      {
        name: 'Opened',
        value: 0,
        ratio: '0 %',
      },
      {
        name: 'Clicked',
        value: 0,
        ratio: '0 %',
      },
      {
        name: 'Replied',
        value: 0,
        ratio: '0 %',
      },
      {
        name: 'Unsubscribed',
        value: 0,
        ratio: '0 %',
      },
      {
        name: 'Bounced',
        value: 0,
        ratio: '0 %',
      },
    ])
  })
})
