import type {
  Contact,
  SequenceContactReviewStatus,
  SequenceContactStatus,
  SequenceStepType,
  SequenceVersion,
  User,
  LeadImportStatusEnum,
} from '@getheroes/shared'
import type { LeadPageContext } from '@internals/features/lead/types/genericLeadType'
import type {
  SequenceStepApi,
  StepAddSteps,
} from '@internals/features/sequence/types/sequenceStep'
import type {
  ListPagination,
  PaginationArgsType,
} from '@internals/types/api/pagination'

export enum SequenceStatus {
  CREATED = 'created',
  IN_PROGRESS = 'in-progress',
  PAUSE = 'pause',
  DONE = 'done',
}

export enum SequenceDays {
  MONDAY = 'monday',
  TUESDAY = 'tuesday',
  WEDNESDAY = 'wednesday',
  THURSDAY = 'thursday',
  FRIDAY = 'friday',
  SATURDAY = 'saturday',
  SUNDAY = 'sunday',
}

export type Sequence = {
  id: string
  name: string
  excludeDuplicatesAnotherSequence: boolean
  excludeLeadStatuses: string[]
  status: SequenceStatus
  scheduleDays: SequenceDays[]
  scheduleTimeStart: string
  scheduleTimeEnd: string
  scheduleTimezone: string
  enable: boolean
  createdBy: User
  linksClicked: number | null
  mailsBounced: number | null
  mailsOpened: number | null
  mailsReplied: number | null
  mailsTotal: number | null
  mailsUnsubscribed: number | null
  version: SequenceVersion
  nbContactReviewed: number
  nbContacts: number
  assignUserList: Pick<User, 'id' | 'firstName' | 'lastName'>[]
  nextExecutionDate: string
  leadImport: {
    id: string
    status: LeadImportStatusEnum
  }
}

export type AddLeadsAndAssignStepResponseType = {
  nbExcludeDuplicatesAnotherSequence: number
  contactIdsAdded: string[]
}

export type SingleStep = {
  type: SequenceStepType
  label: string
  subLabel: string
  icon: React.ReactNode
  isBeta?: boolean
  isFeatureFlag?: boolean
  isDisabledByFeatureGateway?: boolean
}

export type MultipleStep = {
  type: MultipleLinkedInStepEnum
  label: string
  subLabel: string
  icon: React.ReactNode
  steps: SingleStep[]
  isBeta?: boolean
  isFeatureFlag?: boolean
  isDisabledByFeatureGateway?: boolean
}

export enum SequenceStepStatus {
  NEXT = 'next',
  PAST = 'past',
  TO_COME = 'to_come',
}

export enum MultipleLinkedInStepEnum {
  INVIT_MESSAGE = 'invit_message',
  VISIT_INVIT_MESSAGE = 'visit_invit_message',
}

export type LeadBySequence = {
  contactId: string
  contact: Contact
  createdAt: string
  id: string
  updatedAt: string
  status: SequenceContactStatus
  step: SingleStep | null
  customEmail: string | null
  review: {
    status: SequenceContactReviewStatus
    data: {
      missing_variables: string[]
    } | null
    reviewedAt: Date | null
    reviewedBy: User | null
  }
}

export type LeadsBySequence = ListPagination<LeadBySequence>

export type LeadsBySequenceArgs = {
  organizationId: string
  sequenceId: string
  pagination?: PaginationArgsType
  filters?: {
    phones?: boolean
    emails?: boolean
    reviewStatus?: SequenceContactReviewStatus
  }
  orderBy?: string
}

export type StepImportSettings = Pick<
  Sequence,
  'excludeDuplicatesAnotherSequence' | 'excludeLeadStatuses'
>

export type SequenceDraft = {
  id: string
  name: string
  enable: boolean
  status: string
  views: string[]
  stepImportSettings: StepImportSettings
  stepAddSteps: StepAddSteps
}

export type AddLeadsSource =
  | LeadPageContext.ALL_LEADS
  | LeadPageContext.MY_LEADS

export type UserMissingData = {
  [userId: string]: {
    emails: boolean
    phones: boolean
  }
}

export type SequenceLeadErrors = {
  contactId: string
  errorsFields: string[]
}
export type SequenceMacroKpisType = {
  sumLinksClicked: number | null
  sumMailsBounced: number | null
  sumMailsOpened: number | null
  sumMailsReplied: number | null
  sumMailsTotal: number | null
  sumMailsUnsubscribed: number | null
}

export type SequencesContactSteps = {
  sequenceContact: {
    status: SequenceContactStatus
  }
  sequence: Pick<Sequence, 'id' | 'name' | 'status'>
  currentStep: Pick<SequenceStepApi, 'order'>
  lastStep: Pick<SequenceStepApi, 'order'>
  done: boolean
  progress: number
}

export type LeadBySequenceWithActivityList = LeadBySequence & {
  nbOpens: number
  nbClicks: number
}

export enum SignalType {
  CLICKED = 'clicked',
  OPENED = 'opened',
  REPLY = 'reply',
  BOUNCED = 'bounced',
  UNSUBSCRIBED = 'unsubscribed',
}

export type SequencePageStepType =
  | 'add-leads-assign'
  | 'add-steps'
  | 'settings'
  | 'enrich'
  | 'review'

export enum SequenceStepperMode {
  CREATE = 'create',
  EDIT = 'edit',
}

export type ContactSequenceSteps = {
  contactId: string
  steps: SequencesContactSteps[]
}
