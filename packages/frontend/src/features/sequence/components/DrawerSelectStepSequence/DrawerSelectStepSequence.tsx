import { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { SequenceEvents } from '@getheroes/frontend/hooks'
import { SequenceStepType } from '@getheroes/shared'
import { I<PERSON><PERSON><PERSON><PERSON>, Spinner } from '@getheroes/ui'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { StepCardSequence } from '@internals/features/sequence/components/StepCardSequence/StepCardSequence'
import { STEP_WAITING_TIME_INITIAL_VALUE_IN_MINUTES } from '@internals/features/sequence/constants/step.constant'
import { useAddSequenceStep } from '@internals/features/sequence/hooks/useAddSequenceStep'
import { useGetSequenceStepsConfig } from '@internals/features/sequence/hooks/useGetSequenceStepsConfig'
import type {
  MultipleStep,
  SingleStep,
} from '@internals/features/sequence/types/sequence'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'

type DrawerSelectStepSequenceProps = {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
  handleClose: (type?: SequenceStepType) => void
}

export const DrawerSelectStepSequence = ({
  isOpen,
  setIsOpen,
  handleClose,
}: DrawerSelectStepSequenceProps) => {
  /* Vars */

  const { t } = useTranslation('sequence')
  const [isLoadingAddSingleStep, setIsLoadingAddSingleStep] = useState(false)
  const [isLoadingAddMultipleSteps, setIsLoadingAddMultipleSteps] =
    useState(false)

  const { createSequenceStep } = useAddSequenceStep()
  const { SINGLE_SEQUENCE_STEP, MULTIPLE_SEQUENCE_STEP } =
    useGetSequenceStepsConfig()
  const { sendEvent } = useTrackingContext()

  /* Functions */

  const getContentBySequenceType = (type: SequenceStepType) => {
    if (type === SequenceStepType.EMAIL) {
      return {
        subject: '',
        body: '',
      }
    }

    return {
      body: '',
    }
  }

  const addSequenceStep = useCallback(
    async (step: SingleStep) => {
      setIsLoadingAddSingleStep(true)
      await createSequenceStep({
        name: step.label,
        type: step.type,
        waitingBetweenStep: STEP_WAITING_TIME_INITIAL_VALUE_IN_MINUTES,
        content: getContentBySequenceType(step.type),
      })
      setIsLoadingAddSingleStep(false)

      handleClose(step.type)

      sendEvent(SequenceEvents.SEQUENCE_ADD_STEP_DRAWER_CLICK_STEP_OPTION, {
        step: step.label,
      })
    },
    [createSequenceStep, handleClose, sendEvent]
  )

  const addMultipleSequenceStep = useCallback(
    async (step: MultipleStep) => {
      setIsLoadingAddMultipleSteps(true)
      // This is a tricks to create multiple steps in sync mode
      // If we don't do this, the steps will be created in parallel with the same order number
      // Backend team should rework this logic to don't do this
      for (const stepItem of step.steps) {
        await createSequenceStep({
          name: stepItem.label,
          type: stepItem.type,
          waitingBetweenStep: STEP_WAITING_TIME_INITIAL_VALUE_IN_MINUTES,
          content: getContentBySequenceType(stepItem.type),
        })
      }
      setIsLoadingAddMultipleSteps(false)
      handleClose(step.type as unknown as SequenceStepType)

      sendEvent(SequenceEvents.SEQUENCE_ADD_STEP_DRAWER_CLICK_STEP_OPTION, {
        step: step.label,
      })
    },
    [createSequenceStep, handleClose, sendEvent]
  )

  const handleAddStep = useCallback(
    ({
      step,
      isMultiple,
    }:
      | { step: SingleStep; isMultiple: false }
      | { step: MultipleStep; isMultiple: true }) => {
      if (isLoadingAddSingleStep || isLoadingAddMultipleSteps) {
        return
      }

      if (isMultiple) {
        addMultipleSequenceStep(step)
      } else {
        addSequenceStep(step)
      }
    },
    [
      addMultipleSequenceStep,
      addSequenceStep,
      isLoadingAddMultipleSteps,
      isLoadingAddSingleStep,
    ]
  )

  return (
    <Drawer
      classNamePanel="!max-w-[650px] c-sequence-form-drawer"
      open={isOpen}
      onClose={() => setIsOpen(false)}
    >
      {isOpen && (
        <div className="flex flex-col p-6 gap-4">
          <div className="flex items-start w-full justify-between h-[36px]">
            <h2 className="heading-s">{t('Add a step')}</h2>

            <IconButton
              icon={'Xmark'}
              size={'large'}
              dataTestId={'drawer-select-step-sequence-close'}
              variant={'tertiary-outlined'}
              onClick={() => {
                handleClose()
              }}
            />
          </div>

          <div className={'flex flex-col gap-4'}>
            <div className={'flex flex-col gap-4'}>
              {/* --------------------------------------------- */}
              {/* SINGLE STEP */}
              {/* --------------------------------------------- */}
              <div className="flex gap-2">
                <h3 className={'heading-xs'}>{t('Single step')}</h3>
                {isLoadingAddSingleStep && <Spinner />}
              </div>
              <ul className="flex flex-col overflow-y-scroll gap-4">
                {Object.values(SINGLE_SEQUENCE_STEP).map((step: SingleStep) => {
                  return (
                    <li key={`category-${step.type}`} className="">
                      <StepCardSequence
                        stepConfig={step}
                        onClick={() =>
                          handleAddStep({ step, isMultiple: false })
                        }
                      />
                    </li>
                  )
                })}
              </ul>
            </div>

            <div className={'flex flex-col gap-4'}>
              {/* --------------------------------------------- */}
              {/* MULTIPLE STEP */}
              {/* --------------------------------------------- */}

              <div className="flex gap-2">
                <h3 className={'heading-xs'}>{t('Multiple steps')}</h3>
                {isLoadingAddMultipleSteps && <Spinner />}
              </div>
              <ul className="flex flex-col overflow-y-scroll gap-4">
                {Object.values(MULTIPLE_SEQUENCE_STEP).map(
                  (step: MultipleStep) => {
                    return (
                      <li key={`category-${step.type}`} className="">
                        <StepCardSequence
                          stepConfig={step}
                          onClick={() =>
                            handleAddStep({ step, isMultiple: true })
                          }
                        />
                      </li>
                    )
                  }
                )}
              </ul>
            </div>
          </div>
        </div>
      )}
    </Drawer>
  )
}
