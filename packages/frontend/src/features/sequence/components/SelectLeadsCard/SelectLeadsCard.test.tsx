import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'

import type { SelectLeadsCardProps } from './SelectLeadsCard'
import { SelectLeadsCard } from './SelectLeadsCard'

vi.mock('@getheroes/ui', async () => ({
  ...(await vi.importActual('@getheroes/ui')),
  Icon: () => <div data-testid="iconoir-community" />,
}))

describe('SelectLeadsCard', () => {
  const defaultProps: SelectLeadsCardProps = {
    onClick: vi.fn(),
    title: 'Test Title',
    icon: 'Community',
  }

  it('should render the title', () => {
    render(<SelectLeadsCard {...defaultProps} />)
    expect(
      screen.getByTestId('c-sequence-select-leads-card')
    ).toBeInTheDocument()
    expect(screen.getByText('Test Title')).toBeInTheDocument()
  })

  it('should render the icon', () => {
    render(<SelectLeadsCard {...defaultProps} />)
    expect(
      screen.getByTestId('c-sequence-select-leads-card-icon')
    ).toBeInTheDocument()
    expect(screen.getByTestId('iconoir-community')).toBeInTheDocument()
  })

  it('should call onClick when clicked', () => {
    render(<SelectLeadsCard {...defaultProps} />)
    fireEvent.click(screen.getByText('Test Title'))
    expect(defaultProps.onClick).toHaveBeenCalled()
  })
})
