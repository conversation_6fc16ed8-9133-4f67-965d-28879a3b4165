import { useTranslation } from 'react-i18next'

import type { IconName } from '@getheroes/ui'
import { Icon } from '@getheroes/ui'
import { Paper } from '@internals/components/common/dataDisplay/Paper/Paper'

export interface SelectLeadsCardProps {
  onClick: () => void
  title: string
  icon: IconName
}

export const SelectLeadsCard = ({
  onClick,
  title,
  icon,
}: SelectLeadsCardProps) => {
  const { t } = useTranslation('sequence')

  return (
    <Paper dataTestId="c-sequence-select-leads-card" onClick={onClick}>
      <div className="flex items-center gap-2 w-80">
        <div
          data-testid="c-sequence-select-leads-card-icon"
          className="flex w-12 h-12 justify-center items-center rounded-m mr-4 bg-backgroundDecorativeBrand text-textBrand"
        >
          <Icon name={icon} size="medium" />
        </div>

        <div className="mr-4 flex flex-col-reverse">
          <div className="heading-xs font-bold">{title}</div>

          <div className="body-xs-regular text-textSubtle">
            {t('Select from') as string}
          </div>
        </div>
      </div>
    </Paper>
  )
}
