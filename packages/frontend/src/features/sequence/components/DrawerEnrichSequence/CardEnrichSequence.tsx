import { clsx } from 'clsx'
import type { PropsWithChildren } from 'react'

import { Card } from '@getheroes/ui'

type CardEnrichSequenceProps = PropsWithChildren<{
  className?: string
  onClick?: () => void
}>

export const CardEnrichSequence = ({
  onClick,
  children,
}: CardEnrichSequenceProps) => {
  return <Card onClick={onClick}>{children}</Card>
}

CardEnrichSequence.Header = ({
  className,
  children,
}: PropsWithChildren & { className?: string }) => {
  return <h5 className={clsx('heading-xs', className)}>{children}</h5>
}

CardEnrichSequence.Body = ({
  children,
  className,
}: PropsWithChildren & { className?: string }) => {
  return (
    <p className={clsx('body-s-medium text-textSubtle', className)}>
      {children}
    </p>
  )
}

CardEnrichSequence.Icon = ({
  children,
  className,
}: CardEnrichSequenceProps) => {
  return (
    <div
      className={clsx(
        'flex w-12 h-12 justify-center items-center rounded-m mr-4',
        className
      )}
    >
      {children}
    </div>
  )
}
