import isEmpty from 'lodash/isEmpty'
import {
  memo,
  useEffect,
  useLayoutEffect,
  useMemo,
  useReducer,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

import { isEnableSequence } from '@getheroes/frontend/config/store/selectors/sequenceSelectors'
import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { SequenceEvents, useTracking } from '@getheroes/frontend/hooks'
import type { Organization } from '@getheroes/shared'
import { EnrichmentType, getEnrichmentCost } from '@getheroes/shared'
import { Tooltip } from '@getheroes/ui'
import { EnrichLeadsModal } from '@getheroes/ui-business'
import { ContactAssignee } from '@internals/components/business/lead/contact/ContactAssignee/ContactAssignee'
import { Grid } from '@internals/components/common/dataDisplay/Grid/Grid'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { CompanyNameCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/CompanyNameCell'
import { ContactNameCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/ContactNameCell'
import { SocialCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/SocialCell'
import { StringCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/StringCell'
import { WrapperCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/WrapperCell'
import type { GridColDef } from '@internals/components/common/dataDisplay/Grid/types/grid'
import { GridRowSelectionEnum } from '@internals/components/common/dataDisplay/Grid/types/grid'
import { SelectInput } from '@internals/components/common/dataEntry/Select/SelectInput'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import {
  ButtonSize,
  ButtonVariant,
} from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { AddContactsToSequenceDrawer } from '@internals/features/lead/components/AddContactsToSequenceDrawer/AddContactsToSequenceDrawer'
import { LeadStatusItem } from '@internals/features/lead/components/LeadStatusItem/LeadStatusItem'
import { AssignLeadsCommonModal } from '@internals/features/lead/components/LeadsCollectionCard/components/modals/AssignLeadsModal/AssignLeadsCommonModal/AssignLeadsCommonModal'
import { useGetAssignLeadsData } from '@internals/features/lead/components/LeadsCollectionCard/components/modals/AssignLeadsModal/useGetAssignLeadsData'
import { AddTaskToMyLeadsModal } from '@internals/features/lead/components/LeadsModals/AddTaskToMyLeadsModal/AddTaskToMyLeadsModal'
import { ModalLeadBulkEdit } from '@internals/features/lead/components/LeadsModals/ModalLeadBulkEdit/ModalLeadBulkEdit'
import { UnassignMyContactsModal } from '@internals/features/lead/components/LeadsModals/UnassignMyContactsModal/UnassignMyContactsModal'
import { ContactSequenceSignalsCell } from '@internals/features/lead/components/tables/components/Cells/ContactSequenceSignalsCell/ContactSequenceSignalsCell'
import {
  CONTACT_SIGNALS_CELL_MIN_WIDTH,
  CONTACT_STATUS_CELL_MIN_WIDTH,
} from '@internals/features/lead/components/tables/components/Cells/ContactSequenceSignalsCell/ContactSequenceSignalsCell-export'
import { LeadActionsCell } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/LeadActionsCell'
import { useAssignContacts } from '@internals/features/lead/hooks/assignation/useAssignLeads'
import { useEnrichPhoneOrEmail } from '@internals/features/lead/hooks/enrichment/useEnrichPhoneOrEmail'
import { LeadFieldFormat } from '@internals/features/lead/types/leadFieldFormat'
import { ContactSequenceStepProgress } from '@internals/features/sequence/components/ContactSequenceStepProgress/ContactSequenceStepProgress'
import { ModalDeleteLeads } from '@internals/features/sequence/components/ModalDeleteLeads/ModalDeleteLeads'
import { FloatingButtons } from '@internals/features/sequence/components/TableShowAddedLeads/FloatingButtons'
import { LeadStatus } from '@internals/features/sequence/components/TableShowAddedLeads/LeadStatus/LeadStatus'
import { useDeleteLeadsBySequence } from '@internals/features/sequence/hooks/useDeleteLeadsBySequence'
import type {
  LeadBySequence,
  LeadBySequenceWithActivityList,
} from '@internals/features/sequence/types/sequence'
import type { CreateTasksFormValuesType } from '@internals/features/task/hooks/useCreateTasksForOneUser'
import { useCreateTasksForOneUser } from '@internals/features/task/hooks/useCreateTasksForOneUser'
import { privateRoutes } from '@internals/hooks/useRoute'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

const initialState = {
  isOpenModalAssignLeads: false,
  isOpenModalDeleteLeads: false,
  isOpenDrawerTransferToSequence: false,
  isOpenModalAddTask: false,
  isOpenModalUnassign: false,
  isOpenModalEnrich: false,
  isOpenModalEditInBulk: false,
}

const reducer = (state: typeof initialState, action: { type: any }) => {
  switch (action.type) {
    case 'OPEN_MODAL_ASSIGN_LEADS':
      return { ...state, isOpenModalAssignLeads: true }
    case 'CLOSE_MODAL_ASSIGN_LEADS':
      return { ...state, isOpenModalAssignLeads: false }
    case 'OPEN_MODAL_DELETE_LEADS':
      return { ...state, isOpenModalDeleteLeads: true }
    case 'CLOSE_MODAL_DELETE_LEADS':
      return { ...state, isOpenModalDeleteLeads: false }
    case 'TOGGLE_MODAL_TRANSFER_TO_SEQUENCE':
      return {
        ...state,
        isOpenDrawerTransferToSequence: !state.isOpenDrawerTransferToSequence,
      }
    case 'TOGGLE_MODAL_ADD_TASK':
      return { ...state, isOpenModalAddTask: !state.isOpenModalAddTask }
    case 'TOGGLE_MODAL_UNASSIGN':
      return { ...state, isOpenModalUnassign: !state.isOpenModalUnassign }
    case 'TOGGLE_MODAL_ENRICH':
      return { ...state, isOpenModalEnrich: !state.isOpenModalEnrich }
    case 'TOGGLE_MODAL_EDIT_IN_BULK':
      return { ...state, isOpenModalEditInBulk: !state.isOpenModalEditInBulk }
    default:
      return state
  }
}

export type TableShowAddedLeadsProps = {
  onClickContact: (contactId: string) => void
  leads: any[]
  paginationMeta?: any
  sequenceId: string
  isLoading: boolean
  // TODO remove when migration to sequence V2 is done
  context: 'SequenceV1SelectLeads' | 'SequenceV2SelectLeads'
}

export const TableShowAddedLeads = memo(
  ({
    onClickContact,
    leads,
    sequenceId,
    paginationMeta,
    isLoading = false,
    // TODO remove when migration to sequence V2 is done
    context,
  }: TableShowAddedLeadsProps) => {
    /* #region Vars */

    const { t } = useTranslation(['common', 'sequence', 'lead'])
    const navigate = useNavigate()
    const {
      id: organizationId,
      creditBalance,
      plan,
    } = useTypedSelector(selectCurrentUserOrganization) as Organization
    const isEnable = useTypedSelector(isEnableSequence)
    const currentUser = useTypedSelector(selectCurrentUser)

    const [singleUserId, setSingleUserId] = useState<string>('')
    const [isShowFloatingBar, setIsShowFloatingBar] = useState(false)

    const [state, dispatch] = useReducer(reducer, initialState)

    const phoneCreditValue = getEnrichmentCost(EnrichmentType.PHONE, plan)
    const emailCreditValue = getEnrichmentCost(EnrichmentType.EMAIL, plan)

    /* #regions Custom Hooks */

    const { selections, rowSelected, setSelections, selectedRowsData } =
      useGridContext()
    const { handleAddTasks, isLoading: isLoadingAddTasks } =
      useCreateTasksForOneUser()
    const enrichPhoneOrEmail = useEnrichPhoneOrEmail()
    const [handleAssignLeads, { isLoading: isLoadingAssignContacts }] =
      useAssignContacts({ organizationId })
    const { selectOptions } = useGetAssignLeadsData({
      userIdAssignedToContacts: singleUserId,
    })

    const { deleteLeads } = useDeleteLeadsBySequence({ sequenceId })
    const { sendEvent } = useTracking()

    /* #region Memos */

    const columns = useMemo(() => {
      return [
        {
          field: 'name',
          headerName: t('Name', { ns: 'lead' }),
          cellRenderer: ({ data }: { data: LeadBySequence }) => (
            <ContactNameCell contact={data.contact} />
          ),
        },
        {
          field: 'title',
          headerName: t('Job title', { ns: 'lead' }),
          cellRenderer: ({ data }: { data: LeadBySequence }) => {
            return (
              <StringCell
                lead={data.contact}
                value={data.contact.title}
                isEnrichmentColumn={false}
              />
            )
          },
        },
        {
          field: 'Sequences status',
          headerName: t('Sequence', { ns: 'sequence' }),
          cellRenderer: ({
            data,
          }: {
            data: LeadBySequenceWithActivityList
          }) => {
            return (
              <WrapperCell data={data.contact}>
                <ContactSequenceStepProgress
                  contact={data.contact}
                  review={data.review}
                  sequenceId={sequenceId}
                />
              </WrapperCell>
            )
          },
        },
        {
          minWidth: CONTACT_SIGNALS_CELL_MIN_WIDTH,
          maxWidth: CONTACT_SIGNALS_CELL_MIN_WIDTH,
          field: 'signals',
          headerName: t('Signals', { ns: 'lead' }),
          cellRenderer: ({
            data,
          }: {
            data: LeadBySequenceWithActivityList
          }) => {
            return (
              <ContactSequenceSignalsCell
                sequenceId={sequenceId}
                leadBySequenceWithActivity={data}
              />
            )
          },
        },
        {
          field: 'company',
          headerName: t('Company', { ns: 'lead' }),
          cellRenderer: ({
            data,
          }: {
            data: LeadBySequenceWithActivityList
          }) => <CompanyNameCell company={data.contact.company} />,
        },
        {
          minWidth: CONTACT_STATUS_CELL_MIN_WIDTH,
          field: 'status',
          headerName: t("Sequence's Lead Status", { ns: 'sequence' }),
          cellRenderer: ({
            data,
          }: {
            data: LeadBySequenceWithActivityList
          }) => (
            <Tooltip
              placement="left"
              content={
                data.step
                  ? `Step ${data.step.order}: ${data.step.name}`
                  : undefined
              }
            >
              <LeadStatus status={data.status} />
            </Tooltip>
          ),
        },
        {
          field: 'linkedinUrl',
          headerName: t('LinkedIn', { ns: 'lead' }),
          cellRenderer: ({ data }: { data: LeadBySequence }) => (
            <SocialCell
              isEnrichmentColumn={false}
              lead={data.contact}
              icon={'Linkedin'}
              value={data.contact.linkedinUrl}
            />
          ),
        },
        {
          field: 'contact.status',
          headerName: t('Status', { ns: 'lead' }),
          cellRenderer: ({
            data,
          }: {
            data: LeadBySequenceWithActivityList
          }) => {
            const { status } = data.contact
            if (!status) {
              return null
            }
            return (
              <Tooltip content={t('Lead status', { ns: 'lead' })}>
                <div className="flex w-full">
                  <LeadStatusItem status={status} />
                </div>
              </Tooltip>
            )
          },
        },
        {
          field: 'sender',
          headerName: t('Sender', { ns: 'lead' }),
          cellRenderer: ({
            data,
          }: {
            data: LeadBySequenceWithActivityList
          }) => {
            return (
              <WrapperCell data={data.contact}>
                <ContactAssignee
                  shouldSkiptNextTask
                  isEnableSequence={isEnable}
                  contact={data.contact}
                  isFullWidth
                  assignButton={
                    <Button
                      dataTestId={
                        idReferentials.leads.components.ContactAssignee
                          .assignLeadButton
                      }
                      size={ButtonSize.SMALL}
                      variant={ButtonVariant.SECONDARY}
                      as={'div'}
                    >
                      {t('Choose sender', { ns: 'lead' })}
                    </Button>
                  }
                />
              </WrapperCell>
            )
          },
        },
        {
          field: LeadFieldFormat.EMAIL,
          headerName: t('Email', { ns: 'lead' }),
          cellRenderer: ({ data }: { data: LeadBySequence }) => (
            <LeadActionsCell lead={data.contact} hidePhoneButton />
          ),
        },
        {
          field: LeadFieldFormat.PHONE_NUMBER,
          headerName: t('Phone', { ns: 'lead' }),
          cellRenderer: ({ data }: { data: LeadBySequence }) => (
            <LeadActionsCell lead={data.contact} hideEmailButton />
          ),
        },
      ]
    }, [isEnable, sequenceId, t])

    const selectedContactList = useMemo(() => {
      return Object.values(selectedRowsData).map(({ contact }) => contact)
    }, [selectedRowsData])

    /* #region Effects */

    useLayoutEffect(() => {
      if (rowSelected && selections?.length > 1) {
        onClickContact('')
      }
    }, [onClickContact, rowSelected, selections])

    useEffect(() => {
      setIsShowFloatingBar(!isEmpty(selections))
    }, [selections])

    useEffect(() => {
      if (isShowFloatingBar) {
        sendEvent(SequenceEvents.SEQUENCE_SELECT_LEADS_FLOATING_BAR, {
          context: 'SequenceV2SelectLeads',
          action: 'seen',
          sequenceId,
          type: 'Floating bar',
        })
      }
    }, [isShowFloatingBar, sendEvent, sequenceId])

    /* #region Functions */

    const handleClickFloatingBar = (type: string) => {
      sendEvent(SequenceEvents.SEQUENCE_SELECT_LEADS_FLOATING_BAR, {
        context,
        action: 'click',
        sequenceId,
        type,
      })

      dispatch({ type })
    }

    const handleSubmitAddTasks = async (data: CreateTasksFormValuesType) => {
      await handleAddTasks(
        { ...data, assignUserId: currentUser?.id },
        selections
      )
      dispatch({ type: 'TOGGLE_MODAL_ADD_TASK' })
      setSelections([])
    }

    const handleCloseUnassignModal = () => {
      dispatch({ type: 'TOGGLE_MODAL_UNASSIGN' })
      setSelections([])
    }

    const handeEnrichLeads = (enrichmentType?: EnrichmentType) => {
      // noinspection JSIgnoredPromiseFromCall
      enrichPhoneOrEmail(selectedContactList, enrichmentType)
      setSelections([])
    }

    const handleValidateBulkEdit = () => {
      dispatch({ type: 'TOGGLE_MODAL_EDIT_IN_BULK' })
      setSelections([])
    }

    return (
      <div className="flex flex-col overflow-hidden flex-grow relative">
        <Grid
          name={`sequence-added-contacts-table`}
          data={leads || []}
          columns={columns as GridColDef[]}
          checkboxSelection
          rowSelection={GridRowSelectionEnum.MULTIPLE}
          paginationMeta={paginationMeta}
          isLoading={isLoading}
          // autoSizeColumnStrategy={{
          //   type: AutoSizeStrategyEnum.FILL_SET_CONTENTS,
          // }}
          suppressDragLeaveHidesColumns
          // suppressRowClickSelection={false}
          // saveToLocalStorage
          onRowClicked={row => {
            onClickContact(row.contact.id)
          }}
          saveToLocalStorage
          forceRefresh
          hasVirtualization
        />

        {/* Floating Bar */}

        {isShowFloatingBar && (
          <FloatingButtons
            setIsOpenModalAssignLeads={() =>
              handleClickFloatingBar('OPEN_MODAL_ASSIGN_LEADS')
            }
            setIsOpenModalDeleteLeads={() =>
              handleClickFloatingBar('OPEN_MODAL_DELETE_LEADS')
            }
            onClickTransferToSequence={() =>
              handleClickFloatingBar('TOGGLE_MODAL_TRANSFER_TO_SEQUENCE')
            }
            onClickAddTask={() =>
              handleClickFloatingBar('TOGGLE_MODAL_ADD_TASK')
            }
            onClickUnassign={() =>
              handleClickFloatingBar('TOGGLE_MODAL_UNASSIGN')
            }
            onClickEnrich={() => handleClickFloatingBar('TOGGLE_MODAL_ENRICH')}
            onClickEditInBulk={() =>
              handleClickFloatingBar('TOGGLE_MODAL_EDIT_IN_BULK')
            }
          />
        )}

        {/* Floating Bar modals */}

        {state.isOpenModalAssignLeads && (
          <AssignLeadsCommonModal
            isOpen={state.isOpenModalAssignLeads}
            handleClose={() => {
              dispatch({ type: 'CLOSE_MODAL_ASSIGN_LEADS' })
            }}
            isLoading={isLoadingAssignContacts}
            selectedLeadsNames={selections}
            onSubmit={async () => {
              await handleAssignLeads({
                userId: singleUserId,
                contactsIds: selections,
              })
              setSelections([])
              dispatch({ type: 'CLOSE_MODAL_ASSIGN_LEADS' })
            }}
            submitButtonLabel={t('Assign {{count}} leads ', {
              count: selections.length,
            })}
            isContactsCategory={true}
            isDisabledSubmit={!selections.length}
          >
            <SelectInput
              dataTestId={
                idReferentials.sequence.components.TableShowAddedLeads
                  .selectUserInput
              }
              name={
                idReferentials.sequence.components.TableShowAddedLeads
                  .selectUserInput
              }
              isMulti={false}
              onChange={(e: { target: { value: string } }) => {
                const value = e.target.value
                if (!value) {
                  return
                }
                setSingleUserId(value)
              }}
              label={t('Assign user', { ns: 'common' })}
              forceZindex
              options={selectOptions}
            />
          </AssignLeadsCommonModal>
        )}

        <AddTaskToMyLeadsModal
          isOpen={state.isOpenModalAddTask}
          onClose={() => dispatch({ type: 'TOGGLE_MODAL_ADD_TASK' })}
          onSubmit={handleSubmitAddTasks}
          isLoading={isLoadingAddTasks}
        />

        <UnassignMyContactsModal
          isOpen={state.isOpenModalUnassign}
          onClose={handleCloseUnassignModal}
          contactList={selectedContactList}
        />

        <EnrichLeadsModal
          isOpen={state.isOpenModalEnrich}
          onClose={() => dispatch({ type: 'TOGGLE_MODAL_ENRICH' })}
          onClickEnrich={handeEnrichLeads}
          numberOfLeads={selections.length}
          credits={creditBalance}
          costEmailCredits={emailCreditValue}
          costPhoneCredits={phoneCreditValue}
          onClickBuyCredits={() => navigate(privateRoutes.creditsAndPlans.path)}
        />

        {state.isOpenModalEditInBulk && (
          <ModalLeadBulkEdit
            isOpen={state.isOpenModalEditInBulk}
            onClose={() => dispatch({ type: 'TOGGLE_MODAL_EDIT_IN_BULK' })}
            onValidate={handleValidateBulkEdit}
            contactList={selectedContactList}
          />
        )}

        <AddContactsToSequenceDrawer
          isOpen={state.isOpenDrawerTransferToSequence}
          onClose={() =>
            dispatch({ type: 'TOGGLE_MODAL_TRANSFER_TO_SEQUENCE' })
          }
          contactsMap={selectedRowsData}
          isActiveAssignation={false}
          showSettings={false}
          forceExcludeDuplicatesAnotherSequence={false}
          excludedSequenceIds={[sequenceId]}
          redirectAfterAddition={false}
          onContactAdded={() => deleteLeads(selections)}
        />

        <ModalDeleteLeads
          open={state.isOpenModalDeleteLeads}
          onSubmit={() => {
            setSelections([])
            dispatch({ type: 'CLOSE_MODAL_DELETE_LEADS' })
          }}
          onClose={() => {
            dispatch({ type: 'CLOSE_MODAL_DELETE_LEADS' })
          }}
          contactIds={selections}
          sequenceId={sequenceId}
        />
      </div>
    )
  }
)
