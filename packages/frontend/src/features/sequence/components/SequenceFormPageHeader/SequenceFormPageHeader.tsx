import { useContext } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

import { Link } from '@getheroes/ui'
import { Header } from '@internals/components/common/layout/Header/Header'
import { CloseButton } from '@internals/features/sequence/components/SequenceFormPageHeader/components/CloseButton'
import { SequenceHeaderSkeleton } from '@internals/features/sequence/components/SequenceFormPageHeader/components/SequenceHeaderSkeleton'
import { SequenceName } from '@internals/features/sequence/components/SequenceFormPageHeader/components/SequenceName/SequenceName'
import { SequenceStepper } from '@internals/features/sequence/components/SequenceFormPageHeader/components/SequenceStepper'
import { SequenceFormPageStepContext } from '@internals/features/sequence/pages/SequenceFormPage/context/SequenceFormPageStepContext'
import type { Sequence } from '@internals/features/sequence/types/sequence'
import { getSequenceRoute } from '@internals/features/sequence/util/getSequenceRoute'
import { privateRoutes } from '@internals/hooks/useRoute'
import { useFeatureFlag } from '@internals/providers/FeatureFlagProvider/useFeatureFlag'
import { idReferentials } from '@internals/utils/idReferentials'

interface SequenceHeaderProps {
  sequence: Sequence | undefined
}

export const SequenceHeader = ({ sequence }: SequenceHeaderProps) => {
  /* Vars */

  const { isSequenceV2Enabled } = useFeatureFlag()
  const navigate = useNavigate()
  const { t } = useTranslation('sequence')
  const sequenceFormPageStep = useContext(SequenceFormPageStepContext)
  const isLoading = sequenceFormPageStep?.isLoading

  const handleClickNavigateToBeta = (sequenceId: string) => {
    const routeBetaVersion = getSequenceRoute({ sequenceId, stepId: 'leads' })

    const from = {
      url: `${privateRoutes.sequences.path}`,
      label: t('Sequences'),
    }
    navigate(`${routeBetaVersion}${location.search}`, {
      replace: true,
      state: { from },
    })
  }

  if (isLoading || !sequence) {
    return (
      <Header>
        <div className={'flex items-center space-between gap-2 w-full'}>
          <SequenceHeaderSkeleton />

          <CloseButton
            onClick={() => navigate(privateRoutes.sequence.path)}
            dataTestId={
              idReferentials.sequence.components.sequenceHeader.cancelButton
            }
          />
        </div>
      </Header>
    )
  }

  return (
    <Header>
      <div className="flex items-center space-between gap-2 w-full overflow-x-auto">
        <div className="flex-1 flex gap-4">
          {isSequenceV2Enabled && (
            <Link onClick={() => handleClickNavigateToBeta(sequence.id)}>
              beta
            </Link>
          )}

          <SequenceName sequence={sequence} />
        </div>

        <SequenceStepper sequenceId={sequence.id} />
        <CloseButton
          onClick={() => navigate(privateRoutes.sequence.path)}
          dataTestId={
            idReferentials.sequence.components.sequenceHeader.cancelButton
          }
        />
      </div>
    </Header>
  )
}
