import { yupResolver } from '@hookform/resolvers/yup'
import { clsx } from 'clsx'
import { useEffect, useRef } from 'react'
import type { SubmitHandler } from 'react-hook-form'
import { useForm } from 'react-hook-form'
import * as Yup from 'yup'

import { IconButton } from '@getheroes/ui'
import { TextInput } from '@internals/components/common/dataEntry/Text/TextInput'
import { ButtonSize } from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { useUpdateSequence } from '@internals/features/sequence/hooks/useUpdateSequence'
import type { Sequence } from '@internals/features/sequence/types/sequence'
import { idReferentials } from '@internals/utils/idReferentials'

interface EditSequenceNameProps {
  sequence: Sequence
  setIsEditName: (isEditName: boolean) => void
}

export const EditSequenceName = ({
  sequence,
  setIsEditName,
}: EditSequenceNameProps) => {
  /* Hooks */

  const formRef = useRef<HTMLFormElement | null>(null)

  const { updateSequence } = useUpdateSequence()

  const { register, handleSubmit, setValue } = useForm({
    resolver: yupResolver(
      Yup.object({
        name: Yup.string().trim().required(),
      })
    ),
    values: {
      name: sequence?.name ?? '',
    },
  })

  /* Effects */

  useEffect(() => {
    // Adding a setTimeout to trigger event listening after the first Rendering loop
    setTimeout(
      () => document.addEventListener('click', handleClickOutsideForm),
      1
    )

    return () => {
      document.removeEventListener('click', handleClickOutsideForm)
    }
  }, [])

  /* Functions */

  const onSubmit: SubmitHandler<{ name: string }> = ({ name }) => {
    updateSequence(sequence.id, { name })
    setIsEditName(false)
  }

  const handleCancelEdit = () => {
    setValue('name', sequence?.name ?? '')
    setIsEditName(false)
  }

  const handleClickOutsideForm = (event: MouseEvent) => {
    if (formRef.current && !formRef.current.contains(event.target as Node)) {
      handleCancelEdit()
    }
  }

  return (
    <form
      ref={formRef}
      className="flex gap-2 items-center"
      onSubmit={handleSubmit(onSubmit)}
    >
      <h1
        className={clsx('heading-m flex-1')}
        onClick={e => {
          // Double click
          if (e.detail === 2) {
            setIsEditName(true)
          }
        }}
      >
        <TextInput
          {...register('name')}
          isStackingFormfield={false}
          dataTestId={
            idReferentials.sequence.components.sequenceHeader.sequenceNameInput
          }
          className="w-full"
          autoFocus
        />
      </h1>

      <IconButton
        dataTestId={
          idReferentials.sequence.components.sequenceHeader.submitButton
        }
        icon={'Check'}
        variant={'primary'}
        size={'small'}
        type="submit"
      />

      <IconButton
        dataTestId={
          idReferentials.sequence.components.sequenceHeader.submitButton
        }
        variant={'tertiary-outlined'}
        size={ButtonSize.SMALL}
        onClick={handleCancelEdit}
        icon={'Xmark'}
      />
    </form>
  )
}
