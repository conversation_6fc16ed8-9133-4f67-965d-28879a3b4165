import { clsx } from 'clsx'
import { useTranslation } from 'react-i18next'

import { useTracking, SequenceEvents } from '@getheroes/frontend/hooks'
import { IconButton, Tooltip } from '@getheroes/ui'
import type { Sequence } from '@internals/features/sequence/types/sequence'
import { idReferentials } from '@internals/utils/idReferentials'

type ReadOnlySequenceNameProps = {
  sequence: Sequence
  setIsEditName: (isEditName: boolean) => void
  setIsOpenModalDeleteSequence: (isOpenModalDeleteSequence: boolean) => void
}

export const ReadOnlySequenceName = ({
  sequence,
  setIsEditName,
  setIsOpenModalDeleteSequence,
}: ReadOnlySequenceNameProps) => {
  /* Vars */

  const { t } = useTranslation('common')
  const { sendEvent } = useTracking()

  /* Functions */

  const handleEditSequenceName = () => {
    setIsEditName(true)
    sendEvent(SequenceEvents.SEQUENCE_HEADER_CLICK_EDIT_SEQUENCE_NAME)
  }

  const handleDeleteSequence = () => {
    setIsOpenModalDeleteSequence(true)
    sendEvent(SequenceEvents.SEQUENCE_HEADER_CLICK_DELETE_SEQUENCE)
  }

  return (
    <div className="flex items-center gap-2">
      <h1
        className={clsx('heading-m line-clamp-1')}
        onClick={e => e.detail === 2 && handleEditSequenceName()}
      >
        {sequence.name}
      </h1>

      <div className={'flex gap-2'}>
        <Tooltip
          content={t('Edit sequence name', {
            ns: 'sequence',
          })}
          placement={'bottom'}
        >
          <IconButton
            icon={'EditPencil'}
            dataTestId={
              idReferentials.sequence.components.sequenceHeader.editButton
            }
            variant={'tertiary-outlined'}
            size={'small'}
            onClick={handleEditSequenceName}
          />
        </Tooltip>

        <Tooltip
          content={t('Delete sequence', {
            ns: 'sequence',
          })}
          placement={'bottom'}
        >
          <IconButton
            icon={'Trash'}
            dataTestId={
              idReferentials.sequence.components.sequenceHeader.editButton
            }
            variant={'tertiary-outlined'}
            size={'small'}
            onClick={handleDeleteSequence}
          />
        </Tooltip>
      </div>
    </div>
  )
}
