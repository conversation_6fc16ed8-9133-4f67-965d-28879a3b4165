import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

import { ModalDeleteSequence } from '@internals/features/sequence/components/ModalDeleteSequence/ModalDeleteSequence'
import { ReadOnlySequenceName } from '@internals/features/sequence/components/SequenceFormPageHeader/components/SequenceName/CreateSequenceName'
import { EditSequenceName } from '@internals/features/sequence/components/SequenceFormPageHeader/components/SequenceName/EditSequenceName'
import { useDeleteSequence } from '@internals/features/sequence/hooks/useDeleteSequence'
import type { Sequence } from '@internals/features/sequence/types/sequence'
import { privateRoutes } from '@internals/hooks/useRoute'

type SequenceNameProps = {
  sequence: Sequence
}

export const SequenceName = ({ sequence }: SequenceNameProps) => {
  /* Vars */

  const [isEditName, setIsEditName] = useState(false)
  const [isOpenModalDeleteSequence, setIsOpenModalDeleteSequence] =
    useState(false)
  const navigate = useNavigate()

  /* Queries */

  const { deleteSequence, isLoading: isLoadingDeleteSequence } =
    useDeleteSequence()

  /* Functions */

  const handleCloseModalDeleteSequence = () => {
    setIsOpenModalDeleteSequence(false)
  }

  const handleDeleteSequence = async () => {
    await deleteSequence(sequence.id)
    handleCloseModalDeleteSequence()

    navigate(`${privateRoutes.sequences.path}?deleted=true`)
  }

  return (
    <>
      {isEditName ? (
        <EditSequenceName sequence={sequence} setIsEditName={setIsEditName} />
      ) : (
        <ReadOnlySequenceName
          sequence={sequence}
          setIsEditName={setIsEditName}
          setIsOpenModalDeleteSequence={setIsOpenModalDeleteSequence}
        />
      )}
      {/* ----------------------------------------- */}
      {/* ------- Deletion modal ------------------ */}
      {/* ----------------------------------------- */}
      <ModalDeleteSequence
        open={isOpenModalDeleteSequence}
        onClose={handleCloseModalDeleteSequence}
        onCancel={handleCloseModalDeleteSequence}
        onDelete={handleDeleteSequence}
        isLoading={isLoadingDeleteSequence}
      />
    </>
  )
}
