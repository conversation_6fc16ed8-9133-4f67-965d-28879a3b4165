import type { FC } from 'react'
import { useContext, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate } from 'react-router-dom'

import { api } from '@getheroes/frontend/config/api'
import { useTracking, SequenceEvents } from '@getheroes/frontend/hooks'
import { Stepper } from '@internals/components/common/navigation/Stepper/Stepper/Stepper'
import { StepperButtons } from '@internals/components/common/navigation/Stepper/StepperButtons/StepperButtons'
import { useUpdateSequence } from '@internals/features/sequence/hooks/useUpdateSequence'
import { SequenceFormPageStepContext } from '@internals/features/sequence/pages/SequenceFormPage/context/SequenceFormPageStepContext'
import type {
  Sequence,
  SequencePageStepType,
} from '@internals/features/sequence/types/sequence'
import { getSequenceRoute } from '@internals/features/sequence/util/getSequenceRoute'
import { privateRoutes } from '@internals/hooks/useRoute'
import { useAppDispatch } from '@internals/store/store'

interface SequenceStepperProps {
  sequenceId: string
}

export const SequenceStepper: FC<SequenceStepperProps> = ({ sequenceId }) => {
  /* Vars */

  const dispatch = useAppDispatch()
  const location = useLocation()
  const { t } = useTranslation('sequence')
  const {
    steps = [],
    currentStepIndex = 0,
    currentStepKey,
    isLastStep,
    toggleCompleteStep,
    isEditMode,
  } = useContext(SequenceFormPageStepContext) || {}

  const shouldDisplaySuccessToast = true
  const isSequenceCreation = true

  const navigate = useNavigate()

  const { updateSequence, isLoading } = useUpdateSequence(
    shouldDisplaySuccessToast,
    isSequenceCreation
  )

  const { sendEvent } = useTracking()

  /* Memos */

  const nextLabel = useMemo(() => {
    if (isLastStep && isEditMode) {
      return t('Update')
    }

    if (isLastStep) {
      return t('Launch')
    }

    return t('Next')
  }, [isLastStep, t, isEditMode])

  /* Functions */

  const handleLastStepSubmitted = async () => {
    await updateSequence(sequenceId, {
      enable: true,
    } as Pick<Sequence, 'enable'>)

    dispatch(
      api.util.invalidateTags([
        {
          type: 'Sequence',
          id: `SEQUENCE_CONTACT_STEP_LIST`,
        },
      ])
    )

    // Remove all search arguments before routing to the sequence page
    // The search argument "new-sequence" is used to show a discard modal
    // We don't want any discard modal when the user "Launch" the sequence
    navigate({ search: '' })

    setTimeout(() => {
      navigate(privateRoutes.sequences.path)
    }, 200)

    sendEvent(SequenceEvents.SEQUENCE_HEADER_CLICK_LAUNCH)
  }

  const goToStep = (stepKey: SequencePageStepType) => {
    const searchParams = new URLSearchParams(location.search)

    navigate({
      // We force the stepId to be "leads" because it's a legacy code
      pathname: getSequenceRoute({ sequenceId, stepId: 'leads' }),
      search: searchParams.toString(),
    })
  }

  const handleGoToStep = (stepKey: SequencePageStepType) => {
    goToStep(stepKey)
    sendEvent(SequenceEvents.SEQUENCE_HEADER_CLICK_TAB, { stepKey })
  }

  const handlePreviousStep = () => {
    const newStep = currentStepIndex > 0 && steps[currentStepIndex - 1]

    if (newStep) {
      goToStep(newStep.key)
    }
  }

  const handleNextStep = () => {
    // Todo: Ultimately it will have to be called in the formSteps components when the step form will be sent to the back without error
    if (toggleCompleteStep) {
      toggleCompleteStep(currentStepKey, true)
    }

    const newStep = !isLastStep && steps[currentStepIndex + 1]

    if (newStep) {
      goToStep(newStep.key)
      sendEvent(SequenceEvents.SEQUENCE_HEADER_CLICK_NEXT_BUTTON)
    }
  }

  return (
    <div className="flex items-center gap-2">
      <Stepper stepList={steps} onStepClicked={handleGoToStep} />

      <StepperButtons
        stepperName={'edit-or-create-sequence'}
        onPreviousClicked={handlePreviousStep}
        onNextClicked={handleNextStep}
        onLastStepSubmitted={handleLastStepSubmitted}
        stepList={steps}
        stepIndex={currentStepIndex}
        nextLabel={nextLabel}
        isLoadingNextButton={isLoading}
      />
    </div>
  )
}
