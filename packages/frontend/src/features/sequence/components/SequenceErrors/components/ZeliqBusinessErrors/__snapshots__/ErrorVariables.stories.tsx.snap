// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`features/sequence/components/SequenceErrors/components/ZeliqBusinessErrors/ErrorVariables Default smoke-test 1`] = `
<div style="position: fixed; z-index: 9999; inset: 16px; pointer-events: none;">
</div>
<div class="c-error-error-no-sender flex flex-col gap-2 w-full">
  <p class="body-s-regular text-textError">
    This step requires several missing variables, please fill them in.
  </p>
  <form class="flex flex-col gap-2">
    <div class="flex flex-col gap-2">
      <div class="flex flex-col w-full gap-1">
        <div class="flex items-center justify-between gap-1">
          <p class="body-xxs-medium text-textDefault">
            Emails
          </p>
          <div class="flex gap-0.5 items-center">
          </div>
        </div>
        <div class="flex items-start flex-col w-full gap-4">
          <div class="flex items-center w-full ">
            <div data-testid="id-sequences-error-variables-attributes-emails-0-container"
                 class="c-form-field flex-grow c-form-field--no-stacking"
            >
              <div class="c-form-field__form flex items-center relative gap-2">
                <input autocomplete="off"
                       name="emails[0]"
                       placeholder="Add new value"
                       data-testid="id-sequences-error-variables-attributes-emails-0"
                       class="c-input p-1.5"
                       type="email"
                       value
                       style="padding-right: calc(0px + 0.75rem);"
                >
                <div class="c-icon c-icon--end flex items-center gap-2">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="space-x-2 body-xs-regular text-textSubtle">
        </div>
      </div>
      <div class="flex flex-col w-full gap-1">
        <div class="flex items-center justify-between gap-1">
          <p class="body-xxs-medium text-textDefault">
            Phones
          </p>
          <div class="flex gap-0.5 items-center">
          </div>
        </div>
        <div class="flex items-start flex-col w-full gap-4">
          <div class="flex gap-2 items-center w-full ">
            <div class="c-form-field w-full c-form-field--no-stacking">
              <div class="relative flex gap-2 items-center">
                <div class="c-form-field__form c-input-phone pl-1 pr-5 has-error PhoneInput"
                     style="padding-right: calc(0px + 1.25rem);"
                >
                  <div class="PhoneInputCountry">
                    <select name="phones[0]Country"
                            aria-label="Phone number country"
                            class="PhoneInputCountrySelect"
                    >
                      <option value="ZZ">
                        International
                      </option>
                      <option value="AF">
                        Afghanistan
                      </option>
                      <option value="AX">
                        Åland Islands
                      </option>
                      <option value="AL">
                        Albania
                      </option>
                      <option value="DZ">
                        Algeria
                      </option>
                      <option value="AS">
                        American Samoa
                      </option>
                      <option value="AD">
                        Andorra
                      </option>
                      <option value="AO">
                        Angola
                      </option>
                      <option value="AI">
                        Anguilla
                      </option>
                      <option value="AG">
                        Antigua and Barbuda
                      </option>
                      <option value="AR">
                        Argentina
                      </option>
                      <option value="AM">
                        Armenia
                      </option>
                      <option value="AW">
                        Aruba
                      </option>
                      <option value="AC">
                        Ascension Island
                      </option>
                      <option value="AU">
                        Australia
                      </option>
                      <option value="AT">
                        Austria
                      </option>
                      <option value="AZ">
                        Azerbaijan
                      </option>
                      <option value="BS">
                        Bahamas
                      </option>
                      <option value="BH">
                        Bahrain
                      </option>
                      <option value="BD">
                        Bangladesh
                      </option>
                      <option value="BB">
                        Barbados
                      </option>
                      <option value="BY">
                        Belarus
                      </option>
                      <option value="BE">
                        Belgium
                      </option>
                      <option value="BZ">
                        Belize
                      </option>
                      <option value="BJ">
                        Benin
                      </option>
                      <option value="BM">
                        Bermuda
                      </option>
                      <option value="BT">
                        Bhutan
                      </option>
                      <option value="BO">
                        Bolivia
                      </option>
                      <option value="BQ">
                        Bonaire, Sint Eustatius and Saba
                      </option>
                      <option value="BA">
                        Bosnia and Herzegovina
                      </option>
                      <option value="BW">
                        Botswana
                      </option>
                      <option value="BR">
                        Brazil
                      </option>
                      <option value="IO">
                        British Indian Ocean Territory
                      </option>
                      <option value="BN">
                        Brunei Darussalam
                      </option>
                      <option value="BG">
                        Bulgaria
                      </option>
                      <option value="BF">
                        Burkina Faso
                      </option>
                      <option value="BI">
                        Burundi
                      </option>
                      <option value="KH">
                        Cambodia
                      </option>
                      <option value="CM">
                        Cameroon
                      </option>
                      <option value="CA">
                        Canada
                      </option>
                      <option value="CV">
                        Cape Verde
                      </option>
                      <option value="KY">
                        Cayman Islands
                      </option>
                      <option value="CF">
                        Central African Republic
                      </option>
                      <option value="TD">
                        Chad
                      </option>
                      <option value="CL">
                        Chile
                      </option>
                      <option value="CN">
                        China
                      </option>
                      <option value="CX">
                        Christmas Island
                      </option>
                      <option value="CC">
                        Cocos (Keeling) Islands
                      </option>
                      <option value="CO">
                        Colombia
                      </option>
                      <option value="KM">
                        Comoros
                      </option>
                      <option value="CG">
                        Congo
                      </option>
                      <option value="CD">
                        Congo, Democratic Republic of the
                      </option>
                      <option value="CK">
                        Cook Islands
                      </option>
                      <option value="CR">
                        Costa Rica
                      </option>
                      <option value="CI">
                        Cote d'Ivoire
                      </option>
                      <option value="HR">
                        Croatia
                      </option>
                      <option value="CU">
                        Cuba
                      </option>
                      <option value="CW">
                        Curaçao
                      </option>
                      <option value="CY">
                        Cyprus
                      </option>
                      <option value="CZ">
                        Czech Republic
                      </option>
                      <option value="DK">
                        Denmark
                      </option>
                      <option value="DJ">
                        Djibouti
                      </option>
                      <option value="DM">
                        Dominica
                      </option>
                      <option value="DO">
                        Dominican Republic
                      </option>
                      <option value="EC">
                        Ecuador
                      </option>
                      <option value="EG">
                        Egypt
                      </option>
                      <option value="SV">
                        El Salvador
                      </option>
                      <option value="GQ">
                        Equatorial Guinea
                      </option>
                      <option value="ER">
                        Eritrea
                      </option>
                      <option value="EE">
                        Estonia
                      </option>
                      <option value="ET">
                        Ethiopia
                      </option>
                      <option value="FK">
                        Falkland Islands
                      </option>
                      <option value="FO">
                        Faroe Islands
                      </option>
                      <option value="FM">
                        Federated States of Micronesia
                      </option>
                      <option value="FJ">
                        Fiji
                      </option>
                      <option value="FI">
                        Finland
                      </option>
                      <option value="FR">
                        France
                      </option>
                      <option value="GF">
                        French Guiana
                      </option>
                      <option value="PF">
                        French Polynesia
                      </option>
                      <option value="GA">
                        Gabon
                      </option>
                      <option value="GM">
                        Gambia
                      </option>
                      <option value="GE">
                        Georgia
                      </option>
                      <option value="DE">
                        Germany
                      </option>
                      <option value="GH">
                        Ghana
                      </option>
                      <option value="GI">
                        Gibraltar
                      </option>
                      <option value="GR">
                        Greece
                      </option>
                      <option value="GL">
                        Greenland
                      </option>
                      <option value="GD">
                        Grenada
                      </option>
                      <option value="GP">
                        Guadeloupe
                      </option>
                      <option value="GU">
                        Guam
                      </option>
                      <option value="GT">
                        Guatemala
                      </option>
                      <option value="GG">
                        Guernsey
                      </option>
                      <option value="GN">
                        Guinea
                      </option>
                      <option value="GW">
                        Guinea-Bissau
                      </option>
                      <option value="GY">
                        Guyana
                      </option>
                      <option value="HT">
                        Haiti
                      </option>
                      <option value="VA">
                        Holy See (Vatican City State)
                      </option>
                      <option value="HN">
                        Honduras
                      </option>
                      <option value="HK">
                        Hong Kong
                      </option>
                      <option value="HU">
                        Hungary
                      </option>
                      <option value="IS">
                        Iceland
                      </option>
                      <option value="IN">
                        India
                      </option>
                      <option value="ID">
                        Indonesia
                      </option>
                      <option value="IR">
                        Iran
                      </option>
                      <option value="IQ">
                        Iraq
                      </option>
                      <option value="IE">
                        Ireland
                      </option>
                      <option value="IM">
                        Isle of Man
                      </option>
                      <option value="IL">
                        Israel
                      </option>
                      <option value="IT">
                        Italy
                      </option>
                      <option value="JM">
                        Jamaica
                      </option>
                      <option value="JP">
                        Japan
                      </option>
                      <option value="JE">
                        Jersey
                      </option>
                      <option value="JO">
                        Jordan
                      </option>
                      <option value="KZ">
                        Kazakhstan
                      </option>
                      <option value="KE">
                        Kenya
                      </option>
                      <option value="KI">
                        Kiribati
                      </option>
                      <option value="XK">
                        Kosovo
                      </option>
                      <option value="KW">
                        Kuwait
                      </option>
                      <option value="KG">
                        Kyrgyzstan
                      </option>
                      <option value="LA">
                        Laos
                      </option>
                      <option value="LV">
                        Latvia
                      </option>
                      <option value="LB">
                        Lebanon
                      </option>
                      <option value="LS">
                        Lesotho
                      </option>
                      <option value="LR">
                        Liberia
                      </option>
                      <option value="LY">
                        Libya
                      </option>
                      <option value="LI">
                        Liechtenstein
                      </option>
                      <option value="LT">
                        Lithuania
                      </option>
                      <option value="LU">
                        Luxembourg
                      </option>
                      <option value="MO">
                        Macao
                      </option>
                      <option value="MG">
                        Madagascar
                      </option>
                      <option value="MW">
                        Malawi
                      </option>
                      <option value="MY">
                        Malaysia
                      </option>
                      <option value="MV">
                        Maldives
                      </option>
                      <option value="ML">
                        Mali
                      </option>
                      <option value="MT">
                        Malta
                      </option>
                      <option value="MH">
                        Marshall Islands
                      </option>
                      <option value="MQ">
                        Martinique
                      </option>
                      <option value="MR">
                        Mauritania
                      </option>
                      <option value="MU">
                        Mauritius
                      </option>
                      <option value="YT">
                        Mayotte
                      </option>
                      <option value="MX">
                        Mexico
                      </option>
                      <option value="MD">
                        Moldova
                      </option>
                      <option value="MC">
                        Monaco
                      </option>
                      <option value="MN">
                        Mongolia
                      </option>
                      <option value="ME">
                        Montenegro
                      </option>
                      <option value="MS">
                        Montserrat
                      </option>
                      <option value="MA">
                        Morocco
                      </option>
                      <option value="MZ">
                        Mozambique
                      </option>
                      <option value="MM">
                        Myanmar
                      </option>
                      <option value="NA">
                        Namibia
                      </option>
                      <option value="NR">
                        Nauru
                      </option>
                      <option value="NP">
                        Nepal
                      </option>
                      <option value="NL">
                        Netherlands
                      </option>
                      <option value="NC">
                        New Caledonia
                      </option>
                      <option value="NZ">
                        New Zealand
                      </option>
                      <option value="NI">
                        Nicaragua
                      </option>
                      <option value="NE">
                        Niger
                      </option>
                      <option value="NG">
                        Nigeria
                      </option>
                      <option value="NU">
                        Niue
                      </option>
                      <option value="NF">
                        Norfolk Island
                      </option>
                      <option value="KP">
                        North Korea
                      </option>
                      <option value="MK">
                        North Macedonia
                      </option>
                      <option value="MP">
                        Northern Mariana Islands
                      </option>
                      <option value="NO">
                        Norway
                      </option>
                      <option value="OM">
                        Oman
                      </option>
                      <option value="PK">
                        Pakistan
                      </option>
                      <option value="PW">
                        Palau
                      </option>
                      <option value="PS">
                        Palestine
                      </option>
                      <option value="PA">
                        Panama
                      </option>
                      <option value="PG">
                        Papua New Guinea
                      </option>
                      <option value="PY">
                        Paraguay
                      </option>
                      <option value="PE">
                        Peru
                      </option>
                      <option value="PH">
                        Philippines
                      </option>
                      <option value="PL">
                        Poland
                      </option>
                      <option value="PT">
                        Portugal
                      </option>
                      <option value="PR">
                        Puerto Rico
                      </option>
                      <option value="QA">
                        Qatar
                      </option>
                      <option value="RE">
                        Reunion
                      </option>
                      <option value="RO">
                        Romania
                      </option>
                      <option value="RU">
                        Russia
                      </option>
                      <option value="RW">
                        Rwanda
                      </option>
                      <option value="BL">
                        Saint Barthélemy
                      </option>
                      <option value="SH">
                        Saint Helena
                      </option>
                      <option value="KN">
                        Saint Kitts and Nevis
                      </option>
                      <option value="LC">
                        Saint Lucia
                      </option>
                      <option value="MF">
                        Saint Martin (French Part)
                      </option>
                      <option value="PM">
                        Saint Pierre and Miquelon
                      </option>
                      <option value="VC">
                        Saint Vincent and the Grenadines
                      </option>
                      <option value="WS">
                        Samoa
                      </option>
                      <option value="SM">
                        San Marino
                      </option>
                      <option value="ST">
                        Sao Tome and Principe
                      </option>
                      <option value="SA">
                        Saudi Arabia
                      </option>
                      <option value="SN">
                        Senegal
                      </option>
                      <option value="RS">
                        Serbia
                      </option>
                      <option value="SC">
                        Seychelles
                      </option>
                      <option value="SL">
                        Sierra Leone
                      </option>
                      <option value="SG">
                        Singapore
                      </option>
                      <option value="SX">
                        Sint Maarten
                      </option>
                      <option value="SK">
                        Slovakia
                      </option>
                      <option value="SI">
                        Slovenia
                      </option>
                      <option value="SB">
                        Solomon Islands
                      </option>
                      <option value="SO">
                        Somalia
                      </option>
                      <option value="ZA">
                        South Africa
                      </option>
                      <option value="KR">
                        South Korea
                      </option>
                      <option value="SS">
                        South Sudan
                      </option>
                      <option value="ES">
                        Spain
                      </option>
                      <option value="LK">
                        Sri Lanka
                      </option>
                      <option value="SD">
                        Sudan
                      </option>
                      <option value="SR">
                        Suriname
                      </option>
                      <option value="SJ">
                        Svalbard and Jan Mayen
                      </option>
                      <option value="SZ">
                        Swaziland
                      </option>
                      <option value="SE">
                        Sweden
                      </option>
                      <option value="CH">
                        Switzerland
                      </option>
                      <option value="SY">
                        Syria
                      </option>
                      <option value="TW">
                        Taiwan
                      </option>
                      <option value="TJ">
                        Tajikistan
                      </option>
                      <option value="TZ">
                        Tanzania
                      </option>
                      <option value="TH">
                        Thailand
                      </option>
                      <option value="TL">
                        Timor-Leste
                      </option>
                      <option value="TG">
                        Togo
                      </option>
                      <option value="TK">
                        Tokelau
                      </option>
                      <option value="TO">
                        Tonga
                      </option>
                      <option value="TT">
                        Trinidad and Tobago
                      </option>
                      <option value="TA">
                        Tristan da Cunha
                      </option>
                      <option value="TN">
                        Tunisia
                      </option>
                      <option value="TR">
                        Turkey
                      </option>
                      <option value="TM">
                        Turkmenistan
                      </option>
                      <option value="TC">
                        Turks and Caicos Islands
                      </option>
                      <option value="TV">
                        Tuvalu
                      </option>
                      <option value="UG">
                        Uganda
                      </option>
                      <option value="UA">
                        Ukraine
                      </option>
                      <option value="AE">
                        United Arab Emirates
                      </option>
                      <option value="GB">
                        United Kingdom
                      </option>
                      <option value="US">
                        United States
                      </option>
                      <option value="UY">
                        Uruguay
                      </option>
                      <option value="UZ">
                        Uzbekistan
                      </option>
                      <option value="VU">
                        Vanuatu
                      </option>
                      <option value="VE">
                        Venezuela
                      </option>
                      <option value="VN">
                        Vietnam
                      </option>
                      <option value="VG">
                        Virgin Islands, British
                      </option>
                      <option value="VI">
                        Virgin Islands, U.S.
                      </option>
                      <option value="WF">
                        Wallis and Futuna
                      </option>
                      <option value="EH">
                        Western Sahara
                      </option>
                      <option value="YE">
                        Yemen
                      </option>
                      <option value="ZM">
                        Zambia
                      </option>
                      <option value="ZW">
                        Zimbabwe
                      </option>
                    </select>
                    <div aria-hidden="true"
                         class="PhoneInputCountryIcon"
                    >
                      <svg class="PhoneInputCountryIconImg"
                           xmlns="http://www.w3.org/2000/svg"
                           viewbox="0 0 75 50"
                      >
                        <title>
                          International
                        </title>
                        <g class="PhoneInputInternationalIconGlobe"
                           stroke="currentColor"
                           fill="none"
                           stroke-width="2"
                           stroke-miterlimit="10"
                        >
                          <path stroke-linecap="round"
                                d="M47.2,36.1C48.1,36,49,36,50,36c7.4,0,14,1.7,18.5,4.3"
                          >
                          </path>
                          <path d="M68.6,9.6C64.2,12.3,57.5,14,50,14c-7.4,0-14-1.7-18.5-4.3">
                          </path>
                          <line x1="26"
                                y1="25"
                                x2="74"
                                y2="25"
                          >
                          </line>
                          <line x1="50"
                                y1="1"
                                x2="50"
                                y2="49"
                          >
                          </line>
                          <path stroke-linecap="round"
                                d="M46.3,48.7c1.2,0.2,2.5,0.3,3.7,0.3c13.3,0,24-10.7,24-24S63.3,1,50,1S26,11.7,26,25c0,2,0.3,3.9,0.7,5.8"
                          >
                          </path>
                          <path stroke-linecap="round"
                                d="M46.8,48.2c1,0.6,2.1,0.8,3.2,0.8c6.6,0,12-10.7,12-24S56.6,1,50,1S38,11.7,38,25c0,1.4,0.1,2.7,0.2,4c0,0.1,0,0.2,0,0.2"
                          >
                          </path>
                        </g>
                        <path class="PhoneInputInternationalIconPhone"
                              stroke="none"
                              fill="currentColor"
                              d="M12.4,17.9c2.9-2.9,5.4-4.8,0.3-11.2S4.1,5.2,1.3,8.1C-2,11.4,1.1,23.5,13.1,35.6s24.3,15.2,27.5,11.9c2.8-2.8,7.8-6.3,1.4-11.5s-8.3-2.6-11.2,0.3c-2,2-7.2-2.2-11.7-6.7S10.4,19.9,12.4,17.9z"
                        >
                        </path>
                      </svg>
                    </div>
                    <div class="PhoneInputCountrySelectArrow">
                    </div>
                  </div>
                  <input type="tel"
                         autocomplete="tel"
                         data-testid="id-sequences-error-variables-attributes-phones-0"
                         id="phones0"
                         placeholder="Add new value"
                         name="phones[0]"
                         class="PhoneInputInput"
                         value
                  >
                </div>
              </div>
              <p class="text-textError text-size02">
                Phone number is not valid
              </p>
            </div>
          </div>
        </div>
        <div class="space-x-2 body-xs-regular text-textSubtle">
        </div>
      </div>
      <div class="flex flex-col w-full gap-1">
        <div class="flex items-center justify-between gap-1">
          <p class="body-xxs-medium text-textDefault">
            LinkedIn URL
          </p>
          <div class="flex gap-0.5 items-center">
          </div>
        </div>
        <div data-testid="id-sequences-error-variables-attributes-linkedinUrl-container"
             class="c-form-field c-form-field--no-stacking"
        >
          <div class="c-form-field__form flex items-center relative gap-2">
            <input name="linkedinUrl"
                   placeholder="Add new value"
                   data-testid="id-sequences-error-variables-attributes-linkedinUrl"
                   class="c-input p-1.5"
                   type="text"
                   value
                   style="padding-right: calc(0px + 0.75rem);"
            >
          </div>
        </div>
        <div class="space-x-2 body-xs-regular text-textSubtle">
        </div>
      </div>
      <div class="flex flex-col w-full gap-1">
        <div class="flex items-center justify-between gap-1">
          <p class="body-xxs-medium text-textDefault">
            Gender
          </p>
          <div class="flex gap-0.5 items-center">
          </div>
        </div>
        <div data-testid="id-sequences-error-variables-attributes-gender-container"
             class="c-form-field w-full c-form-field--no-stacking"
             style="z-index: var(--zFormFieldForm);"
        >
          <div class="c-form-field__form c-select w-full css-b62m3t-container"
               id="id-sequences-error-variables-attributes-gender-select"
          >
            <span id="react-select-id-sequences-error-variables-attributes-gender-select-instance-id-live-region"
                  class="css-1f43avz-a11yText-A11yText"
            >
            </span>
            <span aria-live="polite"
                  aria-atomic="false"
                  aria-relevant="additions text"
                  class="css-1f43avz-a11yText-A11yText"
            >
            </span>
            <div class="c-select__control css-13cymwt-control">
              <div class="c-select__value-container c-select__value-container--has-value css-art2ul-ValueContainer2">
                <div class="c-select__single-value css-1dimb5e-singleValue">
                  Other
                </div>
                <div class="c-select__input-container css-w9q2zk-Input2"
                     data-value
                >
                  <input class="c-select__input"
                         autocapitalize="none"
                         autocomplete="off"
                         autocorrect="off"
                         id="gender"
                         spellcheck="false"
                         tabindex="0"
                         type="text"
                         aria-autocomplete="list"
                         aria-expanded="false"
                         aria-haspopup="true"
                         aria-invalid="false"
                         role="combobox"
                         value
                         style="color: inherit; background: 0px center; opacity: 1; width: 100%; grid-area: 1 / 2 / auto / auto; font: inherit; min-width: 2px; border: 0px; margin: 0px; outline: 0px; padding: 0px;"
                  >
                </div>
              </div>
              <div class="c-select__indicators css-4xgw5l-IndicatorsContainer2">
                <span class="c-select__indicator-separator css-1u9des2-indicatorSeparator">
                </span>
                <div class="c-select__indicator c-select__dropdown-indicator css-1xc3v61-indicatorContainer"
                     aria-hidden="true"
                >
                  <svg height="20"
                       width="20"
                       viewbox="0 0 20 20"
                       aria-hidden="true"
                       focusable="false"
                       class="css-tj5bde-Svg"
                  >
                    <path d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z">
                    </path>
                  </svg>
                </div>
              </div>
            </div>
            <input name="gender"
                   type="hidden"
                   value="0"
            >
          </div>
        </div>
        <div class="space-x-2 body-xs-regular text-textSubtle">
        </div>
      </div>
      <div class="flex flex-col w-full gap-1">
        <div class="flex items-center justify-between gap-1">
          <p class="body-xxs-medium text-textDefault">
            First name
          </p>
          <div class="flex gap-0.5 items-center">
          </div>
        </div>
        <div data-testid="id-sequences-error-variables-attributes-firstName-container"
             class="c-form-field c-form-field--no-stacking"
        >
          <div class="c-form-field__form flex items-center relative gap-2">
            <input name="firstName"
                   placeholder="Add new value"
                   data-testid="id-sequences-error-variables-attributes-firstName"
                   class="c-input p-1.5"
                   type="text"
                   value
                   style="padding-right: calc(0px + 0.75rem);"
            >
          </div>
        </div>
        <div class="space-x-2 body-xs-regular text-textSubtle">
        </div>
      </div>
    </div>
    <div class="flex justify-end">
      <button type="submit"
              class="c-btn c-btn--tertiary-outlined c-btn--m"
              data-testid="id-sequences-error-variables-save-button"
      >
        <div class="flex items-center gap-x-1 label-s">
          Save
        </div>
      </button>
    </div>
  </form>
</div>
`;
