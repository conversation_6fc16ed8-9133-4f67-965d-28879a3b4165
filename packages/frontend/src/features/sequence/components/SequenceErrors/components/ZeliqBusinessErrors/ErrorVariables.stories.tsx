import type { <PERSON>a, StoryObj } from '@storybook/react'

import { mockedContacts } from '@internals/mocks/contact'
import { mockedFieldList } from '@internals/mocks/field'
import { withProvidersDecorator } from '@internals/utils/storybook/withProvidersDecorator'

import { ErrorVariables } from './ErrorVariables'
import { ContactTitle } from '@internals/components/business/lead/contact/ContactTitle/ContactTitle'

const meta: Meta<typeof ErrorVariables> = {
  component: ErrorVariables,
}

export default meta
type Story = StoryObj<typeof ErrorVariables>

export const WithCarWrapper: Story = {
  decorators: [withProvidersDecorator],
  render: props => (
    <>
      <ErrorVariables {...props} />
    </>
  ),
  args: {
    isLoading: false,
    contact: mockedContacts[0],
    fieldList: mockedFieldList.slice(0, 5),
    onSubmit: data => {
      console.log(data)
    },
    title: (
      <p className="body-s-regular text-textError">
        This step requires several missing variables, please fill them in.
      </p>
    ),
  },
}

export const WithContactCardHeader: Story = {
  decorators: [withProvidersDecorator],
  render: props => (
    <>
      <ErrorVariables {...props} />
    </>
  ),
  args: {
    isLoading: false,
    contact: mockedContacts[0],
    fieldList: mockedFieldList.slice(0, 5),
    onSubmit: data => {
      console.log(data)
    },
    title: (
      <div className="pb-2">
        <ContactTitle contact={mockedContacts[0]} />
      </div>
    ),
  },
}
