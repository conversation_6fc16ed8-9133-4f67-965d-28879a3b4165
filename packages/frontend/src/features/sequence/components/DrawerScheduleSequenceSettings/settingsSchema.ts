import type { InferType } from 'yup'
import { string, object, array, date } from 'yup'

import { SequenceDays } from '@internals/features/sequence/types/sequence'

export const settingsSchema = object({
  scheduleDays: array()
    .of(string().oneOf(Object.values(SequenceDays)).required())
    .min(1)
    .required(),
  scheduleTimeStart: date().required(),
  scheduleTimeEnd: date().required(),
  scheduleTimezone: string().required(),
})
export type SequenceSettings = InferType<typeof settingsSchema>

const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone

export const defaultValues: SequenceSettings = {
  scheduleDays: [],
  scheduleTimeStart: new Date(),
  scheduleTimeEnd: new Date(),
  scheduleTimezone: userTimeZone || '',
}
