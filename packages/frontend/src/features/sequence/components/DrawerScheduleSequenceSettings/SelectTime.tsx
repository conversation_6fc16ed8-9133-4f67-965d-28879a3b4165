import type { UseFormReturn } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { DatePicker } from '@internals/components/common/dataEntry/DatePicker/DatePicker'
import { TimeInput } from '@internals/components/common/dataEntry/DatePicker/Input/TimeInput'
import { idReferentials } from '@internals/utils/idReferentials'

import type { SequenceSettings } from './settingsSchema'

interface Props {
  form: UseFormReturn<SequenceSettings>
  isEnable: boolean
}

/**
 * @deprecated - Use SelectTime from ui-business-components instead
 */
export const SelectTime = ({ form, isEnable }: Props) => {
  const { t } = useTranslation(['sequence', 'common'])
  const { setValue, watch } = form

  const handleTimeChange = (type: 'start' | 'end', date: Date) => {
    setValue(type === 'start' ? 'scheduleTimeStart' : 'scheduleTimeEnd', date)
  }

  return (
    <div className="flex flex-col">
      <div className="heading-s mb-2">{t('Time', { ns: 'common' })}</div>
      <div className="text-textSubtle body-s-regular">
        {t('Select the time you want to allow sequences to run')}
      </div>
      <div className="mt-6 flex gap-4 items-center">
        <div className="flex flex-col">
          <label className="label-m mb-2">
            {t('Between', { ns: 'common' })}
          </label>
          <DatePicker
            name={
              idReferentials.sequence.components.DrawerScheduleSequenceSettings
                .SelectTime.startDate
            }
            dataTestId={
              idReferentials.sequence.components.DrawerScheduleSequenceSettings
                .SelectTime.startDate
            }
            disabled={isEnable}
            id={'RescheduleTaskModal-DatePicker-time'}
            portalId="TaskModal-DatePicker-popper-time"
            popperClassName="TaskModal-DatePicker-popper"
            onChange={date => handleTimeChange('start', date)}
            showTimeSelect
            value={watch('scheduleTimeStart')}
            showTimeSelectOnly
            placeholderText={t('Select a time', { ns: 'common' }) as string}
          >
            <TimeInput
              name={
                idReferentials.sequence.components
                  .DrawerScheduleSequenceSettings.SelectTime.startDateInput
              }
              dataTestId={
                idReferentials.sequence.components
                  .DrawerScheduleSequenceSettings.SelectTime.startDateInput
              }
              disabled={isEnable}
              id={'TaskModal-DatePicker-time-input'}
            />
          </DatePicker>
        </div>
        <div className="flex flex-col">
          <label className="label-m mb-2">{t('And', { ns: 'common' })}</label>
          <DatePicker
            name={
              idReferentials.sequence.components.DrawerScheduleSequenceSettings
                .SelectTime.endDate
            }
            dataTestId={
              idReferentials.sequence.components.DrawerScheduleSequenceSettings
                .SelectTime.endDate
            }
            disabled={isEnable}
            id={'RescheduleTaskModal-DatePicker-time'}
            portalId="TaskModal-DatePicker-popper-time"
            popperClassName="TaskModal-DatePicker-popper"
            onChange={date => handleTimeChange('end', date)}
            showTimeSelect
            value={watch('scheduleTimeEnd')}
            showTimeSelectOnly
            placeholderText={t('Select a time', { ns: 'common' }) as string}
            minDate={watch('scheduleTimeStart')}
          >
            <TimeInput
              id={'TaskModal-DatePicker-time-input'}
              name={
                idReferentials.sequence.components
                  .DrawerScheduleSequenceSettings.SelectTime.endDateInput
              }
              dataTestId={
                idReferentials.sequence.components
                  .DrawerScheduleSequenceSettings.SelectTime.endDateInput
              }
            />
          </DatePicker>
        </div>
      </div>
    </div>
  )
}
