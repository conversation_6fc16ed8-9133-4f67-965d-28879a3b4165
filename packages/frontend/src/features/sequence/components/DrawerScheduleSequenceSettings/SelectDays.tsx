import { upperFirst } from 'lodash'
import type { UseFormReturn } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { getDaysOfWeek } from '@getheroes/frontend/utils'
import { CheckboxButton } from '@internals/components/common/dataEntry/CheckboxButton/CheckboxButton'
import { idReferentials } from '@internals/utils/idReferentials'

import type { SequenceSettings } from './settingsSchema'

interface Props {
  form: UseFormReturn<SequenceSettings>
  isEnable: boolean
}

const weekdays = getDaysOfWeek()

export const SelectDays = ({ form, isEnable }: Props) => {
  const { t } = useTranslation(['sequence', 'common'])
  const { watch, setValue } = form

  const currentDays = watch('scheduleDays')

  const handleChange = ({
    isChecked,
    value,
  }: {
    isChecked: boolean
    value: string | number
  }) => {
    const exist = currentDays.find(day => day === value)
    if (isChecked === true) {
      if (!exist) {
        setValue('scheduleDays', [...currentDays, value.toString()])
      }
    } else {
      if (exist) {
        setValue(
          'scheduleDays',
          currentDays.filter(day => day !== value)
        )
      }
    }
  }
  return (
    <div className="flex flex-col">
      <div
        className="heading-s mb-2"
        data-testid={
          idReferentials.sequence.components.DrawerScheduleSequenceSettings.day
        }
      >
        {t('Days', { ns: 'common' })}
      </div>
      <div className="text-textSubtle body-s-regular">
        {t('Select the days you want allow sequences to run')}
      </div>
      <div className="mt-6 flex gap-4">
        {weekdays.map(day => (
          <CheckboxButton
            disabled={isEnable}
            onChange={handleChange}
            key={day.raw}
            name={upperFirst(day.localized)}
            value={day.raw.toLowerCase()}
            isChecked={currentDays.includes(day.raw.toLowerCase())}
            labelClassName="body-m-medium"
          />
        ))}
      </div>
    </div>
  )
}
