import { yupResolver } from '@hookform/resolvers/yup'
import { format } from 'date-fns-tz'
import { useEffect, useMemo } from 'react'
import type { SubmitHandler } from 'react-hook-form'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import {
  getSequenceId,
  isEnableSequence,
} from '@getheroes/frontend/config/store/selectors/sequenceSelectors'
import { useTracking, SequenceEvents } from '@getheroes/frontend/hooks'
import { getZeliqBlogAddress } from '@getheroes/frontend/utils'
import { Divider, Helper } from '@getheroes/ui'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import {
  ButtonSize,
  ButtonVariant,
} from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { useGetSequence } from '@internals/features/sequence/hooks/useGetSequence'
import { useUpdateSequence } from '@internals/features/sequence/hooks/useUpdateSequence'
import type { SequenceDays } from '@internals/features/sequence/types/sequence'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

import { Header } from './Header'
import { SelectDays } from './SelectDays'
import { SelectTime } from './SelectTime'
import { SelectTimezone } from './SelectTimezone'
import type { SequenceSettings } from './settingsSchema'
import { defaultValues, settingsSchema } from './settingsSchema'

type DrawerScheduleSequenceSettingsProps = {
  onClose: () => void
}

export const DrawerScheduleSequenceSettings = ({
  onClose,
}: DrawerScheduleSequenceSettingsProps) => {
  /* Vars */

  const { t, i18n } = useTranslation(['sequence', 'common'])

  const isEnable = useTypedSelector(isEnableSequence)
  const sequenceId = useTypedSelector(getSequenceId)

  /* Queries */

  const { sequence } = useGetSequence({ sequenceId })

  const { updateSequence, isSuccess } = useUpdateSequence()

  const { sendEvent } = useTracking()

  /* Effects */

  useEffect(() => {
    if (isSuccess) {
      onClose()
    }
  }, [isSuccess, onClose])

  /* Memos */

  const zeliqBlogAddress = useMemo(() => {
    return getZeliqBlogAddress(i18n.language)
  }, [i18n.language])

  const values = useMemo(() => {
    if (!sequence) {
      return defaultValues
    }
    const [startHours, startMinutes] = sequence.scheduleTimeStart.split(':')
    const scheduleTimeStart = new Date()
    scheduleTimeStart.setHours(Number(startHours), Number(startMinutes))

    const [endHours, endMinutes] = sequence.scheduleTimeEnd.split(':')
    const scheduleTimeEnd = new Date()
    scheduleTimeEnd.setHours(Number(endHours), Number(endMinutes))

    return {
      scheduleDays: sequence.scheduleDays,
      scheduleTimeStart,
      scheduleTimeEnd,
      scheduleTimezone: sequence.scheduleTimezone,
    }
  }, [sequence])

  /* Form */

  const form = useForm<SequenceSettings>({
    resolver: yupResolver(settingsSchema),
    defaultValues,
    values,
  })

  const {
    handleSubmit,
    formState: { isValid },
  } = form

  /* Functions */

  const handleSave: SubmitHandler<SequenceSettings> = data => {
    if (isEnable) {
      onClose()
      return
    }

    const scheduleTimeStart = format(data.scheduleTimeStart, 'HH:mm')
    const scheduleTimeEnd = format(data.scheduleTimeEnd, 'HH:mm')

    updateSequence(sequenceId, {
      scheduleDays: data.scheduleDays as SequenceDays[],
      scheduleTimeStart,
      scheduleTimeEnd,
      scheduleTimezone: data.scheduleTimezone,
    })

    sendEvent(SequenceEvents.SEQUENCE_EDIT_SCHEDULE_PAGE_CLICK_SAVE_SCHEDULE)
  }

  return (
    <form onSubmit={handleSubmit(handleSave)} className="h-full p-6">
      <div className="flex flex-col justify-between h-full w-full gap-6">
        <div className="flex flex-col gap-6">
          <Header onClose={onClose} />

          <Helper
            title={t('Feeling lost?', { ns: 'common' })}
            description={t(
              'Check out our guides on our Help Center to learn more about automation and sequences in Zeliq.'
            )}
            icon="QuestionMark"
            endComponent={
              <Button
                dataTestId={idReferentials.common.dataDisplay.helper.button}
                variant={ButtonVariant.TERTIARY}
                link={zeliqBlogAddress}
                size={ButtonSize.SMALL}
                className={'shrink-0'}
              >
                {t('Visit help center')}
              </Button>
            }
          />
        </div>

        <div className="flex flex-col gap-6 overflow-auto">
          <SelectDays form={form} isEnable={isEnable} />

          <Divider />

          <SelectTime form={form} isEnable={isEnable} />

          <Divider />

          <SelectTimezone form={form} isEnable={isEnable} />
        </div>

        <div className="flex flex-col">
          <Divider />

          <div className="flex justify-end pr-14">
            <Button type="submit" disabled={!isValid}>
              {!isEnable
                ? t('Save', { ns: 'common' })
                : t('Close', { ns: 'common' })}
            </Button>
          </div>
        </div>
      </div>
    </form>
  )
}
