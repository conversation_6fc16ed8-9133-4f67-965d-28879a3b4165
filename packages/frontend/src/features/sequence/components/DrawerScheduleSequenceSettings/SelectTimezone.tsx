import type { UseFormReturn } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import type { SelectOption } from '@internals/components/common/dataEntry/Select/SelectInput'
import { SelectInput } from '@internals/components/common/dataEntry/Select/SelectInput'

import type { SequenceSettings } from './settingsSchema'

interface Props {
  form: UseFormReturn<SequenceSettings>
  isEnable: boolean
}

/**
 * @deprecated - Use SelectTimezone from ui-business-components instead
 */
export const SelectTimezone = ({ form, isEnable }: Props) => {
  const { t } = useTranslation('sequence')
  const { register, watch } = form

  const timeZones: SelectOption[] = Intl.supportedValuesOf('timeZone').map(
    timeZone => ({
      label: timeZone.replace(/_/g, ' '),
      value: timeZone,
    })
  )
  const value = timeZones.find(
    timeZone => timeZone.value === watch('scheduleTimezone')
  )

  return (
    <div className="flex flex-col">
      <div>
        <div className="heading-s mb-2">{t('Timezone', { ns: 'common' })}</div>
        <div className="text-textSubtle body-s-regular">
          {t('Choose the timezone you want to use for this sequence')}
        </div>
        <SelectInput
          {...register('scheduleTimezone')}
          disabled={isEnable}
          selectClassname="text-size04"
          defaultValue={value}
          options={timeZones}
        />
      </div>
    </div>
  )
}
