import { useTranslation } from 'react-i18next'

import { Icon } from '@getheroes/ui'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import { ButtonVariant } from '@internals/components/common/navigation/Buttons/Button/Button-export'

export const Header = ({ onClose }: { onClose: () => void }) => {
  const { t } = useTranslation('sequence')

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Icon
          name="ChatBubble"
          backgroundColor="decorative-blue-default"
          color="decorative-blue"
          containerSize="xx-large"
        />

        <div className="flex flex-col justify-center">
          <div className="heading-m">{t('Schedule settings')}</div>
          <div className="text-textSubtle">
            {t('Set allowed days and times to send messages to your leads')}
          </div>
        </div>
      </div>

      <Button variant={ButtonVariant.LINK} onClick={onClose}>
        {t('Cancel & close')}
      </Button>
    </div>
  )
}
