import { useTranslation } from 'react-i18next'

import { Modal } from '@getheroes/ui'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import { ButtonVariant } from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { StepCardSequence } from '@internals/features/sequence/components/StepCardSequence/StepCardSequence'
import type {
  MultipleStep,
  SingleStep,
} from '@internals/features/sequence/types/sequence'

type ConfirmRemoveStepModalProps = {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  stepConfig: SingleStep | MultipleStep
  stepOrderDisplayed: number // step order displayed in the UI (NOT saved in database)
}

export const ConfirmRemoveStepModal = ({
  stepConfig,
  isOpen,
  onClose,
  onConfirm,
  stepOrderDisplayed,
}: ConfirmRemoveStepModalProps) => {
  const { t } = useTranslation('sequence')

  return (
    <Modal open={isOpen} onOpenChange={onClose}>
      <Modal.Content>
        <Modal.Header>{t('Remove step')}</Modal.Header>
        <Modal.Description>
          <div className="flex flex-col gap-5 w-full">
            <p>{t('Are you sure you want to remove this step?')}</p>
            <StepCardSequence
              stepConfig={stepConfig}
              order={stepOrderDisplayed}
            />
          </div>
        </Modal.Description>
        <Modal.Footer>
          <Button
            dataTestId={''}
            className="mr-4"
            onClick={onClose}
            variant={ButtonVariant.TERTIARY}
          >
            {t('Cancel')}
          </Button>
          <Button
            dataTestId={''}
            onClick={onConfirm}
            variant={ButtonVariant.PRIMARY}
          >
            {t('Confirm')}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  )
}
