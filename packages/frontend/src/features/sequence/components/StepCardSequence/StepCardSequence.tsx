import { clsx } from 'clsx'
import { isUndefined } from 'lodash'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'

import { isEnableSequence } from '@getheroes/frontend/config/store/selectors/sequenceSelectors'
import { useTracking, SequenceEvents } from '@getheroes/frontend/hooks'
import { Icon, IconButton, Tag, Tooltip, Card } from '@getheroes/ui'
import { FeatureGatewayPopover } from '@internals/features/featureGateway/components/FeatureGatewayPopover/FeatureGatewayPopover'
import {
  FeatureGatewayImagesEnum,
  useFeatureGatewayImages,
} from '@internals/features/featureGateway/hooks/useFeatureGatewayImages'
import { ConfirmRemoveStepModal } from '@internals/features/sequence/components/StepCardSequence/ConfirmRemoveStepModal'
import { STEP_WAITING_TIME_INITIAL_VALUE_IN_MINUTES } from '@internals/features/sequence/constants/step.constant'
import { useAddSequenceStep } from '@internals/features/sequence/hooks/useAddSequenceStep'
import { useDeleteSequenceStep } from '@internals/features/sequence/hooks/useDeleteSequenceStep'
import type {
  MultipleStep,
  SingleStep,
} from '@internals/features/sequence/types/sequence'
import type {
  EmailStepContent,
  LinkedInMessage,
} from '@internals/features/sequence/types/sequenceStep'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

export type StepCardSequenceProps = {
  stepId?: string
  stepConfig: SingleStep | MultipleStep
  content?: EmailStepContent | LinkedInMessage
  order?: number // step order in the sequence (saved in database)
  subLabel?: string
  isDraggable?: boolean
  isSelected?: boolean
  isDuplicable?: boolean
  isRemovable?: boolean
  onClick?: () => void
  onDuplicateCallback?: () => void
  className?: string
  centerClassName?: string
  showError?: boolean
  isBeta?: boolean
}
export const StepCardSequence = ({
  stepId,
  stepConfig,
  content,
  order,
  isSelected = false,
  isDuplicable = false,
  isRemovable = false,
  onClick = undefined,
  onDuplicateCallback,
  centerClassName,
  showError = false,
}: StepCardSequenceProps) => {
  /* Vars */

  const { t } = useTranslation(['sequence', 'featureGateway'])
  const { sequenceId = '' } = useParams<{
    sequenceId: string
  }>()

  const { type, label, icon, isBeta, subLabel, isFeatureFlag } = stepConfig

  const [showRemoveModal, setShowRemoveModal] = useState(false)

  const isEnable = useTypedSelector(isEnableSequence)

  const featureGatewayImage = useFeatureGatewayImages(
    FeatureGatewayImagesEnum.USE_LINKEDIN_INTEGRATION
  )

  /* Queries */

  const { deleteStepSequence } = useDeleteSequenceStep({ sequenceId })

  const { createSequenceStep } = useAddSequenceStep()

  const { sendEvent } = useTracking()

  /* Functions */

  const handleRemoveStep = (e: React.MouseEvent) => {
    e.stopPropagation()
    setShowRemoveModal(true)
    sendEvent(SequenceEvents.SEQUENCE_ADD_STEP_PAGE_CLICK_DELETE_STEP)
  }

  const handleDuplicateStep = async () => {
    if (isFeatureFlag || stepConfig.isDisabledByFeatureGateway) {
      return
    }

    await createSequenceStep({
      name: label,
      type: type,
      waitingBetweenStep: STEP_WAITING_TIME_INITIAL_VALUE_IN_MINUTES,
      content: content,
    })

    onDuplicateCallback &&
      typeof onDuplicateCallback === 'function' &&
      onDuplicateCallback()

    sendEvent(SequenceEvents.SEQUENCE_ADD_STEP_PAGE_CLICK_DUPLICATE_STEP)
  }

  return (
    <FeatureGatewayPopover
      title={t('Upgrade to {{plan}}', {
        ns: 'featureGateway',
        plan: 'Starter',
      })}
      body={t(
        'This feature is related to LinkedIn. Please upgrade to Starter plan to connect your LinkedIn account with ZELIQ.',
        { ns: 'featureGateway' }
      )}
      image={featureGatewayImage}
      isActive={stepConfig.isDisabledByFeatureGateway || false}
    >
      <Tooltip content={isFeatureFlag ? t('Coming soon') : undefined}>
        <div className="w-full">
          <Card
            isHoverable={
              !isFeatureFlag && !stepConfig.isDisabledByFeatureGateway
            }
            isCondense
            isSelected={isSelected}
            hasError={showError}
          >
            <div
              className="flex items-center gap-2 w-full h-full justify-between"
              onClick={() => {
                if (isFeatureFlag || stepConfig.isDisabledByFeatureGateway) {
                  return
                }
                !isUndefined(onclick) && onClick?.()
              }}
            >
              <div className={'flex w-full items-center gap-4'}>
                {order && (
                  <span
                    className={'heading-s'}
                  >{`${String(order).padStart(2, '0')}.`}</span>
                )}

                <div className={'shrink-0'}>{icon}</div>

                <div className={clsx('flex flex-col mr-4', centerClassName)}>
                  <div className="heading-xs font-bold">{label}</div>
                  <div className="body-xs-regular text-textSubtle">
                    {subLabel}
                  </div>
                </div>
              </div>

              <div className={'flex items-center gap-2'}>
                {isDuplicable && (
                  <IconButton
                    icon={'Copy'}
                    dataTestId={
                      idReferentials.sequence.components.stepCardSequence
                        .duplicateButton
                    }
                    variant={'tertiary-outlined'}
                    onClick={handleDuplicateStep}
                  />
                )}

                {!isEnable && isRemovable && (
                  <IconButton
                    icon={'Trash'}
                    dataTestId={
                      idReferentials.sequence.components.stepCardSequence
                        .removeButton
                    }
                    variant={'tertiary-outlined'}
                    onClick={handleRemoveStep}
                  />
                )}

                {showError && (
                  <Tooltip
                    placement="bottom"
                    content={t('Please fill in email subject and body')}
                  >
                    <Icon name={'WarningCircle'} color={'base-error'} />
                  </Tooltip>
                )}
              </div>

              {!isFeatureFlag && isBeta && (
                <Tooltip
                  content={t(
                    'This feature may contain bugs. Use with caution.'
                  )}
                >
                  <Tag
                    size={'small'}
                    dataTestId={`${stepId}-step`}
                    color={'yellow'}
                    label={t('Beta')}
                  />
                </Tooltip>
              )}
            </div>
          </Card>
        </div>
      </Tooltip>

      {showRemoveModal && (
        <ConfirmRemoveStepModal
          stepConfig={stepConfig}
          stepOrderDisplayed={order as number}
          isOpen={showRemoveModal}
          onClose={() => setShowRemoveModal(false)}
          onConfirm={() => {
            deleteStepSequence({ stepId: stepId as string })
            setShowRemoveModal(false)
          }}
        />
      )}
    </FeatureGatewayPopover>
  )
}
