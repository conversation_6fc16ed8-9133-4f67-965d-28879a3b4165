import { getStepImportSettings } from '@getheroes/frontend/config/store/selectors/sequenceSelectors'
import { LeadStatus } from '@getheroes/shared'
import i18n from '@internals/config/i18n'
import { LeadStatusItem } from '@internals/features/lead/components/LeadStatusItem/LeadStatusItem'
import { getExcludeDuplicatesAnotherSequenceSettingSwitch } from '@internals/features/sequence/components/StepImportSettingsSequence/StepImportSettingsSequence-export'
import { StepImportSettingsSequenceSwitch } from '@internals/features/sequence/components/StepImportSettingsSequence/StepImportSettingsSequenceSwitch'
import { useTypedSelector } from '@internals/store/store'

export type ExcludeList = {
  name: string
  label: string | JSX.Element
  description: string
  isChecked?: boolean
}

const selectLeadStatusList = Object.values(LeadStatus)

const getExcludeListLeadStatusSettingSwitchList = (i18nConfig: any) => [
  ...(selectLeadStatusList.map(status => ({
    name: status,
    label: (
      <>
        {i18nConfig.t('Exclude', { ns: 'sequence' })}
        <LeadStatusItem status={status} className="ml-1" />
      </>
    ),
    description: i18nConfig.t(
      'If the toggle is turned ON (green), all leads with the lead status "{{status}}" will not be imported. If you wish to import these leads, simply turn OFF the toggle. (grey)',
      { ns: 'sequence', status: i18nConfig.t(status, { ns: 'lead' }) }
    ),
  })) as ExcludeList[]),
]

export const StepImportSettingsSequence = (): JSX.Element => {
  const { excludeLeadStatuses, excludeDuplicatesAnotherSequence } =
    useTypedSelector(getStepImportSettings)

  const settingDefinition =
    getExcludeDuplicatesAnotherSequenceSettingSwitch(i18n)

  return (
    <div className="flex flex-col w-full overflow-y-auto mt-2">
      <div className="flex justify-between  flex-col h-full overflow-hidden">
        <div className="flex flex-col w-full h-full overflow-auto">
          <StepImportSettingsSequenceSwitch
            name={settingDefinition.name}
            label={settingDefinition.label}
            description={settingDefinition.description}
            isChecked={excludeDuplicatesAnotherSequence}
          />

          {getExcludeListLeadStatusSettingSwitchList(i18n).map(
            ({ name, label, description }: ExcludeList) => (
              <StepImportSettingsSequenceSwitch
                key={name}
                name={name}
                label={label}
                description={description}
                isChecked={excludeLeadStatuses.includes(name)}
              />
            )
          )}
        </div>
      </div>
    </div>
  )
}
