export const getExcludeDuplicatesAnotherSequenceSettingSwitch = (
  i18nConfig: any
) => ({
  name: 'excludeDuplicatesAnotherSequence',
  label: i18nConfig.t('Exclude leads already in another sequence', {
    ns: 'sequence',
  }),
  description: i18nConfig.t(
    'If the toggle is turned ON (green), all leads that already belong to another "live" or "draft" sequence will not be imported. If you wish to import these leads, simply turn OFF the toggle. (grey)',
    { ns: 'sequence' }
  ),
})
