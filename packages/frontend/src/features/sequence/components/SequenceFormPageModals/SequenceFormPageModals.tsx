import { useBlocker, useParams, useSearchParams } from 'react-router-dom'

import { useTracking, SequenceEvents } from '@getheroes/frontend/hooks'
import { RegexClass } from '@getheroes/shared'
import { DeleteConfirmSequenceDraftModal } from '@internals/features/sequence/components/SequenceFormPageModals/DeleteConfirmSequenceDraftModal/DeleteConfirmSequenceDraftModal'
import { useDeleteSequence } from '@internals/features/sequence/hooks/useDeleteSequence'

const SEQUENCE_URL_BETA = /^\/app\/beta\/sequences\/[a-f0-9-]+\/step\/[^/]+$/

export const SequenceFormPageModals = () => {
  /* Hooks */

  const { deleteSequence } = useDeleteSequence()
  const [searchParams] = useSearchParams()
  const { sequenceId = '' } = useParams()
  const { sendEvent } = useTracking()

  const blockerUnsavedChanges = useBlocker(
    ({ currentLocation, nextLocation }) => {
      // IF we are in the beta version of the sequence page
      if (SEQUENCE_URL_BETA.test(currentLocation.pathname)) {
        return (
          !SEQUENCE_URL_BETA.test(nextLocation.pathname) &&
          searchParams.get('new-sequence') === 'true' &&
          nextLocation.search.includes('deleted=true') === false
        )
      }

      return (
        !RegexClass.SEQUENCE_URL.test(nextLocation.pathname) &&
        !SEQUENCE_URL_BETA.test(nextLocation.pathname) &&
        RegexClass.SEQUENCE_URL.test(currentLocation.pathname) &&
        searchParams.get('new-sequence') === 'true' &&
        nextLocation.search.includes('deleted=true') === false
      )
    }
  )

  /* Handlers */

  const handleConfirmSequence = () => {
    sendEvent(SequenceEvents.SAVE_SEQUENCE_V2_MODAL_CLICK_SAVE_THE_SEQUENCE, {
      context: 'SaveSequenceV2Modal',
      sequenceId,
    })

    if (blockerUnsavedChanges.proceed) {
      blockerUnsavedChanges.proceed()
    }
  }

  const handleDiscardDraft = async () => {
    sendEvent(SequenceEvents.SAVE_SEQUENCE_V2_MODAL_CLICK_QUIT_WITHOUT_SAVING, {
      context: 'SaveSequenceV2Modal',
      sequenceId,
    })

    await deleteSequence(sequenceId)
    if (blockerUnsavedChanges.proceed) {
      blockerUnsavedChanges.proceed()
    }
  }

  const handleCloseConfirmSequenceDraftModal = () => {
    sendEvent(
      SequenceEvents.SAVE_SEQUENCE_V2_MODAL_CLICK_CROSS_SAVE_THE_SEQUENCE,
      {
        context: 'SaveSequenceV2Modal',
        sequenceId,
      }
    )

    if (blockerUnsavedChanges.reset) {
      blockerUnsavedChanges.reset()
    }
  }

  return (
    <DeleteConfirmSequenceDraftModal
      onConfirm={handleConfirmSequence}
      onDiscard={handleDiscardDraft}
      onClose={handleCloseConfirmSequenceDraftModal}
      isOpen={blockerUnsavedChanges.state === 'blocked'}
    />
  )
}
