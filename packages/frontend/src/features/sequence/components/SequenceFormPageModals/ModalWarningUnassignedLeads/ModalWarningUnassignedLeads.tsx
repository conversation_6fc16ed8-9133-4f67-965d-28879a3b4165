import { useTranslation } from 'react-i18next'

import { Helper, Modal } from '@getheroes/ui'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import { ButtonVariant } from '@internals/components/common/navigation/Buttons/Button/Button-export'

interface ModalWarningUnassignedLeadsProps {
  open: boolean
  onSubmit: () => void
  onClose: () => void
}

export const ModalWarningUnassignedLeads = ({
  onSubmit,
  onClose,
  open,
}: ModalWarningUnassignedLeadsProps) => {
  const { t } = useTranslation(['sequence', 'common'])

  return (
    <Modal open={open} onOpenChange={onClose}>
      <Modal.Content>
        <Modal.Header>{t('Unassigned Leads', { ns: 'sequence' })}</Modal.Header>
        <Modal.Description>
          <div className={'flex flex-col gap-6'}>
            <Helper
              icon="WarningCircle"
              description={
                t(
                  'Unassigned leads will be skipped when launching the sequence.'
                ) as string
              }
              color="red-primary"
            />

            <p className="body-s-regular">
              {t('Do you really want to continue to the next step?')}
            </p>

            <div className="flex gap-2 mt-6 items-center justify-end">
              <Button variant={ButtonVariant.TERTIARY} onClick={onClose}>
                {t('Cancel', { ns: 'common' })}
              </Button>

              <Button variant={ButtonVariant.PRIMARY} onClick={onSubmit}>
                {t('Continue', { ns: 'common' })}
              </Button>
            </div>
          </div>
        </Modal.Description>
      </Modal.Content>
    </Modal>
  )
}
