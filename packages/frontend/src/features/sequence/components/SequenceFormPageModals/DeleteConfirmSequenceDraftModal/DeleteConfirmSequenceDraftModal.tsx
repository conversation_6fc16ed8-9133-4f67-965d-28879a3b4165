import { useTranslation } from 'react-i18next'

import { useTracking, SequenceEvents } from '@getheroes/frontend/hooks'
import { Button, Modal } from '@getheroes/ui'
import { idReferentials } from '@internals/utils/idReferentials'

interface DeleteConfirmSequenceDraftModalProps {
  isOpen: boolean
  onConfirm: () => void
  onDiscard: () => void
  onClose: () => void
}

export const DeleteConfirmSequenceDraftModal = ({
  isOpen,
  onConfirm,
  onDiscard,
  onClose,
}: DeleteConfirmSequenceDraftModalProps): JSX.Element => {
  /* Vars */

  const { t } = useTranslation('sequence')
  const { sendEvent } = useTracking()

  /* Functions */

  const handleConfirm = () => {
    sendEvent(SequenceEvents.SAVE_SEQUENCE_MODAL_CLICK_SAVE_THE_SEQUENCE)
    onConfirm()
  }

  const handleDiscard = () => {
    sendEvent(SequenceEvents.SAVE_SEQUENCE_MODAL_CLICK_QUIT_WITHOUT_SAVING)
    onDiscard()
  }

  const handleClose = () => {
    sendEvent(SequenceEvents.SAVE_SEQUENCE_MODAL_CLICK_CROSS_SAVE_THE_SEQUENCE)
    onClose()
  }

  return (
    <Modal open={isOpen} onOpenChange={handleClose}>
      <Modal.Content>
        <Modal.Header>
          {t('Do you want to save your sequence ?', { ns: 'sequence' })}
        </Modal.Header>
        <Modal.Description>
          <div className="flex flex-col gap-4 max-w-xl">
            {t(
              'You are about to exit the sequence currently being created. You can save it for later or not save it.'
            )}
          </div>
        </Modal.Description>
        <Modal.Footer>
          <Button
            dataTestId={
              idReferentials.leads.components
                .ConfirmUnassignContactWithTasksModal.cancelButton
            }
            variant="tertiary-outlined"
            size="large"
            onClick={handleDiscard}
          >
            {t('Quit without saving')}
          </Button>

          <Button
            dataTestId={
              idReferentials.leads.components
                .ConfirmUnassignContactWithTasksModal.confirmButton
            }
            variant="primary"
            size="large"
            onClick={handleConfirm}
          >
            {t('Save the sequence')}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  )
}
