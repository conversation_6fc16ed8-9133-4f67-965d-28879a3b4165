import { t } from 'i18next'
import { useState } from 'react'
import { useLocation } from 'react-router-dom'

import { api } from '@getheroes/frontend/config/api'
import type { Contact } from '@getheroes/shared'
import { ContactSequenceActivities } from '@internals/components/business/ContactSequenceActivities/ContactSequenceActivities'
import { useContactQuickActionsFeatureGateway } from '@internals/components/business/lead/contact/ContactQuickActions/components/QuickActionsButtons/useContactQuickActionsFeatureGateway'
import { Paper } from '@internals/components/common/dataDisplay/Paper/Paper'
import { Popover } from '@internals/components/common/dataDisplay/Popover/Popover'
import { PopoverContent } from '@internals/components/common/dataDisplay/Popover/PopoverContent'
import { PopoverTrigger } from '@internals/components/common/dataDisplay/Popover/PopoverTrigger'
import { Drawer } from '@internals/components/common/navigation/Drawer/Drawer'
import { FeatureGatewayPopover } from '@internals/features/featureGateway/components/FeatureGatewayPopover/FeatureGatewayPopover'
import { AddContactsToSequenceDrawer } from '@internals/features/lead/components/AddContactsToSequenceDrawer/AddContactsToSequenceDrawer'
import { ContactSequenceStepDetails } from '@internals/features/lead/components/ContactSequenceStepCard/ContactSequenceStepCard'
import { ContactSequenceStepProgressList } from '@internals/features/lead/components/ContactSequenceStepProgressList/ContactSequenceStepProgressList'
import { ContactSequenceStepProgressListSkeleton } from '@internals/features/lead/components/ContactSequenceStepProgressList/ContactSequenceStepProgressList.skeleton'
import { useGetContactSequencesSteps } from '@internals/features/lead/hooks/useGetContactSequencesSteps'
import { ModalDeleteLeads } from '@internals/features/sequence/components/ModalDeleteLeads/ModalDeleteLeads'
import type {
  LeadBySequence,
  SequencesContactSteps,
} from '@internals/features/sequence/types/sequence'
import { privateRoutes } from '@internals/hooks/useRoute'
import { useAppDispatch } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'

const CONTACT_SEQUENCE_STEP_CARD_WIDTH = 400
const MAX_HEIGHT_POPOVER_CONTAINER = 500
const NO_SEQUENCE_SELECTED = ''
const SELECTED_CONTACT_SEQUENCE_STEPS_DEFAULT = undefined

export const ContactSequenceStepProgress = ({
  sequenceId,
  contact,
  review,
}: {
  sequenceId?: string
  contact: Contact
  review: LeadBySequence['review']
}) => {
  const dispatch = useAppDispatch()

  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [removingContactFromSequenceId, setRemovingContactFromSequenceId] =
    useState(NO_SEQUENCE_SELECTED)
  const [isOpenDrawerAddToSequence, setIsOpenDrawerAddToSequence] =
    useState(false)
  const [selectedContactSequenceSteps, setSelectedContactSequenceSteps] =
    useState<
      SequencesContactSteps | typeof SELECTED_CONTACT_SEQUENCE_STEPS_DEFAULT
    >(SELECTED_CONTACT_SEQUENCE_STEPS_DEFAULT)

  const {
    featureGatewayPopoverProps,
    isAboveLimit: isNbSequencesCreatedAboveLimit,
  } = useContactQuickActionsFeatureGateway()

  const location = useLocation()

  const {
    steps: contactSequenceStepList,
    isLoading: isLoadingSequenceContactSteps,
    isFetching: isFetchingSequenceContactSteps,
    isUninitialized: isUninitializedSequenceContactSteps,
  } = useGetContactSequencesSteps({
    contactId: contact.id,
    sequenceId,
  })

  const isLoading =
    isLoadingSequenceContactSteps ||
    isFetchingSequenceContactSteps ||
    isUninitializedSequenceContactSteps

  return isNbSequencesCreatedAboveLimit ? (
    <div className="c-contact-sequence-step-progress w-full">
      <FeatureGatewayPopover
        {...featureGatewayPopoverProps}
        isActive={isNbSequencesCreatedAboveLimit}
      >
        <div className={'blur-sm'}>
          {isLoading ? (
            <ContactSequenceStepProgressListSkeleton />
          ) : (
            <ContactSequenceStepProgressList
              contactSequenceStepList={contactSequenceStepList}
              review={review}
              sequenceId={sequenceId}
            />
          )}
        </div>
      </FeatureGatewayPopover>
    </div>
  ) : (
    <div className="c-contact-sequence-step-progress w-full">
      <Popover
        dataTestId={
          idReferentials.leads.components.ContactSequenceStepProgress.popover
        }
        isOpen={
          !isNbSequencesCreatedAboveLimit && contactSequenceStepList.length
            ? isOpen
            : false
        }
        onOpenChange={() => {
          setIsOpen(v => !v)
        }}
        isOpenOnHover={!sequenceId}
        className={'border rounded-m'}
      >
        <PopoverTrigger
          asChild
          onClick={() => {
            if (isNbSequencesCreatedAboveLimit) {
              return
            }
            if (contactSequenceStepList.length && sequenceId) {
              setSelectedContactSequenceSteps(contactSequenceStepList[0])
              return
            }
            if (contactSequenceStepList.length) {
              setIsOpen(!isOpen)
              return
            }
            setIsOpenDrawerAddToSequence(true)
          }}
        >
          <div>
            {isLoading ? (
              <ContactSequenceStepProgressListSkeleton />
            ) : (
              <ContactSequenceStepProgressList
                contactSequenceStepList={contactSequenceStepList}
                review={review}
                sequenceId={sequenceId}
              />
            )}
          </div>
        </PopoverTrigger>

        <PopoverContent className="!cursor-default z-zPopover">
          <div
            style={{
              maxHeight: MAX_HEIGHT_POPOVER_CONTAINER,
              maxWidth: CONTACT_SEQUENCE_STEP_CARD_WIDTH,
            }}
            className="overflow-y-auto p-4 flex flex-col gap-2 bg-backgroundPrimary"
          >
            <h5 className={'heading-xs'}>{t('Sequence')}</h5>
            {contactSequenceStepList.map(contactSequenceSteps => (
              <Paper key={contactSequenceSteps.sequence.id}>
                <ContactSequenceStepDetails
                  key={contactSequenceSteps.sequence.id}
                  contactSequenceSteps={contactSequenceSteps}
                  onClickViewDetails={() => {
                    setSelectedContactSequenceSteps(contactSequenceSteps)
                  }}
                  onClickRemoveLeadFromSequence={() => {
                    setRemovingContactFromSequenceId(
                      contactSequenceSteps.sequence.id
                    )
                  }}
                  contactId={contact.id}
                />
              </Paper>
            ))}
          </div>
        </PopoverContent>
      </Popover>

      <AddContactsToSequenceDrawer
        isOpen={isOpenDrawerAddToSequence}
        onClose={() => {
          setIsOpenDrawerAddToSequence(false)
        }}
        contactsMap={{ [contact.id]: contact }}
        isActiveAssignation={location.pathname.includes(
          privateRoutes.allLeads.path
        )}
      />

      {!!selectedContactSequenceSteps && (
        <Drawer
          open={!!selectedContactSequenceSteps}
          onClose={() =>
            setSelectedContactSequenceSteps(
              SELECTED_CONTACT_SEQUENCE_STEPS_DEFAULT
            )
          }
          classNamePanel="!max-w-xl"
        >
          <ContactSequenceActivities
            contactId={contact.id}
            sequenceId={selectedContactSequenceSteps?.sequence?.id || ''}
            onClose={() =>
              setSelectedContactSequenceSteps(
                SELECTED_CONTACT_SEQUENCE_STEPS_DEFAULT
              )
            }
            onResolveError={() =>
              dispatch(
                api.util.invalidateTags([
                  {
                    type: 'Sequence',
                    id: `SEQUENCE_CONTACT_STEP_LIST`,
                  },
                ])
              )
            }
          />
        </Drawer>
      )}

      <ModalDeleteLeads
        contactIds={[contact.id]}
        open={!!removingContactFromSequenceId}
        onSubmit={() => {
          setRemovingContactFromSequenceId(NO_SEQUENCE_SELECTED)
        }}
        onClose={() => {
          setRemovingContactFromSequenceId(NO_SEQUENCE_SELECTED)
        }}
        sequenceId={removingContactFromSequenceId}
      />
    </div>
  )
}
