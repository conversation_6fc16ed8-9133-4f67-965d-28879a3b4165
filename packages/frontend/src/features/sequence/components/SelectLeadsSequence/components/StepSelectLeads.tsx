import isEmpty from 'lodash/isEmpty'
import orderBy from 'lodash/orderBy'
import type { ChangeEvent } from 'react'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { displayViewByIdThunk } from '@getheroes/frontend/config/store/selectors/leadThunks'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { SequenceEvents, useTracking } from '@getheroes/frontend/hooks'
import {
  CommonLeadFieldEnum,
  ExclusiveContactLeadFieldEnum,
} from '@getheroes/frontend/types'
import type { LeadCategory, Organization } from '@getheroes/shared'
import { But<PERSON>, Tooltip } from '@getheroes/ui'
import { Grid } from '@internals/components/common/dataDisplay/Grid/Grid'
import { useGridContext } from '@internals/components/common/dataDisplay/Grid/GridProvider-export'
import { GridSearchBar } from '@internals/components/common/dataDisplay/Grid/components/GridSearchBar/GridSearchBar'
import type {
  GridColDef,
  RowData,
} from '@internals/components/common/dataDisplay/Grid/types/grid'
import {
  AutoSizeStrategyEnum,
  GridRowSelectionEnum,
} from '@internals/components/common/dataDisplay/Grid/types/grid'
import type { SelectOption } from '@internals/components/common/dataEntry/Select/SelectInput'
import { SelectInput } from '@internals/components/common/dataEntry/Select/SelectInput'
import { Spinner } from '@internals/components/common/feedback/Spinner/Spinner'
import { ErrorBoundary } from '@internals/components/technical/ErrorBoundary/ErrorBoundary'
import { useGetLeadViewsQuery } from '@internals/features/lead/api/leadViewApi'
import { LeadFilters } from '@internals/features/lead/components/LeadsCollectionCard/components/LeadFilters/LeadFilters'
import { useContactColumns } from '@internals/features/lead/hooks/leadsTableColumns/useContactColumns'
import type { LeadPageContext } from '@internals/features/lead/types/genericLeadType'
import { useSelectLeadsSequenceProcess } from '@internals/features/sequence/components/SelectLeadsSequence/hooks/useSelectLeadsSequenceProcess'
import { useStepSequenceLeadsTable } from '@internals/features/sequence/components/SelectLeadsSequence/hooks/useStepSequenceLeadsTable'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'
import { LocalStorageType } from '@internals/types/localStorage'
import { idReferentials } from '@internals/utils/idReferentials'

type StepSelectLeadsProps = {
  contextPage: LeadPageContext
  leadCategory: LeadCategory
}

export const StepSelectLeads = ({
  contextPage,
  leadCategory,
}: StepSelectLeadsProps) => {
  const [isInitialized, setIsInitialized] = useState(false)

  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const { columns: gridCols } = useContactColumns()
  const {
    selections,
    gridApi,
    setSelections,
    lastSelectionEventSource,
    setLastSelectionEventSource,
    pageSize,
    name,
  } = useGridContext()
  const dispatch = useAppDispatch()
  const {
    setSelectedView,
    setSelectedLeads,
    setSelectedAllLeadsInView,
    selectLeadsSequenceState,
  } = useSelectLeadsSequenceProcess()
  const { t } = useTranslation('sequence')

  const {
    data,
    isLoading: isLoadingSearch,
    isFetching: isFetchingSearch,
  } = useStepSequenceLeadsTable({
    contextPage,
    leadCategory,
  })

  const {
    data: leadViews,
    isSuccess: isSuccessGetLeadViews,
    isLoading,
  } = useGetLeadViewsQuery({
    organizationId,
  })

  const { sendEvent } = useTracking()

  // ******************************************************
  // UseEffect Section
  // ******************************************************
  useEffect(() => {
    // Initial state, if I'm navigating between 2 step
    if (
      selectLeadsSequenceState.isAddAllLeadsInView &&
      !isEmpty(gridApi) &&
      !isInitialized
    ) {
      setLastSelectionEventSource({
        source: 'apiSelectAll',
        type: 'onInit',
      })
      setIsInitialized(true)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gridApi, isInitialized])

  // Used to set the first view as default
  useEffect(() => {
    if (leadViews && isSuccessGetLeadViews) {
      setSelectedView(
        selectLeadsSequenceState?.selectedViewId ?? leadViews.items[0].id
      )
      dispatch(
        displayViewByIdThunk({
          viewId:
            selectLeadsSequenceState?.selectedViewId ?? leadViews.items[0].id,
        })
      ).unwrap()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    leadViews,
    isSuccessGetLeadViews,
    selectLeadsSequenceState?.selectedViewId,
  ])

  // Used to set the selected leads into the stepper context
  useEffect(() => {
    setSelectedLeads(selections)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selections])

  // Used to toggle the select all leads in view if used clicked on the banner button "Select all leads in view"
  useEffect(() => {
    if (!isEmpty(gridApi)) {
      setSelectedAllLeadsInView(lastSelectionEventSource.isChecked)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lastSelectionEventSource.isChecked])

  useEffect(() => {
    // Add banner select all to the grid, on the top
    if (!isEmpty(gridApi) && data?.meta?.totalItems > 0) {
      gridApi.updateGridOptions({
        pinnedTopRowData:
          selections.length >= pageSize && data?.meta?.totalPages > 1
            ? [
                {
                  id: 'GridSelectAllBanner',
                  fullWidth: true,
                  totalItems: data?.meta?.totalItems,
                },
              ]
            : [],
      })
    }
  }, [
    gridApi,
    lastSelectionEventSource.isHeaderChecked,
    data?.meta?.totalItems,
    data?.meta?.totalPages,
    selections.length,
  ])

  // ******************************************************
  // UI data section
  // ******************************************************

  const selectViewOptions = useMemo(() => {
    return leadViews?.items?.map(view => ({
      value: view.id,
      label: t(view.name),
    }))
  }, [leadViews?.items, t])
  const gridColumns = useMemo(() => {
    const columnState = JSON.parse(
      localStorage.getItem(LocalStorageType.GRID_CACHE) as string
    )?.[name]
    const columns = gridCols
      .filter(
        (column: GridColDef) =>
          [ExclusiveContactLeadFieldEnum.STATUS].includes(
            column?.field as ExclusiveContactLeadFieldEnum
          ) ||
          [ExclusiveContactLeadFieldEnum.LAST_NAME].includes(
            column?.field as ExclusiveContactLeadFieldEnum
          ) ||
          [ExclusiveContactLeadFieldEnum.JOB_TITLE].includes(
            column?.field as ExclusiveContactLeadFieldEnum
          ) ||
          [CommonLeadFieldEnum.LINKEDIN_URL].includes(
            column?.field as CommonLeadFieldEnum
          ) ||
          [ExclusiveContactLeadFieldEnum.COMPANY_NAME].includes(
            column?.field as ExclusiveContactLeadFieldEnum
          ) ||
          [ExclusiveContactLeadFieldEnum.ENGAGEMENT_SCORING_PRIORITY].includes(
            column?.field as ExclusiveContactLeadFieldEnum
          ) ||
          ['data'].includes(column?.field as string) ||
          ['Sequences status'].includes(column?.field as string)
      )
      .map((column, index) => {
        const columnOrder = columnState?.findIndex(
          item => item.colId === column.colId
        )
        return {
          ...column,
          suppressMovable: true,
          cellRendererParams: {
            order: columnOrder && columnOrder !== -1 ? columnOrder : index,
          },
        }
      })
    return orderBy(columns, ['cellRendererParams.order'], ['asc'])
  }, [gridCols, name])

  if (isLoading || !isSuccessGetLeadViews || !selectViewOptions) {
    return (
      <div className={'flex justify-center items-center h-full w-full'}>
        <Spinner />
      </div>
    )
  }

  const handleSelectViewChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const leadView = e.target as unknown as SelectOption
    const leadViewId = leadView.value as string
    setSelectedView(leadViewId)
    setSelections([])
    dispatch(displayViewByIdThunk({ viewId: leadViewId })).unwrap()
    sendEvent(
      SequenceEvents.SEQUENCE_ADD_LEADS_DRAWER_CHANGE_LIST_SELECTOR_VALUE,
      {
        value: leadView.label as string,
      }
    )
  }

  return (
    <div className={'c-step-select-views flex flex-col h-full gap-2'}>
      <div className={'flex w-full gap-2 items-center'}>
        <SelectInput
          className={'min-w-48'}
          defaultValue={
            selectViewOptions.find(
              view => view.value === selectLeadsSequenceState.selectedViewId
            ) ?? selectViewOptions[0]
          }
          onChange={handleSelectViewChange}
          dataTestId="c-step-select-views-select-sequence-view"
          name={'select-sequence-view'}
          options={selectViewOptions}
          isStackingFormfield={false}
          onFocus={() =>
            sendEvent(
              SequenceEvents.SEQUENCE_ADD_LEADS_DRAWER_CLICK_LIST_SELECTOR
            )
          }
        />

        <Tooltip content={t('Temporarily disabled')} placement="bottom">
          <GridSearchBar className="w-full flex-grow" disabled />
        </Tooltip>

        {/* Filters ----------------------- */}
        <Tooltip content={t('Temporarily disabled')} placement="bottom">
          <ErrorBoundary
            fallbackRender={() => (
              <Button
                dataTestId={
                  idReferentials.leads.myLeadsPage.header.errorBoundaryButton
                }
                iconLeft={'InfoCircle'}
                disabled
              >
                {t('Something went wrong')}
              </Button>
            )}
          >
            <LeadFilters disabled />
          </ErrorBoundary>
        </Tooltip>
      </div>
      <Grid
        forceRefresh
        checkboxSelection
        modeSelection={'banner'}
        rowSelectionIds={
          selectLeadsSequenceState.isAddAllLeadsInView
            ? Object.values(data?.items || {}).map(
                (contact: RowData) => contact?.id
              )
            : (selectLeadsSequenceState.selectedContactIdList as string[])
        }
        name={`sequence-add-${contextPage}-contacts-table`}
        data={Object.values(data?.items || {})}
        columns={gridColumns as GridColDef[]}
        rowSelection={GridRowSelectionEnum.MULTIPLE}
        paginationMeta={data?.meta}
        isLoading={isLoadingSearch || isFetchingSearch}
        forceSizeColumnsToFit
        hasWindowResize
        autoSizeColumnStrategy={{
          type: AutoSizeStrategyEnum.FILL_GRID_WIDTH,
        }}
      />
    </div>
  )
}
