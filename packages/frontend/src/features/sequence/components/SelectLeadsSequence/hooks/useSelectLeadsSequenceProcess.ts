import { createContext, useContext } from 'react'

import type { Step } from '@internals/components/common/navigation/Stepper/Stepper.type'

/************************
 ** Types & Interfaces **
 ************************/

export enum SelectLeadsSequenceStepActionType {
  // First step actions
  SET_SELECTED_VIEW = 'SELECT_VIEW', // used for the lead view <Select />
  SET_SELECT_SOME_LEADS = 'SELECT_SOME_LEADS', // used for the table checkboxes
  SET_SELECT_ALL_LEADS_IN_VIEW = 'SELECT_ALL_LEADS_IN_VIEW', // used when the user click on the "Select all leads in view" button

  // Internal actions
  SET_DISABLE_UNSELECT_LEADS_STEP = 'SET_DISABLE_UNSELECT_LEADS_STEP',
  SET_ENABLE_UNSELECT_LEADS_STEP = 'SET_ENABLE_UNSELECT_LEADS_STEP',
  SET_STEP_LIST = 'SET_STEP_LIST',
}

export enum SelectLeadsSequenceStepKeyEnum {
  SELECT_LEADS_IN_VIEW = 'select_leads_in_view',
  UNSELECT_LEADS = 'unselect_leads',
}

export type LeadsSelectionInCreationState = {
  currentStepList: Step<SelectLeadsSequenceStepKeyEnum>[]
  selectedViewId: string | null
  selectedContactIdList: Array<string> | null
  isAddAllLeadsInView: boolean
}

export interface SelectLeadsSequenceProcessAction {
  type: SelectLeadsSequenceStepActionType
  payload?: Partial<LeadsSelectionInCreationState> | null
}

type SelectLeadsSequenceProcessContextInterface = {
  // generic stepper state
  currentStepKey: string

  // specific state for this process
  selectLeadsSequenceState: LeadsSelectionInCreationState

  // generic stepper actions
  goToStep: (stepKey: SelectLeadsSequenceStepKeyEnum) => void
  goToNextStep: () => void
  goToPreviousStep: () => void

  // specific actions for this process
  setSelectedView: (selectedView: string) => void
  setSelectedLeads: (selectedContactIdList: Array<string>) => void
  setSelectedAllLeadsInView: (isAddAllLeadsInView: boolean) => void
}

/**********************
 ** Context **
 **********************/
export const SelectLeadsSequenceProcessContext = createContext<
  SelectLeadsSequenceProcessContextInterface | undefined
>(undefined)

/**********************
 ** Context hook **
 **********************/
export const useSelectLeadsSequenceProcess = () => {
  const context = useContext(SelectLeadsSequenceProcessContext)
  if (context === undefined) {
    throw new Error(
      'useSelectLeadsSequenceProcess must be used within a SelectLeadsSequenceProcessProvider'
    )
  }
  return context
}
