import { useMemo } from 'react'

import type { LeadCategory } from '@getheroes/shared'
import { useAllLeadsTable } from '@internals/features/lead/hooks/searchLeads/useAllLeadsTable'
import { useMyLeadsTable } from '@internals/features/lead/hooks/searchLeads/useMyLeadsTable'
import { LeadPageContext } from '@internals/features/lead/types/genericLeadType'

type UseStepSequenceLeadsTableProps = {
  contextPage: LeadPageContext
  leadCategory: LeadCategory
}

export const useStepSequenceLeadsTable = ({
  contextPage,
  leadCategory,
}: UseStepSequenceLeadsTableProps) => {
  const {
    data: dataAllLeads,
    isLoading: isLoadingAllLeads,
    isFetching: isFetchingAllLeads,
    isUninitialized: isUninitializedAllLeads,
  } = useAllLeadsTable({
    skip: contextPage === LeadPageContext.MY_LEADS,
    leadCategory,
  })

  const {
    data: dataMyLeads,
    isLoading: isLoadingMyLeads,
    isFetching: isFetchingMyLeads,
    isUninitialized: isUninitializedMyLeads,
  } = useMyLeadsTable({
    skip: contextPage === LeadPageContext.ALL_LEADS,
    leadCategory,
  })

  return useMemo(() => {
    if (contextPage === LeadPageContext.MY_LEADS) {
      return {
        data: dataMyLeads,
        isLoading: isLoadingMyLeads,
        isFetching: isFetchingMyLeads,
        isUninitialized: isUninitializedMyLeads,
      }
    }

    return {
      data: dataAllLeads,
      isLoading: isLoadingAllLeads,
      isFetching: isFetchingAllLeads,
      isUninitialized: isUninitializedAllLeads,
    }
  }, [
    contextPage,
    dataAllLeads,
    isLoadingAllLeads,
    isFetchingAllLeads,
    isUninitializedAllLeads,
    dataMyLeads,
    isLoadingMyLeads,
    isFetchingMyLeads,
    isUninitializedMyLeads,
  ])
}
