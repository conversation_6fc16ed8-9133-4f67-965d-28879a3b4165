import { clsx } from 'clsx'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'

import { getStepImportSettings } from '@getheroes/frontend/config/store/selectors/sequenceSelectors'
import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice'
import {
  clearLeadSlice,
  setCurrentPageContext,
} from '@getheroes/frontend/config/store/slices/leadSlice'
import {
  setStepImportSettings,
  setViews,
} from '@getheroes/frontend/config/store/slices/sequenceSlice'
import { useTracking, SequenceEvents } from '@getheroes/frontend/hooks'
import type { LeadCategory } from '@getheroes/shared'
import { Cancel } from '@internals/assets/svg/icons/Cancel'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import {
  ButtonSize,
  ButtonVariant,
} from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { Stepper } from '@internals/components/common/navigation/Stepper/Stepper/Stepper'
import { StepperButtons } from '@internals/components/common/navigation/Stepper/StepperButtons/StepperButtons'
import { ErrorBoundary } from '@internals/components/technical/ErrorBoundary/ErrorBoundary'
import { useCurrentLeadsFieldsQuery } from '@internals/features/lead/hooks/leadFields/useCurrentLeadsFieldsQuery'
import { LeadPageContext } from '@internals/features/lead/types/genericLeadType'
import { StepSelectLeads } from '@internals/features/sequence/components/SelectLeadsSequence/components/StepSelectLeads'
import {
  SelectLeadsSequenceStepKeyEnum,
  useSelectLeadsSequenceProcess,
} from '@internals/features/sequence/components/SelectLeadsSequence/hooks/useSelectLeadsSequenceProcess'
import { StepImportSettingsSequence } from '@internals/features/sequence/components/StepImportSettingsSequence/StepImportSettingsSequence'
import { useAddLeadsSequence } from '@internals/features/sequence/hooks/useAddLeadsSequence'
import { useUpdateSequence } from '@internals/features/sequence/hooks/useUpdateSequence'
import { ErrorPage } from '@internals/pages/ErrorPage/ErrorPage'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'

import './SelectLeadsSequenceStepper.scoped.scss'
import { useToast } from '@getheroes/ui'

type SelectLeadsSequenceStepperProps = {
  onClose: () => void
  onAddLeadsSuccess?: (contactIdsAdded: string[]) => void
  typeDrawer: LeadPageContext
  leadCategory: LeadCategory
}

const SHOULD_DISPLAY_UPDATE_SEQUENCE_SUCCESS_TOAST = false

export const SelectLeadsSequenceStepper = ({
  onClose,
  onAddLeadsSuccess,
  typeDrawer,
  leadCategory,
}: SelectLeadsSequenceStepperProps) => {
  /* Vars */

  const { t } = useTranslation('sequence')
  const [isApiCallInProgress, setIsApiCallInProgress] = useState(false)
  const { sendEvent } = useTracking()
  const { sequenceId = '' } = useParams<{ sequenceId: string }>()

  const {
    goToStep,
    goToPreviousStep,
    goToNextStep,
    currentStepKey,
    selectLeadsSequenceState,
  } = useSelectLeadsSequenceProcess()

  const currentUser = useTypedSelector(selectCurrentUser)
  const dispatch = useAppDispatch()
  const stepImportSettings = useTypedSelector(getStepImportSettings)
  const { createToast } = useToast()

  /* Queries */

  useCurrentLeadsFieldsQuery()

  const { addLeadsSequence, isLoadingAddLeadsSequence } = useAddLeadsSequence()
  const { updateSequence, isLoading: isLoadingUpdateSequence } =
    useUpdateSequence(SHOULD_DISPLAY_UPDATE_SEQUENCE_SUCCESS_TOAST)

  /* Memos */

  const stepIndex = useMemo(() => {
    return selectLeadsSequenceState.currentStepList.findIndex(
      step => step.key === currentStepKey
    )
  }, [currentStepKey, selectLeadsSequenceState.currentStepList])

  /* Effects */

  // Allow us to display a toast if the API call is long (more than 10s)
  // Note : if user add lot of leads, the API call can be long
  useEffect(() => {
    let timeoutId: NodeJS.Timeout
    if (isApiCallInProgress) {
      timeoutId = setTimeout(() => {
        createToast({
          type: 'main',
          message: t('This might take time if many leads are selected.'),
        })
      }, 10000)
    } else {
      clearTimeout(timeoutId)
    }

    return () => clearTimeout(timeoutId)
  }, [createToast, isApiCallInProgress, t])

  useEffect(() => {
    if (currentUser) {
      dispatch(
        setCurrentPageContext({
          page: typeDrawer,
          userId: currentUser?.id,
        })
      )
    }
    return () => {
      dispatch(setViews([]))
      dispatch(setStepImportSettings(['excludeDuplicatesAnotherSequence']))

      dispatch(clearLeadSlice())
    }
  }, [currentUser, dispatch, typeDrawer])

  /* Functions */

  const handleSubmitLastStep = async () => {
    setIsApiCallInProgress(true)

    // 1 - Update the sequence with the settings
    await updateSequence(sequenceId, {
      excludeDuplicatesAnotherSequence:
        stepImportSettings.excludeDuplicatesAnotherSequence,
      excludeLeadStatuses: stepImportSettings.excludeLeadStatuses,
    })

    // 2 - Add the leads to the sequence
    // Note : API constraint : if we have at least one contact, we don't need to send the views id (API bug)
    // Note : if we send more than 1000 contacts, the API will return pending status #bug
    const data = await addLeadsSequence({
      contacts: !selectLeadsSequenceState.isAddAllLeadsInView
        ? (selectLeadsSequenceState.selectedContactIdList as string[])
        : [],
      views: !selectLeadsSequenceState.isAddAllLeadsInView
        ? []
        : [selectLeadsSequenceState.selectedViewId as string],
      sequenceId,
      onlyMine: typeDrawer === LeadPageContext.MY_LEADS,
    })

    setIsApiCallInProgress(false)
    onClose()
    if (onAddLeadsSuccess) {
      onAddLeadsSuccess(data?.contactIdsAdded || [])
    }

    sendEvent(SequenceEvents.SEQUENCE_ADD_LEADS_DRAWER_CLICK_ADD_LEADS)
  }

  const handleNextClicked = () => {
    goToNextStep()
    sendEvent(SequenceEvents.SEQUENCE_ADD_LEADS_DRAWER_CLICK_NEXT)
  }

  const handlePreviousClicked = () => {
    goToPreviousStep()
    sendEvent(SequenceEvents.SEQUENCE_ADD_LEADS_DRAWER_CLICK_PREVIOUS)
  }

  const handleGoStep = (stepKey: SelectLeadsSequenceStepKeyEnum) => {
    goToStep(stepKey)
    sendEvent(SequenceEvents.SEQUENCE_ADD_LEADS_DRAWER_CLICK_SELECT_TAB, {
      step: stepKey,
    })
  }

  return (
    <div
      className={clsx(
        'c-select-leads-sequence flex flex-col h-full overflow-hidden p-3'
      )}
    >
      <div className={clsx('flex flex-col h-full gap-2')}>
        <div className="flex items-center w-full gap-2">
          <h2 className={'heading-m whitespace-nowrap'}>{t('Add leads')}</h2>
          <div className={clsx('flex w-full justify-between')}>
            <Stepper
              stepList={selectLeadsSequenceState.currentStepList}
              onStepClicked={handleGoStep}
            />

            <div className={'flex gap-2'}>
              <StepperButtons
                stepperName={`${typeDrawer}-add-leads-sequence`}
                stepList={selectLeadsSequenceState.currentStepList}
                onPreviousClicked={handlePreviousClicked}
                onNextClicked={handleNextClicked}
                onLastStepSubmitted={handleSubmitLastStep}
                isLoadingNextButton={
                  isLoadingUpdateSequence || isLoadingAddLeadsSequence
                }
                stepIndex={stepIndex}
              />

              <Button
                size={ButtonSize.SMALL}
                variant={ButtonVariant.TERTIARY_OUTLINED}
                dataTestId={'c-select-leads-sequence-close-button'}
                onClick={() => onClose()}
              >
                <Cancel />
              </Button>
            </div>
          </div>
        </div>

        {/* ---------------------------------------------- */}
        {/* ---- 1st step : Select Views ----------------- */}
        {/* ---------------------------------------------- */}
        {currentStepKey ===
          SelectLeadsSequenceStepKeyEnum.SELECT_LEADS_IN_VIEW && (
          <ErrorBoundary fallbackRender={() => <ErrorPage />}>
            <StepSelectLeads
              contextPage={typeDrawer}
              leadCategory={leadCategory}
            />
          </ErrorBoundary>
        )}

        {/* ---------------------------------------------- */}
        {/* ---- 2nd step : Unselect leads --------------- */}
        {/* ---------------------------------------------- */}
        {currentStepKey === SelectLeadsSequenceStepKeyEnum.UNSELECT_LEADS && (
          <ErrorBoundary fallbackRender={() => <ErrorPage />}>
            <StepImportSettingsSequence />
          </ErrorBoundary>
        )}
      </div>
    </div>
  )
}
