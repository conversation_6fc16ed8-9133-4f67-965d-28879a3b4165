import { useTranslation } from 'react-i18next'

import { useTracking, SequenceEvents } from '@getheroes/frontend/hooks'
import { Button, Modal } from '@getheroes/ui'
import { idReferentials } from '@internals/utils/idReferentials'

interface ModalDeleteSequenceProps {
  open: boolean
  onClose: () => void
  onCancel: () => void
  onDelete: () => void
  isLoading?: boolean
}

export const ModalDeleteSequence = ({
  open,
  onClose,
  onCancel,
  onDelete,
  isLoading = false,
}: ModalDeleteSequenceProps) => {
  const { t } = useTranslation(['sequence', 'common'])

  const { sendEvent } = useTracking()

  /* Functions */

  const handleDelete = () => {
    sendEvent(SequenceEvents.SEQUENCE_DELETE_CONFIRM_CLICK_DELETE_SEQUENCE)
    onDelete()
  }

  const handleCancel = () => {
    sendEvent(
      SequenceEvents.SEQUENCE_DELETE_CONFIRM_CLICK_CANCEL_DELETE_SEQUENCE
    )
    onCancel()
  }

  return (
    <Modal open={open} onOpenChange={onClose}>
      <Modal.Content>
        <Modal.Header>{t('Delete sequence')}</Modal.Header>
        <Modal.Description>
          {t('You’re about to delete this sequence. Are you sure?')}
        </Modal.Description>
        <Modal.Footer>
          <Button
            fullWidth
            dataTestId={
              idReferentials.sequence.components.ModalDeleteSequence
                .cancelButton
            }
            variant={'tertiary-outlined'}
            onClick={handleCancel}
            disabled={isLoading}
          >
            {t('Cancel', { ns: 'common' })}
          </Button>
          <Button
            fullWidth
            dataTestId={
              idReferentials.sequence.components.ModalDeleteSequence
                .deleteButton
            }
            variant={'danger'}
            onClick={handleDelete}
            disabled={isLoading}
          >
            {t('Delete sequence')}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  )
}
