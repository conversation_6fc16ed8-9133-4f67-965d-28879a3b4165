import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import type { LeadExportableFieldDTO } from '@getheroes/shared'
import { LeadFieldExportCategoryEnum } from '@getheroes/shared'
import type { OptionType } from '@getheroes/ui'
import { Select, Tag, Typography } from '@getheroes/ui'
import { LeadExportColumnGroup } from '@internals/features/lead-export/components/LeadExportColumnGroup'
import { LeadExportOption } from '@internals/features/lead-export/enum/LeadExportOption.enum'
import { capitalizeFirstLetter } from '@internals/utils/string'

interface ExportColumnsProps {
  categories: LeadFieldExportCategoryEnum[]
  fields: LeadExportableFieldDTO[]
}

export const LeadExportColumns = ({
  categories,
  fields,
}: ExportColumnsProps) => {
  const { t } = useTranslation('export')

  const allFields = fields.filter(
    field => field.category !== LeadFieldExportCategoryEnum.CUSTOM_FIELDS
  )

  const defaultFields = allFields.filter(field => field.isDefault)

  const options: OptionType<string>[] = useMemo(
    () => [
      {
        value: LeadExportOption.DEFAULT,
        label: t('Default ({{number}})', {
          number: defaultFields?.length || 0,
        }),
      },
      {
        value: LeadExportOption.ALL,
        label: t('All ({{number}})', { number: allFields.length }),
      },
      { value: LeadExportOption.PERSONALIZED, label: t('Personalized') },
    ],
    [t, defaultFields?.length, allFields.length]
  )

  const [value, setValue] = useState<string>(options[0].value)

  useEffect(() => {
    if (fields) {
      setValue(options[0].value)
    }
  }, [fields, options])

  return (
    <div className="grid gap-4">
      <div className="grid gap-2">
        <Typography variant="label" size="s">
          {t('ZELIQ columns')}
        </Typography>
        <div className="w-[21.625rem]">
          <Select
            name="select-fields"
            options={options}
            onChange={newValue => setValue(newValue as string)}
            value={value}
          />
        </div>
      </div>

      {value === LeadExportOption.DEFAULT && (
        <div className="flex flex-wrap gap-1.5 max-w-[700px]">
          {defaultFields?.map(({ id }) => (
            <Tag
              key={id}
              color="brand-secondary"
              label={capitalizeFirstLetter(t(id))}
            />
          ))}
        </div>
      )}

      {value === LeadExportOption.ALL && (
        <div className="flex flex-wrap gap-1.5 max-w-[700px]">
          {allFields?.map(({ id }) => (
            <Tag
              key={id}
              color="brand-secondary"
              label={capitalizeFirstLetter(t(id))}
            />
          ))}
        </div>
      )}

      {value === LeadExportOption.PERSONALIZED &&
        categories.map(exportableCategory => {
          return (
            <LeadExportColumnGroup
              key={exportableCategory}
              name={t(exportableCategory)}
              fields={fields.filter(
                exportableFields =>
                  exportableFields.category === exportableCategory
              )}
            />
          )
        })}
    </div>
  )
}
