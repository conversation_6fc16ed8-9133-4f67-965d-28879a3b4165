import { api } from '@getheroes/frontend/config/api'
import i18n from '@internals/config/i18n'
import type {
  CreateTemplatePayloadType,
  DeleteTemplatePayloadType,
  EditTemplatePayloadType,
  GetTemplatesByOrganizationIdPayloadType,
  GetTemplatesByOrganizationIdResponseType,
} from '@internals/features/template'
import type { OrganizationTemplate } from '@internals/models/index'

export const organizationTemplateApi = api.injectEndpoints({
  endpoints: builder => {
    return {
      createTemplate: builder.mutation({
        query: (payload: CreateTemplatePayloadType) => {
          const { organizationId, ...rest } = payload
          return {
            url: `${organizationId}/templates`,
            method: 'POST',
            body: rest,
          }
        },
        invalidatesTags: [
          { type: 'Template', id: 'LIST' },
          { type: 'Template', id: 'BY_ID' },
        ],
        transformErrorResponse: error => {
          switch (error.status) {
            case 400:
              return {
                message: i18n.t(
                  'An error occurred, please check the form values',
                  { ns: 'template' }
                ),
              }
            case 409:
              return {
                message: i18n.t(
                  'A template has already this name in your organization templates',
                  {
                    ns: 'template',
                  }
                ),
              }
            default:
              return {
                message: i18n.t(
                  'An error occurred. The template could not be created',
                  { ns: 'organization' }
                ),
              }
          }
        },
      }),
      getTemplatesByOrganizationId: builder.query<
        GetTemplatesByOrganizationIdResponseType,
        unknown
      >({
        query: ({
          organizationId,
          order,
          page,
          limitPerPage,
          type,
        }: GetTemplatesByOrganizationIdPayloadType) => {
          const params = new URLSearchParams()
          if (type) {
            params.append('type', type)
          }
          if (order) {
            params.append('order', order)
          }
          if (page) {
            params.append('page', page.toString())
          }
          if (limitPerPage) {
            params.append('limitPerPage', limitPerPage.toString())
          }

          return `${organizationId}/templates${
            params.toString() ? `?${params.toString()}` : ''
          }`
        },
        providesTags: result => {
          if (!Array.isArray(result)) {
            return [{ type: 'Template' as const, id: 'LIST' }]
          }
          return [
            ...result.map(({ id }) => ({ type: 'Template', id }) as const),
            { type: 'Template' as const, id: 'LIST' },
          ]
        },
        transformErrorResponse: () => {
          // TODO - add error handling switch case
          const errorMsg = i18n.t(
            'An error occurred while retrieving the templates',
            { ns: 'template' }
          )
          return { data: { message: errorMsg } }
        },
      }),
      getTemplateById: builder.query<
        OrganizationTemplate,
        { organizationId: string; templateId: string }
      >({
        query: ({ organizationId, templateId }) =>
          `${organizationId}/templates/${templateId}`,
        providesTags: () => [{ type: 'Template' as const, id: 'BY_ID' }],
      }),
      deleteTemplate: builder.mutation({
        query: ({ templateId, organizationId }: DeleteTemplatePayloadType) => {
          const url = `${organizationId}/templates/${templateId}`
          return {
            url,
            method: 'DELETE',
          }
        },
        invalidatesTags: [
          { type: 'Template', id: 'LIST' },
          { type: 'Template', id: 'BY_ID' },
        ],
        transformErrorResponse: () => {
          const errorMsg = i18n.t(
            'There was an error while deleting your template',
            {
              ns: 'template',
            }
          )
          return { data: { message: errorMsg } }
        },
      }),
      editTemplateById: builder.mutation({
        query: (payload: EditTemplatePayloadType) => {
          const { id: templateId, organizationId, ...rest } = payload
          return {
            url: `${organizationId}/templates/${templateId}`,
            method: 'PATCH',
            body: rest,
          }
        },
        invalidatesTags: [
          { type: 'Template', id: 'LIST' },
          { type: 'Template', id: 'BY_ID' },
        ],
      }),
    }
  },
})

export const {
  useCreateTemplateMutation,
  useGetTemplatesByOrganizationIdQuery,
  useLazyGetTemplateByIdQuery,
  useDeleteTemplateMutation,
  useEditTemplateByIdMutation,
} = organizationTemplateApi
