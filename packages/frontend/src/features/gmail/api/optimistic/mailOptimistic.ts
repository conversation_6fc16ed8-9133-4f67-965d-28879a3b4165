import type { AnyAction, ThunkDispatch } from '@reduxjs/toolkit'

import { api } from '@getheroes/frontend/config/api'

type UpdateNoteOptimistic = {
  queryFulfilled: any
  attributeAtUpdate: Partial<any>
  dispatch: ThunkDispatch<any, any, AnyAction>
  originalArgs: any
}

export const updateMailByTaskOptimistic = ({
  queryFulfilled,
  attributeAtUpdate,
  dispatch,
  originalArgs,
}: UpdateNoteOptimistic) => {
  const result = dispatch(
    api.util.updateQueryData(
      'getEmailByTask' as never,
      originalArgs as never,
      (draft: any) => {
        Object.assign(draft, attributeAtUpdate)
      }
    )
  )
  queryFulfilled.catch(result.undo)
}
