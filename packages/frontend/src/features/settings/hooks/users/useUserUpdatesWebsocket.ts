import isEqual from 'lodash/isEqual'
import reduce from 'lodash/reduce'
import { useEffect } from 'react'

import {
  selectCurrentUser,
  setUser,
} from '@getheroes/frontend/config/store/slices/authSlice'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { User } from '@getheroes/shared'
import { NameEvent } from '@getheroes/shared'
import { updateUserWebsocketOptimistic } from '@internals/features/settings/api/optimistic/userWebsocketOptimistic'
import { useWebSocketMessage } from '@internals/hooks/websockets/useWebSocketMessage'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'

export const useUserUpdatesWebsocket = (): void => {
  const dispatch = useAppDispatch()
  const currentOrganization = useTypedSelector(selectCurrentUserOrganization)
  const currentUser = useTypedSelector(selectCurrentUser)

  const { lastMessage: userUpdatedMessage } = useWebSocketMessage(
    NameEvent.UPDATE_USER
  )
  useEffect(() => {
    if (userUpdatedMessage && currentOrganization && currentUser) {
      updateUserWebsocketOptimistic({
        attributeAtUpdate: userUpdatedMessage.payload as Partial<User>,
        originalArgs: { organizationId: currentOrganization.id },
        dispatch,
      })

      // TODO: Why we add current user in the store redux, why we don't use the rtk query cache ?
      // Update auth slice currentUser
      const onlyFieldUpdated = reduce(
        userUpdatedMessage.payload as Partial<User>,
        (result, value, key) => {
          return isEqual(value, currentUser?.[key as keyof User])
            ? result
            : { ...result, [`${key}`]: value }
        },
        []
      )
      dispatch(setUser(onlyFieldUpdated))
    }
  }, [dispatch, userUpdatedMessage]) // eslint-disable-line react-hooks/exhaustive-deps
}
