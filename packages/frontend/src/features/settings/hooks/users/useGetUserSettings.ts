import { useMemo } from 'react'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useGetUserQuery } from '@internals/features/settings/api/userApi'
import { useTypedSelector } from '@internals/store/store'

export const useGetUserSettings = () => {
  const currentOrganization = useTypedSelector(selectCurrentUserOrganization)

  const {
    data: user,
    isError,
    isLoading,
    isSuccess,
    isFetching,
    isUninitialized,
    refetch,
  } = useGetUserQuery(
    { organizationId: currentOrganization?.id || '' },
    { skip: !currentOrganization }
  )

  return useMemo(
    () => ({
      user,
      isError,
      isLoading,
      isSuccess,
      isFetching,
      isUninitialized,
      refetch,
    }),
    [user, isError, isLoading, isSuccess, isFetching, isUninitialized, refetch]
  )
}
