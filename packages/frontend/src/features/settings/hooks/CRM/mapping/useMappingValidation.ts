import { useCallback } from 'react'
import { useTranslation } from 'react-i18next'

import type { ContactOrCompanyLeadFieldId } from '@getheroes/frontend/types'
import { SynchronizationRule } from '@getheroes/shared'
import { mandatoryDefaultFields } from '@internals/features/settings/components/CRMSettings/CRMSyncSettings/Mappings/config/defaultMappingConfig'
import type {
  CRMItemMappingInProcessType,
  MappingErrorType,
} from '@internals/features/settings/types/CRMSettingsType'
import { MappingFieldEnum } from '@internals/features/settings/types/CRMSettingsType'

type UseMappingValidationReturnType = {
  mappingValidator: (
    mapping: CRMItemMappingInProcessType
  ) => CRMItemMappingInProcessType
}

export const useMappingValidation: () => UseMappingValidationReturnType =
  () => {
    const { t } = useTranslation('settings')

    const mandatoryFieldsValidator = useCallback(
      (mapping: CRMItemMappingInProcessType): MappingErrorType[] => {
        // if (
        //   mandatoryDefaultFields.includes(mapping.idZeliq) &&
        //   mapping.idHubspot &&
        //   mapping.rule &&
        //   (mapping.rule === SynchronizationRule.NONE ||
        //     mapping.rule === SynchronizationRule.ZELIQ_TO_HUBSPOT)
        // ) {
        //   return [
        //     {
        //       field: MappingFieldEnum.SYNC_RULE_FIELD,
        //       message: t(
        //         'Mandatory fields should only mapped with Hubspot to Zeliq or bidirectional rule'
        //       ),
        //     },
        //   ]
        // }
        return []
      },
      []
    )

    const zeliqFieldValidator = useCallback(
      (mapping: CRMItemMappingInProcessType): MappingErrorType[] => {
        if (!mapping.idZeliq) {
          return [
            {
              field: MappingFieldEnum.ZELIQ_FIELD,
              message: t('Custom name is required'),
            },
          ]
        }
        return []
      },
      [t]
    )

    const hubspotFieldValidator = useCallback(
      (mapping: CRMItemMappingInProcessType): MappingErrorType[] => {
        const isMandatoryField = mandatoryDefaultFields.includes(
          mapping.idZeliq as ContactOrCompanyLeadFieldId
        )

        if (isMandatoryField && !mapping.idHubspot) {
          return [
            {
              field: MappingFieldEnum.HUBSPOT_FIELD,
              message: t('HubSpot field is required'),
            },
          ]
        }
        return []
      },
      [t]
    )

    const ruleFieldValidator = useCallback(
      (mapping: CRMItemMappingInProcessType): MappingErrorType[] => {
        if (!mapping.rule) {
          return [
            {
              field: MappingFieldEnum.SYNC_RULE_FIELD,
              message: t('Synchronization rule is required'),
            },
          ]
        }

        const isMandatoryField = mandatoryDefaultFields.includes(
          mapping.idZeliq as ContactOrCompanyLeadFieldId
        )

        if (isMandatoryField && mapping.rule === SynchronizationRule.NONE) {
          return [
            {
              field: MappingFieldEnum.SYNC_RULE_FIELD,
              message: t(
                'Synchronization rule cannot be set to none for mandatory fields'
              ),
            },
          ]
        }

        return []
      },
      [t]
    )

    const mappingValidator = useCallback(
      (mapping: CRMItemMappingInProcessType): CRMItemMappingInProcessType => {
        if (!mapping.isCustomFieldAlreadySaved) {
          const zeliqFieldError = zeliqFieldValidator(mapping)
          const hubspotFieldError = hubspotFieldValidator(mapping)
          const syncRuleError = ruleFieldValidator(mapping)
          const mandatoryFieldError = mandatoryFieldsValidator(mapping)
          return {
            ...mapping,
            errors: [
              ...hubspotFieldError,
              ...syncRuleError,
              ...zeliqFieldError,
              ...mandatoryFieldError,
            ],
          }
        }
        return mapping
      },
      [
        zeliqFieldValidator,
        hubspotFieldValidator,
        mandatoryFieldsValidator,
        ruleFieldValidator,
      ]
    )

    return {
      mappingValidator,
    }
  }
