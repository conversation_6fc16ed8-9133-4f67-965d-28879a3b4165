import { clsx } from 'clsx'
import type { FC, ReactNode } from 'react'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { selectVoipIntegration } from '@getheroes/frontend/config/store/slices/integrationSlice'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import {
  formatDate,
  MONTH_DAY_YEAR_INLINE_FORMAT,
} from '@getheroes/frontend/utils'
import type { Organization } from '@getheroes/shared'
import { Avatar, Tag, Typography } from '@getheroes/ui'
import { Spinner } from '@internals/components/common/feedback/Spinner/Spinner'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import {
  ButtonSize,
  ButtonVariant,
} from '@internals/components/common/navigation/Buttons/Button/Button-export'
import { useGetIntegrationsQuery } from '@internals/features/settings/components/integration/api/integrationApi'
import { DisconnectIntegrationModal } from '@internals/features/settings/components/integration/components/Modals/DisconnectIntegrationModal'
import { useIntegrationTracking } from '@internals/features/settings/hooks/useIntegrationTracking'
import type { ConnectIntegrationRequestType } from '@internals/features/settings/types/IntegrationType'
import { IntegrationServiceName } from '@internals/features/settings/types/IntegrationType'
import { useDebouncedValue } from '@internals/hooks/debounce/useDebouncedValue'
import { useRoute } from '@internals/hooks/useRoute'
import { useTypedSelector } from '@internals/store/store'
import { idReferentials } from '@internals/utils/idReferentials'
import {
  capitalizeFirstLetter,
  getInitialsFromPerson,
} from '@internals/utils/string'

type IntegrationProps = {
  connect: (
    payload:
      | {
          payload: { redirectUrl: string }
          organizationId: string
        }
      | ConnectIntegrationRequestType
  ) => void
  isConnectSuccess: boolean
  isConnectLoading: boolean
  authorizationUrl?: string
  disconnect: (organizationId: string) => void
  isDisconnectLoading: boolean
  serviceName: IntegrationServiceName
  logo: ReactNode
}

export const Integration: FC<IntegrationProps> = ({
  // Connect tools
  connect,
  isConnectSuccess,
  isConnectLoading,
  authorizationUrl,
  // Disconnect tools
  disconnect,
  isDisconnectLoading,
  // Common
  logo,
  serviceName,
}) => {
  const [isDisconnectModalOpen, setIsDisconnectModalOpen] =
    useState<boolean>(false)
  const trackIntegrationService = useIntegrationTracking()
  const { t } = useTranslation('settings')
  const { privateRoutes } = useRoute()
  const organization = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const voipIntegration = useTypedSelector(selectVoipIntegration)

  const {
    data: integrations,
    isLoading,
    isFetching,
    isUninitialized,
  } = useGetIntegrationsQuery(organization?.id)
  const [debouncedIntegrations, showSpinner] = useDebouncedValue(
    integrations,
    300
  )

  const currentIntegration = useMemo(
    () =>
      debouncedIntegrations?.find(
        integration => integration.name === serviceName
      ),
    [debouncedIntegrations, serviceName]
  )
  const {
    isConnected,
    createdAt,
    createdBy: userCreator,
  } = currentIntegration || {}
  const { firstName: creatorFirstName, lastName: creatorLastName } =
    userCreator || {}
  const initials = userCreator ? getInitialsFromPerson(userCreator) : ''

  const isNotReady =
    isConnectLoading ||
    isLoading ||
    isDisconnectLoading ||
    showSpinner ||
    isFetching ||
    isUninitialized

  // Disable the connect button if a voip provider is already connected
  const isConnectDisabled = useMemo(() => {
    if (!voipIntegration) {
      return false
    }
    const voipProvider = [
      IntegrationServiceName.AIRCALL,
      IntegrationServiceName.RINGOVER,
    ]
    const isVoipProvider = voipProvider.includes(serviceName)

    if (isVoipProvider && voipIntegration.connected && !isConnected) {
      return true
    }
  }, [isConnected, serviceName, voipIntegration])

  useEffect(() => {
    if (authorizationUrl && isConnectSuccess) {
      window.open(authorizationUrl, '_self')
    }
  }, [isConnectSuccess, authorizationUrl])

  const handleClick = () => {
    if (isConnected) {
      setIsDisconnectModalOpen(true)
    } else {
      trackIntegrationService({ source: serviceName })
      connect({
        payload: {
          redirectUrl: `${import.meta.env.VITE_APP_URL}${
            privateRoutes.settings.path
          }/${privateRoutes.integrationSettings.path}`,
        },
        organizationId: organization?.id,
      })
    }
  }

  return (
    <div className={clsx('flex flex-col mt-2')}>
      <div
        className={clsx(
          'border border-1 border-borderSubtle rounded-base px-4 py-5'
        )}
      >
        <div className={clsx('flex flex-row justify-between')}>
          <div className={clsx('flex items-center gap-4')}>
            {logo}
            <Typography variant={'heading'} size={'s'}>
              {capitalizeFirstLetter(t(serviceName))}
            </Typography>
            {isConnected && (
              <Tag color={'green'} label={t('Enabled')} size={'small'} />
            )}
          </div>
          {isNotReady ? (
            <Spinner />
          ) : (
            <Button
              onClick={handleClick}
              isLoading={isConnectLoading}
              variant={
                isConnected ? ButtonVariant.SECONDARY : ButtonVariant.PRIMARY
              }
              size={ButtonSize.SMALL}
              disabled={isConnectDisabled}
              tooltipText={
                isConnectDisabled
                  ? t(
                      'You can’t connect multiple VoIP providers at the same time'
                    )
                  : undefined
              }
              dataTestId={idReferentials.settings.integration.connectButton}
            >
              {isConnected ? t('Disconnect') : t('Connect')}
            </Button>
          )}
        </div>
        {!isNotReady && isConnected && (
          <div
            className={clsx(
              'border-t text-textSubtle text-size03 font-xs mt-5'
            )}
          >
            <div className={clsx('flex flex-row items-center mt-5')}>
              <div>{t('Enabled by')}</div>
              {createdAt && (
                <span className={clsx('mx-2')}>
                  <Avatar size="xl" color="light" initials={initials} />
                </span>
              )}
              <div className="text-textDefault font-medium">{`${creatorFirstName} ${creatorLastName}`}</div>
              <div className={clsx('ml-2')}>
                {t('on')}{' '}
                {formatDate(createdAt as Date, MONTH_DAY_YEAR_INLINE_FORMAT)}
              </div>
            </div>
          </div>
        )}
      </div>
      <DisconnectIntegrationModal
        isModalOpen={isDisconnectModalOpen}
        setIsModalOpen={setIsDisconnectModalOpen}
        serviceName={serviceName}
        disconnect={() => disconnect(organization?.id)}
        isDisconnectLoading={isDisconnectLoading}
      />
    </div>
  )
}
