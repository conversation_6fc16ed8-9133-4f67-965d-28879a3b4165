import { useState } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { Organization } from '@getheroes/shared'
import { SwitchButton } from '@internals/components/common/dataEntry/Switch/SwitchButton'
import { Button } from '@internals/components/common/navigation/Buttons/Button/Button'
import { usePatchSettings } from '@internals/features/settings/hooks/usePatchSettings'
import type { DomainSettingsType } from '@internals/features/settings/types/DomainSettingsType'
import type { Domain } from '@internals/models/domain'
import { useTypedSelector } from '@internals/store/store'

type OptOutSettings = Pick<DomainSettingsType, 'optOutMode'>

export type OptOutLinkProps = {
  settings: OptOutSettings
  domain: Domain
}

export const OptOutLink = ({ settings, domain }: OptOutLinkProps) => {
  const { t } = useTranslation('settings')
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const [optOutMode, setOptOutMode] = useState(settings.optOutMode)

  const [patchSettings, { isLoading }] = usePatchSettings()

  const handleClickSaveChanges = () => {
    patchSettings({
      organizationId,
      domainId: domain.id,
      settings: { optOutMode },
    })
  }

  return (
    <div>
      <SwitchButton
        name="optOutMode-header"
        labelPosition="end"
        label={t('Unsubscribe via opt-out header') as string}
        onChange={(val: boolean) => {
          setOptOutMode(val ? 'HEADER' : 'NONE')
        }}
        isChecked={optOutMode === 'HEADER'}
      />
      <SwitchButton
        name="optOutMode-link"
        labelPosition="end"
        label={t('Unsubscribe via opt-out link') as string}
        onChange={(val: boolean) => {
          setOptOutMode(val ? 'LINK' : 'NONE')
        }}
        isChecked={optOutMode === 'LINK'}
      />
      <Button
        onClick={handleClickSaveChanges}
        className="mt-6"
        isLoading={isLoading}
      >
        {t('Save changes')}
      </Button>
    </div>
  )
}
