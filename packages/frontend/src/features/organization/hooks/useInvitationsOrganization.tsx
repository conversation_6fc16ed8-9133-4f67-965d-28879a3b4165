import { clsx } from 'clsx'
import { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { formatDate } from '@getheroes/frontend/utils'
import type { InvitationMember, Organization } from '@getheroes/shared'
import { Avatar, IconButton, Tag, useToast } from '@getheroes/ui'
import { DropdownMenu } from '@internals/components/common/navigation/DropdownMenu/DropdownMenu'
import {
  useGetOrganizationByIdQuery,
  useRemindInvitationMemberMutation,
} from '@internals/features/organization/api/organizationApi'
import { useTypedSelector } from '@internals/store/store'
import type { RemindInvitationMemberPayload } from '@internals/types/api/organization'
import { idReferentials } from '@internals/utils/idReferentials'

export const useInvitationsOrganization = () => {
  const { t } = useTranslation()
  const [selectedInvitationMember, setSelectedInvitationMember] =
    useState<InvitationMember | null>(null)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false)

  const { createToast } = useToast()

  const { id: organizationId, name: organizationName } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const {
    data: userOrganization,
    isLoading: isGetOrganizationLoading,
    isError,
    isSuccess: isGetOrganizationSuccess,
  } = useGetOrganizationByIdQuery(organizationId)

  const [
    remindInvitationMember,
    {
      isLoading: isRemindInvitationOrganizationLoading,
      isSuccess: isRemindInvitationOrganizationSuccess,
    },
  ] = useRemindInvitationMemberMutation()

  const isLoading = useMemo(() => {
    return isGetOrganizationLoading || isRemindInvitationOrganizationLoading
  }, [isGetOrganizationLoading, isRemindInvitationOrganizationLoading])

  const isSuccess = useMemo(() => {
    return isGetOrganizationSuccess || isRemindInvitationOrganizationSuccess
  }, [isGetOrganizationSuccess, isRemindInvitationOrganizationSuccess])

  const invitations = userOrganization?.invitations

  const itemsMenu = useCallback((invitation: InvitationMember) => {
    return [
      {
        key: t('Resend invite'),
        label: t('Resend invite'),
        labelClassName: 'text-size03',
        action: async () => {
          const payload: RemindInvitationMemberPayload = {
            organizationId: organizationId as string,
            invitationId: invitation.id,
          }
          await remindInvitationMember(payload)
            .unwrap()
            .then(() => {
              createToast({
                type: 'main',
                message: t('The invitation has been successfully resent'),
              })
            })
            .catch(error => {
              createToast({
                type: 'error',
                message: t(error.message),
              })
            })
        },
      },
      {
        key: t('Cancel invite'),
        label: t('Cancel invite'),
        labelClassName: 'text-size03',
        action: () => {
          setSelectedInvitationMember(invitation)
          setIsDeleteModalOpen(true)
        },
      },
    ]
  }, [])

  const columns = useMemo(() => {
    return [
      {
        field: 'email',
        headerName: t('Email', { ns: 'lead' }),
        cellRenderer: ({ data }: any) => {
          const email = data?.email || ''
          const initials = email.slice(0, 2).toUpperCase()
          return (
            <div className={clsx('flex flex-row items-center gap-4')}>
              <Avatar size="m" initials={initials} />
              <div className="flex flex-col">
                <span className={clsx('body-m-medium')}>{email}</span>
                <span
                  className={clsx('text-textSubtle body-xs-regular')}
                >{`Send on ${formatDate(data.updatedAt)}`}</span>
              </div>
            </div>
          )
        },
      },
      {
        field: 'tag',
        headerName: t('Tag', { ns: 'lead' }),
        cellRenderer: () => {
          return (
            <Tag color={'grey'} size={'small'} label={t('Invite pending')} />
          )
        },
      },
      {
        field: 'actions',
        headerName: t('Actions', { ns: 'lead' }),
        cellRenderer: ({ data }: { data: InvitationMember }) => {
          return (
            <DropdownMenu
              dataTestId={
                idReferentials.organization.useInvitationsOrganization
                  .dropdownMenu
              }
              items={itemsMenu(data)}
              openedButton={
                <IconButton
                  icon={'MoreHoriz'}
                  variant={'tertiary-outlined'}
                  size={'small'}
                />
              }
              closedButton={
                <IconButton
                  icon={'MoreHoriz'}
                  variant={'tertiary-outlined'}
                  size={'small'}
                />
              }
              containerClassName={'w-60'}
            />
          )
        },
      },
    ]
  }, [itemsMenu, t])

  return {
    invitations,
    columns,
    isLoading,
    isSuccess,
    isError,
    organizationName,
    isDeleteModalOpen,
    setIsDeleteModalOpen,
    selectedInvitationMember,
  }
}
