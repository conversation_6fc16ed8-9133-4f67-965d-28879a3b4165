import { SequenceStepType } from '@getheroes/shared'
import type { ContactSequenceStepActivity } from '@internals/features/lead/types/contactSequenceStepActivity'
import { SequenceStepStatus } from '@internals/features/sequence/types/sequence'

import { STEP_WAITING_TIME_INITIAL_VALUE_IN_MINUTES } from '../features/sequence/constants/step.constant'

export const mockedContactSequenceStepActivity: ContactSequenceStepActivity = {
  id: '6644f91c-e931-4192-9627-6ff21bf2da0a',
  name: 'Send an email',
  type: 'email' as SequenceStepType,
  order: 1,
  waitingBetweenStep: STEP_WAITING_TIME_INITIAL_VALUE_IN_MINUTES,
  content: {
    body: '<div><span>Test</span></div>',
    subject: 'Test',
    attachments: [],
  },
  createdAt: new Date('2024-01-15T09:50:12.704Z'),
  updatedAt: new Date('2024-01-15T09:50:18.784Z'),
  status: 'past' as SequenceStepStatus,
  date: new Date('2024-01-16T09:32:49.681Z'),
  formattedContent: {
    body: '<div><span>Test</span></div>',
    subject: 'Test',
    attachments: [],
    from: '<EMAIL>',
    to: '<EMAIL>',
    nbOpens: 0,
    nbClicks: 0,
  },
}

const DEFAULT_COLLECTION_SIZE = 10
export const buildMockedContactSequenceStepActivityList: (
  size?: number
) => ContactSequenceStepActivity[] = (size = DEFAULT_COLLECTION_SIZE) =>
  Array.from(Array(size)).map(buildMockedContactSequenceStepActivity)

function getRandomInt(max: number) {
  return Math.floor(Math.random() * max)
}

export const buildMockedContactSequenceStepActivity: (
  init?: Partial<ContactSequenceStepActivity>
) => ContactSequenceStepActivity = () => ({
  ...mockedContactSequenceStepActivity,
  status: Object.values(SequenceStepStatus)[getRandomInt(3)],
  type: Object.values(SequenceStepType)[getRandomInt(2)],
})
