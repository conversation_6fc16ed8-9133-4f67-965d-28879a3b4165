import type { LexicalEditor, SerializedEditorState } from 'lexical'

import type { AttachmentPluginOptions } from '@internals/components/common/dataEntry/Lexical/plugins/AttachmentsPlugin/AttachmentPlugin'
import type { ToolbarOption } from '@internals/components/common/dataEntry/Lexical/plugins/ToolbarPlugin/toolbar-plugin.type'
import type { AttachmentModel } from '@internals/components/common/dataEntry/Lexical/ui/AttachmentBar'
import type { FormFields } from '@internals/models/form'

export type VariableOptionsType = Array<
  [string, string, string, string, string]
>

export type EditorOnChangeType = {
  editorState: SerializedEditorState
  raw: string
  html: string
  addSignature?: boolean
}

export type EditorProps = FormFields & {
  variant?: 'textarea' | 'input'
  hasError?: boolean
  toolbarOptions?: ToolbarOption[]
  variableOptions?: VariableOptionsType
  variableReferences?: object | any | undefined
  autoFocus?: boolean
  isEnablePreviewOnClick?: boolean
  children?: React.ReactNode
  editable?: boolean
  initalEditorState?: string | null
  maxLength?: number
  onBlur?: () => void
  attachmentOptions?: AttachmentPluginOptions
  namespaceLocalStorage?: string
  namespace?: string
  placeholder?: string
  isLexicalPreview?: boolean
  setIsEditorPreview?: (isPreview: boolean) => void
  toolbarClassName?: string
  getAttachments?: (attachments: AttachmentModel[]) => void
  floatingToolbarOptions?: string[]
  toolbarLocation?: 'top' | 'bottom'
  hasBlockDraggable?: boolean
  showToolbar?: boolean
  showFloatingToolbar?: boolean
  isPlaceholderSingleLine?: boolean
}

export type LexicalImperativeHandle = {
  clear: () => void
  getEditor: () => LexicalEditor
  setEditorState: (editorState: string) => void
  getEditorState: () => SerializedEditorState
  setAttachments: (attachments: AttachmentModel[]) => void
  getAttachments: () => AttachmentModel[]
  setHtmlContent: (html: string) => void
  getHtmlContent: () => string
  getTextContent: () => string
  addHtmlContent: (html: string) => void
  // setVariablesToValues: () => void
  setIsEditable: (isEditable: boolean) => void
  setIsPreview: (isPreview: boolean) => void
  getIsSignature: () => boolean
  setSelectedSignature: (signature: string) => void
  getSelectedSignature: () => string
  setHasSignature: (hasSignature: boolean) => void
  focus: () => void
}
