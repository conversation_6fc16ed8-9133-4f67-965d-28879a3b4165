import { $generateNodesFromDOM } from '@lexical/html'
import type { InitialConfigType } from '@lexical/react/LexicalComposer'
import { LexicalComposer } from '@lexical/react/LexicalComposer'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { clsx } from 'clsx'
import type { LexicalNode } from 'lexical'
import { $getRoot, $insertNodes, ParagraphNode } from 'lexical'
import type { LexicalEditor } from 'lexical/LexicalEditor'
import { camelCase, capitalize, chain } from 'lodash'
import { forwardRef, useEffect, useMemo, useRef } from 'react'
import { v4 } from 'uuid'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { Typography } from '@getheroes/ui'
import type { Organization } from '@getheroes/shared'
import { Editor } from '@internals/components/common/dataEntry/Lexical/Editor'
import { SharedAutocompleteContext } from '@internals/components/common/dataEntry/Lexical/context/SharedAutocompleteContext'
import { SharedHistoryContext } from '@internals/components/common/dataEntry/Lexical/context/SharedHistoryContext'
import { VariableContext } from '@internals/components/common/dataEntry/Lexical/context/VariableContext'
import type {
  EditorProps,
  LexicalImperativeHandle,
  VariableOptionsType,
} from '@internals/components/common/dataEntry/Lexical/lexical.type'
import { CustomParagraphNode } from '@internals/components/common/dataEntry/Lexical/nodes/CustomParagraphNode'
import { PlaygroundNodes } from '@internals/components/common/dataEntry/Lexical/nodes/PlaygroundNodes'
import { PlaygroundEditorTheme } from '@internals/components/common/dataEntry/Lexical/themes/PlaygroundEditorTheme'
import { useGetContactFieldsQuery } from '@internals/features/lead/api/contactApi'
import { useTypedSelector } from '@internals/store/store'

import './lexical.scoped.css'

type LexicalProps = {
  dataTestId?: string
  toolbarLocation?: 'top' | 'bottom'
  isPreview?: boolean
  initialContent?: string
  showCharacterCounter?: boolean
} & EditorProps

const formatNameField = (name: string) => {
  // const reg = /_/gi
  // const lab = snakeCase(name).replace(reg, '.')
  const list = name.split('.')
  const fullName = list.reduce((acc, curr) => `${acc} ${curr}`, '')
  return capitalize(fullName.trim())
}

type Props = { initialContent?: string }

const LoadInitialContent = ({ initialContent }: Props) => {
  const [editor] = useLexicalComposerContext()
  const isMountedRef = useRef<boolean>(false)

  useEffect(() => {
    if (!initialContent || isMountedRef.current) {
      return
    }
    editor.update(() => {
      $getRoot()
        .getChildren()
        .forEach(n => n.remove())
      const parser = new DOMParser()
      const dom = parser.parseFromString(initialContent, 'text/html')
      const nodes: LexicalNode[] = $generateNodesFromDOM(editor, dom)
      // Select the root
      $getRoot().clear()
      $getRoot().select()
      $insertNodes(nodes)
    })
    isMountedRef.current = false
  }, [])
  return null
}

export const Lexical = forwardRef<LexicalImperativeHandle, LexicalProps>(
  (
    {
      name,
      dataTestId,
      toolbarClassName = '',
      toolbarOptions = [],
      variableReferences = undefined,
      children,
      autoFocus,
      editable = true,
      initalEditorState,
      maxLength,
      onChange,
      onBlur,
      isEnablePreviewOnClick = false,
      className,
      namespaceLocalStorage,
      attachmentOptions = {
        maxFileSize: 1,
        maxTotalSize: 12.5,
        maxTotalFile: 999,
        accept: '*',
        maxFiles: 999,
      },
      toolbarLocation = 'bottom',
      showToolbar = true,
      placeholder,
      isLexicalPreview = false,
      setIsEditorPreview,
      getAttachments,
      error,
      floatingToolbarOptions,
      initialContent,
      showFloatingToolbar,
      isPlaceholderSingleLine,
      helperText,
      label,
      variant = 'textarea',
      value,
      showCharacterCounter = true,
    }: LexicalProps,
    ref
  ): JSX.Element => {
    /* Vars */

    const { id: organizationId } = useTypedSelector(
      selectCurrentUserOrganization
    ) as Organization
    const rawString = ref?.current?.getTextContent()

    /* Hooks */

    const { data: fields } = useGetContactFieldsQuery({
      organizationId,
    })

    /* Memos */

    const variableAutoGenerate = useMemo(() => {
      const final: VariableOptionsType = []
      chain(fields)
        .filter(field => field.editable)
        .orderBy(['name'], ['asc'])
        .forEach(field => {
          final.push([
            `{{${camelCase(field.id)}}}`,
            `${formatNameField(field.name)}`,
            field.id,
            field.name,
            field.kind,
          ])
        })
        .value()
      return final
    }, [fields])

    const editorConfig: InitialConfigType = useMemo(
      () => ({
        theme: PlaygroundEditorTheme,
        onError(error: Error, editor: LexicalEditor) {
          throw error
        },
        nodes: [
          ...PlaygroundNodes,
          CustomParagraphNode,
          {
            replace: ParagraphNode,
            with: node => {
              return new CustomParagraphNode()
            },
          },
        ],
        namespace: `Playground ${v4()}`,
        editable,
        // editorState: null,
      }),
      [editable]
    )

    return (
      <LexicalComposer initialConfig={editorConfig}>
        <SharedHistoryContext>
          <SharedAutocompleteContext>
            <VariableContext isEditable={editable}>
              <div
                className={clsx('flex flex-col gap-0.5 w-full', {
                  'h-full': variant === 'textarea',
                })}
              >
                <LoadInitialContent initialContent={initialContent} />

                <div className="flex items-center justify-between">
                  {label && (
                    <Typography
                      variant="label"
                      size="s"
                      weight="medium"
                      color="base-label"
                    >
                      {label}
                    </Typography>
                  )}

                  {showCharacterCounter && maxLength && (
                    <Typography variant="body" size="xs" weight="medium">
                      {rawString?.length || 0} / {maxLength}
                    </Typography>
                  )}
                </div>

                <Editor
                  toolbarLocation={toolbarLocation}
                  hasError={Boolean(error?.trim())}
                  name={name}
                  dataTestId={dataTestId}
                  autoFocus={autoFocus}
                  ref={ref}
                  editable={editable}
                  toolbarOptions={toolbarOptions}
                  initalEditorState={initalEditorState}
                  maxLength={maxLength}
                  onChange={onChange}
                  onBlur={onBlur}
                  isEnablePreviewOnClick={isEnablePreviewOnClick}
                  className={className}
                  attachmentOptions={attachmentOptions}
                  namespaceLocalStorage={namespaceLocalStorage}
                  placeholder={placeholder}
                  variableOptions={variableAutoGenerate}
                  variableReferences={variableReferences}
                  isLexicalPreview={isLexicalPreview}
                  setIsEditorPreview={setIsEditorPreview}
                  toolbarClassName={toolbarClassName}
                  getAttachments={getAttachments}
                  floatingToolbarOptions={floatingToolbarOptions}
                  showToolbar={showToolbar}
                  showFloatingToolbar={showFloatingToolbar}
                  isPlaceholderSingleLine={isPlaceholderSingleLine}
                  variant={variant}
                >
                  {children}
                </Editor>

                {helperText && !error && (
                  <Typography size="xxs" color="base-label">
                    {helperText}
                  </Typography>
                )}

                {error && (
                  <Typography size="xxs" color="base-error">
                    {error}
                  </Typography>
                )}
              </div>
            </VariableContext>
          </SharedAutocompleteContext>
        </SharedHistoryContext>
      </LexicalComposer>
    )
  }
)
