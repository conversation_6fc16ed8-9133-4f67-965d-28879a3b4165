import type { Dispatch, ReactNode } from 'react'

import type { VariableOptionsType } from '@internals/components/common/dataEntry/Lexical/lexical.type'
import type { AttachmentPluginOptions } from '@internals/components/common/dataEntry/Lexical/plugins/AttachmentsPlugin/AttachmentPlugin'

export const dropDownActiveClass = (active: boolean) => {
  if (active) {
    return 'active dropdown-item-active'
  } else {
    return ''
  }
}

export type ToolbarOption =
  | 'familyFont'
  | 'sizeFont'
  | 'singleLine'
  | 'blockTypeFormat'
  | 'textAlign'
  | 'formatLink'
  | 'insertAttachmentBtn'
  | 'signature'
  | 'variable'
  | 'clearFormatting'
  | 'aiAssistant'
  | 'preview'
  | 'speechToText'
  | 'isSentByZeliq'

export type ToolbarPluginProps = {
  blockTypeFormat?: boolean
  children?: ReactNode
  toolbarOptions: ToolbarOption[]
  variableOptions: VariableOptionsType
  attachmentOptions?: AttachmentPluginOptions
  toolbarClassName?: string
  hasSignature?: boolean
  setIsLinkEditMode: Dispatch<boolean>
  isEssentialAdvancedEnterprisePlan?: boolean
}
