import { $isLinkNode, TOGGLE_LINK_COMMAND } from '@lexical/link'
import { $isListNode, ListNode } from '@lexical/list'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { $isDecoratorBlockNode } from '@lexical/react/LexicalDecoratorBlockNode'
import { $isHeadingNode, $isQuoteNode } from '@lexical/rich-text'
import {
  $getSelectionStyleValueForProperty,
  $isParentElementRTL,
} from '@lexical/selection'
import { $isTableNode, $isTableSelection } from '@lexical/table'
import {
  $findMatchingParent,
  $getNearestBlockElementAncestorOrThrow,
  $getNearestNodeOfType,
  mergeRegister,
} from '@lexical/utils'
import type { ElementFormatType, NodeKey } from 'lexical'
import {
  $createParagraphNode,
  $getSelection,
  $isElementNode,
  $isRangeSelection,
  $isRootOrShadowRoot,
  $isTextNode,
  CAN_REDO_COMMAND,
  CAN_UNDO_COMMAND,
  COMMAND_PRIORITY_CRITICAL,
  COMMAND_PRIORITY_NORMAL,
  KEY_MODIFIER_COMMAND,
  REDO_COMMAND,
  SELECTION_CHANGE_COMMAND,
  UNDO_COMMAND,
} from 'lexical'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { Divider, IconButton, Tooltip } from '@getheroes/ui'
import { VariableSelect } from '@internals/components/business/lead/contact/VariableSelect/VariableSelect'
import type { rootTypeToRootName } from '@internals/components/common/dataEntry/Lexical/components/BlockFormatDropDown/BlockFormatDropDown'
import {
  BlockFormatDropDown,
  blockTypeToBlockName,
} from '@internals/components/common/dataEntry/Lexical/components/BlockFormatDropDown/BlockFormatDropDown'
import { ElementFormatDropdown } from '@internals/components/common/dataEntry/Lexical/components/ElementFormatDropdown/ElementFormatDropdown'
import { FontDropDown } from '@internals/components/common/dataEntry/Lexical/components/FontDropDown/FontDropDown'
import { FontSize } from '@internals/components/common/dataEntry/Lexical/components/FontSize/FontSize'
import { useModal } from '@internals/components/common/dataEntry/Lexical/hooks/useModal'
import { AttachmentButton } from '@internals/components/common/dataEntry/Lexical/plugins/AttachmentsPlugin/AttachmentPlugin'
import { SignatureButton } from '@internals/components/common/dataEntry/Lexical/plugins/SignaturePlugin'
import type { ToolbarPluginProps } from '@internals/components/common/dataEntry/Lexical/plugins/ToolbarPlugin/toolbar-plugin.type'
import { INSERT_VARIABLE_COMMAND } from '@internals/components/common/dataEntry/Lexical/plugins/VariablePlugin'
import { IS_APPLE } from '@internals/components/common/dataEntry/Lexical/shared/environment'
import { getSelectedNode } from '@internals/components/common/dataEntry/Lexical/utils/getSelectedNode'
import { sanitizeUrl } from '@internals/components/common/dataEntry/Lexical/utils/url'
import { isDev } from '@internals/components/technical/FeatureFlag/OnlyDev/OnlyDev-export'

import { AIAssistantPlugin } from '../AIAssistantPlugin/AIAssistantPlugin'
import {
  SPEECH_TO_TEXT_COMMAND,
  SUPPORT_SPEECH_RECOGNITION,
} from '../SpeechToTextPlugin'

/*
const atLeastOneIsTrue = (data: any) => Object.values(data).some(Boolean)

const atLeastOneIsTrueArray = (data: string[]) =>
  Object.values(data).some(Boolean)
*/

export const ToolbarPlugin = ({
  blockTypeFormat,
  children,
  toolbarOptions,
  attachmentOptions,
  variableOptions,
  toolbarClassName,
  hasSignature,
  setIsLinkEditMode,
  isEssentialAdvancedEnterprisePlan,
}: ToolbarPluginProps): JSX.Element => {
  const { t } = useTranslation('mail')
  const [editor] = useLexicalComposerContext()
  const [activeEditor, setActiveEditor] = useState(editor)
  const [blockType, setBlockType] =
    useState<keyof typeof blockTypeToBlockName>('paragraph')
  const [rootType, setRootType] =
    useState<keyof typeof rootTypeToRootName>('root')
  const [selectedElementKey, setSelectedElementKey] = useState<NodeKey | null>(
    null
  )
  const [fontSize, setFontSize] = useState<string>('15px')
  const [fontFamily, setFontFamily] = useState<string>('Arial')
  const [elementFormat, setElementFormat] = useState<ElementFormatType>('left')
  const [isLink, setIsLink] = useState(false)
  const [canUndo, setCanUndo] = useState(false)
  const [canRedo, setCanRedo] = useState(false)
  const [modal, showModal] = useModal()
  const [isRTL, setIsRTL] = useState(false)
  const [isEditable, setIsEditable] = useState(() => editor.isEditable())
  const [isSpeechToText, setIsSpeechToText] = useState(false)
  const [isArcBrowser, setIsArcBrowser] = useState<boolean>(false)

  const $updateToolbar = useCallback(() => {
    const selection = $getSelection()
    if ($isRangeSelection(selection)) {
      const anchorNode = selection.anchor.getNode()
      let element =
        anchorNode.getKey() === 'root'
          ? anchorNode
          : $findMatchingParent(anchorNode, e => {
              const parent = e.getParent()
              return parent !== null && $isRootOrShadowRoot(parent)
            })

      if (element === null) {
        element = anchorNode.getTopLevelElementOrThrow()
      }

      const elementKey = element.getKey()
      const elementDOM = activeEditor.getElementByKey(elementKey)

      // Update text format
      setIsRTL($isParentElementRTL(selection))

      // Update links
      const node = getSelectedNode(selection)
      const parent = node.getParent()
      if ($isLinkNode(parent) || $isLinkNode(node)) {
        setIsLink(true)
      } else {
        setIsLink(false)
      }

      const tableNode = $findMatchingParent(node, $isTableNode)
      if ($isTableNode(tableNode)) {
        setRootType('table')
      } else {
        setRootType('root')
      }

      if (elementDOM !== null) {
        setSelectedElementKey(elementKey)
        if ($isListNode(element)) {
          const parentList = $getNearestNodeOfType<ListNode>(
            anchorNode,
            ListNode
          )
          const type = parentList
            ? parentList.getListType()
            : element.getListType()
          setBlockType(type)
        } else {
          const type = $isHeadingNode(element)
            ? element.getTag()
            : element.getType()
          if (type in blockTypeToBlockName) {
            setBlockType(type as keyof typeof blockTypeToBlockName)
          }
        }
      }
      // Handle buttons
      setFontSize(
        $getSelectionStyleValueForProperty(selection, 'font-size', '15px')
      )
      setFontFamily(
        $getSelectionStyleValueForProperty(selection, 'font-family', 'Arial')
      )
      let matchingParent
      if ($isLinkNode(parent)) {
        // If node is a link, we need to fetch the parent paragraph node to set format
        matchingParent = $findMatchingParent(
          node,
          parentNode => $isElementNode(parentNode) && !parentNode.isInline()
        )
      }

      // If matchingParent is a valid node, pass it's format type
      setElementFormat(
        $isElementNode(matchingParent)
          ? matchingParent.getFormatType()
          : $isElementNode(node)
            ? node.getFormatType()
            : parent?.getFormatType() || 'left'
      )
    }
  }, [activeEditor])

  useEffect(() => {
    return editor.registerCommand(
      SELECTION_CHANGE_COMMAND,
      (_payload, newEditor) => {
        $updateToolbar()
        setActiveEditor(newEditor)
        return false
      },
      COMMAND_PRIORITY_CRITICAL
    )
  }, [editor, $updateToolbar])

  useEffect(() => {
    return mergeRegister(
      editor.registerEditableListener(editable => {
        setIsEditable(editable)
      }),
      activeEditor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          $updateToolbar()
        })
      }),
      activeEditor.registerCommand<boolean>(
        CAN_UNDO_COMMAND,
        payload => {
          setCanUndo(payload)
          return false
        },
        COMMAND_PRIORITY_CRITICAL
      ),
      activeEditor.registerCommand<boolean>(
        CAN_REDO_COMMAND,
        payload => {
          setCanRedo(payload)
          return false
        },
        COMMAND_PRIORITY_CRITICAL
      )
    )
  }, [$updateToolbar, activeEditor, editor])

  useEffect(() => {
    return activeEditor.registerCommand(
      KEY_MODIFIER_COMMAND,
      payload => {
        const event: KeyboardEvent = payload
        const { code, ctrlKey, metaKey } = event

        if (code === 'KeyK' && (ctrlKey || metaKey)) {
          event.preventDefault()
          let url: string | null
          if (!isLink) {
            setIsLinkEditMode(true)
            url = sanitizeUrl('https://')
          } else {
            setIsLinkEditMode(false)
            url = null
          }
          return activeEditor.dispatchCommand(TOGGLE_LINK_COMMAND, url)
        }
        return false
      },
      COMMAND_PRIORITY_NORMAL
    )
  }, [activeEditor, isLink, setIsLinkEditMode])

  const clearFormatting = useCallback(() => {
    activeEditor.update(() => {
      const selection = $getSelection()
      if ($isRangeSelection(selection) || $isTableSelection(selection)) {
        const anchor = selection.anchor
        const focus = selection.focus
        const nodes = selection.getNodes()

        if (anchor.key === focus.key && anchor.offset === focus.offset) {
          return
        }

        nodes.forEach((node, idx) => {
          // We split the first and last node by the selection
          // So that we don't format unselected text inside those nodes
          if ($isTextNode(node)) {
            // Use a separate variable to ensure TS does not lose the refinement
            let textNode = node
            if (idx === 0 && anchor.offset !== 0) {
              textNode = textNode.splitText(anchor.offset)[1] || textNode
            }
            if (idx === nodes.length - 1) {
              textNode = textNode.splitText(focus.offset)[0] || textNode
            }
            /**
             * If the selected text has one format applied
             * selecting a portion of the text, could
             * clear the format to the wrong portion of the text.
             *
             * The cleared text is based on the length of the selected text.
             */
            // We need this in case the selected text only has one format
            const extractedTextNode = selection.extract()[0]
            if (nodes.length === 1 && $isTextNode(extractedTextNode)) {
              textNode = extractedTextNode
            }

            if (textNode.__style !== '') {
              textNode.setStyle('')
            }
            if (textNode.__format !== 0) {
              textNode.setFormat(0)
              $getNearestBlockElementAncestorOrThrow(textNode).setFormat('')
            }
            node = textNode
          } else if ($isHeadingNode(node) || $isQuoteNode(node)) {
            node.replace($createParagraphNode(), true)
          } else if ($isDecoratorBlockNode(node)) {
            node.setFormat('')
          }
        })
      }
    })
  }, [activeEditor])

  const insertLink = useCallback(() => {
    if (!isLink) {
      setIsLinkEditMode(true)
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, sanitizeUrl('https://'))
    } else {
      setIsLinkEditMode(false)
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, null)
    }
  }, [editor, isLink, setIsLinkEditMode])

  useEffect(() => {
    // The speech to text doesn't work with Arc Browser
    setIsArcBrowser(
      !!getComputedStyle(document.documentElement).getPropertyValue(
        '--arc-palette-title'
      )
    )
  }, [])

  return (
    <>
      <div className="flex flex-wrap items-center justify-between gap-4 w-full">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-1">
            <Tooltip
              content={IS_APPLE ? `${t('Undo')} (⌘Z)` : `${t('Undo')} (Ctrl+Z)`}
            >
              <IconButton
                icon="Undo"
                dataTestId="lexical-editor-undo-button"
                disabled={!canUndo || !isEditable}
                onClick={() => {
                  activeEditor.dispatchCommand(UNDO_COMMAND, undefined)
                }}
              />
            </Tooltip>

            <Tooltip
              content={IS_APPLE ? `${t('Redo')} (⌘Y)` : `${t('Redo')} (Ctrl+Y)`}
            >
              <IconButton
                icon="Redo"
                dataTestId="lexical-editor-redo-button"
                disabled={!canRedo || !isEditable}
                onClick={() => {
                  activeEditor.dispatchCommand(REDO_COMMAND, undefined)
                }}
              />
            </Tooltip>
          </div>

          <Divider orientation="vertical" />

          <div className="flex items-center gap-1">
            {toolbarOptions.includes('familyFont') && (
              <FontDropDown
                disabled={!isEditable}
                style={'font-family'}
                value={fontFamily}
                editor={editor}
              />
            )}

            {toolbarOptions.includes('sizeFont') && (
              <FontSize
                selectionFontSize={fontSize.slice(0, -2)}
                editor={editor}
                disabled={!isEditable}
              />
            )}

            {toolbarOptions.includes('blockTypeFormat') &&
              blockType in blockTypeToBlockName &&
              activeEditor === editor && (
                <BlockFormatDropDown
                  disabled={!isEditable}
                  blockType={blockType}
                  rootType={rootType}
                  editor={editor}
                />
              )}

            {toolbarOptions.includes('textAlign') && (
              <ElementFormatDropdown
                disabled={!isEditable}
                value={elementFormat}
                editor={editor}
                isRTL={isRTL}
              />
            )}

            {toolbarOptions.includes('formatLink') && (
              <Tooltip content="Insert link">
                <IconButton
                  disabled={!isEditable}
                  dataTestId="lexical-editor-insert-link-button"
                  onClick={insertLink}
                  icon="Link"
                />
              </Tooltip>
            )}

            {toolbarOptions.includes('insertAttachmentBtn') && (
              <Tooltip content={t('Insert attachment') as string}>
                <AttachmentButton attachmentOptions={attachmentOptions} />
              </Tooltip>
            )}

            {toolbarOptions.includes('signature') && (
              <SignatureButton hasSignature={hasSignature || false} />
            )}

            {toolbarOptions.includes('variable') && (
              <VariableSelect
                name="selectVariable"
                selectPlaceholder={t('Insert variable', { ns: 'component' })}
                selectOptionEmptyMessage={t('No items found', { ns: 'common' })}
                inputSearchPlaceholder={t('Search', { ns: 'component' })}
                variables={variableOptions}
                onChange={option =>
                  option &&
                  activeEditor.dispatchCommand(INSERT_VARIABLE_COMMAND, {
                    value: option[2],
                    text: option[0],
                    version: 1,
                  })
                }
              />
            )}

            {toolbarOptions.includes('clearFormatting') && (
              <Tooltip content={t('Clear text formatting')}>
                <IconButton
                  dataTestId="lexical-editor-clear-formatting"
                  disabled={!isEditable}
                  onClick={clearFormatting}
                  icon="Trash"
                />
              </Tooltip>
            )}

            {isEssentialAdvancedEnterprisePlan &&
              isDev &&
              toolbarOptions.includes('aiAssistant') && (
                <Tooltip content={t('AI Assistant')}>
                  <AIAssistantPlugin editor={editor} />
                </Tooltip>
              )}

            {SUPPORT_SPEECH_RECOGNITION &&
              !isArcBrowser &&
              toolbarOptions.includes('speechToText') && (
                <Tooltip content={t('Speech to text')}>
                  <IconButton
                    dataTestId="lexical-editor-text-to-speech-button"
                    disabled={!isEditable}
                    onClick={() => {
                      editor.dispatchCommand(
                        SPEECH_TO_TEXT_COMMAND,
                        !isSpeechToText
                      )
                      setIsSpeechToText(!isSpeechToText)
                    }}
                    icon="Microphone"
                  />
                </Tooltip>
              )}
          </div>
        </div>

        {children}
      </div>

      {modal}
    </>
  )
}

ToolbarPlugin.defaultProps = {
  insertHorizontalBar: false,
  insertImage: false,
  insertImageBtn: false,
  insertAttachmentBtn: false,
  insertGif: false,
  leftAlign: false,
  rightAlign: false,
  centerAlign: false,
  justifyAlign: false,
  outdent: false,
  indent: false,
  insertExcalidraw: false,
  insertCollapsibleContainer: false,
  poll: false,
  equation: false,
  stickyNote: false,
  insertEmbed: false,
  undo: false,
  redo: false,
  clearFormat: false,
  superScript: false,
  subScript: false,
  strikeScript: false,
  fontColorSelect: false,
  bgColorSelect: false,
  formatCode: false,
  formatUnderline: false,
  formatItalic: false,
  formatBold: false,
  formatLink: false,
  familyFont: false,
  sizeFont: false,
  blockTypeFormat: false,
}

export default ToolbarPlugin
