import { Transition } from '@headlessui/react'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { mergeRegister } from '@lexical/utils'
import { clsx } from 'clsx'
import { t } from 'i18next'
import type { EditorState, LexicalCommand, LexicalEditor } from 'lexical'
import { COMMAND_PRIORITY_EDITOR, createCommand } from 'lexical'
import type { Dispatch, SetStateAction } from 'react'
import { useState, useEffect, forwardRef, useLayoutEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

import {
  Button,
  Divider,
  IconButton,
  Popover,
  Tooltip,
  Spinner,
  CardV2,
  Typography,
} from '@getheroes/ui'
import { Signature } from '@internals/assets/svg/icons/Signature'
import { useLazyGetAccountSettingsQuery } from '@internals/features/gmail/api/mailApi'
import { useMineConnectedAccountQuery } from '@internals/features/integration/api/integrationApi'
import { useIntegration } from '@internals/features/integration/hooks/useIntegration'
import { privateRoutes } from '@internals/hooks/useRoute'

export const INSERT_SIGNATURE_COMMAND: LexicalCommand<string | undefined> =
  createCommand('INSERT_SIGNATURE_COMMAND')

export const REMOVE_SIGNATURE_COMMAND: LexicalCommand<string | undefined> =
  createCommand('REMOVE_SIGNATURE_COMMAND')

const NoSignatureButton = () => {
  const navigate = useNavigate()
  return (
    <div
      className="text text-signature-plugin flex justify-center items-center  body-s-medium text-center"
      dangerouslySetInnerHTML={{
        __html: 'You have 0 signature <br /> Go to set your signature' ?? '',
      }}
      onClick={() => {
        navigate(
          `${privateRoutes.settings.path}/${privateRoutes.emailAndLinkedInSetting.path}`
        )
      }}
    />
  )
}

export const SignatureButton = ({
  hasSignature,
}: {
  hasSignature: boolean
}) => {
  /* #region Vars */

  const { t } = useTranslation(['mail'])
  const [editor] = useLexicalComposerContext()
  const mailIntegration = useIntegration()

  const [isOpenPopover, setIsOpenPopover] = useState(false)

  /* #region Queries */

  const [
    refreshAliases,
    { data: dataRefreshAlias, isLoading: isLoadingRefreshAlias },
  ] = useLazyGetAccountSettingsQuery()
  const { data: accountList = [] } = useMineConnectedAccountQuery()

  /* #region Effects */

  useEffect(() => {
    if (!mailIntegration.isUninitialized && !mailIntegration.isFetching) {
      refreshAliases({
        type: mailIntegration?.data?.name || '',
        accountId: accountList.find(
          ({ type, isConnected }) => isConnected && type === 'email'
        )?.id as string,
      })
    }
  }, [accountList, mailIntegration, refreshAliases])

  useEffect(() => {
    if (!hasSignature) {
      return
    }
    if (!dataRefreshAlias || dataRefreshAlias?.signature === '') {
      return
    }
    editor.dispatchCommand(
      INSERT_SIGNATURE_COMMAND,
      dataRefreshAlias?.signature
    )
  }, [dataRefreshAlias, hasSignature])

  /* #region Handlers */

  const handleAddSignature = () => {
    if (dataRefreshAlias?.signature === '') {
      return
    }
    editor.dispatchCommand(
      INSERT_SIGNATURE_COMMAND,
      dataRefreshAlias?.signature
    )
    setIsOpenPopover(false)
  }

  const handleClickRemoveSignature = () => {
    editor.dispatchCommand(REMOVE_SIGNATURE_COMMAND, '')
  }

  return (
    <Popover
      placement="top"
      open={isOpenPopover}
      onOpenChange={setIsOpenPopover}
    >
      <Popover.Trigger onClick={() => setIsOpenPopover(!isOpenPopover)}>
        <Tooltip content={t('Add your signature')}>
          <Button iconRight="NavArrowDown" size="medium">
            {t('Signature')}
          </Button>
        </Tooltip>
      </Popover.Trigger>

      <Popover.Content onMouseDown={event => event.stopPropagation()}>
        {isLoadingRefreshAlias ? (
          <div className="flex items-center justify-center h-full w-full">
            <Spinner />
          </div>
        ) : (
          <div className="flex flex-col gap-2">
            {dataRefreshAlias?.signature === '' ? (
              <NoSignatureButton />
            ) : (
              <div
                onClick={handleAddSignature}
                className="text text-signature-plugin p-2 rounded-md hover:bg-base-default"
                dangerouslySetInnerHTML={{
                  __html: dataRefreshAlias?.signature ?? '',
                }}
              />
            )}

            <Divider />

            <Button
              variant="danger"
              size="small"
              onClick={handleClickRemoveSignature}
            >
              {t('Remove signature')}
            </Button>
          </div>
        )}
      </Popover.Content>
    </Popover>
  )
}

type SignaturePluginProps = {
  selectedSignature: string
  setSelectedSignature: Dispatch<SetStateAction<string>>
  onChange: (
    editorState: EditorState,
    editor: LexicalEditor,
    addSignature: boolean
  ) => void
}

export const SignaturePlugin = forwardRef(
  (
    { selectedSignature, setSelectedSignature, onChange }: SignaturePluginProps,
    ref
  ) => {
    const [editor] = useLexicalComposerContext()
    const [isVisible, setIsVisible] = useState(false)

    useLayoutEffect(() => {
      if (!editor) {
        return
      }
      return mergeRegister(
        editor.registerCommand(
          INSERT_SIGNATURE_COMMAND,
          (signature: string) => {
            setSelectedSignature(signature)
            onChange(editor.getEditorState(), editor, true)
            return false
          },
          COMMAND_PRIORITY_EDITOR
        ),
        editor.registerCommand(
          REMOVE_SIGNATURE_COMMAND,
          () => {
            setSelectedSignature('')
            onChange(editor.getEditorState(), editor, false)
            return false
          },
          COMMAND_PRIORITY_EDITOR
        )
      )
    }, [editor, setSelectedSignature])

    return (
      selectedSignature && (
        <CardV2 isHoverable>
          <div
            className={clsx('w-full body-s-regular relative', {
              'h-40 overflow-auto': isVisible,
            })}
          >
            <div className="absolute top-0 right-2 h-full flex items-center gap-2">
              <Tooltip
                content={
                  !isVisible
                    ? t('Show signature', { ns: 'mail' })
                    : t('Hide signature', { ns: 'mail' })
                }
              >
                <IconButton
                  dataTestId={'lexical-editor-preview-button'}
                  type="button"
                  variant={'tertiary-outlined'}
                  onClick={() => {
                    setIsVisible(!isVisible)
                  }}
                  icon={isVisible ? 'Eye' : 'EyeClosed'}
                />
              </Tooltip>

              <Tooltip content={t('Remove signature', { ns: 'mail' })}>
                <IconButton
                  dataTestId={'lexical-editor-signature-remove-button'}
                  variant={'tertiary-outlined'}
                  onClick={() => {
                    editor.dispatchCommand(REMOVE_SIGNATURE_COMMAND, '')
                    setIsVisible(false)
                  }}
                  icon={'Trash'}
                />
              </Tooltip>
            </div>

            <Transition
              appear
              show={isVisible}
              enter="transition-opacity ease-in-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="transition-opacity ease-in-out duration-0"
              leaveFrom="opacity-0"
              leaveTo="opacity-0"
            >
              <div
                className="pointer-events-none signature-html"
                dangerouslySetInnerHTML={{
                  __html: selectedSignature ?? '',
                }}
              />
            </Transition>

            <Transition
              appear
              show={!isVisible}
              enter="transition-opacity ease-in-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="transition-opacity ease-in-out duration-0"
              leaveFrom="opacity-0"
              leaveTo="opacity-0"
            >
              <div className="flex items-center gap-2">
                <Signature />

                <Typography size="s" weight="medium">
                  {t('Signature')}
                </Typography>
              </div>
            </Transition>
          </div>
        </CardV2>
      )
    )
  }
)
