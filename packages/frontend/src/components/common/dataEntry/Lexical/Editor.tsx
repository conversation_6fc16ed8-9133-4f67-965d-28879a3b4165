import { $generateHtmlFromNodes, $generateNodesFromDOM } from '@lexical/html'
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin'
import { CharacterLimitPlugin } from '@lexical/react/LexicalCharacterLimitPlugin'
import { CheckListPlugin } from '@lexical/react/LexicalCheckListPlugin'
import { ClearEditorPlugin } from '@lexical/react/LexicalClearEditorPlugin'
import LexicalClickableLinkPlugin from '@lexical/react/LexicalClickableLinkPlugin'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import LexicalErrorBoundary from '@lexical/react/LexicalErrorBoundary'
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin'
import { ListPlugin } from '@lexical/react/LexicalListPlugin'
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin'
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin'
import { clsx } from 'clsx'
import type { EditorState, LexicalEditor, LexicalNode } from 'lexical'
import { $getRoot, $insertNodes } from 'lexical'
import isEmpty from 'lodash/isEmpty'
import isEqual from 'lodash/isEqual'
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import sanitizeHtml from 'sanitize-html'

import { Icon, IconButton, Tooltip, Typography } from '@getheroes/ui'
import { ClickListener } from '@getheroes/ui-business'
import { VariableSelect } from '@internals/components/business/lead/contact/VariableSelect/VariableSelect'
import { useSettings } from '@internals/components/common/dataEntry/Lexical/context/SettingsContext'
import { useSharedHistoryContext } from '@internals/components/common/dataEntry/Lexical/context/SharedHistoryContext'
import { useVariableContext } from '@internals/components/common/dataEntry/Lexical/context/VariableContext'
import type {
  EditorProps,
  LexicalImperativeHandle,
} from '@internals/components/common/dataEntry/Lexical/lexical.type'
import type { AttachmentPluginImperativeHandle } from '@internals/components/common/dataEntry/Lexical/plugins/AttachmentsPlugin/AttachmentPlugin'
import { AttachmentPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/AttachmentsPlugin/AttachmentPlugin'
import { AutoLinkPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/AutoLinkPlugin'
import { AutocompletePlugin } from '@internals/components/common/dataEntry/Lexical/plugins/AutocompletePlugin'
import { CodeHighlightPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/CodeHighlightPlugin'
import { ComponentPickerMenuPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/ComponentPickerMenuPlugin'
import { DragDropPaste } from '@internals/components/common/dataEntry/Lexical/plugins/DragDropPastePlugin'
import { DraggableBlockPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/DraggableBlockPlugin'
import { FloatingLinkEditorPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/FloatingLinkEditorPlugin'
import { FloatingTextFormatToolbarPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/FloatingTextFormatToolbarPlugin'
import { LinkPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/LinkPlugin'
import { ListMaxIndentLevelPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/ListMaxIndentLevelPlugin'
import { LocalStoragePlugin } from '@internals/components/common/dataEntry/Lexical/plugins/LocalStoragePlugin'
import { MarkdownShortcutPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/MarkdownShortcutPlugin'
import { MaxLengthPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/MaxLengthPlugin'
import { SignaturePlugin } from '@internals/components/common/dataEntry/Lexical/plugins/SignaturePlugin'
import { SingleLinePlugin } from '@internals/components/common/dataEntry/Lexical/plugins/SingleLinePlugin'
import SpeechToTextPlugin from '@internals/components/common/dataEntry/Lexical/plugins/SpeechToTextPlugin'
import { TabFocusPlugin } from '@internals/components/common/dataEntry/Lexical/plugins/TabFocusPlugin'
import ToolbarPlugin from '@internals/components/common/dataEntry/Lexical/plugins/ToolbarPlugin'
import {
  INSERT_VARIABLE_COMMAND,
  VariablePlugin,
} from '@internals/components/common/dataEntry/Lexical/plugins/VariablePlugin'
import type { AttachmentModel } from '@internals/components/common/dataEntry/Lexical/ui/AttachmentBar'
import { ContentEditable } from '@internals/components/common/dataEntry/Lexical/ui/ContentEditable'
import { Placeholder } from '@internals/components/common/dataEntry/Lexical/ui/Placeholder'
import { LEXICAL_EMPTY_CONTENT } from '@internals/components/common/dataEntry/Lexical/utils/isEmptyLegacyContent'
import { sanitizeLexicalHtml } from '@internals/components/common/dataEntry/Lexical/utils/sanitizeLexicalHtml'
import { setEditorStateControlled } from '@internals/components/common/dataEntry/Lexical/utils/setEditorStateControlled'
import { useCurrentPlan } from '@internals/features/subscription/hooks/useCurrentPlan'
import { isHtml } from '@internals/utils/validation'

const DEFAULT_MAX_CHARS = 524288

export const Editor = forwardRef<LexicalImperativeHandle, EditorProps>(
  (
    {
      toolbarOptions = [],
      variableOptions = [],
      variableReferences,
      autoFocus,
      children,
      editable = true,
      maxLength,
      onChange,
      onBlur,
      isEnablePreviewOnClick,
      className = '',
      attachmentOptions,
      namespaceLocalStorage = '',
      placeholder,
      initalEditorState,
      isLexicalPreview,
      setIsEditorPreview,
      toolbarClassName,
      dataTestId,
      getAttachments,
      floatingToolbarOptions = [],
      hasError = false,
      toolbarLocation,
      hasBlockDraggable = false,
      showToolbar,
      showFloatingToolbar = true,
      isPlaceholderSingleLine = true,
      variant = 'textarea',
    }: EditorProps,
    ref
  ) => {
    /* #region Vars */

    const [isHovered, setIsHovered] = useState<boolean>(false)
    const [isFocused, setIsFocused] = useState<boolean>(false)
    const [floatingAnchorElem, setFloatingAnchorElem] =
      useState<HTMLDivElement | null>(null)
    const [attachments, setAttachments] = useState<AttachmentModel[]>([])
    const [htmlContent, setHtmlContent] = useState<string>(
      LEXICAL_EMPTY_CONTENT
    )
    const [textContent, setTextContent] = useState<string>('')
    const [selectedSignature, setSelectedSignature] = useState<string>('')
    const [isLinkEditMode, setIsLinkEditMode] = useState<boolean>(false)

    const fileAttachmentRef = useRef<AttachmentPluginImperativeHandle>(null)

    const { isEssentialAdvancedEnterprisePlan } = useCurrentPlan()
    const [editor] = useLexicalComposerContext()
    const { t } = useTranslation('component')
    const { historyState } = useSharedHistoryContext()
    const { setReference, setIsPreviewCtx, setVariableOptions, isPreview } =
      useVariableContext()
    const {
      settings: { isRichText },
    } = useSettings()

    const placeHolderDefaultText = isRichText
      ? t('Enter some rich text...', { ns: 'mail' })
      : t('Enter some plain text...', { ns: 'mail' })

    const placeholderElement = (
      <Placeholder
        className={clsx({
          'left-0 top-1': toolbarOptions?.includes('singleLine'),
          'whitespace-nowrap': isPlaceholderSingleLine,
        })}
      >
        {placeholder ?? placeHolderDefaultText}
      </Placeholder>
    )

    /* #region Effects */

    useEffect(() => {
      editor.setEditable(editable)
    }, [editable])

    useEffect(() => {
      if (variableReferences) {
        setReference(variableReferences)
      }
    }, [variableReferences])

    useEffect(() => {
      if (isLexicalPreview) {
        editor.setEditable(false)
        setIsPreviewCtx(isLexicalPreview)
      }
    }, [isLexicalPreview])

    useEffect(() => {
      if (variableOptions) {
        setVariableOptions(variableOptions)
      }
    }, [variableOptions])

    useEffect(() => {
      if (initalEditorState) {
        setEditorStateControlled(editor, initalEditorState)
      }
    }, [editor, initalEditorState])

    /* #region Functions */

    const onRef = (_floatingAnchorElem: HTMLDivElement) => {
      if (_floatingAnchorElem !== null) {
        setFloatingAnchorElem(_floatingAnchorElem)
      }
    }

    const onUpdateEditorSignatureState = (
      _: EditorState,
      editor: LexicalEditor,
      addSignature: boolean
    ) => {
      editor.update(() => {
        if (onChange) {
          onChange({ addSignature })
        }
      })
    }

    const onUpdateEditorState = (_: EditorState, editor: LexicalEditor) => {
      editor.update(() => {
        const htmlString = $generateHtmlFromNodes(editor, null)
        const sanitizedHtml = sanitizeLexicalHtml(htmlString)
        const rawString = $getRoot().getTextContent()
        setHtmlContent(sanitizedHtml)
        setTextContent(rawString)

        if (onChange && !isEqual(htmlContent, sanitizedHtml)) {
          onChange({
            editorState: editor.getEditorState().toJSON(),
            html: sanitizedHtml,
            raw: rawString,
          })
        }
      })
    }

    useImperativeHandle(ref, () => ({
      getEditor: () => {
        return editor
      },
      setEditorState: (editorState: string) => {
        setEditorStateControlled(editor, editorState)
      },
      getEditorState: () => {
        return editor.getEditorState().toJSON()
      },
      getAttachments: () => {
        return attachments?.map(item => ({
          data: item.data,
          name: item.name,
          size: item.size,
        }))
      },
      setAttachments: (attachments: AttachmentModel[]) => {
        setAttachments(attachments)
        fileAttachmentRef?.current?.setFileAttachment(attachments)
      },
      setHtmlContent: (html: string) => {
        const sanitizedHtml = sanitizeHtml(
          !isHtml(html) ? `<p><span>${html}</span></p>` : html
        )
        editor.update(() => {
          const parser = new DOMParser()
          const dom = parser.parseFromString(sanitizedHtml, 'text/html')
          const nodes: LexicalNode[] = $generateNodesFromDOM(editor, dom)

          // Select the root
          $getRoot().clear()
          $getRoot().select()

          // const isEmpty = $getRoot().isEmpty()
          // if (!isEmpty) {
          //   $getRoot().select().insertLineBreak()
          // }
          $insertNodes(nodes)
        })
      },
      getHtmlContent: () => {
        return htmlContent
      },
      getTextContent: () => {
        return textContent
      },
      clear: () => {
        editor.update(() => {
          $getRoot().clear()
        })
      },
      addHtmlContent: (html: string) => {
        const sanitizedHtml = sanitizeHtml(html)
        editor.update(() => {
          const parser = new DOMParser()
          const dom = parser.parseFromString(sanitizedHtml, 'text/html')
          const nodes = $generateNodesFromDOM(editor, dom)

          const isEmpty = $getRoot().isEmpty()

          if (!isEmpty) {
            $getRoot().select().insertLineBreak()
          }

          $insertNodes(nodes)
        })
      },
      setIsEditable: async (isEditable: boolean) => {
        editor.setEditable(isEditable)
      },
      setIsPreview: (isPreview: boolean) => {
        editor.setEditable(!isPreview)
        setIsPreviewCtx(isPreview)
      },
      getIsSignature: () => !isEmpty(selectedSignature),
      setSelectedSignature: (signature: string) => {
        setSelectedSignature(signature)
      },
      setHasSignature: hasSignature => {
        if (hasSignature) {
          setSelectedSignature(hasSignature.toString())
        }
      },
      getSelectedSignature: () => selectedSignature,
      focus: () => editor.focus(),
    }))

    const handlePreviewMode = (value: boolean) => {
      if (setIsEditorPreview) {
        setIsEditorPreview(value)
      }
      setIsPreviewCtx(value)
      editor.setEditable(!value)
      setIsFocused(!value)
    }

    const handleClickOutside = () => {
      if (onBlur) {
        onBlur()
      }

      if (isEnablePreviewOnClick) {
        handlePreviewMode(true)
      }
    }

    const handleClikInside = () => {
      if (isEnablePreviewOnClick) {
        handlePreviewMode(false)
      }
    }

    return (
      <ClickListener
        isTriggerOnClickOutsideOnlyAfterClickInside
        onClickOutside={handleClickOutside}
        onClickInside={handleClikInside}
        className={clsx(
          'c-lexical flex flex-col gap-4 transition min-h-10 p-2 py-[5px] bg-base-primary border border-ui-base-subtle rounded-xl',
          {
            'hover:border-action-secondary-active hover:bg-base-secondary':
              !hasError,
          },
          {
            '!bg-action-disabled cursor-not-allowed !py-2': !editable,
          },
          {
            '!border-action-primary-active !bg-base-secondary': isFocused,
          },
          {
            'border-ui-error-subtle hover:border-ui-error-accent hover:bg-base-error':
              hasError,
          },
          {
            'flex-col-reverse': toolbarLocation === 'bottom',
            'py-1': toolbarOptions.includes('singleLine'),
          },
          {
            'h-full !py-3': variant === 'textarea',
            'h-10': variant === 'input',
          }
        )}
      >
        {editable && showToolbar && !toolbarOptions.includes('singleLine') && (
          <div
            className={clsx('c-toolbar-attachment flex w-full', {
              'items-end': !editor.isEditable(),
            })}
          >
            {isRichText && editor.isEditable() && !isPreview ? (
              <ToolbarPlugin
                toolbarClassName={toolbarClassName}
                variableOptions={variableOptions}
                toolbarOptions={toolbarOptions}
                attachmentOptions={attachmentOptions}
                hasSignature={Boolean(selectedSignature)}
                setIsLinkEditMode={setIsLinkEditMode}
                isEssentialAdvancedEnterprisePlan={
                  isEssentialAdvancedEnterprisePlan
                }
              >
                {children}
              </ToolbarPlugin>
            ) : (
              <div className="flex justify-end w-full">{children}</div>
            )}
          </div>
        )}

        {toolbarOptions.includes('singleLine') && <SingleLinePlugin />}

        {(toolbarOptions.includes('signature') ||
          toolbarOptions.includes('insertAttachmentBtn')) && (
          <div className="flex flex-col gap-2">
            {toolbarOptions.includes('signature') && (
              <SignaturePlugin
                selectedSignature={selectedSignature}
                setSelectedSignature={setSelectedSignature}
                onChange={onUpdateEditorSignatureState}
              />
            )}
            {toolbarOptions.includes('insertAttachmentBtn') && (
              <AttachmentPlugin
                ref={fileAttachmentRef}
                attachmentOptions={attachmentOptions}
                setAttachments={setAttachments}
                getAttachments={getAttachments}
              />
            )}
          </div>
        )}

        <div
          data-testid={`${dataTestId}-editor-container`}
          className={clsx(
            'editor-container relative flex flex-col gap-4 flex-grow overflow-y-auto',
            className,
            {
              '!flex !flex-row w-full items-center':
                toolbarOptions.includes('singleLine'),
            }
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {toolbarOptions.includes('preview') &&
            !isEmpty(variableReferences) &&
            !isEnablePreviewOnClick &&
            isHovered && (
              <div className="absolute top-0 right-0 z-zPreviewButtonLexical">
                <Tooltip
                  content={
                    !isPreview
                      ? (t('Show preview', { ns: 'component' }) as string)
                      : (t('Hide preview', { ns: 'component' }) as string)
                  }
                >
                  <IconButton
                    data-testid="lexical-editor-preview-button"
                    type="button"
                    onClick={() => {
                      handlePreviewMode(!isPreview)
                      setIsFocused(!isPreview)
                    }}
                    icon={isPreview ? 'EyeClosed' : 'Eye'}
                  />
                </Tooltip>
              </div>
            )}

          <OnChangePlugin onChange={onUpdateEditorState} />

          <RichTextPlugin
            contentEditable={
              <div className="body-s-regular flex-grow">
                <div
                  className={clsx(
                    'flex items-center editor gap-1 h-full w-full',
                    {
                      'resize-none': toolbarOptions.includes('singleLine'),
                      'flex-col': !toolbarOptions.includes('singleLine'),
                    }
                  )}
                  ref={onRef}
                >
                  <ContentEditable
                    className={clsx(
                      { 'p-0': !hasBlockDraggable },
                      toolbarOptions.includes('singleLine')
                        ? 'single-line flex-grow'
                        : ''
                    )}
                  />

                  {toolbarOptions.includes('isSentByZeliq') && (
                    <Typography
                      size="s"
                      weight="medium"
                      color="base-subtle"
                      align="right"
                    >
                      {t('Sent by Zeliq')}
                    </Typography>
                  )}
                </div>
              </div>
            }
            placeholder={placeholderElement}
            ErrorBoundary={LexicalErrorBoundary}
          />

          <MaxLengthPlugin maxLength={maxLength || DEFAULT_MAX_CHARS} />

          {editable && (
            <>
              {!toolbarOptions.includes('singleLine') && (
                <>
                  {/*<AutoEmbedPlugin />*/}
                  {/*<YouTubePlugin />*/}
                  <ComponentPickerMenuPlugin />
                  <HistoryPlugin externalHistoryState={historyState} />
                  <DragDropPaste />
                  <ClearEditorPlugin />
                  <AutoLinkPlugin />
                  <MarkdownShortcutPlugin />
                  <CodeHighlightPlugin />
                  <ListPlugin />
                  <CheckListPlugin />
                  <ListMaxIndentLevelPlugin maxDepth={7} />
                  {!isPreview && <TabFocusPlugin />}
                  <SpeechToTextPlugin />
                  <LexicalClickableLinkPlugin disabled={editor.isEditable()} />
                  {/*<ImagesPlugin />*/}
                  <LinkPlugin />
                  {showFloatingToolbar && floatingAnchorElem && (
                    <>
                      {/* FLOAT TOOLBAR */}
                      {hasBlockDraggable &&
                        !toolbarOptions.includes('singleLine') && (
                          <DraggableBlockPlugin
                            anchorElem={floatingAnchorElem}
                          />
                        )}
                      {/* FLOAT FORM LINK */}
                      <FloatingLinkEditorPlugin
                        anchorElem={floatingAnchorElem}
                        isLinkEditMode={isLinkEditMode}
                        setIsLinkEditMode={setIsLinkEditMode}
                      />
                      {/* FLOAT TOOLBAR */}
                      <FloatingTextFormatToolbarPlugin
                        anchorElem={floatingAnchorElem}
                        setIsLinkEditMode={setIsLinkEditMode}
                        floatingToolbarOptions={floatingToolbarOptions}
                      />
                    </>
                  )}

                  {toolbarOptions.includes('maxCharLimit') && (
                    <CharacterLimitPlugin
                      charset={
                        !toolbarOptions.includes('isCharLimitUtf8')
                          ? 'UTF-16'
                          : 'UTF-8'
                      }
                      maxLength={maxLength || DEFAULT_MAX_CHARS}
                    />
                  )}
                  {toolbarOptions.includes('autoComplete') && (
                    <AutocompletePlugin />
                  )}

                  <LocalStoragePlugin namespace={namespaceLocalStorage} />
                </>
              )}

              {autoFocus && !isPreview && <AutoFocusPlugin />}

              {toolbarOptions.includes('variable') && (
                <>
                  <VariablePlugin variableOptions={variableOptions} />
                  {/* TODO Create Component DropdownVariable (this code is duplicate in ToolbarPlugin) */}
                  {toolbarOptions.includes('singleLine') && (
                    <div className={clsx({ invisible: isPreview })}>
                      <VariableSelect
                        name="selectVariable"
                        selectPlaceholder={t('Insert variable')}
                        selectOptionEmptyMessage={t('No items found', {
                          ns: 'common',
                        })}
                        inputSearchPlaceholder={t('Search')}
                        variables={variableOptions}
                        renderTrigger={() => (
                          <Tooltip
                            placement="left"
                            content={t('Insert variable', {
                              ns: 'component',
                            })}
                          >
                            <Icon name="PlusCircle" />
                          </Tooltip>
                        )}
                        onChange={option =>
                          option &&
                          editor.dispatchCommand(INSERT_VARIABLE_COMMAND, {
                            value: option[2],
                            text: option[0],
                            version: 1,
                          })
                        }
                      />
                    </div>
                  )}
                </>
              )}
            </>
          )}
        </div>
      </ClickListener>
    )
  }
)

export default Editor
