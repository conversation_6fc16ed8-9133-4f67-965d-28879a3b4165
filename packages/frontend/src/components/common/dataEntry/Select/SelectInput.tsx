import { clsx } from 'clsx'
import type { ComponentProps, ComponentType } from 'react'
import React, { forwardRef, useEffect, useImperativeHandle } from 'react'
import { useTranslation } from 'react-i18next'
import type {
  DropdownIndicatorProps,
  GroupBase,
  MultiValue,
  MultiValueGenericProps,
  OnChangeValue,
  OptionProps,
  OptionsOrGroups,
  SingleValue,
  StylesConfig,
} from 'react-select'
import { default as ReactSelect, components } from 'react-select'

import { Icon } from '@getheroes/ui'
import {
  SelectSize,
  selectSizes,
  SelectVariant,
} from '@internals/components/common/dataEntry/Select/SelectInput-export'
import { Header } from '@internals/components/common/dataEntry/components/Header'
import { Spinner } from '@internals/components/common/feedback/Spinner/Spinner'
import type { FormFields, FormInputValue } from '@internals/models/form'

import './Select.scoped.scss'

type ValueObject = { [key: string]: string | number | string[] }

export type SelectOption = {
  value: string | number | string[] | ValueObject[] | ValueObject
  label: string | JSX.Element
  key?: string
  icon?: React.ReactNode
  iconUrl?: string
  isDisabled?: boolean
  [key: string]: any
}

type ReactSelectProps = ComponentProps<typeof ReactSelect>

export type SelectOptionsOrGroups = OptionsOrGroups<
  SelectOption,
  GroupBase<SelectOption>
>

/**
 * @deprecated Use `Select` in DS
 */
export type SelectInputProps = Omit<ReactSelectProps, 'onChange' | 'ref'> &
  FormFields & {
    onChange: (event: SelectOption) => void
    options: SelectOptionsOrGroups
    defaultValue?: SelectOption
    defaultValues?: SelectOptionsOrGroups
    selectClassname?: string
    isMulti?: boolean
    fullWidth?: boolean
    helperText?: string
    CustomOptionComponent?: ComponentType<
      OptionProps<SelectOption, boolean, GroupBase<SelectOption>>
    >
    MultiValueContainer?: ComponentType<
      MultiValueGenericProps<SelectOption, boolean, GroupBase<SelectOption>>
    >
    styles?: StylesConfig<SelectOption, boolean, GroupBase<SelectOption>>
    /**
     * If true, the dropdown menu will render correctly even when in a modal.
     * But it will lose its styling (classes are not passed correctly)
     * TODO: fix the loss of style when menuPortalTarget is set to document.body
     */
    forceZindex?: boolean
    formatOptionLabel?: (option: SelectOption) => string | JSX.Element
    formatGroupLabel?: ReactSelectProps['formatGroupLabel']
    // TODO Pelo: need design for possible variants
    variant?: SelectVariant
    selectSize?: SelectSize
  }

export type SelectInputHandle = {
  reset: () => void
}

export const SelectInput = forwardRef<SelectInputHandle, SelectInputProps>(
  (
    {
      id,
      options,
      defaultValue,
      defaultValues,
      name,
      onChange,
      label = undefined,
      placeholder = '',
      disabled,
      readonly = false,
      error,
      value,
      isMulti = false,
      menuPlacement = 'auto',
      menuPosition = 'absolute',
      menuShouldScrollIntoView = false,
      minMenuHeight,
      maxMenuHeight,
      helperText,
      // Styles
      style,
      className = '',
      labelClassName = '',
      selectClassname = '',
      isStackingFormfield = true,
      isShowOptional,
      CustomOptionComponent,
      MultiValueContainer,
      styles,
      fullWidth = false,
      // Test
      dataTestId,
      // TODO: fix the dropdown menu positioning and remove this prop
      forceZindex = false,
      formatOptionLabel,
      variant = SelectVariant.DEFAULT,
      selectSize = SelectSize.DEFAULT,
      ...props
    },
    ref
  ) => {
    const inputId = id ?? name
    const isError = Boolean(error)

    // prettier-ignore
    const [selectedOption, setSelectedOption] = React.useState<SelectOption | null>(defaultValue || null)
    const [multiSelectedOptions, setMultiSelectedOptions] =
      React.useState<SelectOptionsOrGroups>(defaultValues || [])
    const [selectIsOpen, setSelectIsOpen] = React.useState<boolean>(false)
    const { t } = useTranslation('form')

    useEffect(() => {
      if (defaultValue) {
        setSelectedOption(defaultValue)
      }
      if (defaultValues) {
        setMultiSelectedOptions(defaultValues)
      }
    }, [defaultValue, defaultValues])

    // intermediary method to provide the form's onChange function
    // with the expected value format
    const handleChange = (event: OnChangeValue<SelectOption, false>) => {
      if (!event) {
        return
      }
      const inputValue: FormInputValue<string | number> = {
        target: {
          ...event,
          name: name,
        },
      }
      setSelectedOption(event)
      onChange(inputValue)
    }

    const handleChangeMulti = (event: OnChangeValue<SelectOption, true>) => {
      const inputValue: FormInputValue<(string | number)[]> = {
        target: {
          name,
          value: event.map(option => option.value),
          label: event.map(option => option.label),
        },
      }
      setMultiSelectedOptions(event as SelectOption[])
      onChange(inputValue)
    }
    const reset = () => {
      setSelectedOption(defaultValue || null)
      setMultiSelectedOptions(defaultValues || [])
    }

    useImperativeHandle(ref, () => ({
      reset: () => {
        reset()
      },
    }))

    return (
      <div
        ref={ref}
        data-testid={`${dataTestId}-container`}
        className={clsx('c-form-field', className, {
          'c-form-field--no-stacking': !isStackingFormfield,
        })}
        style={{
          ...style,
          // To prevent overlapping consecutive select, the z-index is
          // dynamically adjusted. An open select will have a bigger
          // z-index to ensure it is on the top.
          zIndex: `var(${
            selectIsOpen ? '--zFormFieldOpenSelect' : '--zFormFieldForm'
          })` as unknown as number,
        }}
      >
        <Header
          label={label}
          name={name}
          helperText={helperText}
          isShowOptional={isShowOptional}
          labelClassName={labelClassName}
        />
        <ReactSelect
          styles={styles}
          id={`${dataTestId}-select`}
          menuPortalTarget={forceZindex ? document.body : undefined}
          options={options}
          minMenuHeight={minMenuHeight}
          maxMenuHeight={maxMenuHeight}
          menuPlacement={menuPlacement}
          menuPosition={menuPosition}
          menuShouldScrollIntoView={menuShouldScrollIntoView}
          value={isMulti ? multiSelectedOptions : selectedOption}
          inputId={inputId || `${dataTestId}-select-input`}
          instanceId={`${dataTestId}-select-instance-id`}
          name={name}
          isMulti={isMulti}
          isDisabled={disabled || readonly}
          placeholder={placeholder}
          inputValue={value}
          noOptionsMessage={() => t('No result found')}
          className={clsx(
            'c-form-field__form c-select w-full',
            selectClassname,
            selectSizes[selectSize],
            {
              'has-error': isError,
              'c-select--is-basic-variant': variant === SelectVariant.BASIC,
              'c-select--is-open': selectIsOpen,
              '!z-zSelectMenu': forceZindex,
            }
          )}
          classNamePrefix={'c-select'}
          onMenuOpen={() => setSelectIsOpen(true)}
          onMenuClose={() => setSelectIsOpen(false)}
          onChange={event => {
            if (!readonly) {
              isMulti
                ? handleChangeMulti(event as MultiValue<SelectOption>)
                : handleChange(event as SingleValue<SelectOption>)
            }
          }}
          formatOptionLabel={
            formatOptionLabel ??
            ((option: SelectOption) => {
              if (option.icon) {
                return (
                  <div
                    className={clsx('flex items-center', {
                      'cursor-disabled': option.isDisabled,
                    })}
                  >
                    <span className="mr-2 shrink-0">{option.icon}</span>
                    <span className={clsx('label-xs')}>{option.label}</span>
                  </div>
                )
              } else {
                return <>{option.label}</>
              }
            })
          }
          aria-invalid={isError}
          aria-live="polite"
          components={{
            ...(CustomOptionComponent
              ? {
                  Option: CustomOptionComponent,
                }
              : {}),
            ...(MultiValueContainer
              ? {
                  MultiValueContainer,
                }
              : {}),
            LoadingIndicator: () => (
              <Spinner size={'1rem'} className="flex items-center pr-2" />
            ),
            DropdownIndicator,
          }}
          {...props}
        />

        {/* Error messages */}
        {isError && <p className="text-textError text-size02">{error}</p>}
      </div>
    )
  }
)

const DropdownIndicator = (
  props: JSX.IntrinsicAttributes &
    DropdownIndicatorProps<unknown, boolean, GroupBase<unknown>>
) =>
  components.DropdownIndicator && (
    <components.DropdownIndicator className="c-dropdown-indicator" {...props}>
      <Icon name={'NavArrowDown'} />
    </components.DropdownIndicator>
  )
