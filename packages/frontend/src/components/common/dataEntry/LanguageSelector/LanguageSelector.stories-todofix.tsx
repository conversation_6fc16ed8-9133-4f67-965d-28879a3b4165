import type { Meta, StoryObj } from '@storybook/react'
import { useTranslation } from 'react-i18next'

import { LanguageSelector } from '@internals/components/common/dataEntry/LanguageSelector/LanguageSelector'

const meta: Meta<typeof LanguageSelector> = {
  component: LanguageSelector,
  decorators: [
    Story => {
      const { t } = useTranslation(['settings', 'sequence'])

      return (
        <div className="flex flex-col gap-6 min-h-64">
          {/*<Helper
            showZeliqHelpButton
            title={t('Feeling lost?', { ns: 'sequence' })}
            description={t(
              'Check out our guides on our Help Center to learn more about automation and sequences in Zeliq.'
            )}
            icon={<QuestionMark />}
          /> */}

          <Story className="min-h-fit" />
        </div>
      )
    },
  ],
  tags: ['LanguageSelector'],
}

export default meta
type Story = StoryObj<typeof LanguageSelector>

export const Default: Story = {}
