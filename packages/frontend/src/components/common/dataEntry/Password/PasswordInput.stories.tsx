import type { StoryObj, Meta } from '@storybook/react'

import { PasswordInput } from './PasswordInput'

// --------------------------------------------
// Story configuration 🔻
// --------------------------------------------

const meta: Meta = {
  component: PasswordInput,
  tags: ['Inputs', 'PasswordInput'],
  decorators: [
    Story => (
      <div className="flex">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    label: {
      name: 'label',
      defaultValue: null,
      description: 'The label of the input',
      control: {
        type: 'text',
      },
    },
    placeholder: {
      name: 'placeholder',
      defaultValue: null,
      description: 'The placeholder of the input',
      control: {
        type: 'text',
      },
    },
    disabled: {
      name: 'disabled',
      defaultValue: false,
      description: 'Whether the input is disabled or not',
      control: {
        type: 'boolean',
      },
    },
    required: {
      name: 'required',
      defaultValue: false,
      description: 'Whether the input is required or not',
      control: {
        type: 'boolean',
      },
    },
    startIcon: {
      name: 'startIcon',
      defaultValue: null,
      description: 'The icon to be displayed at the start of the input',
      control: {
        type: 'select',
        options: {
          none: null,
          Enveloppe: '✉',
          Lock: '🔒',
        },
      },
    },
    endIcon: {
      name: 'endIcon',
      defaultValue: null,
      description: 'The icon to be displayed at the end of the input',
      control: {
        type: 'select',
        options: {
          none: null,
          Enveloppe: '✉',
          Lock: '🔒',
        },
      },
    },
    id: {
      name: 'id',
      defaultValue: null,
      description: 'The id of the input',
    },
    name: {
      name: 'name',
      defaultValue: null,
      description: 'The name of the input',
    },
    value: {
      name: 'value',
      defaultValue: null,
      description: 'The value of the input',
    },
    onChange: {
      name: 'onChange',
      defaultValue: null,
      description: 'The function to be called when the input value changes',
    },
    error: {
      name: 'error',
      defaultValue: null,
      description: 'The error message to be displayed',
    },
    className: {
      name: 'className',
      defaultValue: null,
      description: 'The class of the input',
    },
    classNameLabel: {
      name: 'classNameLabel',
      defaultValue: null,
      description: 'The class of the label',
    },
    classNameInput: {
      name: 'classNameInput',
      defaultValue: null,
      description: 'The class of the input',
    },
  },
}

export default meta

type Story = StoryObj<typeof PasswordInput>

// --------------------------------------------
// Stories 🔻
// --------------------------------------------

export const Default: Story = {
  args: {
    label: 'Password',
    placeholder: 'Placeholder',
  },
}

export const WithoutShowPasswordButton: Story = {
  args: {
    label: 'Password',
    placeholder: 'Placeholder',
    showPasswordButton: false,
  },
}

export const Error: Story = {
  args: {
    label: 'Password',
    placeholder: 'Placeholder',
    error: 'This field is required',
  },
}
