import type { IRowNode } from 'ag-grid-community'
import { clsx } from 'clsx'
import { isEmpty } from 'lodash'
import type { ChangeEvent } from 'react'
import { useEffect, useState } from 'react'

import { useGridContext } from '../../GridProvider-export'

export const CustomCheckboxHeaderCell = ({
  shouldSelectCurrentPageOnly = false,
}: {
  shouldSelectCurrentPageOnly?: boolean
}) => {
  const [isChecked, setIsChecked] = useState<boolean>(false)
  const { gridApi, pageSize, selections, currentPage } = useGridContext()

  useEffect(() => {
    // Reset selection when currentPage changes
    if (isEmpty(gridApi)) {
      return
    }
    if (shouldSelectCurrentPageOnly) {
      // setIsChecked(false)
      gridApi?.forEachNode((node: IRowNode) => {
        node.setSelected(false)
      })
      gridApi?.purgeInfiniteCache()
    } else {
      if (isChecked) {
        setTimeout(() => {
          // setIsChecked(true)
          gridApi?.forEachNode((node: IRowNode) => {
            node.setSelected(true)
          })
          setIsChecked(true)
        }, 500)
      }
    }
  }, [currentPage]) // eslint-disable-line react-hooks/exhaustive-deps

  if (isEmpty(gridApi)) {
    return false
  }

  return (
    <div
      className={clsx(
        'c-grid-custom-checkbox-header-cell ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper ag-checkbox-header-custom',
        {
          'ag-checked': shouldSelectCurrentPageOnly
            ? selections.length === gridApi?.getDisplayedRowCount()
            : (gridApi &&
                gridApi?.getDisplayedRowCount() === selections.length) ||
              pageSize === selections.length,
          'ag-indeterminate':
            selections.length !== pageSize &&
            selections.length !== 0 &&
            shouldSelectCurrentPageOnly,
        }
      )}
    >
      <input
        type="checkbox"
        // checked={isChecked}
        onChange={(e: ChangeEvent<HTMLInputElement>) => {
          if (e.currentTarget.checked) {
            gridApi?.forEachNode((node: IRowNode<any>) => {
              node.setSelected(true)
            })
          } else {
            gridApi?.forEachNode((node: IRowNode<any>) => {
              node.setSelected(false)
            })
          }
          setIsChecked(e.currentTarget.checked)
        }}
      />
    </div>
  )
}
