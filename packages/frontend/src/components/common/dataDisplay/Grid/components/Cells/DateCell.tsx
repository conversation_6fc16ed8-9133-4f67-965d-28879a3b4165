import { clsx } from 'clsx'

import { formatDate } from '@getheroes/frontend/utils'
import { COMMON_CHILD_CLASSNAME } from '@internals/types/components/table'

import { WrapperCell } from './WrapperCell'

type DateColumnProps = {
  value: Date | string
  className?: string
}

export const DateCell = ({ value, className }: DateColumnProps) => (
  <WrapperCell data={value}>
    <span className={clsx(COMMON_CHILD_CLASSNAME, className)}>
      {value ? formatDate(value) : ''}
    </span>
  </WrapperCell>
)
