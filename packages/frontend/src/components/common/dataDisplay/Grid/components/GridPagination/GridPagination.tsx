import { clsx } from 'clsx'
import { forwardRef, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { LIMIT_SELECTION_BY_REQUEST } from '@getheroes/shared'
import { IconButton } from '@getheroes/ui'
import { Skeleton } from '@internals/components/common/feedback/Skeletons/Skeleton/Skeleton'

import { useGridContext } from '../../GridProvider-export'
import { GridDefaultOptions, GridPageSizesEnum } from '../../types/grid'

type GridPaginationProps = {
  shouldDisplayLastPageBtn?: boolean
  shouldDisplayFirstPageBtn?: boolean
  pageCount: number
  gotoPage: (pageIndex: number) => void
  nextPage: () => void
  previousPage: () => void
  setPageSize?: (pageSize: number) => void
  isShowLimitedPerPage?: boolean
  displayTotalItems?: boolean
  totalItems?: number
  defaultCurrentPage?: number
  className?: string
  isSelectorPage?: boolean
  formattedCurrentPage?: string
  shouldDisplaySkeleton: boolean
}

export const GridPagination = forwardRef(
  (
    {
      shouldDisplayLastPageBtn,
      shouldDisplayFirstPageBtn,
      pageCount,
      gotoPage,
      nextPage,
      previousPage,
      setPageSize,
      isShowLimitedPerPage = false,
      displayTotalItems = false,
      totalItems = 0,
      defaultCurrentPage = GridDefaultOptions.PAGE,
      className,
      isSelectorPage = false,
      formattedCurrentPage,
      shouldDisplaySkeleton,
    }: GridPaginationProps,
    ref
  ) => {
    const { t } = useTranslation(['table', 'common'])
    const { currentPage, pageSize } = useGridContext()
    const [pageArray, setPageArray] = useState<number[]>([])

    const isShowPagination = useMemo(
      () => totalItems > pageSize,
      [pageSize, totalItems]
    )

    useEffect(() => {
      if (pageCount) {
        setPageArray(new Array(pageCount).fill(0).map((_, index) => index + 1))
      }
    }, [pageCount])

    if (shouldDisplaySkeleton) {
      return (
        <div className={'flex items-center justify-between py-5 pr-16'}>
          <Skeleton height={1} width={10} hasAnimation />
          <Skeleton height={1} width={5} hasAnimation />
        </div>
      )
    }

    return (
      <div
        className={clsx(
          'c-pagination flex justify-between flex-grow items-center py-4',
          className
        )}
      >
        <div className={'flex gap-4'}>
          {displayTotalItems && (
            <div className="flex items-center">
              <span className={clsx('body-xs-regular text-textSubtle')}>
                {t('Total')}
              </span>
              <span className={clsx('ml-1 body-xs-medium text-textLabel')}>
                {totalItems.toLocaleString()}
              </span>
            </div>
          )}

          {isShowLimitedPerPage && setPageSize && (
            <div className={'flex items-center gap-1'}>
              <span className={'body-xs-regular text-textSubtle'}>
                {t('Show', { ns: 'common' })}:
              </span>
              <select
                name={'table-page-size'}
                className={'body-xs-medium text-textLabel cursor-pointer py-1'}
                value={pageSize}
                onChange={e => {
                  gotoPage(defaultCurrentPage)
                  setPageSize(Number(e.target.value))
                }}
              >
                {[
                  GridPageSizesEnum.RANGE1,
                  GridPageSizesEnum.RANGE2,
                  GridPageSizesEnum.RANGE3,
                  GridPageSizesEnum.RANGE4,
                  GridPageSizesEnum.RANGE5,
                ]
                  .filter(pageSize => pageSize <= LIMIT_SELECTION_BY_REQUEST)
                  .map(pageSize => (
                    <option
                      key={pageSize}
                      value={pageSize}
                      className={'text-textSubtle'}
                    >
                      {pageSize}
                    </option>
                  ))}
              </select>
            </div>
          )}
        </div>
        <div className="flex items-center h-3 body-xs-medium text-textLabel gap-1">
          {isShowPagination && (
            <>
              {shouldDisplayFirstPageBtn && (
                <IconButton
                  icon={'FastArrowLeft'}
                  dataTestId="pagination-first-page"
                  variant={'tertiary-outlined'}
                  size={'extra-small'}
                  onClick={() => {
                    gotoPage(defaultCurrentPage)
                  }}
                  disabled={currentPage === defaultCurrentPage}
                />
              )}
              <IconButton
                icon={'NavArrowLeft'}
                dataTestId="pagination-previous-page"
                variant={'tertiary-outlined'}
                size={'extra-small'}
                onClick={() => {
                  previousPage()
                }}
                disabled={currentPage === defaultCurrentPage}
              />
              {!isSelectorPage ? (
                <span className="label-xs text-textBrand bg-interactionBaseActive py-2 px-3 rounded-m">
                  {formattedCurrentPage || currentPage}
                </span>
              ) : (
                <>
                  {pageArray.length > 1 && (
                    <select
                      className={'body-xs-medium text-textLabel cursor-pointer'}
                      value={currentPage}
                      onChange={e => {
                        const page = Number(e.target.value)
                        gotoPage(page)
                      }}
                    >
                      {pageArray.map(pageNumber => (
                        <option
                          key={pageNumber}
                          value={pageNumber}
                          className={'text-textSubtle'}
                        >
                          {pageNumber}
                        </option>
                      ))}
                    </select>
                  )}
                </>
              )}

              <IconButton
                icon={'NavArrowRight'}
                dataTestId="pagination-first-page"
                variant={'tertiary-outlined'}
                size={'extra-small'}
                onClick={() => {
                  nextPage()
                }}
                disabled={currentPage >= pageCount}
              />
              {shouldDisplayLastPageBtn && (
                <IconButton
                  icon={'FastArrowRight'}
                  dataTestId="pagination-previous-page"
                  variant={'tertiary-outlined'}
                  size={'extra-small'}
                  onClick={() => {
                    gotoPage(pageCount)
                  }}
                  disabled={currentPage >= pageCount}
                />
              )}
            </>
          )}
        </div>
      </div>
    )
  }
)
