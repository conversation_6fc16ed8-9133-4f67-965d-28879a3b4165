import { clsx } from 'clsx'

import { CurrencyCode, formatCurrency } from '@getheroes/shared'
import { WrapperCell } from '@internals/components/common/dataDisplay/Grid/components/Cells/WrapperCell'
import { COMMON_CHILD_CLASSNAME } from '@internals/types/components/table'

type CurrencyCellProps = {
  value: string | undefined
  className?: string
}

export const CurrencyCell = ({ value }: CurrencyCellProps) => {
  return (
    <WrapperCell>
      {value === null || value === undefined ? (
        <></>
      ) : (
        <span className={clsx(COMMON_CHILD_CLASSNAME)}>
          {formatCurrency({
            value: Number(value),
            locale: 'en-US',
            currency: CurrencyCode.USD,
          })}
        </span>
      )}
    </WrapperCell>
  )
}
