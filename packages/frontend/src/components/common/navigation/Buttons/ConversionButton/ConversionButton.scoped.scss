@import '../Button-basis.scoped';
@import './ConversionButtonVariables.scss';

.c-conversion-btn {
  position: relative;
  padding: 1px;
  @include button-basis();

  &--ai,
  &--ai[type='submit'],
  &--ai[type='button'],
  &--ai[type='reset'] {
    color: var(--textBrand);
    background: var(--backgroundDecorativeConversionAiDark);

    background-size: 350% 350%;

    &:hover {
      --parentAiBgColor1: var(--ai500);
      --parentAiBgColor2: var(--ai700);
    }

    button {
      background: linear-gradient(
        190deg,
        var(--childAiBgColor1) 5.31%,
        var(--childAiBgColor2) 50.47%,
        var(--childAiBgColor3) 72.45%,
        var(--childAiBgColor4) 123.75%
      );
      transition:
        --childAiBgColor1 0.4s,
        --childAiBgColor2 0.4s,
        --childAiBgColor3 0.4s,
        --childAiBgColor4 ease-in-out 0.4s;

      &:hover {
        --childAiBgColor1: var(--ai400);
        --childAiBgColor2: var(--ai300);
        --childAiBgColor3: var(--ai200);
        --childAiBgColor4: var(--ai100);
      }
      &:active {
        --childAiBgColor1: var(--actionSecondaryActive);
        --childAiBgColor2: var(--actionSecondaryActive);
        --childAiBgColor3: var(--actionSecondaryActive);
        --childAiBgColor4: var(--actionSecondaryActive);
      }
    }
  }

  &--data,
  &--data[type='submit'],
  &--data[type='button'],
  &--data[type='reset'] {
    color: var(--decorativeBlue700);
    background: var(--backgroundDecorativeConversionDataDark);

    background-size: 350% 350%;

    &:hover {
      --parentDataBgColor1: var(--data500);
      --parentDataBgColor2: var(--data700);
    }

    button {
      background: linear-gradient(
        190deg,
        var(--childDataBgColor1) 5.31%,
        var(--childDataBgColor2) 50.47%,
        var(--childDataBgColor3) 72.45%,
        var(--childDataBgColor4) 123.75%
      );
      transition:
        --childDataBgColor1 0.4s,
        --childDataBgColor2 0.4s,
        --childDataBgColor3 0.4s,
        --childDataBgColor4 ease-in-out 0.4s;

      &:hover {
        --childDataBgColor1: var(--data400);
        --childDataBgColor2: var(--data300);
        --childDataBgColor3: var(--data200);
        --childDataBgColor4: var(--data100);
      }
      &:active {
        --childDataBgColor1: var(--decorativeBlue300);
        --childDataBgColor2: var(--decorativeBlue300);
        --childDataBgColor3: var(--decorativeBlue300);
        --childDataBgColor4: var(--decorativeBlue300);
      }
    }
  }

  &--plg,
  &--plg[type='submit'],
  &--plg[type='button'],
  &--plg[type='reset'] {
    color: var(--textDecorativePink);
    background: var(--backgroundDecorativeConversionPlgDark);

    background-size: 350% 350%;

    &:hover {
      --parentPlgBgColor1: var(--plg500);
      --parentPlgBgColor2: var(--plg700);
    }

    button {
      background: linear-gradient(
        190deg,
        var(--childPlgBgColor1) 5.31%,
        var(--childPlgBgColor2) 50.47%,
        var(--childPlgBgColor3) 72.45%,
        var(--childPlgBgColor4) 123.75%
      );
      transition:
        --childPlgBgColor1 0.4s,
        --childPlgBgColor2 0.4s,
        --childPlgBgColor3 0.4s,
        --childPlgBgColor4 ease-in-out 0.4s;

      &:hover {
        --childPlgBgColor1: var(--plg400);
        --childPlgBgColor2: var(--plg300);
        --childPlgBgColor3: var(--plg200);
        --childPlgBgColor4: var(--plg100);
      }
      &:active {
        --childPlgBgColor1: var(--backgroundDecorativePink300);
        --childPlgBgColor2: var(--backgroundDecorativePink300);
        --childPlgBgColor3: var(--backgroundDecorativePink300);
        --childPlgBgColor4: var(--backgroundDecorativePink300);
      }
    }
  }

  &--disabled {
    cursor: not-allowed;
    background: var(--actionDisabled);

    button {
      background: var(--actionDisabled);
      color: var(--textDisabled);
    }
  }

  &--loading {
    .c-conversion-btn__button {
      &--s {
        padding: 7px 8px;
      }
      &--m {
        padding: 10px 12px;
      }
      &--l {
        padding: 12px 16px;
      }
    }
  }

  &__button {
    white-space: nowrap;
    &--xs {
      padding: 5px 4px;
      &-with-icon-and-children {
        padding: 5px 4px;
      }
      &-with-icon {
        padding: 4px 4px;
      }
    }
    &--s {
      padding: 9px 8px;
      &-with-icon-and-children {
        padding: 7px 8px;
      }
      &-with-icon {
        padding: 7px 7px;
      }
    }
    &--m {
      padding: 13px 12px;
      &-with-icon-and-children {
        padding: 10px 12px;
      }
      &-with-icon {
        padding: 12px 12px;
      }
    }
    &--l {
      padding: 15px 16px;
      &-with-icon-and-children {
        padding: 12px 16px;
      }
      &-with-icon {
        padding: 16px 16px;
      }
    }
    border-radius: 7px;
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
