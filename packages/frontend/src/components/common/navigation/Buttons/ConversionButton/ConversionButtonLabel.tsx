import { clsx } from 'clsx'
import type { ReactNode } from 'react'

export type ConversionButtonLabelProps = {
  dataTestId?: string
  startIcon?: ReactNode
  children: ReactNode
  endIcon?: ReactNode
}

export const ConversionButtonLabel = ({
  dataTestId,
  startIcon,
  children,
  endIcon,
}: ConversionButtonLabelProps) => {
  return (
    <div
      className={
        'flex items-center justify-center gap-2 label-s w-full flex-grow truncate'
      }
      data-testid={dataTestId}
    >
      {startIcon && <span className={clsx('max-h-6')}>{startIcon}</span>}
      {children}
      {endIcon && <span className={clsx('max-h-6')}>{endIcon}</span>}
    </div>
  )
}
