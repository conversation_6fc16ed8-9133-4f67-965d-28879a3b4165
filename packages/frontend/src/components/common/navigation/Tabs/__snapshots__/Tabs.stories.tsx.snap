// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`components/common/navigation/Tabs DefaultHorizontal smoke-test 1`] = `
<div class="bg-white p-4">
  <div>
    <ul class="c-tabs__selectors__default flex-row">
      <li role="button"
          aria-pressed="true"
          class="label-xs justify-center items-center"
      >
        <svg width="1.5em"
             height="1.5em"
             stroke-width="1.5"
             viewbox="0 0 24 24"
             fill="none"
             xmlns="http://www.w3.org/2000/svg"
             color="currentColor"
             class="mr-2"
        >
          <path d="M18.1182 14.702L14 15.5C11.2183 14.1038 9.5 12.5 8.5 10L9.26995 5.8699L7.81452 2L4.0636 2C2.93605 2 2.04814 2.93178 2.21654 4.04668C2.63695 6.83 3.87653 11.8765 7.5 15.5C11.3052 19.3052 16.7857 20.9564 19.802 21.6127C20.9668 21.8662 22 20.9575 22 19.7655L22 16.1812L18.1182 14.702Z"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
          >
          </path>
        </svg>
        Phone
      </li>
      <li role="button"
          aria-pressed="false"
          class="label-xs justify-center items-center"
      >
        Account
      </li>
      <li role="button"
          aria-pressed="false"
          class="label-xs justify-center items-center"
      >
        Integrations
      </li>
    </ul>
    <div class="c-tabs__content flex flex-col">
      Phone content
    </div>
  </div>
</div>
`;

exports[`components/common/navigation/Tabs DefaultVertical smoke-test 1`] = `
<div class="bg-white p-4">
  <div class="flex">
    <ul class="c-tabs__selectors__default flex-col">
      <li role="button"
          aria-pressed="true"
          class="label-xs"
      >
        <svg width="1.5em"
             height="1.5em"
             stroke-width="1.5"
             viewbox="0 0 24 24"
             fill="none"
             xmlns="http://www.w3.org/2000/svg"
             color="currentColor"
             class="mr-2"
        >
          <path d="M18.1182 14.702L14 15.5C11.2183 14.1038 9.5 12.5 8.5 10L9.26995 5.8699L7.81452 2L4.0636 2C2.93605 2 2.04814 2.93178 2.21654 4.04668C2.63695 6.83 3.87653 11.8765 7.5 15.5C11.3052 19.3052 16.7857 20.9564 19.802 21.6127C20.9668 21.8662 22 20.9575 22 19.7655L22 16.1812L18.1182 14.702Z"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
          >
          </path>
        </svg>
        Phone
      </li>
      <li role="button"
          aria-pressed="false"
          class="label-xs"
      >
        Account
      </li>
      <li role="button"
          aria-pressed="false"
          class="label-xs"
      >
        Integrations
      </li>
    </ul>
    <div class="c-tabs__content flex flex-col">
      Phone content
    </div>
  </div>
</div>
`;

exports[`components/common/navigation/Tabs SegmentedHorizontal smoke-test 1`] = `
<div class="bg-white p-4">
  <div class="flex flex-col items-start">
    <ul class="c-tabs__selectors__segmented flex-row">
      <li role="button"
          aria-pressed="true"
          class="label-xs justify-center items-center"
      >
        <svg width="1.5em"
             height="1.5em"
             stroke-width="1.5"
             viewbox="0 0 24 24"
             fill="none"
             xmlns="http://www.w3.org/2000/svg"
             color="currentColor"
             class="mr-2"
        >
          <path d="M18.1182 14.702L14 15.5C11.2183 14.1038 9.5 12.5 8.5 10L9.26995 5.8699L7.81452 2L4.0636 2C2.93605 2 2.04814 2.93178 2.21654 4.04668C2.63695 6.83 3.87653 11.8765 7.5 15.5C11.3052 19.3052 16.7857 20.9564 19.802 21.6127C20.9668 21.8662 22 20.9575 22 19.7655L22 16.1812L18.1182 14.702Z"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
          >
          </path>
        </svg>
        Phone
      </li>
      <li role="button"
          aria-pressed="false"
          class="label-xs justify-center items-center"
      >
        Account
      </li>
      <li role="button"
          aria-pressed="false"
          class="label-xs justify-center items-center"
      >
        Integrations
      </li>
    </ul>
    <div class="c-tabs__content flex flex-col">
      Phone content
    </div>
  </div>
</div>
`;

exports[`components/common/navigation/Tabs SegmentedVertical smoke-test 1`] = `
<div class="bg-white p-4">
  <div class="flex">
    <ul class="c-tabs__selectors__segmented flex-col">
      <li role="button"
          aria-pressed="true"
          class="label-xs"
      >
        <svg width="1.5em"
             height="1.5em"
             stroke-width="1.5"
             viewbox="0 0 24 24"
             fill="none"
             xmlns="http://www.w3.org/2000/svg"
             color="currentColor"
             class="mr-2"
        >
          <path d="M18.1182 14.702L14 15.5C11.2183 14.1038 9.5 12.5 8.5 10L9.26995 5.8699L7.81452 2L4.0636 2C2.93605 2 2.04814 2.93178 2.21654 4.04668C2.63695 6.83 3.87653 11.8765 7.5 15.5C11.3052 19.3052 16.7857 20.9564 19.802 21.6127C20.9668 21.8662 22 20.9575 22 19.7655L22 16.1812L18.1182 14.702Z"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
          >
          </path>
        </svg>
        Phone
      </li>
      <li role="button"
          aria-pressed="false"
          class="label-xs"
      >
        Account
      </li>
      <li role="button"
          aria-pressed="false"
          class="label-xs"
      >
        Integrations
      </li>
    </ul>
    <div class="c-tabs__content flex flex-col">
      Phone content
    </div>
  </div>
</div>
`;
