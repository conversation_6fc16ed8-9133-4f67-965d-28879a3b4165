import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { AsyncAction, Organization } from '@getheroes/shared'
import { Divider, Typography } from '@getheroes/ui'
import { Skeleton } from '@internals/components/common/feedback/Skeletons/Skeleton/Skeleton'
import { useGetOneImportQuery } from '@internals/features/leadImport/core/api/leadImportApi'
import { ContinueLeadImportButton } from '@internals/features/leadImport/core/components/HistoryLeadImport/components/ContinueLeadImportButton'
import { useTypedSelector } from '@internals/store/store'

type RequestYourAttentionAsyncActionProps = {
  asyncAction: AsyncAction
}

export const RequestYourAttentionAsyncAction = ({
  asyncAction,
}: RequestYourAttentionAsyncActionProps) => {
  const currentOrganization = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization

  const {
    data: leadImport,
    isLoading,
    isFetching,
  } = useGetOneImportQuery({
    organizationId: currentOrganization.id,
    leadImportId: asyncAction.internalRelationId as string,
  })

  return (
    <>
      <div className={'flex justify-between h-[32px] gap-1'}>
        <div className={'flex flex-col overflow-hidden justify-center'}>
          <Typography variant={'body'} size={'s'} weight={'regular'}>
            <div className={'truncate'}>{asyncAction.label}</div>
          </Typography>
          <Typography
            variant={'body'}
            size={'xs'}
            weight={'regular'}
            color={'base-subtle'}
          >
            {asyncAction.category}
          </Typography>
        </div>
        <div className={'flex items-center'}>
          {isFetching || isLoading ? (
            <Skeleton height={1} width={10} />
          ) : (
            <ContinueLeadImportButton
              leadImport={leadImport}
              size="small"
              variant="secondary"
            />
          )}
        </div>
      </div>
      <div className={'mb-1 mt-1'}>
        <Divider />
      </div>
    </>
  )
}
