import type { AsyncAction } from '@getheroes/shared'
import { Divider, Typography } from '@getheroes/ui'
import { useAsyncActionPercentageLoading } from '@internals/components/common/navigation/Sidebar/hooks/useAsyncActionPercentageLoading'
import { LeadImportStatusIndicator } from '@internals/features/leadImport/core/components/LeadImportStatusIndicator/LeadImportStatusIndicator'

type RequestFromTheLast24hAsyncActionProps = {
  asyncAction: AsyncAction
}

export const RequestFromTheLast24hAsyncAction = ({
  asyncAction,
}: RequestFromTheLast24hAsyncActionProps) => {
  const { percentageProgress, loadingMessage } =
    useAsyncActionPercentageLoading(asyncAction)

  return (
    <>
      <div className={'flex justify-between h-[32px] gap-1'}>
        <div className={'flex flex-col overflow-hidden justify-center'}>
          <Typography variant={'body'} size={'s'} weight={'regular'}>
            <div className={'truncate'}>{asyncAction.label}</div>
          </Typography>
          <Typography
            variant={'body'}
            size={'xs'}
            weight={'regular'}
            color={'base-subtle'}
          >
            {asyncAction.category}
          </Typography>
        </div>
        <div className={'flex items-center justify-end w-[90px]'}>
          <LeadImportStatusIndicator
            status={asyncAction.status}
            percentageProgress={percentageProgress}
            loadingMessage={loadingMessage}
          />
        </div>
      </div>
      <div className={'mb-1 mt-1'}>
        <Divider />
      </div>
    </>
  )
}
