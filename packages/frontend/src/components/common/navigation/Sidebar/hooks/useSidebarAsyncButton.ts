import { useCallback, useEffect, useMemo, useState } from 'react'

import { selectHasEnrichmentInProgress } from '@getheroes/frontend/config/store/slices/leadMetaDataSlice'
import { getSecondsAgo } from '@getheroes/frontend/utils'
import { AsyncActionStatusEnum } from '@getheroes/shared'
import {
  GetAsyncActionsAppliedFilterEnum,
  useGetAsyncActions,
} from '@internals/features/asyncAction/hooks/useGetAsyncActions'
import { useTypedSelector } from '@internals/store/store'

export enum AsyncActionButtonStatusEnum {
  IN_PROGRESS = 'in-progress',
  DONE = 'done',
  ERROR = 'error',
  IDLE = 'idle',
}

const NB_SECONDS_TO_SHOW_ERROR_AND_DONE_STATUS = 30

const statusUpdatedWithinLastSeconds = (
  lastStatusUpdate: Date,
  seconds: number
): boolean => {
  const tenSecondsAgo = getSecondsAgo(seconds)
  return lastStatusUpdate >= tenSecondsAgo
}

export const useSidebarAsyncButton = () => {
  const hasEnrichmentInProgressInRedux = useTypedSelector(
    selectHasEnrichmentInProgress
  )

  const { asyncActions } = useGetAsyncActions(
    GetAsyncActionsAppliedFilterEnum.MOST_RECENT_PER_STATUS
  )

  const [buttonStatus, setButtonStatus] = useState(
    AsyncActionButtonStatusEnum.IDLE
  )

  const updateButtonStatus = useCallback(() => {
    const atLeastOneErrorStatusWithinSeconds = asyncActions
      .filter(asyncAction => asyncAction.status === AsyncActionStatusEnum.ERROR)
      .some(asyncAction =>
        statusUpdatedWithinLastSeconds(
          asyncAction.lastStatusUpdate,
          NB_SECONDS_TO_SHOW_ERROR_AND_DONE_STATUS
        )
      )

    const atLeastOneDoneStatusWithinSeconds =
      asyncActions
        .filter(
          asyncAction => asyncAction.status === AsyncActionStatusEnum.DONE
        )
        .some(asyncAction =>
          statusUpdatedWithinLastSeconds(
            asyncAction.lastStatusUpdate,
            NB_SECONDS_TO_SHOW_ERROR_AND_DONE_STATUS
          )
        ) && !hasEnrichmentInProgressInRedux

    const atLeastOneInProgressStatus =
      asyncActions.some(asyncAction =>
        [
          AsyncActionStatusEnum.IN_PROGRESS,
          AsyncActionStatusEnum.PRELOADING,
        ].includes(asyncAction.status)
      ) || hasEnrichmentInProgressInRedux

    if (atLeastOneErrorStatusWithinSeconds) {
      setButtonStatus(AsyncActionButtonStatusEnum.ERROR)
    } else if (atLeastOneInProgressStatus) {
      setButtonStatus(AsyncActionButtonStatusEnum.IN_PROGRESS)
    } else if (atLeastOneDoneStatusWithinSeconds) {
      setButtonStatus(AsyncActionButtonStatusEnum.DONE)
    } else {
      setButtonStatus(AsyncActionButtonStatusEnum.IDLE)
    }
  }, [asyncActions, hasEnrichmentInProgressInRedux])

  useEffect(() => {
    updateButtonStatus()
  }, [updateButtonStatus])

  // TODO: Memory leak: if async action is never done, useInterval will never be cleared
  // useInterval(
  //   () => {
  //     if (buttonStatus !== AsyncActionButtonStatusEnum.IDLE) {
  //       updateButtonStatus()
  //     }
  //   },
  //   ONE_SECOND_IN_MILLISECONDS
  // )

  return useMemo(
    () => ({
      buttonStatus,
    }),
    [buttonStatus]
  )
}
