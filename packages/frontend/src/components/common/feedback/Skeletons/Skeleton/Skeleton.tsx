import { clsx } from 'clsx'

type SkeletonPropsType = {
  width?: number
  height?: number
  hasAnimation?: boolean
  id?: string
  className?: string
}

export const Skeleton = ({
  width,
  height,
  hasAnimation = false,
  id,
  className,
}: SkeletonPropsType) => {
  return (
    <div
      data-testid={id}
      style={{
        ...(width && { width: `${width}rem` }),
        ...(height && {
          height: `${height}rem`,
          borderRadius: `${height / 2}rem`,
        }),
      }}
      className={clsx(
        `c-skeleton bg-backgroundSkeleton`,
        {
          'w-full mx-4 py-4': width === undefined,
          'h-full': height === undefined,
          'animate-pulse': hasAnimation,
        },
        className
      )}
    ></div>
  )
}
