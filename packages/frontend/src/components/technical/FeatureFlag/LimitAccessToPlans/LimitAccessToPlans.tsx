import type { PropsWithChildren } from 'react'
import { Fragment } from 'react'

import type { OrganizationServiceSubscriptionPlan } from '@getheroes/shared'
import { useCurrentPlan } from '@internals/features/subscription/hooks/useCurrentPlan'

interface LimitAccessToPlansProps {
  authorizedPlans?: OrganizationServiceSubscriptionPlan[]
  allowFreeAndStarter?: boolean
  allowEssentialAdvancedEnterprise?: boolean
}

export const LimitAccessToPlans = ({
  authorizedPlans,
  allowFreeAndStarter,
  allowEssentialAdvancedEnterprise,
  children,
}: PropsWithChildren<LimitAccessToPlansProps>) => {
  const {
    getAuthorizationForPlans,
    isFreeOrStarterPlan,
    isEssentialAdvancedEnterprisePlan,
  } = useCurrentPlan()

  let isAccessAuthorized = false

  if (authorizedPlans) {
    isAccessAuthorized = getAuthorizationForPlans(authorizedPlans)
  }
  if (allowFreeAndStarter && isFreeOrStarterPlan) {
    isAccessAuthorized = true
  }
  if (allowEssentialAdvancedEnterprise && isEssentialAdvancedEnterprisePlan) {
    isAccessAuthorized = true
  }

  return isAccessAuthorized ? children : <Fragment />
}
