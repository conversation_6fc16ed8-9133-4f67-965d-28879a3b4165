import { useTranslation } from 'react-i18next'
import { useDispatch } from 'react-redux'

import { setScrollToAttribute } from '@getheroes/frontend/config/store/slices/leadSlice'
import { CommonLeadFieldEnum } from '@getheroes/frontend/types'
import type { Contact } from '@getheroes/shared'
import { PersonalityNotGenerated } from '@internals/components/business/lead/contact/Personality/PersonalityPanel/components/PersonalityNotGenerated/PersonalityNotGenerated'
import { PersonalitySkeleton } from '@internals/components/business/lead/contact/Personality/PersonalityPanel/components/PersonalitySkeleton/PersonalitySkeleton'
import { PersonalitySummarized } from '@internals/components/business/lead/contact/Personality/PersonalityPanel/components/PersonalitySummarized/PersonalitySummarized'
import { NoLinkedInUrl } from '@internals/features/personality/components/NoLinkedInUrl/NoLinkedInUrl'
import { useGetPersonality } from '@internals/features/personality/hooks/useGetPersonality'
import type { ExtractPersonalityFullOutput } from '@internals/features/personality/types/output'

type PersonalityProps = {
  contact: Contact | undefined
  shouldDisplaySkeleton?: boolean
}

export const Personality = ({
  contact,
  shouldDisplaySkeleton,
}: PersonalityProps) => {
  const { t } = useTranslation('personality')
  const dispatch = useDispatch()
  const {
    getPersonality,
    isError,
    isLoading,
    isFetching,
    profileFull: profile,
    cost,
    available,
    hasLinkedinUrl,
  } = useGetPersonality({ contact })

  const isNotAlreadyGenerated =
    !available && !isFetching && !isLoading && !profile

  if (shouldDisplaySkeleton || isFetching || isLoading) {
    return <PersonalitySkeleton />
  }

  if (!hasLinkedinUrl) {
    return (
      <NoLinkedInUrl
        onClick={() => {
          dispatch(setScrollToAttribute(CommonLeadFieldEnum.LINKEDIN_URL))
        }}
      />
    )
  }

  if (isError) {
    return (
      <div className="text-center py-9 text-textDecorativeRed body-s-regular">
        {t('We were unable to retrieve the lead’s personality.')}
      </div>
    )
  }

  return (
    <div className={'relative flex flex-col gap-2'}>
      {isNotAlreadyGenerated ? (
        <PersonalityNotGenerated onClickGenerate={getPersonality} cost={cost} />
      ) : profile ? (
        <PersonalitySummarized
          profile={profile as ExtractPersonalityFullOutput}
        />
      ) : null}
    </div>
  )
}
