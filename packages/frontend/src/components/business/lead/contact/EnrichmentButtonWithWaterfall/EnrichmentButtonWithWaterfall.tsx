import type { Contact, EnrichmentType } from '@getheroes/shared'
import { getEnrichmentCost } from '@getheroes/shared'
import { Icon } from '@getheroes/ui'
import { PlgCreditTag } from '@internals/components/business/organization/credit/PlgCreditTag/PlgCreditTag'
import { ConversionButtonSize } from '@internals/components/common/navigation/Buttons/ConversionButton/ConversionButton-export'
import { EnrichmentWaterfall } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover'
import { useSubscriptions } from '@internals/features/subscription/hooks/useSubscriptions'
import { PopoverPlacementEnum } from '@internals/types/components/orientation'
import { idReferentials } from '@internals/utils/idReferentials'

export interface EnrichmentButtonWithWaterfallProps {
  contact: Contact
  onClickEnrich: () => void
  enrichmentType: EnrichmentType
  label: string
  container?: HTMLElement | null | React.MutableRefObject<HTMLElement | null>
}

export const EnrichmentButtonWithWaterfall = ({
  contact,
  onClickEnrich,
  enrichmentType,
  label,
  container,
}: EnrichmentButtonWithWaterfallProps) => {
  const { currentPlan } = useSubscriptions()

  return (
    <EnrichmentWaterfall.Popover
      placement={PopoverPlacementEnum.LEFT_START}
      dataTestId={idReferentials.leads.components.Attribute.enrichButton}
      container={container}
    >
      <EnrichmentWaterfall.Trigger
        lead={contact}
        handleClickEnrich={onClickEnrich}
        enrichmentType={enrichmentType}
        conversionButtonProps={{
          startIcon: <Icon name="MagicWand" color="decorative-blue" />,
          size: ConversionButtonSize.SMALL,
        }}
      >
        <div className="flex justify-between items-center w-full">
          <span>{label}</span>
          <PlgCreditTag
            icon="DollarCircle"
            isFree={getEnrichmentCost(enrichmentType, currentPlan) === 0}
            count={getEnrichmentCost(enrichmentType, currentPlan)}
            color="decorative-blue"
            dataTestId={
              idReferentials.leads.components.Attribute.enrichButtonTag
            }
          />
        </div>
      </EnrichmentWaterfall.Trigger>
      <EnrichmentWaterfall.Content
        lead={contact}
        handleClickEnrich={onClickEnrich}
        enrichmentType={enrichmentType}
      />
    </EnrichmentWaterfall.Popover>
  )
}
