import { capitalize } from 'lodash'
import type { ReactNode, RefObject } from 'react'
import {
  createRef,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'

import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import {
  formatDate,
  getLeadMostRecentEnrichment,
  MONTH_DAY_YEAR_INLINE_FORMAT,
} from '@getheroes/frontend/utils'
import type { Company, Contact } from '@getheroes/shared'
import { EnrichmentType, getEnrichmentCost } from '@getheroes/shared'
import { Icon } from '@getheroes/ui'
import type {
  AttributeImperativeHandle,
  PhonesEmailsAttributesImperativeHandle,
} from '@internals/components/business/lead/common/Attribute/LeadAttribute/LeadAttribute'
import { LeadAttribute } from '@internals/components/business/lead/common/Attribute/LeadAttribute/LeadAttribute'
import { LeadAttributeLabel } from '@internals/components/business/lead/common/Attribute/LeadAttribute/LeadAttributeLabel'
import { useAttribute } from '@internals/components/business/lead/common/Attribute/hooks/useAttribute'
import { PlgCreditTag } from '@internals/components/business/organization/credit/PlgCreditTag/PlgCreditTag'
import { ConversionButtonSize } from '@internals/components/common/navigation/Buttons/ConversionButton/ConversionButton-export'
import { RenderAs } from '@internals/components/technical/RenderAs/RenderAs'
import { EnrichmentWaterfall } from '@internals/features/lead/components/tables/components/Cells/LeadActionsCell/components/Popovers/EnrichmentWaterfallPopover'
import type { LeadsField } from '@internals/features/lead/types/leadsTableColumn'
import type { UseSubscriptionsResult } from '@internals/features/subscription/hooks/useSubscriptions'
import { useSubscriptions } from '@internals/features/subscription/hooks/useSubscriptions'
import { useTrackingContext } from '@internals/providers/TrackingContextProvider/useTrackingContext'
import { PopoverPlacementEnum } from '@internals/types/components/orientation'
import { idReferentials } from '@internals/utils/idReferentials'

type AttributeProps = {
  lead: Contact | Company
  attribute: LeadsField
  fieldOptions?: {
    // key should be of type LeadsFields['id']
    [key: string]: any
  }
  editAndShowError?: string
  onValidate?: () => void
  forceEditable?: boolean
  dataTestIdPrefix: string
  hasLabel?: boolean
  buttonRef?: RefObject<AttributeImperativeHandle>
  onDirtyChange?: (isShowSaveButton: boolean) => void
  onClickEnrich?: (
    fieldId:
      | ExclusiveContactLeadFieldEnum.PHONES
      | ExclusiveContactLeadFieldEnum.EMAILS
  ) => void
  isLoadingEnrichment?: boolean
  messageInputHelper?: ReactNode
  creditCost?: number
  isEnrichButtonDisabled?: boolean
  container?: HTMLElement | null | React.MutableRefObject<HTMLElement | null>
}

export const Attribute = forwardRef<
  HTMLDivElement | HTMLFormElement,
  AttributeProps
>((props, ref) => {
  const { t } = useTranslation(['lead', 'common'])
  const leadAttributeRef = createRef<PhonesEmailsAttributesImperativeHandle>()
  const [hideEmptyStatePhoneOrEmail, setHideEmptyStatePhoneOrEmail] =
    useState(false)

  const { currentPlan } = useSubscriptions() as UseSubscriptionsResult

  const { context, page, sendEvent } = useTrackingContext()

  const {
    attribute,
    lead,
    editAndShowError,
    dataTestIdPrefix,
    forceEditable = false,
    hasLabel = true,
    buttonRef,
    onDirtyChange,
    onClickEnrich = () => Promise.resolve(),
    isLoadingEnrichment = false,
    container,
  } = props

  const {
    getAttributeLabel,
    formMethods,
    renderAsProps,
    isUsingFormContext,
    getDefaultValue,
    checkIfEmailIsFromEnrichment,
    checkIfPhoneIsFromEnrichment,
  } = useAttribute({
    attribute,
    lead,
  })

  const {
    formState: { isDirty, isSubmitSuccessful },
    setError,
    reset,
    clearErrors,
    handleSubmit,
    getValues,
  } = formMethods

  const values = getValues(attribute.id) || []

  const isPhone = attribute.id === ExclusiveContactLeadFieldEnum.PHONES
  const isEmail = attribute.id === ExclusiveContactLeadFieldEnum.EMAILS
  const isPhoneOrEmail = isEmail || isPhone
  const isEmptyPhonesOrEmail =
    !isDirty &&
    isPhoneOrEmail &&
    values &&
    values.filter(({ value }: { value: string }) => value).length === 0

  const resetForm = useCallback(() => {
    if (!isUsingFormContext) {
      clearErrors()
      reset({
        [attribute.id]: getDefaultValue(),
      })
    }
  }, [attribute.id, clearErrors, getDefaultValue, isUsingFormContext, reset])

  useEffect(() => {
    // If ref is not null, then the drawer has been opened and scroll to an attribute has been asked
    if (editAndShowError) {
      setError(attribute.id, {
        message: editAndShowError,
      })
    }
  }, [attribute.id, editAndShowError, setError, t])

  useEffect(() => {
    if (!isUsingFormContext && isSubmitSuccessful) {
      resetForm()
    }
  }, [
    attribute,
    getAttributeLabel,
    isSubmitSuccessful,
    isUsingFormContext,
    resetForm,
    t,
  ])

  useEffect(() => {
    if (!isUsingFormContext) {
      onDirtyChange?.(isDirty)
    }
  }, [isDirty, isUsingFormContext, onDirtyChange])

  const handleEnrich = async () => {
    if (isPhoneOrEmail) {
      onClickEnrich(
        attribute.id as
          | ExclusiveContactLeadFieldEnum.PHONES
          | ExclusiveContactLeadFieldEnum.EMAILS
      )
    }
  }

  useImperativeHandle(buttonRef, () => ({
    onClickEnrich: handleEnrich,
    addInput: () => leadAttributeRef.current?.addInput(),
    handleSubmit: () => handleSubmit(renderAsProps?.onSubmit()),
  }))

  const mostRecentEnrichment = getLeadMostRecentEnrichment(lead)

  return (
    <RenderAs
      ref={ref}
      {...renderAsProps}
      className="flex flex-col w-full gap-1"
      id={attribute.id}
      onKeyDown={e => {
        if (e.key === 'Escape') {
          resetForm()
        }
      }}
    >
      {hasLabel && (
        <LeadAttributeLabel
          tooltip={capitalize(
            t(
              (isEmail && mostRecentEnrichment) ||
                (isPhone && lead.enrichmentAdvancedDate)
                ? `Last enrichment made on {{date}}`
                : isEmail || (isPhone && !mostRecentEnrichment)
                  ? `lead never enriched`
                  : `lead phone never enriched`,
              {
                date:
                  isEmail && mostRecentEnrichment
                    ? formatDate(
                        mostRecentEnrichment,
                        MONTH_DAY_YEAR_INLINE_FORMAT
                      )
                    : !isEmail && lead.enrichmentAdvancedDate
                      ? formatDate(
                          lead.enrichmentAdvancedDate,
                          MONTH_DAY_YEAR_INLINE_FORMAT
                        )
                      : '',
              }
            ) as string
          )}
          label={getAttributeLabel()}
          isLoadingEnrichment={isLoadingEnrichment}
          shouldShowSaveButton={!isUsingFormContext && isDirty}
          shouldShowAddButton={!isUsingFormContext && isPhoneOrEmail}
          shouldShowEnrichButton={
            !isUsingFormContext &&
            isPhoneOrEmail &&
            (hideEmptyStatePhoneOrEmail || !isEmptyPhonesOrEmail)
          }
          handleAddButton={() => {
            setHideEmptyStatePhoneOrEmail(true)
            if (hideEmptyStatePhoneOrEmail || !isEmptyPhonesOrEmail) {
              leadAttributeRef.current?.addInput()
            }
          }}
          handleEnrichButton={handleEnrich}
          isEnrichButtonDisabled={props.isEnrichButtonDisabled}
        />
      )}

      {!hideEmptyStatePhoneOrEmail && isEmptyPhonesOrEmail ? (
        <EnrichmentWaterfall.Popover
          placement={PopoverPlacementEnum.LEFT_START}
          dataTestId={idReferentials.leads.components.Attribute.enrichButton}
          container={container}
        >
          <EnrichmentWaterfall.Trigger
            lead={lead as Contact}
            handleClickEnrich={handleEnrich}
            enrichmentType={
              attribute.id === ExclusiveContactLeadFieldEnum.PHONES
                ? EnrichmentType.PHONE
                : EnrichmentType.EMAIL
            }
            conversionButtonProps={{
              startIcon: (
                <Icon
                  name={'MagicWand'}
                  size={'small'}
                  color={'decorative-blue'}
                />
              ),
              size: ConversionButtonSize.SMALL,
            }}
          >
            <div className="flex justify-between items-center w-full">
              <span>
                {t(
                  attribute.id === ExclusiveContactLeadFieldEnum.PHONES
                    ? 'Enrich phone'
                    : 'Enrich email'
                )}
              </span>
              <PlgCreditTag
                icon={'DollarCircle'}
                color={'decorative-blue'}
                isFree={
                  attribute.id === ExclusiveContactLeadFieldEnum.EMAILS
                    ? getEnrichmentCost(EnrichmentType.EMAIL, currentPlan) === 0
                    : false
                }
                count={
                  attribute.id === ExclusiveContactLeadFieldEnum.EMAILS
                    ? getEnrichmentCost(EnrichmentType.EMAIL, currentPlan)
                    : getEnrichmentCost(EnrichmentType.PHONE, currentPlan)
                }
                dataTestId={
                  idReferentials.leads.components.Attribute.enrichButtonTag
                }
              />
            </div>
          </EnrichmentWaterfall.Trigger>
          <EnrichmentWaterfall.Content
            lead={lead as Contact}
            handleClickEnrich={handleEnrich}
            enrichmentType={
              attribute.id === ExclusiveContactLeadFieldEnum.PHONES
                ? EnrichmentType.PHONE
                : EnrichmentType.EMAIL
            }
          />
        </EnrichmentWaterfall.Popover>
      ) : (
        <LeadAttribute
          ref={leadAttributeRef}
          {...formMethods}
          {...props}
          isLoadingEnrichment={isLoadingEnrichment}
          lead={lead}
          forceEditable={forceEditable}
          checkIfEmailIsFromEnrichment={checkIfEmailIsFromEnrichment}
          checkIfPhoneIsFromEnrichment={checkIfPhoneIsFromEnrichment}
          dataTestId={`${dataTestIdPrefix}-${attribute.id.replace('.', '-')}`}
        />
      )}
    </RenderAs>
  )
})
