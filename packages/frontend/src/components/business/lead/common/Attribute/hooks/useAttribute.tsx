import { yupResolver } from '@hookform/resolvers/yup'
import isEmpty from 'lodash/isEmpty'
import isEqual from 'lodash/isEqual'
import omit from 'lodash/omit'
import { useCallback, useEffect, useMemo } from 'react'
import { useForm, useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { v4 as uuidv4 } from 'uuid'

import { api } from '@getheroes/frontend/config/api'
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import type { CommonLeadFieldEnum } from '@getheroes/frontend/types'
import {
  ENRICHMENT_RESULT_FIELD_PREFIX,
  ExclusiveCompanyLeadFieldEnum,
  ExclusiveContactLeadFieldEnum,
} from '@getheroes/frontend/types'
import type { Company, Contact, Organization } from '@getheroes/shared'
import { LeadCategory } from '@getheroes/shared'
import { useEditCompanyByIdMutation } from '@internals/features/lead/api/companyApi'
import {
  COMPANIES_FIELDS_CONFIG,
  CONTACTS_FIELDS_CONFIG,
} from '@internals/features/lead/config/leadsFieldConfig'
import { useGetCompanyFieldSchema } from '@internals/features/lead/hooks/leadFields/useGetCompanyFieldSchema'
import { useGetContactFieldSchema } from '@internals/features/lead/hooks/leadFields/useGetContactFieldSchema'
import { useEditLead } from '@internals/features/lead/hooks/useEditLead'
import type { LeadsField } from '@internals/features/lead/types/leadsTableColumn'
import { TypeLeadEnum } from '@internals/features/lead/types/leadsTableColumn'
import { useAppDispatch, useTypedSelector } from '@internals/store/store'
import { findDeepObject } from '@internals/utils/deepObject'
import { removeSpaces } from '@internals/utils/string'

type UseAttributeFormProps = {
  attribute: LeadsField
  lead: Contact | Company
}

export const useAttribute = ({ attribute, lead }: UseAttributeFormProps) => {
  const dispatch = useAppDispatch()
  const { id: organizationId } = useTypedSelector(
    selectCurrentUserOrganization
  ) as Organization
  const { t } = useTranslation('lead')

  const isContact = useMemo(() => {
    return 'firstName' in lead
  }, [lead])

  // ***********************************$
  // Custom hooks
  // ***********************************$

  const [editLead] = useEditLead({
    leadCategory: isContact ? LeadCategory.CONTACT : LeadCategory.COMPANY,
  })

  const [editCompany] = useEditCompanyByIdMutation()

  // ***********************************
  // Exported state
  // ***********************************
  const isPhoneAttribute =
    attribute.id === ExclusiveContactLeadFieldEnum.PHONES ||
    attribute.id === ExclusiveCompanyLeadFieldEnum.PHONE_NUMBER
  const isEmailAttribute = attribute.id === ExclusiveContactLeadFieldEnum.EMAILS

  const requiredFieldsConfig = isContact
    ? CONTACTS_FIELDS_CONFIG.requiredFields
    : COMPANIES_FIELDS_CONFIG.requiredFields

  const isPhoneOrEmailAttribute = isPhoneAttribute || isEmailAttribute
  const requiredFields = requiredFieldsConfig.includes(
    attribute.id as CommonLeadFieldEnum
  )
    ? [attribute.id]
    : []

  // ***********************************
  // Form hooks and states
  // ***********************************

  const formMethodsFromContext = useFormContext()
  const contactSchema = useGetContactFieldSchema({
    fields: [attribute],
  })
  const companySchema = useGetCompanyFieldSchema({
    fields: [attribute],
  })

  const schema = isContact ? contactSchema : companySchema

  const getDefaultValue = useCallback(() => {
    const defaultValue = findDeepObject(lead, attribute.id)

    if (isPhoneOrEmailAttribute) {
      const emptyValue = [
        {
          value: '',
          id: uuidv4(),
        },
      ]
      return isEmpty(defaultValue)
        ? emptyValue
        : (defaultValue as Array<string>).map(value => ({
            value,
            id: uuidv4(),
          }))
    }

    return defaultValue
  }, [attribute.id, isPhoneOrEmailAttribute, lead])

  const formMethodsNewForm = useForm({
    mode: 'onChange',
    resolver: (data, context, options) => {
      return yupResolver(schema)(
        data,
        {
          ...context,
          isRequired: requiredFieldsConfig.includes(
            attribute.id as CommonLeadFieldEnum
          ),
          requiredFields,
        },
        options
      )
    },
    defaultValues: {
      [attribute.id]: getDefaultValue(),
    },
  })

  const isUsingFormContext = Boolean(formMethodsFromContext)
  const formMethods = formMethodsFromContext ?? formMethodsNewForm

  useEffect(() => {
    const currentValue = formMethods.getValues(attribute.id)

    const newValue = getDefaultValue()

    const isSameValue = isEqual(currentValue, newValue)

    if (!isUsingFormContext && !isSameValue) {
      formMethods.setValue(attribute.id, getDefaultValue())
    }
  }, [attribute.id, formMethods, getDefaultValue, isUsingFormContext, lead])

  const renderAsProps = useMemo(() => {
    return isUsingFormContext
      ? {
          as: 'div' as keyof HTMLElementTagNameMap,
        }
      : {
          as: 'form' as keyof HTMLElementTagNameMap,
          onSubmit: formMethods.handleSubmit(async data => {
            const companyFormKey =
              ExclusiveContactLeadFieldEnum.COMPANY_ID.split('.')[0]
            if (data[companyFormKey]) {
              await editCompany({
                organizationId,
                companyId: (lead as Contact).company.id,
                ...data[companyFormKey],
              })
              dispatch(
                api.util.invalidateTags([
                  { type: 'Contact', id: `CONTACT_${lead.id}` },
                ])
              )
              data = omit(data, companyFormKey)
            }

            if (!isEmpty(data)) {
              if (isPhoneOrEmailAttribute) {
                data = {
                  [attribute.id]: (
                    data[attribute.id] as Array<{
                      value: string
                      id: string
                    }>
                  )
                    .filter(({ value }) => !isEmpty(value))
                    .map(({ value }) => value),
                }
              }
              const hasEmptyValue =
                data[attribute.id] === null ||
                data[attribute.id] === undefined ||
                data[attribute.id] === ''

              if (hasEmptyValue) {
                data = {
                  [attribute.id]: null,
                }
              }

              editLead(lead.id, data)
            }
          }),
        }
  }, [
    attribute.id,
    dispatch,
    editCompany,
    editLead,
    formMethods,
    isPhoneOrEmailAttribute,
    isUsingFormContext,
    lead,
    organizationId,
  ])

  // ***********************************
  // Exported methods
  // ***********************************

  const checkIfPhoneIsFromEnrichment = useCallback(
    (phone?: string): boolean => {
      if (
        attribute.id !== ExclusiveContactLeadFieldEnum.PHONES ||
        typeof phone !== 'string'
      ) {
        return false
      }
      const { enrichmentResult } = lead as Contact
      const enrichmentPhones = (enrichmentResult?.phones || []).map(
        enrichedPhone => removeSpaces(enrichedPhone)
      )
      const phoneToCheck = removeSpaces(phone || '')
      return enrichmentPhones.includes(phoneToCheck)
    },
    [attribute.id, lead]
  )

  const checkIfEmailIsFromEnrichment = useCallback(
    (email?: string): boolean => {
      if (
        attribute.id !== ExclusiveContactLeadFieldEnum.EMAILS ||
        typeof email !== 'string'
      ) {
        return false
      }
      const { enrichmentResult } = lead as Contact
      const enrichmentEmails = (enrichmentResult?.emails || []).map(
        enrichedEmail => removeSpaces(enrichedEmail)
      )
      const emailToCheck = removeSpaces(email || '')
      return enrichmentEmails.includes(emailToCheck)
    },
    [attribute.id, lead]
  )

  const hasMultipleValues = useMemo((): boolean => {
    const isArray = attribute.type === TypeLeadEnum.STRING_ARRAY
    if (isArray) {
      const value = findDeepObject(lead, attribute.id) as string[] | undefined
      return (value || []).length > 1
    }
    return false
  }, [attribute.id, attribute.type, lead])

  const getAttributeLabel = useCallback((): string => {
    if (attribute.name.includes(ENRICHMENT_RESULT_FIELD_PREFIX)) {
      return `${t('Enrichment')} ${t(
        attribute.name.replace(ENRICHMENT_RESULT_FIELD_PREFIX, '')
      )}`
    }

    const attributeNameSplit = attribute.name.split('.')

    if (attributeNameSplit[0] === 'company') {
      return t(attributeNameSplit[1], { ns: 'lead' })
    }

    if (attributeNameSplit.length > 1) {
      return t(attributeNameSplit[0], { ns: 'lead' })
    }

    return t(attribute.name, { ns: 'lead' })
  }, [attribute.name, t])

  return useMemo(
    () => ({
      getAttributeLabel,
      checkIfPhoneIsFromEnrichment,
      checkIfEmailIsFromEnrichment,
      hasMultipleValues,
      isPhoneAttribute,
      isEmailAttribute,
      isPhoneOrEmailAttribute,
      formMethods,
      renderAsProps,
      isUsingFormContext,
      getDefaultValue,
    }),
    [
      checkIfEmailIsFromEnrichment,
      checkIfPhoneIsFromEnrichment,
      formMethods,
      getAttributeLabel,
      getDefaultValue,
      hasMultipleValues,
      isEmailAttribute,
      isPhoneAttribute,
      isPhoneOrEmailAttribute,
      isUsingFormContext,
      renderAsProps,
    ]
  )
}
