import { clsx } from 'clsx'
import { debounce } from 'lodash'
import type { FormEvent } from 'react'
import { forwardRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import type { Contact } from '@getheroes/shared'
import { <PERSON><PERSON>, Divider, Icon } from '@getheroes/ui'
import { SelectMenu } from '@internals/components/common/dataEntry/SelectMenu/SelectMenu'
import { TextInput } from '@internals/components/common/dataEntry/Text/TextInput'
import { CreateLeadCompanyModal } from '@internals/features/lead/components/LeadsModals/CreateLeadsModals/CreateLeadCompanyModal/CreateLeadCompanyModal'
import { useSearchAndUpdateCompanyName } from '@internals/features/lead/hooks/searchLeads/useSearchAndUpdateCompanyName'

interface LeadAttributeCompanyNameProps {
  contact: Contact
  dataTestId: string
  value: string
  container?: HTMLElement | null | React.MutableRefObject<HTMLElement | null>
}

export const LeadAttributeCompanyName = forwardRef<
  HTMLDivElement,
  LeadAttributeCompanyNameProps
>(({ contact, dataTestId, value, container }, ref): JSX.Element => {
  const { t } = useTranslation('lead')
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateCompanyModalOpen, setIsCreateCompanyModalOpen] =
    useState(false)
  const { items, isUpdateLoading } = useSearchAndUpdateCompanyName({
    searchTerm,
    contact,
    handleCreateCompany: () => setIsCreateCompanyModalOpen(true),
  })

  const debounceSetSearchTerm = debounce(setSearchTerm, 200)
  const updateSearchTerm = (e: FormEvent<HTMLInputElement>) => {
    debounceSetSearchTerm(e.currentTarget.value.toLowerCase())
  }

  return (
    <>
      <div className={clsx('mt-1 w-full')} ref={ref}>
        {isUpdateLoading ? (
          <div
            className={clsx(
              'flex flex-row items-center justify-between c-attribute',
              'company-name-select'
            )}
          >
            <Button
              loading={true}
              variant={'tertiary-outlined'}
              size={'small'}
              fullWidth
            />
          </div>
        ) : (
          <SelectMenu
            container={container}
            items={items}
            button={
              <TextInput
                name={'company.name'}
                dataTestId={dataTestId}
                placeholder={t('Company name') as string}
                defaultValue={contact.company?.name}
                value={value}
                isStackingFormfield={false}
              />
            }
            searchOptionsComponent={
              <div className="flex flex-col gap-2">
                <TextInput
                  autoFocus
                  name={'search-company'}
                  placeholder={t('Search company...') as string}
                  defaultValue={searchTerm}
                  onChange={updateSearchTerm}
                  onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                    if (e.code == 'Space') {
                      e.stopPropagation() // prevents closing the select menu
                    }
                  }}
                  className={'border-none rounded-none'}
                  isStackingFormfield={false}
                  hasInitialBorder={false}
                  startIcon={<Icon name={'Search'} />}
                />

                <Divider />
              </div>
            }
          />
        )}
      </div>

      <CreateLeadCompanyModal
        container={container}
        companyName={searchTerm}
        isOpen={isCreateCompanyModalOpen}
        handleClose={() => {
          setIsCreateCompanyModalOpen(false)
          setSearchTerm('')
        }}
      />
    </>
  )
})
