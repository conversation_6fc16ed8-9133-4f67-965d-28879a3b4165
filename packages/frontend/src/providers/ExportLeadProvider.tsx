import type { PropsWithChildren } from 'react'
import { useMemo, useState } from 'react'

import type { IExportLeadContext } from '@internals/features/lead-export/ExportLeadContext'
import { ExportLeadContext } from '@internals/features/lead-export/ExportLeadContext'

export const ExportLeadProvider = ({ children }: PropsWithChildren) => {
  const [isExportOpen, setIsExportOpen] = useState<boolean>(false)
  const [enrichmentHubId, setEnrichmentHubId] = useState<string | undefined>(
    undefined
  )
  const [leadImportId, setLeadImportId] = useState<string | undefined>(
    undefined
  )
  const [exportsInProgress, setExportsInProgress] = useState<string[]>([])
  const [isOriginalColumnsChoice, setIsOriginalColumnsChoice] =
    useState<boolean>(true)

  const value: IExportLeadContext = useMemo(
    () => ({
      isExportOpen,
      toggleExport: () => {
        setIsExportOpen(prevState => !prevState)
      },
      enrichmentHubId,
      leadImportId,
      setLeadImportId: (leadImportID: string | undefined) => {
        setLeadImportId(() => leadImportID)

        return leadImportID
      },
      setEnrichmentHubId: (
        enrichmentHubId: string | undefined
      ): string | undefined => {
        setEnrichmentHubId(() => enrichmentHubId)

        return enrichmentHubId
      },
      exportsInProgress,
      addExportsInProgress: (id: string): string[] => {
        const newValues: string[] = [...exportsInProgress, id]

        setExportsInProgress(() => newValues)

        return newValues
      },
      removeExportsInProgress: (id: string): string[] => {
        const newValues: string[] = exportsInProgress.filter(
          exportId => exportId !== id
        )

        setExportsInProgress(() => newValues)

        return newValues
      },
      isOriginalColumnsChoice,
      setIsOriginalColumnsChoice: (value: boolean) => {
        setIsOriginalColumnsChoice(() => value)

        return value
      },
    }),
    [
      isExportOpen,
      enrichmentHubId,
      leadImportId,
      exportsInProgress,
      isOriginalColumnsChoice,
    ]
  )

  return (
    <ExportLeadContext.Provider value={value}>
      {children}
    </ExportLeadContext.Provider>
  )
}
