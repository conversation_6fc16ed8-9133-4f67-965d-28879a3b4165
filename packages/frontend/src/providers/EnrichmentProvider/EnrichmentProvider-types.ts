import type { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types'
import type { Contact, ExternalContact } from '@getheroes/shared'

export type AddExternalContactContextProcessState =
  | 'created'
  | 'loading'
  | 'error'

export type AddExternalContactContextProcessContact = {
  contact: Contact | undefined
  state: AddExternalContactContextProcessState
}

export type ProcessedContacts = Record<
  string,
  AddExternalContactContextProcessContact
>

export type FieldIdType =
  | ExclusiveContactLeadFieldEnum.EMAILS
  | ExclusiveContactLeadFieldEnum.PHONES

export type EnrichmentContextType = {
  enrichContact: (
    contact: Contact | ExternalContact,
    fieldId: FieldIdType
  ) => Promise<void>
  createAndAssignExternalContactProcessState: ProcessedContacts
  handleCreateExternalContact: (
    contact: ExternalContact
  ) => Promise<Contact | undefined>
}
