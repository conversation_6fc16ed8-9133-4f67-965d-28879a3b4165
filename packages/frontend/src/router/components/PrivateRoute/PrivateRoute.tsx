import type { FC } from 'react'
import { useEffect } from 'react'
import { Outlet } from 'react-router-dom'

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice'
import { useIdentify } from '@internals/features/auth/hooks/useIdentify'
import { useOnUserAuthenticated } from '@internals/features/auth/hooks/useOnUserAuthenticated'
import { useLazyGetIntegrationsQuery } from '@internals/features/integration/api/integrationApi'
import { useJimo } from '@internals/hooks/integrations/useJimo'
import { useHandleRedirections } from '@internals/router/components/PrivateRoute/useHandleRedirections'
import { useSubscribeToWebsockets } from '@internals/router/components/PrivateRoute/useSubscribeToWebsockets'
import { useTypedSelector } from '@internals/store/store'

import { useHandleIntegrationRedirectionCallback } from './useHandleIntegrationRedirectionCallback'

export const PrivateRoute: FC = () => {
  const currentOrganization = useTypedSelector(selectCurrentUserOrganization)

  useIdentify()
  const [getIntegration] = useLazyGetIntegrationsQuery()

  useHandleRedirections()
  useOnUserAuthenticated()
  useSubscribeToWebsockets()
  useHandleIntegrationRedirectionCallback()
  useJimo()

  useEffect(() => {
    if (currentOrganization?.id) {
      getIntegration(currentOrganization.id)
    }
  }, [currentOrganization?.id, getIntegration])

  if (!currentOrganization) {
    return null
  }

  return <Outlet />
}
