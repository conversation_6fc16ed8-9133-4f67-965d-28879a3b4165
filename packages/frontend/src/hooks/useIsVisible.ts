import type { MutableRefObject } from 'react'
import { useEffect, useState } from 'react'

/**
 * Hook to check if an element is visible in the viewport
 * @param ref React ref to the element
 * @returns boolean
 * @source https://dev.to/jmalvarez/check-if-an-element-is-visible-with-react-hooks-27h8
 *
 * To see this hook in action, check out the following files: Card.stories.tsx
 */
export function useIsVisible(ref: MutableRefObject<HTMLElement | undefined>) {
  const [isIntersecting, setIntersecting] = useState(false)

  useEffect(() => {
    if (!ref.current) {
      return
    }

    const observer = new IntersectionObserver(([entry]) =>
      setIntersecting(entry.isIntersecting)
    )

    observer.observe(ref.current)

    return () => {
      observer.disconnect()
    }
  }, [ref])

  return isIntersecting
}
