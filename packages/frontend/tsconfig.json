{"compilerOptions": {"module": "esnext", "baseUrl": ".", "jsx": "react-jsx", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "declaration": true, "types": ["vite/client", "vitest"], "paths": {"@internals/*": ["./src/*"], "@getheroes/shared": ["./../../libraries/shared/src/index.ts"], "@getheroes/ui": ["./../../libraries/frontend/ui-components/src/index.ts"], "@getheroes/ui-business": ["./../../libraries/frontend/ui-business-components/src/index.ts"], "@getheroes/frontend/config/*": ["./../../libraries/frontend/frontend-config/src/lib/*"], "@getheroes/frontend/hooks": ["./../../libraries/frontend/hooks/src/index.ts"], "@getheroes/frontend/types": ["./../../libraries/frontend/frontend-types/src/index.ts"], "@getheroes/frontend/utils": ["./../../libraries/frontend/frontend-utils/src/index.ts"]}}, "files": [], "include": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.spec.json"}, {"path": "./tsconfig.storybook.json"}], "extends": "../../tsconfig.base.json"}