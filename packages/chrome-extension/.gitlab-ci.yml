.chrome-extension-changes: &chrome-extension-changes
  - packages/chrome-extension/*
  - packages/chrome-extension/**/*
  - '.gitlab-ci.yml'
  - 'package.json'
  - 'tsconfig.base.json'
  - 'yarn.lock'

.chrome_extension_rules:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
    when: never
  - if: $CI_PIPELINE_SOURCE == "pipeline"
    when: never
  # Do not trigger if we are creating a tag (from master)
  - if: '$CI_COMMIT_TAG != null'
    when: never
  # Do not trigger when merging on develop, release or master
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "master"'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    changes: *chrome-extension-changes
    when: always
  - when: never

.chrome_extension_rules_lint:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
    when: never
  - if: $CI_PIPELINE_SOURCE == "pipeline"
    when: never
  # Do not trigger if we are creating a tag (from master)
  - if: '$CI_COMMIT_TAG != null'
    when: never
  # Do not trigger when merging on develop, release or master
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "master"'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    changes: *chrome-extension-changes
    when: always
  - when: never


test:chrome-extension:
  stage: test
  needs: [ 'install' ]
  image: node:22.15.0-alpine3.21
  coverage: '/All files[^\|]*\|[^\|]*\s+([\d\.]+)/'
  cache:
    - !reference [ .cache_app_pull ]
    - !reference [ .cache_coverage ]
  rules:
    - !reference [ .chrome_extension_rules ]
  script:
    - npx nx run chrome-extension:build
    - npx nx run chrome-extension:test:production --ci --coverage --skip-nx-cache

test:chrome-extension-lint:
  stage: test
  needs: [ 'install' ]
  image: node:22.15.0-alpine3.21
  cache:
    - !reference [ .cache_app_pull ]
  rules:
    - !reference [ .chrome_extension_rules_lint ]
  script:
    - npx nx lint chrome-extension # --skip-nx-cache
  interruptible: true
