import { join } from 'path';

import { createGlobPatternsForDependencies } from '@nx/react/tailwind';

import configPreset from '../../libraries/frontend/frontend-config/src/lib/tailwind.config';

/** @type {import('tailwindcss').Config} */
export default {
  presets: [configPreset],
  content: [join(__dirname, './src/**/*.{js,ts,jsx,tsx,html}'), ...createGlobPatternsForDependencies(__dirname)],
  plugins: [],
  corePlugins: {
    preflight: false,
  },
  // Increase our tailwind classes specificity
  important: '.zeliq',
};
