import { useCreateContactFromParsed } from '@root/hooks/useCreateContactFromParsed';
import { useCurrentPage } from '@root/hooks/useCurrentPage';
import { useEnrichWithVoyager } from '@root/hooks/useEnrichWithVoyager';
import { useGetLinkedInSourceInfo } from '@root/hooks/useGetLinkedInSourceInfo';
import { useToggleSidePanel } from '@root/hooks/useToggleSidePanel';
import { selectCurrentContact, setCurrentContact } from '@root/store/slices/contactProfileSlice';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';

import { selectCurrentUser } from '@getheroes/frontend/config/store/slices/authSlice';
import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice';
import { LeadSource } from '@getheroes/shared';
import type { User } from '@getheroes/shared';
import { Button } from '@getheroes/ui';
import { useAssignUserToLeadsMutation } from '@internals/features/lead/api/assignApi';
import { useTypedSelector } from '@internals/store/store';

import { AssignationButton } from './AssignationButton';

export const ActionButtonsWrapper = ({
  onOpenActivity,
  onScheduleTask,
}: {
  onOpenActivity: () => void;
  onScheduleTask: () => void;
}) => {
  const { t } = useTranslation('lead');
  const organization = useTypedSelector(selectCurrentUserOrganization);
  const currentUser = useTypedSelector(selectCurrentUser) as User;
  const { getLinkedInSourceDetail } = useGetLinkedInSourceInfo();
  const currentUrl = useCurrentPage();
  const { isOpenSidePanel, toggleSidePanel } = useToggleSidePanel();
  const dispatch = useDispatch();
  const { enrichWithVoyager } = useEnrichWithVoyager();

  const [isLoadingAddContact, setIsLoadingAddContact] = useState(false);
  const contact = useSelector(selectCurrentContact);
  const isContactAlreadyAdded = contact && 'createdAt' in contact && !!contact.createdAt;
  const isContactAssigned = contact && 'assignUser' in contact && !!contact.assignUser;
  const isAssignedToCurrentUser = contact && 'assignUser' in contact && contact.assignUser?.id === currentUser?.id;

  const [assignUserToLeads, { isLoading: isLoadingAssignation }] = useAssignUserToLeadsMutation();

  const [createContactFromParsed, { isLoading: isLoadingContactFromParsed }] = useCreateContactFromParsed({
    source: LeadSource.LINKEDIN,
    sourceMeta: {
      info: getLinkedInSourceDetail(currentUrl),
    },
  });

  if (!isContactAlreadyAdded) {
    return (
      <Button
        variant="primary"
        disabled={!contact || !contact.id}
        onClick={async () => {
          if (!contact || !contact.id) {
            return;
          }
          try {
            setIsLoadingAddContact(true);
            !isOpenSidePanel && toggleSidePanel(true);
            const enrichedContact = await enrichWithVoyager(contact);
            const { createResult } = await createContactFromParsed(enrichedContact);
            if (contact && 'data' in createResult) {
              dispatch(setCurrentContact(createResult.data));
            }
          } catch (error) {
            // todo - add a toast ?
            console.error('Failed to create contact:', error);
          } finally {
            setIsLoadingAddContact(false);
          }
        }}
        loading={isLoadingAddContact || isLoadingContactFromParsed}
        iconLeft="UserPlus">
        {t('Add contact')}
      </Button>
    );
  }

  if (isContactAlreadyAdded && !isContactAssigned) {
    return (
      <AssignationButton
        assignButtonLabel={t('Assign to myself')}
        isLoading={isLoadingAssignation}
        onClickAssignation={async () => {
          if (!contact || !contact.id || !organization?.id) {
            return;
          }
          try {
            !isOpenSidePanel && toggleSidePanel(true);
            const assignResult = await assignUserToLeads({
              organizationId: organization.id,
              userId: currentUser?.id,
              contacts: [contact?.id],
            });

            // Error or no data from the API
            if ('error' in assignResult || !assignResult.data) {
              return;
            }
            // Optimistic update if the API call is successful
            dispatch(
              setCurrentContact({
                ...contact,
                assignUser: {
                  id: currentUser?.id,
                  firstName: currentUser?.firstName,
                  lastName: currentUser?.lastName,
                },
              }),
            );
          } catch (error) {
            // todo - add a toast ?
            console.error('Failed to assign contact:', error);
          }
        }}
      />
    );
  }

  if (isContactAlreadyAdded && isContactAssigned && !isAssignedToCurrentUser) {
    return (
      <Button variant="secondary" iconLeft="Clock" onClick={onOpenActivity}>
        {t('See activity')}
      </Button>
    );
  }

  if (isContactAlreadyAdded && isAssignedToCurrentUser) {
    return (
      <>
        <Button variant="secondary" iconLeft="Clock" onClick={onOpenActivity}>
          {t('See activity')}
        </Button>
        <Button variant="primary" onClick={onScheduleTask} iconLeft="Calendar">
          {t('Schedule task')}
        </Button>
      </>
    );
  }

  return null;
};
