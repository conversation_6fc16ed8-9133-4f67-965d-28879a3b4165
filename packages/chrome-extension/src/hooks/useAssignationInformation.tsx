import type { ParsedContact } from '@root/types/parsed-contact';
import { useTranslation } from 'react-i18next';

import type { User, Contact } from '@getheroes/shared';
import type { IconName, TextColor } from '@getheroes/ui';

export const useAssignationInformation = (currentUser: User | null, contact: ParsedContact | Contact) => {
  const { t } = useTranslation('lead');
  const isContactAlreadyAdded = contact && 'createdAt' in contact && !!contact.createdAt;
  const isContactAssigned = contact && 'assignUser' in contact && !!contact.assignUser;
  const isAssignedToCurrentUser =
    contact && 'assignUser' in contact && !!contact.assignUser && contact.assignUser.id === currentUser?.id;

  const assignationIconColor: TextColor = !isContactAlreadyAdded
    ? 'decorative-red'
    : isContactAssigned
      ? 'decorative-green'
      : 'decorative-orange';

  const assignationBackgroundColor = !isContactAlreadyAdded
    ? 'bg-base-error'
    : isContactAssigned
      ? 'bg-decorative-green-default'
      : 'bg-decorative-orange-default';

  const getAssignationWording = () => {
    const messages = {
      notInZeliq: t("Contact isn't in Zeliq"),
      notAssignedYet: t("Contact isn't assigned yet"),
    };

    if (!isContactAlreadyAdded) {
      return {
        key: messages.notInZeliq,
      };
    }

    if (!isContactAssigned) {
      return { key: messages.notAssignedYet };
    }

    return isAssignedToCurrentUser
      ? { key: t('Contact is assigned to you') }
      : {
          key: t('Contact is assigned to {{name}}', {
            name: `${contact.assignUser?.firstName} ${contact.assignUser?.lastName}`,
          }),
        };
  };

  const iconName: IconName = !isContactAlreadyAdded ? 'Xmark' : isContactAssigned ? 'Check' : 'WarningCircle';

  return {
    iconName,
    assignationIconColor,
    assignationBackgroundColor,
    getAssignationWording,
  };
};
