import { useCreateContactFromParsed } from '@root/hooks/useCreateContactFromParsed';
import { useCurrentPage } from '@root/hooks/useCurrentPage';
import { useGetLinkedInSourceInfo } from '@root/hooks/useGetLinkedInSourceInfo';
import type { ParsedContact } from '@root/types/parsed-contact';
import isEmpty from 'lodash/isEmpty';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { ExclusiveContactLeadFieldEnum } from '@getheroes/frontend/types';
import { getEnrichmentTypeFromSelectedFields } from '@getheroes/frontend/utils';
import type { Contact } from '@getheroes/shared';
import { LeadSource } from '@getheroes/shared';
import { useEnrichPhoneOrEmail } from '@internals/features/lead/hooks/enrichment/useEnrichPhoneOrEmail';

type EnrichFunction = {
  (
    contact: Contact,
    fieldId: ExclusiveContactLeadFieldEnum.PHONES | ExclusiveContactLeadFieldEnum.EMAILS,
    isContactFromParsedData: false,
  ): void;
  (contact: ParsedContact, fieldId: ExclusiveContactLeadFieldEnum.PHONES | ExclusiveContactLeadFieldEnum.EMAILS): void;
};

type UseEnrichPhoneOrEmailExtensionReturnType = [
  (
    contact: Contact | ParsedContact,
    fieldId: ExclusiveContactLeadFieldEnum.PHONES | ExclusiveContactLeadFieldEnum.EMAILS,
  ) => void,
];

export const useEnrichPhoneOrEmailExtension = (): UseEnrichPhoneOrEmailExtensionReturnType => {
  const [contact, setContact] = useState<Contact>();
  const [isCreatingContact, setIsCreatingContact] = useState(false);
  const [enrichmentPending, setEnrichmentPending] = useState<
    (ExclusiveContactLeadFieldEnum.EMAILS | ExclusiveContactLeadFieldEnum.PHONES)[]
  >([]);

  const currentUrl = useCurrentPage();
  const { getLinkedInSourceDetail } = useGetLinkedInSourceInfo();
  const [createContactFromParsed, { isLoading }] = useCreateContactFromParsed({
    source: LeadSource.LINKEDIN,
    sourceMeta: {
      info: getLinkedInSourceDetail(currentUrl),
    },
  });

  const onEnrichPhoneOrEmail = useEnrichPhoneOrEmail();

  const EnrichContact = useCallback(
    async (contact: Contact, fieldId: ExclusiveContactLeadFieldEnum) => {
      const fieldNameProxy = fieldId === ExclusiveContactLeadFieldEnum.PHONES ? 'phone' : 'email';
      return onEnrichPhoneOrEmail([contact], getEnrichmentTypeFromSelectedFields({ [fieldNameProxy]: true }));
    },
    [onEnrichPhoneOrEmail],
  );

  const handleEnrichmentButton: EnrichFunction = useCallback(
    async (
      contact: Contact | ParsedContact,
      fieldId: ExclusiveContactLeadFieldEnum.PHONES | ExclusiveContactLeadFieldEnum.EMAILS,
    ) => {
      if (!contact) {
        return;
      }

      let currentContact: Contact;
      const isContactAlreadyAdded = (contact as Contact)?.createdAt;

      /* ----------------------------------------------- */
      // Contact already added (= assign / enrich)
      /* ----------------------------------------------- */
      if (isContactAlreadyAdded) {
        currentContact = contact as Contact;
        !isEmpty(enrichmentPending) && setEnrichmentPending(prev => prev.filter(f => f !== fieldId));
        EnrichContact(currentContact, fieldId);
      }
      /* ----------------------------------------------- */
      // Contact not yet added (= create / assign / enrich)
      /* ----------------------------------------------- */
      else {
        if (!isCreatingContact) {
          setIsCreatingContact(true);
          const createdContact = await createContactFromParsed(contact as ParsedContact);
          if (createdContact && 'data' in createdContact.createResult) {
            setContact(createdContact.createResult?.data);
            !isEmpty(enrichmentPending) && setEnrichmentPending(prev => prev.filter(f => f !== fieldId));

            // we must wait the invalidation triggered by the create contact endpoint
            // because if we don't wait, the getContact query is not in the RTK Query Cache
            // and we need it to push the contact enrichment result in the getContact cache
            setTimeout(() => {
              EnrichContact(createdContact.createResult?.data, fieldId);
            }, 500);
          } else {
            return;
          }
        } else {
          setEnrichmentPending([...enrichmentPending, fieldId]);
        }
      }
    },
    [EnrichContact, createContactFromParsed, enrichmentPending, isCreatingContact],
  );

  useEffect(() => {
    if (contact?.createdAt && !isEmpty(enrichmentPending)) {
      enrichmentPending.forEach(fieldId => {
        EnrichContact(contact, fieldId);
      });
      setEnrichmentPending([]);
    }
  }, [EnrichContact, contact, enrichmentPending]);

  return useMemo(() => {
    return [handleEnrichmentButton, { isLoadingContactCreation: isLoading }];
  }, [handleEnrichmentButton, isLoading]);
};
