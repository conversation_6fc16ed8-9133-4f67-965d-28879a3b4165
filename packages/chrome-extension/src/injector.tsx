/* eslint-disable import/no-duplicates */

import { Providers as RootProviders } from '@root/context/Providers';
import React, { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';

import './index.scss';

import reset from './styles/reset.css?inline';
import { getElementFromSelector } from './utils/selectors';

type InjectShadowDomType = {
  rootId?: string;
  Component: React.ReactNode;
  Provider?: React.ComponentType<{ children: React.ReactNode }>;
  target?: Element;
  selector?: string;
  insert?: 'prepend' | 'insertBefore' | 'insertAfter';
  rootClassName?: string;
  rootStyles?: Record<string, string>;
};

const createRootSingleton = ({
  rootId,
  rootClassName,
  rootStyles,
}: {
  rootId?: string;
  rootClassName?: string;
  rootStyles?: Record<string, string>;
}) => {
  const root = document.createElement('div');
  // If has rootId make sure we render only once
  if (rootId) {
    root.id = rootId;
    // Remove existing element if it exists
    const existingElement = getElementFromSelector(`#${rootId}`);
    if (existingElement) {
      existingElement.remove();
    }
  }
  // Apply classNames if provided
  if (rootClassName) {
    root.classList.add(...rootClassName.split(' '));
  }
  // Apply inline styles if provided
  if (rootStyles) {
    Object.entries(rootStyles).forEach(([property, value]) => {
      root.style[property as any] = value;
    });
  }
  return root;
};

const injectStyles = async (shadowRoot: ShadowRoot) => {
  // Load the fonts
  const robotoFont = new FontFace('Roboto', `url(${chrome.runtime.getURL('/fonts/Roboto-Medium.ttf')})`);
  await robotoFont.load();
  document.fonts.add(robotoFont);

  const interFont = new FontFace('Inter', `url(${chrome.runtime.getURL('/fonts/Inter.ttf')})`);
  await interFont.load();
  document.fonts.add(interFont);

  const telegrafFont = new FontFace('PPTelegraf', `url(${chrome.runtime.getURL('/fonts/PPTelegraf-Bold.ttf')})`);
  await telegrafFont.load();
  document.fonts.add(telegrafFont);

  // Use a link element to load the css to prevent fetching the css from the extension
  const linkElement = document.createElement('link');
  linkElement.setAttribute('rel', 'stylesheet');
  linkElement.href = chrome.runtime.getURL('/assets/content.css');
  shadowRoot.appendChild(linkElement);

  // Create and append the reset CSS
  const resetStyleElement = document.createElement('style');
  resetStyleElement.textContent = reset;
  shadowRoot.appendChild(resetStyleElement);
};

// Inject any React component into shadow dom on page
export const injectShadowDom = async ({
  rootId,
  Component,
  Provider = React.Fragment,
  target,
  selector = 'body',
  insert,
  rootClassName,
  rootStyles,
}: InjectShadowDomType) => {
  const root = createRootSingleton({ rootId, rootClassName, rootStyles });
  const getTargetElement = () => {
    if (!target && !selector) {
      return null;
    }

    if (target) {
      return target;
    }

    // Handle selector
    if (insert === 'insertBefore') {
      const element = getElementFromSelector(selector);
      if (!element) {
        return null;
      }
      const div = document.createElement('div');
      element.parentElement?.insertBefore(div, element);
      return div;
    }

    if (insert === 'insertAfter') {
      const element = getElementFromSelector(selector);
      if (!element) {
        return null;
      }

      const div = document.createElement('div');
      element?.after(div);
      return div;
    }

    return getElementFromSelector(selector);
  };

  const targetElement = getTargetElement();

  if (!targetElement) {
    // eslint-disable-next-line no-console
    console.warn(`Could not find element with selector: ${selector}`);
    return;
  }

  // Append root
  if (insert === 'prepend') {
    targetElement.prepend(root);
  } else {
    targetElement.append(root);
  }

  // Create a div element to hold the React component inside the shadow DOM
  const rootIntoShadow = document.createElement('div');
  rootIntoShadow.classList.add('zeliq');

  // Attach a shadow DOM to the root element
  const shadowRoot = root.attachShadow({ mode: 'open' });

  // Handle styles
  await injectStyles(shadowRoot);

  // Append the div into the shadow DOM
  shadowRoot.appendChild(rootIntoShadow);

  // Render the React component inside the div element with optional providers
  const rootIntoShadowRoot = createRoot(rootIntoShadow);
  rootIntoShadowRoot.render(
    <StrictMode>
      <RootProviders>
        <Provider>{Component}</Provider>
      </RootProviders>
    </StrictMode>,
  );
};
