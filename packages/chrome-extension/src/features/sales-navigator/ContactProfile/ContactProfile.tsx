import { ContactProfileOverlay } from '@root/components/Overlay/ContactProfileOverlay/ContactProfileOverLay';
import { ContactSidePanel } from '@root/components/SidePanel/ContactSidePanel/ContactSidePanel';
import { useMatchingContact } from '@root/hooks/useMatchingContact';
import { NoContactPage } from '@root/pages/NoContactPage';
import { selectCurrentContact, selectIsContactLoading } from '@root/store/slices/contactProfileSlice';
import { useSelector } from 'react-redux';

import { ErrorPage } from '@internals/pages/ErrorPage/ErrorPage';

export const ContactProfilePage = () => {
  const contact = useSelector(selectCurrentContact);
  const isContactLoading = useSelector(selectIsContactLoading);
  const { isError, errorType } = useMatchingContact({ context: 'salesnav' });

  if (isError && errorType === 'error') {
    return <ErrorPage />;
  }

  if (isError && errorType === 'no-contact') {
    return <NoContactPage />;
  }

  return (
    <>
      <ContactProfileOverlay context={'linkedin'} />
      <ContactSidePanel contact={contact} isContactLoading={isContactLoading} />
    </>
  );
};
