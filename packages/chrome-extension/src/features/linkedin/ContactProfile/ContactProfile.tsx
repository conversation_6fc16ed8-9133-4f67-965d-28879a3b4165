import { ContactProfileOverlay } from '@root/components/Overlay/ContactProfileOverlay/ContactProfileOverLay';
import { ContactSidePanel } from '@root/components/SidePanel/ContactSidePanel/ContactSidePanel';
import { useMatchingContact } from '@root/hooks/useMatchingContact';
import { selectCurrentContact, selectIsContactLoading } from '@root/store/slices/contactProfileSlice';
import { useSelector } from 'react-redux';

import { ErrorPage } from '@internals/pages/ErrorPage/ErrorPage';

import { NoContactPage } from '../../../pages/NoContactPage';

export const ContactProfilePage = () => {
  const contact = useSelector(selectCurrentContact);
  const isContactLoading = useSelector(selectIsContactLoading);
  const { isError, errorType } = useMatchingContact({ context: 'linkedin' });

  if (isError && errorType === 'error') {
    return <ErrorPage />;
  }

  if (isError && errorType === 'no-contact') {
    return <NoContactPage />;
  }

  return (
    <>
      <ContactProfileOverlay context="linkedin" />
      <ContactSidePanel contact={contact} isContactLoading={isContactLoading} />
    </>
  );
};
