import { useLazyNavigationObserver } from '@root/hooks/useNavigationObserver';
import { useGetSelectorsQuery } from '@root/services/selectorsApi';
import { setIsLoadingContacts, updateContacts } from '@root/store/slices/contactSearchSlice';
import { useAppDispatch, useTypedSelector } from '@root/store/store';
import type { NewContact } from '@root/types/contact-search-page';
import { waitForElements } from '@root/utils/mutation-observer';
import { searchContacts } from '@root/utils/searchContacts';
import { getAllElementsFromSelector, getElementFromSelector, getMostRecentSelector } from '@root/utils/selectors';
import { debounce } from 'lodash';
import { useEffect } from 'react';

import { selectCurrentUserOrganization } from '@getheroes/frontend/config/store/slices/organizationSlice';
import { useLazySearchMatchingContactsForChromeExtensionQuery } from '@internals/features/lead/api/contactApi';

import { scrapeContact } from '../utils/scrapeContact';

const TIMEOUT_DELAY = 400;

export const useMatchingContact = () => {
  const { data: selectors } = useGetSelectorsQuery();
  const dispatch = useAppDispatch();
  const { id: organizationId } = useTypedSelector(selectCurrentUserOrganization) || {};
  const [searchMatchingContacts, { isLoading, isError }] = useLazySearchMatchingContactsForChromeExtensionQuery();
  const { observeNavigation } = useLazyNavigationObserver();

  const watchedContainerSelector = getMostRecentSelector(selectors?.linkedin?.contactSearchResults?.watchedContainer);

  useEffect(() => {
    if (!selectors?.linkedin?.contactSearchResults?.contactCard || !organizationId) {
      return;
    }

    const getContactCards = async () => {
      dispatch(setIsLoadingContacts(true));
      const withCompany: NewContact[] = [];
      const withoutCompany: NewContact[] = [];
      const alreadyAdded: NewContact[] = [];

      const peopleSearchResultsContainerSelector = getMostRecentSelector(
        selectors.linkedin.contactSearchResults.contactCard,
      );
      const nameLinkSelector = getMostRecentSelector(selectors.linkedin.contactSearchResults.contactCardNameLink);
      const cardNameSelector = getMostRecentSelector(selectors.linkedin.contactSearchResults.contactCardName);
      const positionSelector = getMostRecentSelector(selectors.linkedin.contactSearchResults.contactCardPosition);
      const headLineSelector = getMostRecentSelector(selectors.linkedin.contactSearchResults.contactCardHeadline);
      const avatarSelector = getMostRecentSelector(selectors.linkedin.contactSearchResults.contactCardImage);

      const isFound = await waitForElements([
        peopleSearchResultsContainerSelector,
        nameLinkSelector,
        cardNameSelector,
        headLineSelector,
        avatarSelector,
      ]);
      if (!isFound) {
        dispatch(setIsLoadingContacts(false));
        return;
      }
      const contactCards = Array.from(
        getAllElementsFromSelector<HTMLElement>(peopleSearchResultsContainerSelector, {
          throwOnError: true,
        }),
      );
      const contactsToSearch = [];
      for (const contactCard of contactCards) {
        const contact = scrapeContact({
          element: contactCard,
          nameLinkSelector,
          cardNameSelector,
          positionSelector,
          headLineSelector,
          avatarSelector,
        });

        if (!contact) {
          continue;
        }

        contactsToSearch.push(contact);
      }

      try {
        const {
          withCompany: withCompanyFromSearch,
          withoutCompany: withoutCompanyFromSearch,
          alreadyAdded: alreadyAddedFromSearch,
        } = await searchContacts({
          searchMatchingContacts,
          organizationId,
          contactsToSearch,
        });

        withCompany.push(...withCompanyFromSearch);
        withoutCompany.push(...withoutCompanyFromSearch);
        alreadyAdded.push(...alreadyAddedFromSearch);
      } catch (error) {
        console.error('Error searching contacts:', error);
        dispatch(setIsLoadingContacts(false));
      }
      // Update the contacts when all the are scraped
      dispatch(
        updateContacts({
          withCompany,
          alreadyAdded,
          withoutCompany,
        }),
      );
      dispatch(setIsLoadingContacts(false));
      return true;
    };

    const debouncedGetContactCards = debounce(async () => await getContactCards(), 100);
    debouncedGetContactCards();

    const handleMutation = async (observer: MutationObserver) => {
      dispatch(setIsLoadingContacts(true));
      const timeout = setTimeout(async () => {
        const isSuccess = await debouncedGetContactCards();
        if (isSuccess) {
          observer.disconnect();
          clearTimeout(timeout);
        }
      }, TIMEOUT_DELAY);
    };

    // On every navigation, we need to start a new observer to be able to scrape all contacts
    const { disconnect } = observeNavigation(() => {
      const changesObserver = new MutationObserver(() => handleMutation(changesObserver));
      const watchedContainer = getElementFromSelector(watchedContainerSelector, {
        throwOnError: true,
      });
      if (watchedContainer) {
        changesObserver.observe(watchedContainer, { subtree: true, childList: true });
      }
    });
    return () => {
      disconnect();
    };
  }, [dispatch, observeNavigation, organizationId, searchMatchingContacts, selectors, watchedContainerSelector]);

  return {
    isLoading,
    isError,
  };
};
