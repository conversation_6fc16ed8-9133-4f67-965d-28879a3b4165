import type { ParsedContact } from '@root/types/parsed-contact';
import { generateMatchingId } from '@root/utils/api';
import { getPositionAndCompany } from '@root/utils/contact/extractPositionAndCompanyFromContactSearch';
import { parseFullName } from '@root/utils/fullName';
import { normalizeLinkedinUrl } from '@root/utils/normalize';
import { getElementFromSelector } from '@root/utils/selectors';

export const scrapeContact = ({
  element,
  nameLinkSelector,
  cardNameSelector,
  positionSelector,
  headLineSelector,
  avatarSelector,
}: {
  element: HTMLElement;
  nameLinkSelector: string;
  cardNameSelector: string;
  positionSelector: string;
  headLineSelector: string;
  avatarSelector: string;
}): ParsedContact | null => {
  const nameLink = getElementFromSelector<HTMLAnchorElement>(nameLinkSelector, {
    element,
  });
  const fullNameElement = getElementFromSelector<HTMLAnchorElement>(cardNameSelector, {
    element: nameLink,
    throwOnError: true,
  });
  const fullName = fullNameElement?.textContent?.trim() ?? '';

  const { firstName, lastName } = parseFullName(fullName);

  // If the linkedin url is not found, skip the contact
  if (!nameLink?.href) {
    return null;
  }

  const { linkedInUrlCleaned, linkedInIdentifier } = normalizeLinkedinUrl(nameLink?.href.split('?')[0] ?? '') || {};

  const { currentPosition, companyName } = getPositionAndCompany({
    element,
    positionSelector,
    headLineSelector,
  });
  const avatarElement = getElementFromSelector<HTMLImageElement>(avatarSelector, {
    element,
  });
  const avatar = avatarElement?.src;

  return {
    id: linkedInIdentifier || '',
    externalId: linkedInIdentifier || '',
    fullName: `${firstName} ${lastName}`,
    firstName,
    lastName,
    picture: avatar || '',
    linkedinId: linkedInIdentifier || '',
    linkedinUrl: linkedInUrlCleaned || '',
    experiences: [
      {
        companyName: companyName || '',
        position: currentPosition || '',
      },
    ],
    company: {
      name: companyName || '',
    },
    emails: [],
    phones: [],
    title: currentPosition || '',
    matchingId: generateMatchingId(linkedInIdentifier || `${firstName}-${lastName}-${companyName}`),
  };
};
