import { ContactSearchSidePanel } from '@root/features/linkedin/ContactSearch/components/ContactSearchSidePanel';

import { ErrorPage } from '@internals/pages/ErrorPage/ErrorPage';

import { ContactSearchResults } from './components/ContactSearchResults';
import { useMatchingContact } from './hooks/useMatchingContact';

export const ContactSearchPage = () => {
  const { isError } = useMatchingContact();
  if (isError) {
    return <ErrorPage />;
  }

  return (
    <>
      <ContactSearchResults />
      <ContactSearchSidePanel />
    </>
  );
};
