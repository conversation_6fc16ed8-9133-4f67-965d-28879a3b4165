import {
  MatchingSource,
  TransformedMatchingContactsResponseType,
  ContactMatchingInputDto,
  MatchingResult,
} from '@getheroes/shared';
import { buildMockedContact } from '@internals/mocks/contact';

import { getMostScoredResult } from './getMostScoredResult';

describe('getMostScoredResult', () => {
  const data: TransformedMatchingContactsResponseType = [
    {
      source: [
        {
          matchingId: '123e4567-e89b-12d3-a456-426614174000',
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Doe',
          companyName: 'TechCorp',
          phoneNumbers: ['+1234567890'],
        },
      ],
      matches: {
        '1': {
          contact: {
            ...buildMockedContact(),
            firstName: 'Joe',
            lastName: 'Doe',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          matchingSource: 'HUBSPOT' as MatchingSource,
          scoring: {
            score: '89',
            criterias: [
              {
                name: 'email' as keyof ContactMatchingInputDto,
                result: 'MATCH' as MatchingResult,
              },
            ],
          },
        },
        '2': {
          contact: {
            ...buildMockedContact(),
            firstName: 'Johnny',
            lastName: 'Doe',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          matchingSource: 'HUBSPOT',
          scoring: {
            score: '100',
            criterias: [
              {
                name: 'email' as keyof ContactMatchingInputDto,
                result: 'MATCH' as MatchingResult,
              },
            ],
          },
        },
      },
    },
  ];
  const { matches } = data[0];

  describe('basic functionality', () => {
    it('should return the contact with highest score', () => {
      const result = getMostScoredResult({ matches });
      expect(result).toEqual(matches['2'].contact);
    });

    it('should return null when no matches are found', () => {
      const result = getMostScoredResult({ matches: {} });
      expect(result).toBeNull();
    });
  });
});
