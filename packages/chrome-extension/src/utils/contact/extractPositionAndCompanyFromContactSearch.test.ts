import {
  extractCompanyAndPositionFromHeadline,
  extractCompanyNameFromSalesNavButton,
  extractPositionAndCompanyFromContactSearch,
  getPositionAndCompany,
} from './extractPositionAndCompanyFromContactSearch';

// Mock datadogLogs
jest.mock('../datadog/datadogLogs', () => ({
  datadogLogs: {
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
  },
}));

describe('extractPositionAndCompanyFromContactSearch', () => {
  it('should return null if no current prefix or separator is found', () => {
    expect(extractPositionAndCompanyFromContactSearch('Software Engineer')).toBeNull();
    expect(extractPositionAndCompanyFromContactSearch('Current:')).toBeNull();
    expect(extractPositionAndCompanyFromContactSearch('at Google')).toBeNull();
  });

  it('should extract position and company with English format', () => {
    const result = extractPositionAndCompanyFromContactSearch('Current: Software Engineer at Google');
    expect(result).toEqual({
      position: 'Software Engineer',
      company: 'Google',
    });
  });

  it('should extract position and company with French format', () => {
    const result = extractPositionAndCompanyFromContactSearch(
      'Entreprise actuelle  : Sales and Marketing Product Manager chez Devoteam',
    );
    expect(result).toEqual({
      position: 'Sales and Marketing Product Manager',
      company: 'Devoteam',
    });
  });
  it('should extract position and company with complex French format', () => {
    const result = extractPositionAndCompanyFromContactSearch(
      'Entreprise actuelle  : Account Manager / Sales Manager - Aerospace & Defense chez Accenture',
    );
    expect(result).toEqual({
      position: 'Account Manager / Sales Manager - Aerospace & Defense',
      company: 'Accenture',
    });
  });

  it('should handle extra spaces in the input', () => {
    const result = extractPositionAndCompanyFromContactSearch('Current:   Product Manager   at   Apple Inc.  ');
    expect(result).toEqual({
      position: 'Product Manager',
      company: 'Apple Inc.',
    });
  });

  it('should handle complex position titles', () => {
    const result = extractPositionAndCompanyFromContactSearch(
      'Current: Senior Staff Software Engineer, AI/ML at Tesla',
    );
    expect(result).toEqual({
      position: 'Senior Staff Software Engineer, AI/ML',
      company: 'Tesla',
    });
  });

  it('should handle complex company names', () => {
    const result = extractPositionAndCompanyFromContactSearch('Current: Designer at Acme Corp. (2020-Present)');
    expect(result).toEqual({
      position: 'Designer',
      company: 'Acme Corp. (2020-Present)',
    });
  });

  it('should return null if separator comes before prefix', () => {
    expect(extractPositionAndCompanyFromContactSearch('Engineer at Google Current: Product Manager')).toBeNull();
  });
});

describe('extractCompanyAndPositionFromHeadline', () => {
  it('should extract position and company with @ symbol', () => {
    const result = extractCompanyAndPositionFromHeadline('Software Engineer@Google');
    expect(result).toEqual({
      position: 'Software Engineer',
      company: 'Google',
    });
  });

  it('should extract position and company with "chez" format', () => {
    const result = extractCompanyAndPositionFromHeadline('Product Manager chez Microsoft');
    expect(result).toEqual({
      position: 'Product Manager',
      company: 'Microsoft',
    });
  });

  it('should extract position and company with "at" format', () => {
    const result = extractCompanyAndPositionFromHeadline('Data Scientist at Amazon');
    expect(result).toEqual({
      position: 'Data Scientist',
      company: 'Amazon',
    });
  });

  it('should handle spaces around separators', () => {
    const result = extractCompanyAndPositionFromHeadline('UX Designer   @   Apple');
    expect(result).toEqual({
      position: 'UX Designer',
      company: 'Apple',
    });
  });

  it('should remove ex-company parts from company name', () => {
    const result = extractCompanyAndPositionFromHeadline('Software Developer at Google | ex-Facebook');
    expect(result).toEqual({
      position: 'Software Developer',
      company: 'Google',
    });
  });

  it('should handle multiple ex-company parts', () => {
    const result = extractCompanyAndPositionFromHeadline('Product Manager at Amazon | ex-Microsoft | ex-Apple');
    expect(result).toEqual({
      position: 'Product Manager',
      company: 'Amazon',
    });
  });

  it('should handle company name at the end with @ symbol', () => {
    const result = extractCompanyAndPositionFromHeadline('Decarbonizing Fashion @Carbonfact');
    expect(result).toEqual({
      position: 'Decarbonizing Fashion',
      company: 'Carbonfact',
    });
  });

  it('should return position only when company is separated by pipe', () => {
    const result = extractCompanyAndPositionFromHeadline('Frontend Developer | React Expert');
    expect(result).toEqual({
      position: 'Frontend Developer',
      company: null,
    });
  });

  it('should return position only when no company is present', () => {
    const result = extractCompanyAndPositionFromHeadline('Freelance Consultant');
    expect(result).toEqual({
      position: 'Freelance Consultant',
      company: null,
    });
  });
});

describe('getPositionAndCompany', () => {
  let mockElement: HTMLElement;

  beforeEach(() => {
    mockElement = document.createElement('div');
  });

  it('should extract position and company from position selector', () => {
    mockElement.innerHTML = `
      <div class="position">Current: Software Engineer at Google</div>
    `;

    const result = getPositionAndCompany({
      element: mockElement,
      positionSelector: '.position',
    });

    expect(result).toEqual({
      currentPosition: 'Software Engineer',
      companyName: 'Google',
    });
  });

  it('should fallback to headline when position does not have company', () => {
    mockElement.innerHTML = `
      <div class="position">Current: Freelance Developer</div>
      <div class="headline">Frontend Developer at Microsoft</div>
    `;

    const result = getPositionAndCompany({
      element: mockElement,
      positionSelector: '.position',
      headLineSelector: '.headline',
    });

    expect(result).toEqual({
      currentPosition: 'Frontend Developer',
      companyName: 'Microsoft',
    });
  });

  it('should return empty values when no selectors match', () => {
    mockElement.innerHTML = `
      <div class="other">Some other content</div>
    `;

    const result = getPositionAndCompany({
      element: mockElement,
      positionSelector: '.position',
      headLineSelector: '.headline',
    });

    expect(result).toEqual({
      currentPosition: '',
      companyName: '',
    });
  });

  it('should return empty values when headLineSelector is not provided and position has no company', () => {
    mockElement.innerHTML = `
      <div class="position">Current: Freelance Developer</div>
    `;

    const result = getPositionAndCompany({
      element: mockElement,
      positionSelector: '.position',
    });

    expect(result).toEqual({
      currentPosition: '',
      companyName: '',
    });
  });

  it('should handle null textContent in selectors', () => {
    mockElement.innerHTML = `
      <div class="position"></div>
      <div class="headline"></div>
    `;

    const result = getPositionAndCompany({
      element: mockElement,
      positionSelector: '.position',
      headLineSelector: '.headline',
    });

    expect(result).toEqual({
      currentPosition: '',
      companyName: '',
    });
  });

  it('should use headline when position selector does not exist', () => {
    mockElement.innerHTML = `
      <div class="headline">Product Manager at Apple</div>
    `;

    const result = getPositionAndCompany({
      element: mockElement,
      positionSelector: '.position',
      headLineSelector: '.headline',
    });

    expect(result).toEqual({
      currentPosition: 'Product Manager',
      companyName: 'Apple',
    });
  });

  it('should handle basic headline cases in french', () => {
    mockElement.innerHTML = `
      <div class="headline">Sales Manager chez Hcorpo, Groupe Accor</div>
    `;

    const result = getPositionAndCompany({
      element: mockElement,
      positionSelector: '.position',
      headLineSelector: '.headline',
    });

    expect(result).toEqual({
      currentPosition: 'Sales Manager',
      companyName: 'Hcorpo, Groupe Accor',
    });
  });

  it('should handle complex headline cases in french', () => {
    mockElement.innerHTML = `
      <div class="headline">Account manager - Business Developer at YoungCapital</div>
    `;

    const result = getPositionAndCompany({
      element: mockElement,
      positionSelector: '.position',
      headLineSelector: '.headline',
    });

    expect(result).toEqual({
      currentPosition: 'Account manager - Business Developer',
      companyName: 'YoungCapital',
    });
  });
});

describe('extractCompanyNameFromSalesNavButton', () => {
  it('should return null for empty input', () => {
    const result = extractCompanyNameFromSalesNavButton('');
    expect(result).toBeNull();
  });

  it('should extract company name from English format', () => {
    const result = extractCompanyNameFromSalesNavButton('See more about Microsoft');
    expect(result).toBe('Microsoft');
  });

  it('should extract company name from French format', () => {
    const result = extractCompanyNameFromSalesNavButton('Voir plus sur Google');
    expect(result).toBe('Google');
  });

  it('should handle company names with spaces', () => {
    const result = extractCompanyNameFromSalesNavButton('See more about Acme Corporation');
    expect(result).toBe('Acme Corporation');
  });

  it('should handle company names with special characters', () => {
    const result = extractCompanyNameFromSalesNavButton('See more about Johnson & Johnson');
    expect(result).toBe('Johnson & Johnson');
  });

  it('should be case insensitive for English format', () => {
    const result = extractCompanyNameFromSalesNavButton('SEE MORE ABOUT Amazon');
    expect(result).toBe('Amazon');
  });

  it('should be case insensitive for French format', () => {
    const result = extractCompanyNameFromSalesNavButton('VOIR PLUS SUR Facebook');
    expect(result).toBe('Facebook');
  });

  it('should return null for non-matching formats', () => {
    const result = extractCompanyNameFromSalesNavButton('Learn more about Apple');
    expect(result).toBeNull();
  });
});
