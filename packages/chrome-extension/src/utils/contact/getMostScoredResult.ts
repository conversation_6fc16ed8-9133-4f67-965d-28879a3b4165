import type { ContactWithMatchingSourceAndScoring } from '@getheroes/shared';

export const getMostScoredResult = ({ matches }: { matches: Record<string, ContactWithMatchingSourceAndScoring> }) => {
  const results = Object.values(matches);
  if (results.length === 0) {
    return null;
  }

  // Filter out archived contacts
  const nonArchivedResults = results.filter(result => !result.contact.archived);
  if (nonArchivedResults.length === 0) {
    return null;
  }

  // Sort the results by score and return the most scored contact
  const mostScoredContact = nonArchivedResults.sort(
    (a, b) => parseFloat(b.scoring.score) - parseFloat(a.scoring.score),
  );
  return mostScoredContact[0].contact;
};
