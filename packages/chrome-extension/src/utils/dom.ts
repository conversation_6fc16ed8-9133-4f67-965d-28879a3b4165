import { getElementFromSelector } from './selectors';

// Instantiate and keep only one container for all apps
export const setContainerSingleton = (containerSelector: string) => {
  const containerDiv = getElementFromSelector(`#${containerSelector}`);
  if (containerDiv) {
    return containerDiv;
  }
  const container = document.createElement('div');
  container.id = containerSelector;
  document.body.append(container);
  return container;
};

export const injectStyles = (styleContent: string, id = 'zeliq-styles') => {
  let styleTag = getElementFromSelector<HTMLStyleElement>(`#${id}`);
  if (!styleTag) {
    styleTag = document.createElement('style');
    styleTag.id = id;
    document.head.appendChild(styleTag);
  }
  styleTag.textContent = styleContent;
};

export const getShadowDomContainer = () => {
  const zeliqRoot = getElementFromSelector('#zeliq-root');
  return getElementFromSelector('.zeliq', { element: zeliqRoot?.shadowRoot });
};
