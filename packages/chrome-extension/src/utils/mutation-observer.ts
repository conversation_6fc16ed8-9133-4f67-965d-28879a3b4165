import { upsertCurrentUrlLocation } from './chrome';
import { getElementFromSelector } from './selectors';

// Watch for SPA navigation
// It's necessary due to Linkedin SPA navigation
export const watchSPANavigation = () => {
  let lastUrl = location.href;
  const observer = new MutationObserver(() => {
    const currentUrl = location.href;
    if (currentUrl !== lastUrl) {
      lastUrl = currentUrl;
      upsertCurrentUrlLocation(currentUrl);
    }
  });
  observer.observe(document.documentElement, { subtree: true, childList: true });
};

export const waitForElements = (
  selectors: string[],
  targetNode: Node = document.body,
  timeout = 60000,
): Promise<boolean> => {
  if (!selectors || selectors.length === 0 || selectors.some(selector => !selector)) {
    return Promise.resolve(false);
  }

  // Check if the elements exist initially to avoid unnecessary waiting
  const elementsExistInitially = selectors.every(selector => getElementFromSelector(selector));
  if (elementsExistInitially) {
    return Promise.resolve(true);
  }

  return new Promise<boolean>((resolve, reject) => {
    const config = { childList: true, subtree: true };
    const elementsFound: Record<string, boolean> = {};
    let observer: MutationObserver | null = null;
    let timeoutId: NodeJS.Timeout | null = null;

    const cleanup = () => {
      observer?.disconnect();
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      document.removeEventListener('DOMContentLoaded', startObserving);
    };

    const mutationCallback = () => {
      for (const selector of selectors) {
        try {
          if (!elementsFound[selector] && getElementFromSelector(selector)) {
            elementsFound[selector] = true;
          }
        } catch (error) {
          console.error(`Error querying selector "${selector}":`, error);
          cleanup();
          reject(new Error(`Error querying selector "${selector}":`, { cause: error }));
        }
      }

      if (Object.keys(elementsFound).length === selectors.length) {
        cleanup();
        resolve(true);
      }
    };

    const startObserving = () => {
      observer = new MutationObserver(mutationCallback);
      observer.observe(targetNode, config);

      timeoutId = setTimeout(() => {
        cleanup();
        reject(new Error(`Timeout waiting for elements: ${selectors.join(', ')}`)); // More specific message
      }, timeout);
    };

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', startObserving);
    } else {
      startObserving();
    }
  });
};
