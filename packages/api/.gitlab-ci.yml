.api-changes: &api-changes
  - packages/api/*
  - packages/api/**/*
  - '.gitlab-ci.yml'
  - 'package.json'
  - 'tsconfig.base.json'
  - 'yarn.lock'
  - docker/api/Dockerfile

.api-migration-changes: &api-migration-changes
  - packages/api/src/migrations/*
  - packages/api/src/migrations/**/*

.api_rules: &rules_api
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
    when: never
  - if: $CI_PIPELINE_SOURCE == "pipeline"
    when: never
  # Do not trigger if we are creating a tag (from master)
  - if: '$CI_COMMIT_TAG != null'
    when: never
  # Do not trigger when merging on develop, release or master
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "master"'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    changes: *api-changes
    when: always
  - when: never

.api_rules_e2e:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
    when: never
  - if: $CI_PIPELINE_SOURCE == "pipeline"
    when: never
  # Do not trigger if we are creating a tag (from master)
  - if: '$CI_COMMIT_TAG != null'
    when: never
  # Do not trigger when merging on develop, release or master
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "master"'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    changes: *api-changes
    when: always
  - when: never

.api_rules_deploy:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    when: never
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_TAG != null'
    changes: *api-changes
    when: always
  - when: never

.api_migration_rules_deploy:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    when: never
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_TAG != null'
    changes: *api-migration-changes
    when: always
  - when: never

test:api-lint:
  stage: test
  needs: ['install']
  image: node:22.15.0-alpine3.21
  cache:
    - !reference [.cache_app_pull]
  rules:
    - !reference [.api_rules_e2e]
  script:
    - npx nx lint api
  interruptible: true

test:api:e2e:
  stage: test
  needs:
    - job: install
      artifacts: false
  image: node:22.15.0-alpine3.21
  cache:
    - !reference [.cache_app_pull]
  rules:
    - !reference [.api_rules_e2e]
  script:
    - nohup ./scripts/test-setup.sh &
    - sleep 10 && npx nx test api
  variables:
    REDIS_PORT: 6379 # weird port override
  services:
    - docker:stable-dind
    - redis:latest
    - name: postgres:15-alpine
      alias: db
      variables:
        POSTGRES_DB: $DB_NAME
        POSTGRES_USER: $DB_USER
        POSTGRES_PASSWORD: $DB_PASSWORD
        # POSTGRES_HOST_AUTH_METHOD: trust
    - name: 'docker.elastic.co/elasticsearch/elasticsearch:8.6.2'
      alias: 'elasticsearch'
      command:
        [
          'bin/elasticsearch',
          '-Expack.security.enabled=true',
          '-Ediscovery.type=single-node',
        ]
      variables:
        ELASTIC_USERNAME: elastic
        ELASTIC_PASSWORD: password
        ES_JAVA_OPTS: -Xms1g -Xmx1g
  interruptible: true

test:api:
  stage: test
  needs:
    - job: install
      artifacts: false
  image: node:22.15.0-alpine3.21
  cache:
    - !reference [.cache_app_pull]
    - !reference [.cache_coverage]
  rules:
    - !reference [.api_rules]
  script:
    - npx nx run api:test:production --codeCoverage -ci --skip-nx-cache
  coverage: '/All files[^\|]*\|[^\|]*\s+([\d\.]+)/'
  interruptible: true

build:api:
  stage: build
  needs:
    - job: install
    - job: test:api
      optional: true
    - job: test:api:e2e
      optional: true
    - job: test:api-lint
      optional: true
  image: node:22.15.0-alpine3.21
  rules:
    - !reference [.api_rules_deploy]
  cache:
    - !reference [.cache_app_pull]
  before_script:
    - apk add docker jq aws-cli
  services:
    - docker:stable-dind
  script:
    - VERSION=$(jq -r .version package.json)
    - npx nx build api --configuration=production
    - !reference [.docker-build]
  variables:
    DOCKERFILE: docker/api/Dockerfile
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ''
    IMAGE_NAME: $DASHBOARD_API_IMAGE_NAME
    TARGET: 'prod-stage'
    PUSH_LATEST_TAG: 1
  environment:
    action: prepare
    name: $ENVIRONMENT/dashboard-api
  resource_group: zeliq-$ENVIRONMENT

migration:
  stage: deploy
  needs: ['install', 'build:cli']
  rules:
    - !reference [.api_migration_rules_deploy]
  image:
    name: amazon/aws-cli
    entrypoint: ['']
  before_script:
    - yum install -y jq
  script:
    #- echo $AWS_ACCESS_KEY_ID
    - ./scripts/migrate-ecs $ENVIRONMENT
  resource_group: zeliq-$ENVIRONMENT
  environment:
    action: prepare
    name: $ENVIRONMENT/dashboard-cli
  timeout: 30m

deploy:api:
  needs:
    - job: install
    - job: build:api
    - job: migration
      optional: true
  stage: deploy
  rules:
    - !reference [.api_rules_deploy]
  image:
    name: amazon/aws-cli
    entrypoint: ['']
  before_script:
    - yum install -y jq
  script:
    - ./scripts/update-ecs
  variables:
    CLUSTER_NAME: $DASHBOARD_CLUSTER_NAME
    SERVICE_NAME: $DASHBOARD_API_SERVICE_NAME
    IMAGE_NAME: $DASHBOARD_API_IMAGE_NAME
    TASK_DEFINITION_NAME: $DASHBOARD_API_TASK_DEFINITION_NAME
  environment:
    name: $ENVIRONMENT/dashboard-api
    url: $DASHBOARD_API_URL
    deployment_tier: other
  resource_group: zeliq-$ENVIRONMENT
  timeout: 10m

deploy:cron:
  needs:
    - job: install
    - job: build:api
    - job: migration
      optional: true
  stage: deploy
  rules:
    - !reference [.api_rules_deploy]
  image:
    name: amazon/aws-cli
    entrypoint: ['']
  before_script:
    - yum install -y jq
  script:
    - ./scripts/update-ecs
  variables:
    CLUSTER_NAME: $DASHBOARD_CLUSTER_NAME
    SERVICE_NAME: $DASHBOARD_CRON_SERVICE_NAME
    IMAGE_NAME: $DASHBOARD_API_IMAGE_NAME
    TASK_DEFINITION_NAME: $DASHBOARD_CRON_TASK_DEFINITION_NAME
  environment:
    name: $ENVIRONMENT/dashboard-cron
    deployment_tier: other
  resource_group: zeliq-$ENVIRONMENT
  timeout: 10m

deploy:consumer:
  needs:
    - job: install
    - job: build:api
    - job: migration
      optional: true
  stage: deploy
  rules:
    #No consumer service on staging
    - if: '$ENVIRONMENT == "staging"'
      when: never
    - !reference [.api_rules_deploy]
  image:
    name: amazon/aws-cli
    entrypoint: ['']
  before_script:
    - yum install -y jq
  script:
    - ./scripts/update-ecs
  variables:
    CLUSTER_NAME: $DASHBOARD_CLUSTER_NAME
    SERVICE_NAME: $DASHBOARD_CONSUMER_SERVICE_NAME
    IMAGE_NAME: $DASHBOARD_API_IMAGE_NAME
    TASK_DEFINITION_NAME: $DASHBOARD_CONSUMER_TASK_DEFINITION_NAME
  environment:
    name: $ENVIRONMENT/dashboard-consumer
    deployment_tier: other
  resource_group: zeliq-$ENVIRONMENT
  timeout: 10m
