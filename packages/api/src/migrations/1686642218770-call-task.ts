import { MigrationInterface, QueryRunner } from 'typeorm'

export class CallTask1686642218770 implements MigrationInterface {
  name = 'CallTask1686642218770'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "calls" ADD "task_id" uuid`)
    await queryRunner.query(
      `ALTER TABLE "calls" ADD CONSTRAINT "FK_e365c34469956c46994f9a52a37" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    )
    await queryRunner.query(
      `ALTER TABLE "gmail_messages" ADD "direction" character varying DEFAULT 'OUTBOUND'`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "calls" DROP CONSTRAINT "FK_e365c34469956c46994f9a52a37"`
    )
    await queryRunner.query(`ALTER TABLE "calls" DROP COLUMN "task_id"`)
    await queryRunner.query(
      `ALTER TABLE "gmail_messages" DROP COLUMN "direction"`
    )
  }
}
