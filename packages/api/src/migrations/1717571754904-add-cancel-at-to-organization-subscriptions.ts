import { MigrationInterface, QueryRunner } from 'typeorm'

export class CancelAtToOrganizationSubscriptions1717571754904
  implements MigrationInterface
{
  name = 'CancelAtToOrganizationSubscriptions1717571754904'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "organization_subscriptions" ADD "cancel_at" TIMESTAMP WITH TIME ZONE`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "organization_subscriptions" DROP COLUMN "cancel_at"`
    )
  }
}
