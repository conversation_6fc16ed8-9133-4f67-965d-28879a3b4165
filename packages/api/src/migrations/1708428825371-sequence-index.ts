import { MigrationInterface, QueryRunner } from 'typeorm'

export class SequenceIndex1708428825371 implements MigrationInterface {
  name = 'SequenceIndex1708428825371'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX "IDX_9f7fd589d6b6271c353a243893" ON "sequences" ("enable") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_0b9842ca7f709398ee1bb5ec3c" ON "sequences" ("status") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_64a3fbc5d8774c70cc962e6860" ON "sequences" ("next_execution_date") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_e1ea64b7fae703a950bdb9bab1" ON "sequences_step" ("order") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_cdf686e66910e4868dc320bbdb" ON "sequences_contact" ("status") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_0022dbb04c835e2e8bc6ff36da" ON "sequences_contact" ("sequence_created_at") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_2ac6b73dcfcb8fa2856e3c35f6" ON "sequences_contact" ("next_step_id") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_838b8eae80af3f9cb246a037d7" ON "sequences_contact" ("next_step_type") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_938feae64cf5849f754ba2f21e" ON "async_actions" ("created_by_id") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_ec0922effa0814b1eaa53a474e" ON "async_actions" ("internal_relation_id") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_098b82bd39db5d019fd74258cf" ON "messages" ("resource_thread_id") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_86c67fabfda07da22699b87bdc" ON "messages" ("resource_id") `
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_86c67fabfda07da22699b87bdc"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_098b82bd39db5d019fd74258cf"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ec0922effa0814b1eaa53a474e"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_938feae64cf5849f754ba2f21e"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_838b8eae80af3f9cb246a037d7"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2ac6b73dcfcb8fa2856e3c35f6"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0022dbb04c835e2e8bc6ff36da"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_cdf686e66910e4868dc320bbdb"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e1ea64b7fae703a950bdb9bab1"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_64a3fbc5d8774c70cc962e6860"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0b9842ca7f709398ee1bb5ec3c"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9f7fd589d6b6271c353a243893"`
    )
  }
}
