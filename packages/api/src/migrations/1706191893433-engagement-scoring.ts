import { MigrationInterface, QueryRunner } from 'typeorm'

export class EngagementScoring1706191893433 implements MigrationInterface {
  name = 'EngagementScoring1706191893433'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contacts" ADD "engagement_scoring_metadata" jsonb`
    )
    await queryRunner.query(
      `ALTER TABLE "contacts" ADD "engagement_scoring_priority" character varying`
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_9edf447eae2f1ffdf15db6f3b4" ON "contacts" ("engagement_scoring_priority") `
    )
    await queryRunner.query(
      `ALTER TABLE "contacts" ADD "assignment_date" TIMESTAMP WITH TIME ZONE`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contacts" DROP COLUMN "engagement_scoring_metadata"`
    )
    await queryRunner.query(
      `ALTER TABLE "contacts" DROP COLUMN "engagement_scoring_priority"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9edf447eae2f1ffdf15db6f3b4"`
    )
    await queryRunner.query(
      `ALTER TABLE "contacts" DROP COLUMN "assignment_date"`
    )
  }
}
