import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddW<PERSON>elistedEmails1728994565530 implements MigrationInterface {
  name = 'AddWhitelistedEmails1728994565530'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the whitelisted_emails table
    await queryRunner.query(
      `CREATE TABLE "whitelisted_emails" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "email" character varying NOT NULL, CONSTRAINT "PK_whitelisted_emails" PRIMARY KEY ("id"))`
    )

    // Add a unique constraint on the email column
    await queryRunner.query(
      `ALTER TABLE "whitelisted_emails" ADD CONSTRAINT "UQ_whitelisted_emails_email" UNIQUE ("email")`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "whitelisted_emails"`)
  }
}
