import { MigrationInterface, QueryRunner } from 'typeorm'

export class CaptainDataJobsIndex1730716789688 implements MigrationInterface {
  name = 'CaptainDataJobsIndex1730716789688'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX "IDX_captainDataJobs_sequenceContactId_status" ON "captain_data_jobs" ("sequence_contact_id", "status") `
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_captainDataJobs_createdById_status" ON "captain_data_jobs" ("created_by_id", "status") `
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_captainDataJobs_createdById_status"`
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_captainDataJobs_sequenceContactId_status"`
    )
  }
}
