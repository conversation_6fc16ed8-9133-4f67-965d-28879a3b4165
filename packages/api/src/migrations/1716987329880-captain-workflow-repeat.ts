import { MigrationInterface, QueryRunner } from 'typeorm'

export class CaptainDataWorkflowRepeat1716987329880
  implements MigrationInterface
{
  name = 'CaptainDataWorkflowRepeat1716987329880'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "captain_data_workflows" ADD "repeat" jsonb`
    )
    await queryRunner.query(
      `ALTER TABLE "captain_data_jobs" ALTER COLUMN "sequence_step_id" DROP NOT NULL`
    )
    await queryRunner.query(
      `ALTER TABLE "captain_data_jobs" ALTER COLUMN "sequence_contact_id" DROP NOT NULL`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "captain_data_workflows" DROP COLUMN "repeat"`
    )
    await queryRunner.query(
      `ALTER TABLE "captain_data_jobs" ALTER COLUMN "sequence_contact_id" SET NOT NULL`
    )
    await queryRunner.query(
      `ALTER TABLE "captain_data_jobs" ALTER COLUMN "sequence_step_id" SET NOT NULL`
    )
  }
}
