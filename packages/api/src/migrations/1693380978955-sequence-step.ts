import { MigrationInterface, QueryRunner } from "typeorm";

export class SequenceStep1693380978955 implements MigrationInterface {
  name = 'SequenceStep1693380978955'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "sequences_step" DROP COLUMN "order"`);
    await queryRunner.query(`ALTER TABLE "sequences_step" ADD "order" integer NOT NULL DEFAULT '1'`);
    await queryRunner.query(`COMMENT ON COLUMN "sequences_step"."waiting_between_step" IS 'in days'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`COMMENT ON COLUMN "sequences_step"."waiting_between_step" IS NULL`);
    await queryRunner.query(`ALTER TABLE "sequences_step" DROP COLUMN "order"`);
    await queryRunner.query(`ALTER TABLE "sequences_step" ADD "order" character varying NOT NULL DEFAULT '1'`);
  }
}
