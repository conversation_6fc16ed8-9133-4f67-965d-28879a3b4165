import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'
import { TransferAssignTaskQuery } from './transfer-assign-task.query'
import {
  TASK_REPOSITORY_INTERFACE,
  TaskRepositoryInterface,
} from '../../../../domain/task/model/task-repository.interface'

@QueryHandler(TransferAssignTaskQuery)
export class TransferAssignTaskHandler
  implements IQueryHandler<TransferAssignTaskQuery>
{
  constructor(
    @Inject(TASK_REPOSITORY_INTERFACE)
    private taskRepository: TaskRepositoryInterface
  ) {}

  execute(query: TransferAssignTaskQuery): Promise<void> {
    this.taskRepository.transferAssignTask(
      query.contactId,
      query.oldAssignId,
      query.newAssignId
    )

    return
  }
}
