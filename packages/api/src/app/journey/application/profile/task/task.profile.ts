import {
  createMap,
  for<PERSON><PERSON><PERSON>,
  map<PERSON>rom,
  Mapper,
  MappingProfile,
} from '@automapper/core'
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs'
import { Injectable } from '@nestjs/common'
import { CreateTaskDto } from '../../../../../ui/api/journey/dto/task/create-task.dto'
import { TaskModel } from '../../../domain/task/model/task.model'
import { TaskEntity } from '../../../infrastructure/entity/task/task.entity'
import { TaskOutputDto } from '../../../../../ui/api/journey/dto/task/task.output.dto'

@Injectable()
export class TaskProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper)
  }

  get profile(): MappingProfile {
    return mapper => {
      createMap(mapper, CreateTaskDto, TaskModel)
      createMap(mapper, TaskModel, TaskEntity)
      createMap(mapper, TaskEntity, TaskModel)
      createMap(
        mapper,
        TaskModel,
        TaskOutputDto,
        forMember(
          destination => destination.sequence,
          mapFrom(source => {
            if (!source.sequenceContact) return null
            return {
              id: source.sequenceContact.sequence.id,
              name: source.sequenceContact.sequence.name,
              version: source.sequenceContact.sequence.version,
            }
          })
        )
      )
    }
  }
}
