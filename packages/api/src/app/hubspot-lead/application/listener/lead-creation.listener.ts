import { OnEvent } from '@nestjs/event-emitter'
import { LeadEvents } from '../../../shared/domain/event/event.enum'
import { Injectable } from '@nestjs/common'
import { HubspotPushService } from '../service/push/push.service'
import { ContactCreatedEvent } from '../../../shared/domain/event/lead/contact-created.event'
import { CompanyCreatedEvent } from '../../../shared/domain/event/lead/company-created-event'
import { CompaniesCreatedEvent } from '../../../shared/domain/event/lead/companies-created-event'

@Injectable()
export class LeadCreationListener {
  constructor(private readonly pushService: HubspotPushService) {}

  @OnEvent(LeadEvents.CONTACT_CREATED)
  async handleContactCreation(event: ContactCreatedEvent) {
    if (event.contactsIds.length > 1) {
      await this.pushService.pushNewContacts(event.contactsIds)
      return
    }

    await this.pushService.pushNewContact(event.contactsIds[0])
  }

  @OnEvent(LeadEvents.COMPANY_CREATED)
  async handleCompanyCreation(event: CompanyCreatedEvent) {
    await this.pushService.pushNewCompany(event.companyId)
  }

  @OnEvent(LeadEvents.COMPANIES_CREATED)
  async handleCompaniesCreation(event: CompaniesCreatedEvent) {
    await this.pushService.pushNewCompanies(event.companyIds)
  }
}
