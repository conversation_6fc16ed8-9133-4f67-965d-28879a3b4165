import { createMap, Mapper, MappingProfile } from '@automapper/core'
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs'
import { Injectable } from '@nestjs/common'
import { CreateSnippetDto } from '../../../../ui/api/snippet/dto/create-snippet.dto'
import { SnippetModel } from '../../domain/model/snippet.model'
import { SnippetEntity } from '../../infrastructure/entity/snippet.entity'

@Injectable()
export class SnippetProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper)
  }

  get profile(): MappingProfile {
    return mapper => {
      createMap(mapper, CreateSnippetDto, SnippetModel)
      createMap(mapper, SnippetModel, SnippetEntity)
      createMap(mapper, SnippetEntity, SnippetModel)
    }
  }
}
