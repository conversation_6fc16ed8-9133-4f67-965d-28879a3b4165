import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs'
import { DisconnectCommand } from './disconnect.command'
import { WebSocketService } from '../../../../shared/infrastructure/service/websocket.service'

@CommandHandler(DisconnectCommand)
export class DisconnectHandler implements ICommandHandler<DisconnectCommand> {
  constructor(private websocketService: WebSocketService) {}
  async execute(command: DisconnectCommand): Promise<void> {
    return this.websocketService.disconnect(command.connectionId)
  }
}
