import { createMap, Mapper, MappingProfile } from '@automapper/core'
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs'
import { Injectable } from '@nestjs/common'
import { CreateTemplateDto } from '../../../../ui/api/template/dto/create-template.dto'
import { TemplateModel } from '../../domain/model/template.model'
import { TemplateEntity } from '../../infrastructure/entity/template.entity'
import { QueryFilterTemplateDto } from '../../../../ui/api/template/dto/query-filter-template.dto'
import { QueryFilterTemplateObject } from '../../domain/object/query-filter-template.object'
import { UpdateTemplateDto } from '../../../../ui/api/template/dto/update-template.dto'

@Injectable()
export class TemplateProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper)
  }

  get profile(): MappingProfile {
    return mapper => {
      createMap(mapper, QueryFilterTemplateDto, QueryFilterTemplateObject)
      createMap(mapper, UpdateTemplateDto, TemplateModel)
      createMap(mapper, CreateTemplateDto, TemplateModel)
      createMap(mapper, TemplateModel, TemplateEntity)
      createMap(mapper, TemplateEntity, TemplateModel)
    }
  }
}
