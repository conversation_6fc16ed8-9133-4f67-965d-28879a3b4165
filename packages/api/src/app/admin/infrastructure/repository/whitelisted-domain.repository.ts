import { Mapper } from '@automapper/core'
import { InjectMapper } from '@automapper/nestjs'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { FindManyOptions } from 'typeorm/find-options/FindManyOptions'
import { WhitelistedDomainEntity } from '../entity/whitelisted-domain.entity'
import { WhitelistedDomainRepositoryInterface } from '../../domain/interface/whitelisted-domain-repository.interface'
import { WhitelistedDomainModel } from '../../domain/model/whitelisted-domain.model'

@Injectable()
export class WhitelistedDomainRepository
  implements WhitelistedDomainRepositoryInterface
{
  constructor(
    @InjectRepository(WhitelistedDomainEntity)
    private readonly whitelistedDomainEntityRepository: Repository<WhitelistedDomainEntity>,
    @InjectMapper() private readonly mapper: Mapper
  ) {}

  async findAll(
    options?: FindManyOptions<WhitelistedDomainEntity>
  ): Promise<WhitelistedDomainModel[]> {
    const whitelistedDomainEntities =
      await this.whitelistedDomainEntityRepository.find(options)

    return this.mapper.mapArray(
      whitelistedDomainEntities,
      WhitelistedDomainEntity,
      WhitelistedDomainModel
    )
  }

  async findByDomain(domain: string): Promise<WhitelistedDomainModel | null> {
    const whitelistedDomainEntity =
      await this.whitelistedDomainEntityRepository.findOne({
        where: { domain },
      })

    return whitelistedDomainEntity
      ? this.mapper.map(
          whitelistedDomainEntity,
          WhitelistedDomainEntity,
          WhitelistedDomainModel
        )
      : null
  }

  async add(domain: string): Promise<void> {
    const whitelistedDomainEntity = new WhitelistedDomainEntity()
    whitelistedDomainEntity.domain = domain
    await this.whitelistedDomainEntityRepository.save(whitelistedDomainEntity)
  }

  async remove(domain: string): Promise<void> {
    const whitelistedDomainEntity =
      await this.whitelistedDomainEntityRepository.findOne({
        where: { domain },
      })
    if (whitelistedDomainEntity) {
      await this.whitelistedDomainEntityRepository.remove(
        whitelistedDomainEntity
      )
    }
  }
}
