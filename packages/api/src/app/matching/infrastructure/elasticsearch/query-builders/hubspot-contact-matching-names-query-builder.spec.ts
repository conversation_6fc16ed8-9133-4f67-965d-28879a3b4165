import { QueryContainer } from '@elastic/elasticsearch/api/types'
import { HubspotContactMatchingNamesQueryBuilder } from './hubspot-contact-matching-names-query-builder'
import { ContactMatchingInputModel } from '@matching/domain/model/contact-matching-input.model'
import { v4 } from 'uuid'

describe('Matching Infrastructure - Hubspot contact matching names query builder', () => {
  describe('GIVEN A List of contact matching inputs is provided', () => {
    describe('WHEN Provided list is empty', () => {
      it('THEN A query filtered on existing firstName + lastName + companyName with an empty should condition should be returned', () => {
        const actual = HubspotContactMatchingNamesQueryBuilder.create()
          .withInputs([])
          .build()

        expect(actual).toEqual<QueryContainer>({
          bool: {
            minimum_should_match: 1,
            filter: [
              { exists: { field: 'firstName' } },
              { exists: { field: 'lastName' } },
              { exists: { field: 'companyName' } },
            ],
            should: [],
          },
        })
      })
    })

    describe('WHEN Provided list contains inputs with firstName, lastName and CompanyName', () => {
      it(`THEN Resulting Query will search for contacts in my organization WHERE the 3 fields exist,
          matching exactly firstname + lastName + company.name among provided triplets (firstName,lastName,companyName)
          AND each term query in the exact match is uniquely identified by the input matching id`, () => {
        const matchingInput = ContactMatchingInputModel.builder()
          .withMatchingId(v4())
          .withOrganizationId(v4())
          .withCompanyName('TEST COMPANY')
          .withIdentity('JANE', 'DOE')
          .build()

        const actual: QueryContainer =
          HubspotContactMatchingNamesQueryBuilder.create()
            .withInputs([matchingInput])
            .withOrganizatonFilter(matchingInput.organizationId)
            .build()

        expect(actual).toEqual<QueryContainer>({
          bool: {
            minimum_should_match: 1,
            filter: [
              { exists: { field: 'firstName' } },
              { exists: { field: 'lastName' } },
              { exists: { field: 'companyName' } },
              { term: { organizationId: matchingInput.organizationId } },
            ],
            should: [
              {
                bool: {
                  must: [
                    {
                      match: {
                        firstName: {
                          query: matchingInput.firstName,
                          _name: matchingInput.matchingId,
                        },
                      },
                    },
                    {
                      match: {
                        lastName: {
                          query: matchingInput.lastName,
                          _name: matchingInput.matchingId,
                        },
                      },
                    },
                    {
                      match: {
                        companyName: {
                          query: matchingInput.companyName,
                          _name: matchingInput.matchingId,
                        },
                      },
                    },
                  ],
                },
              },
            ],
          },
        })
      })
    })
  })
})
