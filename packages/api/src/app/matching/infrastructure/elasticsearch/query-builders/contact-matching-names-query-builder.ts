import { QueryContainer } from '@elastic/elasticsearch/api/types'
import { CriteriaType } from '@matching/domain/value-objects/criteria'
import { ContactMatchingQueryBuilder } from './contact-matching-query-builder'

export class ContactMatchingNamesQueryBuilder extends ContactMatchingQueryBuilder {
  private static readonly filter = [
    { exists: { field: 'firstName' } },
    { exists: { field: 'lastName' } },
    { exists: { field: 'company' } },
  ]

  private constructor() {
    super(
      CriteriaType.FIRSTNAME_AND_LASTNAME_AND_COMPANY_NAME,
      ContactMatchingNamesQueryBuilder.filter
    )
  }

  public build(): QueryContainer {
    this.initialize()

    const should = this.current.bool?.should as QueryContainer[]

    for (const input of this.inputs) {
      should.push({
        bool: {
          must: [
            {
              match: {
                firstName: {
                  query: input.firstName,
                  _name: input.matchingId,
                },
              },
            },
            {
              match: {
                lastName: {
                  query: input.lastName,
                  _name: input.matchingId,
                },
              },
            },
            {
              match: {
                'company.name': {
                  query: input.companyName,
                  _name: input.matchingId,
                },
              },
            },
          ],
        },
      })
    }

    return this.current
  }

  public static create() {
    return new ContactMatchingNamesQueryBuilder()
  }
}
