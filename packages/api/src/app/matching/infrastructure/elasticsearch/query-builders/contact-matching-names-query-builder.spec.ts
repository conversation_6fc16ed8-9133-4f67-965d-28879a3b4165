import { QueryContainer } from '@elastic/elasticsearch/api/types'
import { v4 } from 'uuid'
import { ContactMatchingInputModel } from '../../../domain/model/contact-matching-input.model'
import { ContactMatchingNamesQueryBuilder } from './contact-matching-names-query-builder'
describe('Matching Infrastructure - Contact matching names query builder tests', () => {
  describe('GIVEN A List of contact matching inputs is provided', () => {
    describe('WHEN Provided list is empty', () => {
      it('THEN A query filtered on existing firstName + lastName + company with an empty should condition should be returned', () => {
        const actual = ContactMatchingNamesQueryBuilder.create()
          .withInputs([])
          .build()

        expect(actual).toEqual<QueryContainer>({
          bool: {
            minimum_should_match: 1,
            filter: [
              { exists: { field: 'firstName' } },
              { exists: { field: 'lastName' } },
              { exists: { field: 'company' } },
            ],
            should: [],
          },
        })
      })
    })

    describe('WHEN Provided list contains inputs with firstName, lastName and CompanyName', () => {
      it(`THEN Resulting Query will search for contacts WHERE the 3 fields exist,
          matching exactly firstname + lastName + company.name among provided triplets (firstName,lastName,companyName)
          AND each term query in the exact match is uniquely identified by the input matching id`, () => {
        const matchingInput = ContactMatchingInputModel.builder()
          .withMatchingId(v4())
          .withCompanyName('TEST COMPANY')
          .withIdentity('JANE', 'DOE')
          .build()

        const actual: QueryContainer = ContactMatchingNamesQueryBuilder.create()
          .withInputs([matchingInput])
          .build()

        expect(actual).toEqual<QueryContainer>({
          bool: {
            minimum_should_match: 1,
            filter: [
              { exists: { field: 'firstName' } },
              { exists: { field: 'lastName' } },
              { exists: { field: 'company' } },
            ],
            should: [
              {
                bool: {
                  must: [
                    {
                      match: {
                        firstName: {
                          query: matchingInput.firstName,
                          _name: matchingInput.matchingId,
                        },
                      },
                    },
                    {
                      match: {
                        lastName: {
                          query: matchingInput.lastName,
                          _name: matchingInput.matchingId,
                        },
                      },
                    },
                    {
                      match: {
                        'company.name': {
                          query: matchingInput.companyName,
                          _name: matchingInput.matchingId,
                        },
                      },
                    },
                  ],
                },
              },
            ],
          },
        })
      })
    })
  })
})
