import { QueryContainer } from '@elastic/elasticsearch/api/types'
import { ContactMatchingQueryBuilder } from './contact-matching-query-builder'
import { CriteriaType } from '@matching/domain/value-objects/criteria'

export class HubspotContactMatchingNamesQueryBuilder extends ContactMatchingQueryBuilder {
  private static readonly filter: QueryContainer[] = [
    { exists: { field: 'firstName' } },
    { exists: { field: 'lastName' } },
    { exists: { field: 'companyName' } },
  ]

  private constructor() {
    super(
      CriteriaType.FIRSTNAME_AND_LASTNAME_AND_COMPANY_NAME,
      HubspotContactMatchingNamesQueryBuilder.filter
    )
  }

  public build(): QueryContainer {
    this.initialize()

    const should = this.current.bool?.should as QueryContainer[]

    for (const input of this.inputs) {
      should.push({
        bool: {
          must: [
            {
              match: {
                firstName: {
                  query: input.firstName,
                  _name: input.matchingId,
                },
              },
            },
            {
              match: {
                lastName: {
                  query: input.lastName,
                  _name: input.matchingId,
                },
              },
            },
            {
              match: {
                companyName: {
                  query: input.companyName,
                  _name: input.matchingId,
                },
              },
            },
          ],
        },
      })
    }

    return this.current
  }

  public static create() {
    return new HubspotContactMatchingNamesQueryBuilder()
  }
}
