import { LeadCategory, LeadSource } from '@getheroes/shared'
import { MatchingSource } from '@matching/domain/model/matching-source.enum'
import { Injectable } from '@nestjs/common'
import { ElasticsearchService } from '@nestjs/elasticsearch'
import { CompanyModel } from '../../../lead/domain/model/company.model'
import { LogFeature, LoggerService } from '../../../shared/logger.service'
import CompanyMatchingStrategy from '../../domain/interface/company-matching-strategy'
import CompanyMatchingInputModel from '../../domain/model/company-matching-input.model'
import ScorableCompanyModel from '../../domain/model/scorable-company.model'
import { Scoring } from '../../domain/model/scoring'
import { CriteriaType } from '../../domain/value-objects/criteria'
import { CriteriaResultType } from '../../domain/value-objects/criteria-result'

@Injectable()
export default class CompanyESMatchingStrategy
  implements CompanyMatchingStrategy
{
  private static readonly ES_NAME_FIELD = 'name'
  private static readonly ES_DOMAIN_FIELD = 'domain'
  private static readonly ES_LINKEDIN_URL_FIELD = 'linkedinUrl'

  private readonly logger = new LoggerService({
    context: CompanyESMatchingStrategy.name,
    feature: LogFeature.MATCHING_SERVICE,
  })

  constructor(private readonly elasticSearchService: ElasticsearchService) {}

  async findMatches(
    companyInputs: CompanyMatchingInputModel[],
    organizationId: string
  ): Promise<WeakMap<CompanyMatchingInputModel, ScorableCompanyModel[]>> {
    const indexName = 'companies_' + organizationId

    const results = new WeakMap<
      CompanyMatchingInputModel,
      ScorableCompanyModel[]
    >()

    companyInputs.forEach(input => {
      results.set(input, [])
    })

    const { body: indexExists } =
      await this.elasticSearchService.indices.exists({
        index: indexName,
      })

    if (!indexExists) {
      return results
    }

    const should = companyInputs
      .flatMap(this.buildEsQuery.bind(this))
      .filter(Boolean)

    const query = {
      bool: {
        should,
        minimum_should_match: 1,
      },
    }

    const response = await this.elasticSearchService.search({
      index: indexName,
      body: {
        query,
      },
      size: 10000,
    })

    if (response.body?.hits?.hits) {
      this.logger.log({
        message: `Found ${response.body.hits.hits.length} hits`,
      })

      for (const input of companyInputs) {
        const matchesForInput: ScorableCompanyModel[] = []

        for (const hit of response.body.hits.hits) {
          const matchedQueries = hit.matched_queries || []
          if (matchedQueries.length === 0) continue
          if (!this.hasMatchForInput(input, matchedQueries)) continue

          const { scoring, matchedOnFields } =
            this.computeScoringFromMatchedQueries(input, matchedQueries)

          if (matchedOnFields.length > 0) {
            const company = CompanyModel.fromPlain({
              ...hit._source,
            })
            company.source = LeadSource.SEARCH
            const scorableCompany = ScorableCompanyModel.builder()
              .withCompany(company)
              .withMatchedFrom(MatchingSource.ALL_LEADS)
              .withMatchedOn(matchedOnFields)
              .withScoring(scoring)
              .build()

            matchesForInput.push(scorableCompany)
          }
        }
        results.set(input, matchesForInput)
      }
    }

    return results
  }

  private hasMatchForInput(
    input: CompanyMatchingInputModel,
    machedQueries: string[]
  ) {
    return (
      machedQueries.includes(
        `${CompanyESMatchingStrategy.ES_NAME_FIELD}_${input.getMatchingId()}`
      ) ||
      machedQueries.includes(
        `${CompanyESMatchingStrategy.ES_DOMAIN_FIELD}_${input.getMatchingId()}`
      ) ||
      machedQueries.includes(
        `${CompanyESMatchingStrategy.ES_LINKEDIN_URL_FIELD}_${input.getMatchingId()}`
      ) ||
      machedQueries.includes(
        `${CompanyESMatchingStrategy.ES_LINKEDIN_URL_FIELD}_${input.getMatchingId()}_no_slash`
      )
    )
  }

  private computeScoringFromMatchedQueries(
    input: CompanyMatchingInputModel,
    matchedQueries: string[]
  ) {
    let scoring = Scoring.initializeFor(LeadCategory.COMPANY)
    const matchedOnFields: Array<'linkedinUrl' | 'domain' | 'name'> = []

    if (!input.getLinkedinUrl()) {
      scoring = scoring.updateCriteriaResult(
        CriteriaType.LINKEDIN,
        CriteriaResultType.EMPTY
      )
    } else if (
      matchedQueries.includes(
        `${CompanyESMatchingStrategy.ES_LINKEDIN_URL_FIELD}_${input.getMatchingId()}`
      ) ||
      matchedQueries.includes(
        `${CompanyESMatchingStrategy.ES_LINKEDIN_URL_FIELD}_${input.getMatchingId()}_no_slash`
      )
    ) {
      scoring = scoring.updateCriteriaResult(
        CriteriaType.LINKEDIN,
        CriteriaResultType.MATCH
      )
      matchedOnFields.push(CompanyESMatchingStrategy.ES_LINKEDIN_URL_FIELD)
    } else {
      scoring = scoring.updateCriteriaResult(
        CriteriaType.LINKEDIN,
        CriteriaResultType.NO_MATCH
      )
    }

    if (!input.getDomain()) {
      scoring = scoring.updateCriteriaResult(
        CriteriaType.DOMAIN,
        CriteriaResultType.EMPTY
      )
    } else if (
      matchedQueries.includes(
        `${CompanyESMatchingStrategy.ES_DOMAIN_FIELD}_${input.getMatchingId()}`
      )
    ) {
      scoring = scoring.updateCriteriaResult(
        CriteriaType.DOMAIN,
        CriteriaResultType.MATCH
      )
      matchedOnFields.push(CompanyESMatchingStrategy.ES_DOMAIN_FIELD)
    } else {
      scoring = scoring.updateCriteriaResult(
        CriteriaType.DOMAIN,
        CriteriaResultType.NO_MATCH
      )
    }

    if (!input.getName()) {
      scoring = scoring.updateCriteriaResult(
        CriteriaType.NAME,
        CriteriaResultType.EMPTY
      )
    } else if (
      matchedQueries.includes(
        `${CompanyESMatchingStrategy.ES_NAME_FIELD}_${input.getMatchingId()}`
      )
    ) {
      scoring = scoring.updateCriteriaResult(
        CriteriaType.NAME,
        CriteriaResultType.MATCH
      )
      matchedOnFields.push(CompanyESMatchingStrategy.ES_NAME_FIELD)
    } else {
      scoring = scoring.updateCriteriaResult(
        CriteriaType.NAME,
        CriteriaResultType.NO_MATCH
      )
    }

    return {
      scoring,
      matchedOnFields,
    }
  }

  private buildLinkedinUrlTerm(
    companyMatchingInput: CompanyMatchingInputModel
  ) {
    if (!companyMatchingInput.getLinkedinUrl()) return null
    const url = companyMatchingInput.getLinkedinUrl()
    const urlWithoutSlash = url.endsWith('/') ? url.slice(0, -1) : url
    const urlWithSlash = url.endsWith('/') ? url : `${url}/`
    const queryName =
      CompanyESMatchingStrategy.ES_LINKEDIN_URL_FIELD +
      '_' +
      companyMatchingInput.getMatchingId()

    return {
      bool: {
        should: [
          {
            term: {
              [`${CompanyESMatchingStrategy.ES_LINKEDIN_URL_FIELD}.keyword`]: {
                value: urlWithoutSlash,
                _name: queryName + '_no_slash',
              },
            },
          },
          {
            term: {
              [`${CompanyESMatchingStrategy.ES_LINKEDIN_URL_FIELD}.keyword`]: {
                value: urlWithSlash,
                _name: queryName,
              },
            },
          },
        ],
        minimum_should_match: 1,
      },
    }
  }

  private buildNameMatch(companyMatchingInput: CompanyMatchingInputModel) {
    if (!companyMatchingInput.getName()) return null
    return {
      bool: {
        must: [
          {
            match: {
              [CompanyESMatchingStrategy.ES_NAME_FIELD]: {
                query: companyMatchingInput.getName(),
                boost: 2,
              },
            },
          },
        ],
        _name:
          CompanyESMatchingStrategy.ES_NAME_FIELD +
          '_' +
          companyMatchingInput.getMatchingId(),
      },
    }
  }

  private buildDomainTerm(companyMatchingInput: CompanyMatchingInputModel) {
    if (!companyMatchingInput.getDomain()) return null
    return {
      term: {
        [CompanyESMatchingStrategy.ES_DOMAIN_FIELD + '.keyword']: {
          value: companyMatchingInput.getDomain(),
          _name:
            CompanyESMatchingStrategy.ES_DOMAIN_FIELD +
            '_' +
            companyMatchingInput.getMatchingId(),
        },
      },
    }
  }

  private buildEsQuery(companyMatchingInput: CompanyMatchingInputModel) {
    return [
      this.buildLinkedinUrlTerm(companyMatchingInput),
      this.buildNameMatch(companyMatchingInput),
      this.buildDomainTerm(companyMatchingInput),
    ]
  }
}
