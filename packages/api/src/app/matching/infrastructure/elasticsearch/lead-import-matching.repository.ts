import { SearchResponse } from '@elastic/elasticsearch/api/types'
import { LeadImportMatchingRepository } from '../../domain/interface/lead-import-matching.repository'
import { LeadImportMatchingCompaniesGroupReadModel } from '../../domain/read-model/lead-import-matching-companies-group.read-model'
import { LeadImportMatchingContactsGroupReadModel } from '../../domain/read-model/lead-import-matching-contacts-group.read-model'
import { Injectable } from '@nestjs/common'
import { ElasticsearchService } from '@nestjs/elasticsearch'
import { chunk } from 'lodash'
import { LogFeature, LoggerService } from '../../../shared/logger.service'
import {
  LEAD_IMPORT_MATCHING_COMPANY_GROUPS_INDEX,
  LEAD_IMPORT_MATCHING_CONTACT_GROUPS_INDEX,
} from './configuration/lead-import-matching.configuration'

@Injectable()
export class ElasticsearchLeadImportMatchingRepository
  implements LeadImportMatchingRepository
{
  private readonly bulkSize = 1000

  private readonly logger = new LoggerService({
    feature: LogFeature.MATCHING_SERVICE,
    context: ElasticsearchLeadImportMatchingRepository.name,
  })

  public constructor(private readonly client: ElasticsearchService) {}

  async projectMatchingContactGroups(
    groups: LeadImportMatchingContactsGroupReadModel[]
  ): Promise<void> {
    if (groups.length === 0) return

    const batches = chunk(groups, this.bulkSize)

    this.logger.log({
      message: `Matching - Lead import projection: Projecting ${groups.length} matching contact groups`,
      data: {
        organizationId: groups[0].organizationId,
        leadImportId: groups[0].leadImportId,
      },
    })

    for (const batch of batches) {
      const operations = batch.flatMap(group => [
        {
          update: {
            _index: LEAD_IMPORT_MATCHING_CONTACT_GROUPS_INDEX,
            _id: `${group.organizationId}_${group.leadImportId}_${group.source.matchingId}`,
          },
        },
        {
          doc: group,
          doc_as_upsert: true,
        },
      ])

      await this.client.bulk({ body: operations })
    }
  }

  async projectMatchingCompanyGroups(
    groups: LeadImportMatchingCompaniesGroupReadModel[]
  ): Promise<void> {
    if (groups.length === 0) return

    const batches = chunk(groups, this.bulkSize)

    this.logger.log({
      message: `Matching - Lead import projection: Projecting ${groups.length} matching company groups`,
      data: {
        organizationId: groups[0].organizationId,
        leadImportId: groups[0].leadImportId,
      },
    })

    for (const batch of batches) {
      const operations = batch.flatMap(group => [
        {
          update: {
            _index: LEAD_IMPORT_MATCHING_COMPANY_GROUPS_INDEX,
            _id: `${group.organizationId}_${group.leadImportId}_${group.source.getMatchingId()}`,
          },
        },
        {
          doc: group,
          doc_as_upsert: true,
        },
      ])

      await this.client.bulk({ body: operations })
    }
  }

  async findMatchingContactGroups(
    organizationId: string,
    leadImportId: string
  ): Promise<LeadImportMatchingContactsGroupReadModel[]> {
    try {
      const response = await this.client.search<
        SearchResponse<LeadImportMatchingContactsGroupReadModel>
      >({
        index: LEAD_IMPORT_MATCHING_CONTACT_GROUPS_INDEX,
        body: {
          query: {
            bool: {
              filter: [
                { term: { organizationId } },
                { term: { leadImportId } },
              ],
            },
          },
        },
      })

      return (response?.body?.hits?.hits ?? []).map(hit => hit._source)
    } catch (error) {
      this.logger.error({
        message: `Matching - Lead import projection: An error occured while searching matching contact groups for lead import:${leadImportId}`,
        data: { organizationId, leadImportId },
        error,
      })

      return []
    }
  }

  async findMatchingCompanyGroups(
    organizationId: string,
    leadImportId: string
  ): Promise<LeadImportMatchingCompaniesGroupReadModel[]> {
    try {
      const response = await this.client.search<
        SearchResponse<LeadImportMatchingCompaniesGroupReadModel>
      >({
        index: LEAD_IMPORT_MATCHING_COMPANY_GROUPS_INDEX,
        body: {
          query: {
            bool: {
              filter: [
                { term: { organizationId } },
                { term: { leadImportId } },
              ],
            },
          },
        },
      })

      return (response?.body?.hits?.hits ?? []).map(hit => hit._source)
    } catch (error) {
      this.logger.error({
        message: `Matching - Lead import projection: An error occured while searching matching company groups for lead import:${leadImportId}`,
        data: { organizationId, leadImportId },
        error,
      })

      return []
    }
  }

  async deleteMatchingGroups(
    organizationId: string,
    leadImportId: string
  ): Promise<void> {
    const query = {
      query: {
        bool: {
          filter: [{ term: { organizationId } }, { term: { leadImportId } }],
        },
      },
    }

    this.logger.log({
      message: `Matching - Lead import projection: Clearing matching contact/company groups for lead import ${leadImportId}`,
      data: { organizationId, leadImportId },
    })

    await this.client.deleteByQuery({
      index: LEAD_IMPORT_MATCHING_CONTACT_GROUPS_INDEX,
      body: query,
    })

    await this.client.deleteByQuery({
      index: LEAD_IMPORT_MATCHING_COMPANY_GROUPS_INDEX,
      body: query,
    })
  }
}
