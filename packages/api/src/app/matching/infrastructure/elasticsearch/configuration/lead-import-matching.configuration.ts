import { Injectable, OnApplicationBootstrap } from '@nestjs/common'
import { ElasticsearchService } from '@nestjs/elasticsearch'
import { LogFeature, LoggerService } from '../../../../shared/logger.service'

export const LEAD_IMPORT_MATCHING_CONTACT_GROUPS_INDEX =
  'lead_import_matching_contact_groups'
export const LEAD_IMPORT_MATCHING_COMPANY_GROUPS_INDEX =
  'lead_import_matching_company_groups'

const mappingDefinition = {
  mappings: {
    dynamic_templates: [
      {
        strings_as_keywords: {
          match_mapping_type: 'string',
          mapping: {
            type: 'keyword',
          },
        },
      },
    ],
  },
}

@Injectable()
export class LeadImportMatchingConfiguration implements OnApplicationBootstrap {
  private readonly logger = new LoggerService({
    feature: LogFeature.MATCHING_SERVICE,
    context: LeadImportMatchingConfiguration.name,
  })

  constructor(private readonly elasticSearchClient: ElasticsearchService) {}

  private async tryCreateIndex(index: string) {
    try {
      const { body } = await this.elasticSearchClient.indices.exists({ index })

      if (body) {
        return
      }

      this.logger.log({
        feature: LogFeature.MATCHING_SERVICE,
        message: `Matching - Creating Elasticsearch index ${index}`,
      })

      await this.elasticSearchClient.indices.create({
        index,
        body: mappingDefinition,
      })
    } catch (error) {
      this.logger.error({
        feature: LogFeature.MATCHING_SERVICE,
        message: `Matching - An error occured while creating index ${index}`,
        error,
      })
    }
  }

  async onApplicationBootstrap() {
    await this.tryCreateIndex(LEAD_IMPORT_MATCHING_CONTACT_GROUPS_INDEX)
    await this.tryCreateIndex(LEAD_IMPORT_MATCHING_COMPANY_GROUPS_INDEX)
  }
}
