import { RedisNamespaceDefinition } from './redis-namespace.definition'
describe('Redis namespace definition', () => {
  it('Should provide the namespace WHEN definition does not have a parent', () => {
    const actual = RedisNamespaceDefinition.from({
      namespace: 'TEST',
    }).toString()
    expect(actual).toBe('TEST')
  })

  it('Should provide parent definition + separator + namespace WHEN definition has a parent', () => {
    const parent = RedisNamespaceDefinition.from({ namespace: 'PARENT' })
    const subParent = RedisNamespaceDefinition.from({
      namespace: 'SUBPARENT',
      parent,
    })

    const actual = RedisNamespaceDefinition.from({
      namespace: 'CHILD',
      parent: subParent,
      separator: '_',
    }).toString()
    expect(actual).toBe('PARENT:SUBPARENT_CHILD')
  })
})
