import { LaunchHubspotMatchingCompanyFullSyncCommandHandler } from '@matching/application/command/launch-hubspot-matching-company-sync/launch-hubspot-matching-company-full-sync.command'
import { LaunchHubspotMatchingCompanyIncrementalSyncCommandHandler } from '@matching/application/command/launch-hubspot-matching-company-sync/launch-hubspot-matching-company-incremental-sync.command'
import CrmFullSyncCron from '@matching/application/cron/crm-full-sync.cron'
import CrmIncrementalSyncCron from '@matching/application/cron/crm-incremental-sync.cron'
import { HasCompanyMatchingQueryHandler } from '@matching/application/query/has-company-matching/has-company-matching.query'
import {
  COMPANY_ES_MATCHING_STRATEGY,
  COMPANY_HUBSPOT_MATCHING_STRATEGY,
} from '@matching/domain/interface/company-matching-strategy'
import { HUBSPOT_COMPANY_CACHE } from '@matching/domain/interface/crm-company.cache'
import { HUBSPOT_COMPANY_INDEXER } from '@matching/domain/interface/crm-company.indexer'
import { HUBSPOT_COMPANY_REPOSITORY } from '@matching/domain/interface/crm-company.repository'
import CrmMatchingSyncService from '@matching/domain/service/crm-matching-sync.service'
import CompanyHubspotMatchingStrategy from '@matching/infrastructure/elasticsearch/company-hubspot-matching.strategy'
import CompanyHubspotIndexer from '@matching/infrastructure/elasticsearch/company-hubspot.indexer'
import HubspotCompanyRepository from '@matching/infrastructure/hubspot/hubspot-company.repository'
import CompanyHubspotCache from '@matching/infrastructure/redis/company-hubspot.cache'
import { BullModule } from '@nestjs/bull'
import { Module } from '@nestjs/common'
import { CqrsModule } from '@nestjs/cqrs'
import { ElasticsearchService } from '@nestjs/elasticsearch'
import { TypeOrmModule } from '@nestjs/typeorm'
import { HubspotAuthorizationEntity } from '../integration/hubspot/infrastructure/entity/hubspot-authorization.entity'
import { IntegrationHubspotModule } from '../integration/hubspot/integration-hubspot.module'
import { CompanyMatchingService } from '../lead/application/service/company-matching/company-matching.service'
import { COMPANY_REPOSITORY_INTERFACE } from '../lead/domain/model/company-repository.interface'
import { CompanyEntity } from '../lead/infrastructure/entity/company.entity'
import { ContactEntity } from '../lead/infrastructure/entity/contact.entity'
import { CompanyRepository } from '../lead/infrastructure/repository/company.repository'
import { LaunchHubspotMatchingContactFullSyncHandler } from './application/command/launch-hubspot-matching-contact-full-sync/launch-hubspot-matching-contact-full-sync.handler'
import { LaunchHubspotMatchingContactIncrementalSyncHandler } from './application/command/launch-hubspot-matching-contact-incremental-sync/launch-hubspot-matching-contact-incremental-sync.handler'
import { LeadImportMatchingProjectionConsumer } from './application/consumer/lead-import-matching-projection.consumer'
import { LeadImportMatchingListener } from './application/listener/lead-import-matching.listener'
import { GetCompanyMatchingGroupsByLeadImportHandler } from './application/query/get-company-matching-groups-by-lead-import/get-company-matching-groups-by-lead-import.handler'
import GetCompanyMatchingGroupsQueryHandler from './application/query/get-company-matching-groups/get-company-matching-groups.query'
import { GetContactMatchingGroupsByLeadImportHandler } from './application/query/get-contact-matching-groups-by-lead-import/get-contact-matching-groups-by-lead-import.handler'
import { GetContactMatchingGroupsHandler } from './application/query/get-contact-matching-groups/get-contact-matching-groups.handler'
import { LEAD_IMPORT_MATCHING_PROJECTION_QUEUE } from './application/queues'
import { CONTACT_MATCHING_STRATEGIES } from './domain/interface/contact-matching-strategy'
import { CRM_CONTACT_CACHE } from './domain/interface/crm-contact.cache'
import { CRM_CONTACT_REPOSITORY } from './domain/interface/crm-contact.repository'
import { HUBSPOT_MATCHING_CONTACT_REPOSITORY } from './domain/interface/hubspot-matching-contact.repository'
import { LEAD_IMPORT_MATCHING_REPOSITORY } from './domain/interface/lead-import-matching.repository'
import { LINKEDIN_ID_RESOLVER } from './domain/interface/linkedin-id-resolver'
import { ScorableContactAggregator } from './domain/service/scorable-contact-aggregator'
import CompanyESMatchingStrategy from './infrastructure/elasticsearch/company-es-matching.strategy'
import { MatchingHubspotConfiguration } from './infrastructure/elasticsearch/configuration'
import { LeadImportMatchingConfiguration } from './infrastructure/elasticsearch/configuration/lead-import-matching.configuration'
import { CoresignalLinkedinIdResolver } from './infrastructure/elasticsearch/coresignal-linkedin-id.resolver'
import { EmailContactMatchingStrategy } from './infrastructure/elasticsearch/email-contact-matching.strategy'
import { EmailHubspotContactMatchingStrategy } from './infrastructure/elasticsearch/email-hubspot-contact-matching.strategy'
import { ElasticsearchHubspotMatchingContactRepository } from './infrastructure/elasticsearch/hubspot-matching-contact.repository'
import { ElasticsearchLeadImportMatchingRepository } from './infrastructure/elasticsearch/lead-import-matching.repository'
import { LinkedinIdContactMatchingStrategy } from './infrastructure/elasticsearch/linkedin-id-contact-matching.strategy'
import { LinkedinIdHubspotContactMatchingStrategy } from './infrastructure/elasticsearch/linkedin-id-hubspot-contact-matching.strategy'
import { NamesContactMatchingStrategy } from './infrastructure/elasticsearch/names-contact-matching.strategy'
import { NamesHubspotContactMatchingStrategy } from './infrastructure/elasticsearch/names-hubspot-contact-matching.strategy'
import { PhonesContactMatchingStrategy } from './infrastructure/elasticsearch/phones-contact-matching-strategy'
import { PhonesHubspotContactMatchingStrategy } from './infrastructure/elasticsearch/phones-hubspot-contact-matching.strategy'
import { SearchHandler } from './infrastructure/elasticsearch/search-handler'
import { HubspotClientFactory } from './infrastructure/hubspot/hubspot-client.factory'
import { HubspotContactRepository } from './infrastructure/hubspot/hubspot-contact.repository'
import { HubspotTokenProvider } from './infrastructure/hubspot/hubspot-token-provider'
import { RedisContactCache } from './infrastructure/redis/contact-hubspot.cache'

const crons = [CrmFullSyncCron, CrmIncrementalSyncCron]
const queryHandlers = [
  GetCompanyMatchingGroupsQueryHandler,
  GetContactMatchingGroupsHandler,
  GetContactMatchingGroupsByLeadImportHandler,
  GetCompanyMatchingGroupsByLeadImportHandler,
  HasCompanyMatchingQueryHandler,
]
const commandHandlers = [
  LaunchHubspotMatchingContactFullSyncHandler,
  LaunchHubspotMatchingCompanyFullSyncCommandHandler,
  LaunchHubspotMatchingContactIncrementalSyncHandler,
  LaunchHubspotMatchingCompanyIncrementalSyncCommandHandler,
]

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CompanyEntity,
      ContactEntity,
      HubspotAuthorizationEntity,
    ]),
    IntegrationHubspotModule,
    BullModule.registerQueue({ name: LEAD_IMPORT_MATCHING_PROJECTION_QUEUE }),
    CqrsModule,
  ],
  providers: [
    MatchingHubspotConfiguration,
    LeadImportMatchingConfiguration,
    LeadImportMatchingListener,
    LeadImportMatchingProjectionConsumer,
    LinkedinIdContactMatchingStrategy,
    NamesContactMatchingStrategy,
    EmailContactMatchingStrategy,
    PhonesContactMatchingStrategy,
    NamesHubspotContactMatchingStrategy,
    EmailHubspotContactMatchingStrategy,
    PhonesHubspotContactMatchingStrategy,
    LinkedinIdHubspotContactMatchingStrategy,
    CrmMatchingSyncService,
    {
      provide: 'ELASTICSEARCH_SEARCH_NODE',
      useFactory: () => {
        try {
          return new ElasticsearchService({
            node: process.env.ELASTICSEARCH_SEARCH_URL,
            auth: {
              username: process.env.ELASTICSEARCH_SEARCH_USERNAME,
              password: process.env.ELASTICSEARCH_SEARCH_PASSWORD,
            },
          })
        } catch {
          return null
        }
      },
    },
    {
      useFactory: (
        linkedinIdStrategy: LinkedinIdContactMatchingStrategy,
        namesStrategy: NamesContactMatchingStrategy,
        emailStrategy: EmailContactMatchingStrategy,
        phonesStrategy: PhonesContactMatchingStrategy,
        hubspotNamesStrategy: NamesHubspotContactMatchingStrategy,
        hubspotEmailStrategy: EmailHubspotContactMatchingStrategy,
        hubspotPhonesStrategy: PhonesHubspotContactMatchingStrategy,
        hubspotLinkedinIdStrategy: LinkedinIdHubspotContactMatchingStrategy
      ) => {
        return [
          linkedinIdStrategy,
          namesStrategy,
          emailStrategy,
          phonesStrategy,
          hubspotNamesStrategy,
          hubspotEmailStrategy,
          hubspotPhonesStrategy,
          hubspotLinkedinIdStrategy,
        ]
      },
      provide: CONTACT_MATCHING_STRATEGIES,
      inject: [
        LinkedinIdContactMatchingStrategy,
        NamesContactMatchingStrategy,
        EmailContactMatchingStrategy,
        PhonesContactMatchingStrategy,
        NamesHubspotContactMatchingStrategy,
        EmailHubspotContactMatchingStrategy,
        PhonesHubspotContactMatchingStrategy,
        LinkedinIdHubspotContactMatchingStrategy,
      ],
    },
    GetContactMatchingGroupsHandler,
    ScorableContactAggregator,
    {
      provide: COMPANY_ES_MATCHING_STRATEGY,
      useClass: CompanyESMatchingStrategy,
    },
    {
      provide: COMPANY_HUBSPOT_MATCHING_STRATEGY,
      useClass: CompanyHubspotMatchingStrategy,
    },
    CompanyMatchingService,
    HubspotTokenProvider,
    HubspotClientFactory,
    {
      provide: COMPANY_REPOSITORY_INTERFACE,
      useClass: CompanyRepository,
    },
    {
      provide: CRM_CONTACT_REPOSITORY,
      useClass: HubspotContactRepository,
    },
    {
      provide: HUBSPOT_MATCHING_CONTACT_REPOSITORY,
      useClass: ElasticsearchHubspotMatchingContactRepository,
    },
    {
      provide: CRM_CONTACT_CACHE,
      useClass: RedisContactCache,
    },
    {
      provide: HUBSPOT_COMPANY_CACHE,
      useClass: CompanyHubspotCache,
    },
    {
      provide: HUBSPOT_COMPANY_INDEXER,
      useClass: CompanyHubspotIndexer,
    },
    {
      provide: HUBSPOT_COMPANY_REPOSITORY,
      useClass: HubspotCompanyRepository,
    },
    {
      provide: LEAD_IMPORT_MATCHING_REPOSITORY,
      useClass: ElasticsearchLeadImportMatchingRepository,
    },
    {
      provide: LINKEDIN_ID_RESOLVER,
      useClass: CoresignalLinkedinIdResolver,
    },
    SearchHandler,
    ...queryHandlers,
    ...commandHandlers,
    ...crons,
  ],
})
export class MatchingModule {}
