import { LeadImportMatchingContactsGroupReadModel } from '../read-model/lead-import-matching-contacts-group.read-model'
import { LeadImportMatchingCompaniesGroupReadModel } from '../read-model/lead-import-matching-companies-group.read-model'

export const LEAD_IMPORT_MATCHING_REPOSITORY = Symbol.for(
  'LeadImportMatchingRepository'
)

export interface LeadImportMatchingRepository {
  projectMatchingContactGroups(
    groups: LeadImportMatchingContactsGroupReadModel[]
  ): Promise<void>
  projectMatchingCompanyGroups(
    groups: LeadImportMatchingCompaniesGroupReadModel[]
  ): Promise<void>
  deleteMatchingGroups(
    organizationId: string,
    leadImportId: string
  ): Promise<void>
  findMatchingContactGroups(
    organizationId: string,
    leadImportId: string
  ): Promise<LeadImportMatchingContactsGroupReadModel[]>
  findMatchingCompanyGroups(
    organizationId: string,
    leadImportId: string
  ): Promise<LeadImportMatchingCompaniesGroupReadModel[]>
}
