import { ValueObject } from '.'

export enum CriteriaResultType {
  MATCH = 'MATCH',
  NO_MATCH = 'NO_MATCH',
  EMPTY = 'EMPTY',
}

export class CriteriaResult implements ValueObject<CriteriaResult> {
  private constructor(
    readonly type: CriteriaResultType,
    readonly points: number
  ) {}

  public equals(other?: CriteriaResult): boolean {
    if (!other) return false
    return this.type === other.type && this.points === other.points
  }

  public static from(type: string, points: number) {
    return new CriteriaResult(type as CriteriaResultType, points)
  }
}
