import { CompanyModel } from '../../../lead/domain/model/company.model'
import CompanyMatchingInputModel from '../model/company-matching-input.model'
import { MatchingSource } from '../model/matching-source.enum'
import { CriteriaType } from '../value-objects/criteria'
import { CriteriaResultType } from '../value-objects/criteria-result'

export type LeadImportMatchingCompaniesGroupReadModel = {
  leadImportId: string
  organizationId: string
  source: CompanyMatchingInputModel
  matches: {
    company: CompanyModel
    matchingSource: MatchingSource
    scoring: {
      score: number
      criterias: {
        name: CriteriaType
        result: CriteriaResultType
      }[]
    }
  }[]
}
