import { ContactModel } from '../../../lead/domain/model/contact.model'
import { CriteriaType } from '../value-objects/criteria'
import { CriteriaResultType } from '../value-objects/criteria-result'
import { MatchingSource } from '@matching/domain/model/matching-source.enum'

export type ContactMatchingInputReadModel = {
  readonly matchingId: string | null
  readonly linkedinUrl: string | null
  readonly email: string | null
  readonly firstName: string | null
  readonly lastName: string | null
  readonly companyName: string | null
  readonly phoneNumbers: string[]
}

export type LeadImportMatchingContactsGroupReadModel = {
  leadImportId: string
  organizationId: string
  source: ContactMatchingInputReadModel
  matches: {
    contact: ContactModel
    matchingSource: MatchingSource
    scoring: {
      score: number
      criterias: {
        name: CriteriaType
        result: CriteriaResultType
      }[]
    }
  }[]
}
