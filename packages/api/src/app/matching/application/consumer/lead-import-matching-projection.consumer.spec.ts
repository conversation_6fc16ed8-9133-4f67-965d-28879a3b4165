import { TestBed } from '@automock/jest'
import {
  LeadCategory,
  LeadImportContextEnum,
  LeadImportSourceEnum,
  LeadImportStatusEnum,
  LeadImportTypeEnum,
} from '@getheroes/shared'
import {
  LEAD_IMPORT_MATCHING_REPOSITORY,
  LeadImportMatchingRepository,
} from '../../domain/interface/lead-import-matching.repository'
import { ContactMatchingInputModel } from '../../domain/model/contact-matching-input.model'
import { ScorableContactModel } from '../../domain/model/scorable-contact.model'
import { Scoring } from '../../domain/model/scoring'
import { QueryBus } from '@nestjs/cqrs'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { v4 } from 'uuid'
import { GetOneLeadImportByIdQuery } from '../../../lead-import/application/query/get-one-lead-import-by-id/get-one-lead-import-by-id.query'
import { LeadImportModel } from '../../../lead-import/domain/models/lead-import.model'
import { FindContactsByLeadImportIdQuery } from '../../../lead/application/query/contact/find-contacts-by-lead-import-id/find-contacts-by-lead-import-id.query'
import { ContactModel } from '../../../lead/domain/model/contact.model'
import { MatchingEvents } from '../../../shared/domain/event/event.enum'
import { LeadImportMatchingReadyEvent } from '../../../shared/domain/event/matching/lead-import-matching-ready.event'
import { UserModel } from '../../../shared/domain/model/user.model'
import { ContactMatchingGroupResponseDto } from '../query/get-contact-matching-groups/contact-matching-group-response.dto'
import { GetContactMatchingGroupsQuery } from '../query/get-contact-matching-groups/get-contact-matching-groups.query'
import { LeadImportMatchingProjectionConsumer } from './lead-import-matching-projection.consumer'
import { ConfigService } from '@nestjs/config'
import { MatchingSource } from '@matching/domain/model/matching-source.enum'

describe('Matching Application - Lead import matching projection consumer', () => {
  let sut: LeadImportMatchingProjectionConsumer
  let eventEmitter: jest.Mocked<EventEmitter2>
  let queryBus: jest.Mocked<QueryBus>
  let leadImportMatchingRepository: jest.Mocked<LeadImportMatchingRepository>
  let configService: jest.Mocked<ConfigService>

  describe('GIVEN A Lead import is created (contact), AND currently storing batches of leads', () => {
    beforeEach(() => {
      const { unit, unitRef } = TestBed.create(
        LeadImportMatchingProjectionConsumer
      ).compile()

      sut = unit
      eventEmitter = unitRef.get(EventEmitter2)
      leadImportMatchingRepository = unitRef.get(
        LEAD_IMPORT_MATCHING_REPOSITORY
      )

      queryBus = unitRef.get(QueryBus)
    })
    describe('WHEN Consuming a "lead import excluded and indexed" event', () => {
      const leadImport = LeadImportModel.create({
        id: v4(),
        organizationId: v4(),
        leadType: LeadImportTypeEnum.CONTACT,
        nbLeadsTotal: 10,
        context: LeadImportContextEnum.ALL_LEADS,
        isAvailableInWorkspace: true,
        labels: [],
        createdBy: new UserModel(),
        createdByUserId: v4(),
        contextSpecificProps: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        nbLeadsImported: 1,
        status: LeadImportStatusEnum.IMPORTING,
        source: LeadImportSourceEnum.ZELIQ_SEARCH,
        sourceSpecificProps: {
          linkedinUrls: ['https://linkedin.com/in/johndoe'],
          filters: [],
        },
      })

      const johnDoe = new ContactModel()
      johnDoe.id = v4()
      johnDoe.organizationId = leadImport.organizationId
      johnDoe.linkedinUrl = 'https://linkedin.com/in/john-doe'
      johnDoe.leadImportIds = [leadImport.id]

      const matchingInput = ContactMatchingInputModel.builder()
        .withOrganizationId(leadImport.organizationId)
        .withMatchingId(v4())
        .withLinkedinUrlsFrom([johnDoe.linkedinUrl])
        .build()

      const matchingGroup: ContactMatchingGroupResponseDto =
        ContactMatchingGroupResponseDto.from(matchingInput, [
          new ScorableContactModel(
            johnDoe,
            MatchingSource.ALL_LEADS,
            Scoring.initializeFor(LeadCategory.CONTACT)
          ),
        ])

      it(`THEN It should project the matching groups having N > 0 matches
          AND Store processed contacts in projection cache
          AND LEAD_IMPORT_MATCHING_READY should be emitted IF projection is completed`, async () => {
        queryBus.execute.mockImplementation(query => {
          if (query instanceof GetOneLeadImportByIdQuery) {
            return Promise.resolve(leadImport)
          }

          if (query instanceof FindContactsByLeadImportIdQuery) {
            return Promise.resolve([johnDoe])
          }

          if (query instanceof GetContactMatchingGroupsQuery) {
            return Promise.resolve([matchingGroup])
          }

          return Promise.resolve(null)
        })

        leadImportMatchingRepository.projectMatchingContactGroups.mockResolvedValueOnce()

        await sut.process({
          data: {
            organizationId: leadImport.organizationId,
            leadImportId: leadImport.id,
          },
        } as any)

        expect(
          leadImportMatchingRepository.projectMatchingContactGroups
        ).toHaveBeenCalledWith([
          {
            organizationId: leadImport.organizationId,
            leadImportId: leadImport.id,
            source: {
              matchingId: matchingGroup.source.matchingId,
              firstName: matchingGroup.source.firstName,
              lastName: matchingGroup.source.lastName,
              email: matchingGroup.source.email,
              linkedinUrl: matchingGroup.source.linkedinUrl,
              companyName: matchingGroup.source.companyName,
              phoneNumbers: matchingGroup.source.phoneNumbers,
            },
            matches: matchingGroup.matches,
          },
        ])

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          MatchingEvents.LEAD_IMPORT_MATCHING_READY,
          new LeadImportMatchingReadyEvent(
            leadImport.organizationId,
            leadImport.id,
            leadImport.leadType
          )
        )
      })
    })
  })
})
