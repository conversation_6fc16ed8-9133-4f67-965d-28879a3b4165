import { LeadImportTypeEnum } from '@getheroes/shared'
import CompanyMatchingInputModel from '@matching/domain/model/company-matching-input.model'
import { MatchingSource } from '@matching/domain/model/matching-source.enum'
import { Process } from '@nestjs/bull'
import { Inject, Injectable } from '@nestjs/common'
import { QueryBus } from '@nestjs/cqrs'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { Job } from 'bull'
import { chunk } from 'lodash'
import { GetOneLeadImportByIdQuery } from '../../../lead-import/application/query/get-one-lead-import-by-id/get-one-lead-import-by-id.query'
import { LeadImportModel } from '../../../lead-import/domain/models/lead-import.model'
import { FindCompaniesByLeadImportIdQuery } from '../../../lead/application/query/company/find-companies-by-lead-import-id/find-companies-by-lead-import-id.query'
import { FindContactsByLeadImportIdQuery } from '../../../lead/application/query/contact/find-contacts-by-lead-import-id/find-contacts-by-lead-import-id.query'
import { CompanyModel } from '../../../lead/domain/model/company.model'
import { ContactModel } from '../../../lead/domain/model/contact.model'
import { MatchingEvents } from '../../../shared/domain/event/event.enum'
import { LeadImportMatchingErrorEvent } from '../../../shared/domain/event/matching/lead-import-matching-error.event'
import { LeadImportMatchingReadyEvent } from '../../../shared/domain/event/matching/lead-import-matching-ready.event'
import { LogFeature, LoggerService } from '../../../shared/logger.service'
import {
  LEAD_IMPORT_MATCHING_REPOSITORY,
  LeadImportMatchingRepository,
} from '../../domain/interface/lead-import-matching.repository'
import { LeadImportMatchingListener } from '../listener/lead-import-matching.listener'
import CompanyMatchingResultDto from '../query/get-company-matching-groups/dto/company-matching-result.dto'
import { GetCompanyMatchingGroupsQuery } from '../query/get-company-matching-groups/get-company-matching-groups.query'
import { ContactMatchingGroupResponseDto } from '../query/get-contact-matching-groups/contact-matching-group-response.dto'
import {
  ContactMatchingInput,
  GetContactMatchingGroupsQuery,
} from '../query/get-contact-matching-groups/get-contact-matching-groups.query'

type LeadImportProjectionEvent = {
  organizationId: string
  leadImportId: string
}

@Injectable()
export class LeadImportMatchingProjectionConsumer {
  private readonly batchSize = 250

  private readonly logger = new LoggerService({
    feature: LogFeature.MATCHING_SERVICE,
    context: LeadImportMatchingListener.name,
  })

  constructor(
    private readonly queryBus: QueryBus,
    @Inject(LEAD_IMPORT_MATCHING_REPOSITORY)
    private readonly leadImportMatchingRepository: LeadImportMatchingRepository,
    private readonly eventEmitter: EventEmitter2
  ) {}

  private async handleMatchesProjectionErrorFor(
    leadImport: LeadImportModel,
    error: Error
  ) {
    this.logger.error({
      message: `Matching - Lead import projection: An error occured while preparing matching ${leadImport.leadType}s for lead import ${leadImport.id} in organization ${leadImport.organizationId}`,
      data: {
        organizationId: leadImport.organizationId,
        leadImportId: leadImport.id,
      },
      error,
    })

    await this.leadImportMatchingRepository.deleteMatchingGroups(
      leadImport.organizationId,
      leadImport.id
    )

    this.eventEmitter.emit(
      MatchingEvents.LEAD_IMPORT_MATCHING_ERROR,
      new LeadImportMatchingErrorEvent(
        leadImport.organizationId,
        leadImport.id,
        leadImport.leadType
      )
    )
  }

  private async handleContactMatchesProjectionFor(leadImport: LeadImportModel) {
    const contacts = await this.queryBus.execute<
      FindContactsByLeadImportIdQuery,
      ContactModel[]
    >(
      new FindContactsByLeadImportIdQuery(
        leadImport.organizationId,
        leadImport.id
      )
    )

    if (contacts.length === 0) {
      return
    }

    this.logger.log({
      message: `Matching - Lead import projection: Evaluating matching contacts for lead import ${leadImport.id}`,
      data: {
        organizationId: leadImport.organizationId,
        leadImportId: leadImport.id,
      },
    })

    const matchingInputs = contacts.map(contact =>
      ContactMatchingInput.from({
        organizationId: contact.organizationId,
        matchingId: contact.id,
        firstName: contact.firstName,
        lastName: contact.lastName,
        companyName: contact.company?.name,
        email: contact.emails?.[0],
        linkedinUrl: contact.linkedinUrl,
        phoneNumbers: contact.phones,
      })
    )

    const batches = chunk(matchingInputs, this.batchSize)

    for (const matchingInputsBatch of batches) {
      try {
        const matchingGroups = await this.queryBus.execute<
          GetContactMatchingGroupsQuery,
          ContactMatchingGroupResponseDto[]
        >(new GetContactMatchingGroupsQuery(matchingInputsBatch))

        const groupsToProject = matchingGroups.filter(
          group => group.matches.length > 0
        )

        await this.leadImportMatchingRepository.projectMatchingContactGroups(
          groupsToProject.map(group => {
            const source = group.source.toDomain()
            return {
              organizationId: leadImport.organizationId,
              leadImportId: leadImport.id,
              source: {
                matchingId: source.matchingId,
                firstName: source.firstName,
                lastName: source.lastName,
                email: source.email,
                linkedinUrl: source.linkedinUrls?.[0]?.originalUrl,
                companyName: source.companyName,
                phoneNumbers: source.phoneNumbers,
              },
              matches: group.matches,
            }
          })
        )
      } catch (error) {
        await this.handleMatchesProjectionErrorFor(leadImport, error)
        break
      }
    }
  }

  private async handleCompanyMatchesProjectionFor(leadImport: LeadImportModel) {
    const companies = await this.queryBus.execute<
      FindContactsByLeadImportIdQuery,
      CompanyModel[]
    >(
      new FindCompaniesByLeadImportIdQuery(
        leadImport.organizationId,
        leadImport.id
      )
    )

    if (companies.length === 0) {
      return
    }

    this.logger.log({
      message: `Matching - Lead import projection: Evaluating matching companies for lead import ${leadImport.id}`,
      data: {
        organizationId: leadImport.organizationId,
        leadImportId: leadImport.id,
      },
    })

    const matchingInputs = companies.map(company =>
      CompanyMatchingInputModel.builder()
        .withMatchingId(company.id)
        .withDomain(company.domain)
        .withName(company.name)
        .withLinkedinUrl(company.linkedinUrl)
        .build()
    )

    const batches = chunk(matchingInputs, this.batchSize)

    for (const matchingInputsBatch of batches) {
      try {
        const matchingGroups = await this.queryBus.execute<
          GetCompanyMatchingGroupsQuery,
          CompanyMatchingResultDto[]
        >(
          new GetCompanyMatchingGroupsQuery(
            matchingInputsBatch,
            leadImport.organizationId
          )
        )

        const groupsToProject = matchingGroups.filter(
          group => group.matches.length > 0
        )

        await this.leadImportMatchingRepository.projectMatchingCompanyGroups(
          groupsToProject.map(group => ({
            leadImportId: leadImport.id,
            organizationId: leadImport.organizationId,
            source: group.source,
            matches: group.matches.map(match => ({
              company: match.company,
              matchingSource: match.matchingSource as unknown as MatchingSource,
              scoring: {
                score: match.scoring.score,
                criterias: match.scoring.criterias,
              },
            })),
          }))
        )
      } catch (error) {
        await this.handleMatchesProjectionErrorFor(leadImport, error)
        break
      }
    }
  }

  @Process({ concurrency: 3 })
  async process(job: Job<LeadImportProjectionEvent>) {
    const leadImport = await this.queryBus.execute(
      new GetOneLeadImportByIdQuery(
        job.data.leadImportId,
        job.data.organizationId
      )
    )

    if (!leadImport) {
      this.logger.log({
        message: `Matching - Lead import projection: Lead import ${job.data.leadImportId} does not exist in organization ${job.data.organizationId}`,
      })
      return
    }

    if (leadImport.leadType === LeadImportTypeEnum.CONTACT) {
      await this.handleContactMatchesProjectionFor(leadImport)
    } else {
      await this.handleCompanyMatchesProjectionFor(leadImport)
    }

    this.eventEmitter.emit(
      MatchingEvents.LEAD_IMPORT_MATCHING_READY,
      new LeadImportMatchingReadyEvent(
        leadImport.organizationId,
        leadImport.id,
        leadImport.leadType
      )
    )
  }
}
