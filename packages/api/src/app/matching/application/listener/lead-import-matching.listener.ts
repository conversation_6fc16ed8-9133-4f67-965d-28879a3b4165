import { InjectQueue } from '@nestjs/bull'
import { Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { Queue } from 'bull'
import { LeadImportEvents } from '../../../shared/domain/event/event.enum'
import { LeadImportLeadsExcludedAndIndexedEvent } from '../../../shared/domain/event/lead-import/lead-import-leads-excluded-and-indexed.event'
import { LEAD_IMPORT_MATCHING_PROJECTION_QUEUE } from '../queues'

@Injectable()
export class LeadImportMatchingListener {
  constructor(
    @InjectQueue(LEAD_IMPORT_MATCHING_PROJECTION_QUEUE)
    private readonly leadImportMatchingProjectionQueue: Queue
  ) {}

  @OnEvent(LeadImportEvents.LEAD_IMPORT_LEADS_EXCLUDED_AND_INDEXED)
  async handleLeadImportProcessingFinished(
    event: LeadImportLeadsExcludedAndIndexedEvent
  ) {
    await this.leadImportMatchingProjectionQueue.add(
      {
        organizationId: event.organizationId,
        leadImportId: event.leadImportId,
      },
      { jobId: `${event.organizationId}-${event.leadImportId}` }
    )
  }
}
