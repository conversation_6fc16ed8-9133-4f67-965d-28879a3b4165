import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { GetContactMatchingGroupsByLeadImportQuery } from './get-contact-matching-groups-by-lead-import.query'
import { LeadImportMatchingContactsGroupReadModel } from '../../../domain/read-model/lead-import-matching-contacts-group.read-model'
import { Inject } from '@nestjs/common'
import {
  LEAD_IMPORT_MATCHING_REPOSITORY,
  LeadImportMatchingRepository,
} from '../../../domain/interface/lead-import-matching.repository'

@QueryHandler(GetContactMatchingGroupsByLeadImportQuery)
export class GetContactMatchingGroupsByLeadImportHandler
  implements
    IQueryHandler<
      GetContactMatchingGroupsByLeadImportQuery,
      LeadImportMatchingContactsGroupReadModel[]
    >
{
  constructor(
    @Inject(LEAD_IMPORT_MATCHING_REPOSITORY)
    private readonly LeadImportMatchingRepository: LeadImportMatchingRepository
  ) {}

  async execute(
    query: GetContactMatchingGroupsByLeadImportQuery
  ): Promise<LeadImportMatchingContactsGroupReadModel[]> {
    const groups =
      await this.LeadImportMatchingRepository.findMatchingContactGroups(
        query.organizationId,
        query.leadImportId
      )

    return groups
  }
}
