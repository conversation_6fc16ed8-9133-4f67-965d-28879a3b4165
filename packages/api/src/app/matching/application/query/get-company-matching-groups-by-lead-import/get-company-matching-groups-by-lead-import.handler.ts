import {
  LEAD_IMPORT_MATCHING_REPOSITORY,
  LeadImportMatchingRepository,
} from '../../../domain/interface/lead-import-matching.repository'
import { LeadImportMatchingCompaniesGroupReadModel } from '../../../domain/read-model/lead-import-matching-companies-group.read-model'
import { Inject } from '@nestjs/common'
import { I<PERSON>ueryHandler, QueryHandler } from '@nestjs/cqrs'
import { GetCompanyMatchingGroupsByLeadImportQuery } from './get-company-matching-groups-by-lead-import.query'

@QueryHandler(GetCompanyMatchingGroupsByLeadImportQuery)
export class GetCompanyMatchingGroupsByLeadImportHandler
  implements
    IQueryHandler<
      GetCompanyMatchingGroupsByLeadImportQuery,
      LeadImportMatchingCompaniesGroupReadModel[]
    >
{
  constructor(
    @Inject(LEAD_IMPORT_MATCHING_REPOSITORY)
    private readonly LeadImportMatchingRepository: LeadImportMatchingRepository
  ) {}

  async execute(
    query: GetCompanyMatchingGroupsByLeadImportQuery
  ): Promise<LeadImportMatchingCompaniesGroupReadModel[]> {
    const groups =
      await this.LeadImportMatchingRepository.findMatchingCompanyGroups(
        query.organizationId,
        query.leadImportId
      )

    return groups
  }
}
