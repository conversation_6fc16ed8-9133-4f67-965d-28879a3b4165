import { MatchingSource } from '@matching/domain/model/matching-source.enum'
import { CompanyModel } from '../../../../../lead/domain/model/company.model'
import CompanyMatchingInputModel from '../../../../domain/model/company-matching-input.model'
import ScorableCompany from '../../../../domain/model/scorable-company.model'
import { CriteriaType } from '@matching/domain/value-objects/criteria'
import { CriteriaResultType } from '@matching/domain/value-objects/criteria-result'

export default class CompanyMatchingResultDto {
  private constructor(
    readonly source: CompanyMatchingInputModel,
    readonly matches: {
      company: CompanyModel
      matchingSource: MatchingSource
      scoring: {
        score: number
        criterias: {
          name: CriteriaType
          result: CriteriaResultType
        }[]
      }
    }[]
  ) {
    this.source = source
    this.matches = matches
  }

  public static from(
    input: CompanyMatchingInputModel,
    matches: ScorableCompany[]
  ) {
    return new CompanyMatchingResultDto(
      input,
      matches.map(match => {
        return {
          company: match.getCompany(),
          matchingSource: match.getMatchingSource(),
          scoring: {
            score: match.getScoring().score,
            criterias: match
              .getScoring()
              .criterias.list()
              .map(criteria => ({
                name: criteria.name,
                result: criteria.activeResultType,
              })),
          },
        }
      })
    )
  }
}
