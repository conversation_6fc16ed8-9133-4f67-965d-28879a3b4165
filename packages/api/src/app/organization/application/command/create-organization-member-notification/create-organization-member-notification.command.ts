import { OrganizationMemberNotificationModel } from '../../../domain/model/organization-member-notification.model'

export interface CreateOrganizationMemberNotificationCommandProps
  extends Pick<
    OrganizationMemberNotificationModel,
    'name' | 'emailTemplateId' | 'sentAt'
  > {
  organizationMemberId: string
}

export class CreateOrganizationMemberNotificationCommand {
  constructor(
    readonly props: CreateOrganizationMemberNotificationCommandProps
  ) {}
}
