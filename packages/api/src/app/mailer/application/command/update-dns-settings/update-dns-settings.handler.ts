import { UpdateDnsSettingsCommand } from './update-dns-settings.command'
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs'
import {
  DNS_SETTINGS_REPOSITORY_INTERFACE, DnsSettingsRepositoryInterface,
} from '../../../domain/interface/repository/dns-settings-repository.interface'
import { Inject } from '@nestjs/common'
import { MailerDnsSettingsService } from '../../service/dns-settings.service'
import { DnsSettingsModel } from '../../../domain/model/dns-settings.model'

@CommandHandler(UpdateDnsSettingsCommand)
export class UpdateDnsSettingsHandler
  implements ICommandHandler<UpdateDnsSettingsCommand>
{
  constructor(
    @Inject(DNS_SETTINGS_REPOSITORY_INTERFACE)
    readonly dnsSettingsRepository: DnsSettingsRepositoryInterface,
    private readonly mailerDnsSettingsService: MailerDnsSettingsService
  ) {}

  async execute(
    updateDnsSettingsCommand: UpdateDnsSettingsCommand
  ): Promise<void> {
    const dnsVerificationResults = await this.mailerDnsSettingsService.verifyDns(updateDnsSettingsCommand.domain);

    let dnsSettings = await this.dnsSettingsRepository.findByOrganizationAndDomain(
      updateDnsSettingsCommand.organizationId,
      updateDnsSettingsCommand.domain,
    );
    if (!dnsSettings) {
      dnsSettings = new DnsSettingsModel();
      dnsSettings.organizationId = updateDnsSettingsCommand.organizationId;
      dnsSettings.domain = updateDnsSettingsCommand.domain;
    }

    dnsSettings.spf = dnsVerificationResults.spf;
    dnsSettings.spfValid = dnsVerificationResults.spfValid;
    dnsSettings.dkim = dnsVerificationResults.dkim;
    dnsSettings.dkimValid = dnsVerificationResults.dkimValid;
    dnsSettings.dmarc = dnsVerificationResults.dmarc;
    dnsSettings.dmarcValid = dnsVerificationResults.dmarcValid;
    dnsSettings.mx = dnsVerificationResults.mx;
    dnsSettings.mxValid = dnsVerificationResults.mxValid;
    dnsSettings.domainCreatedAt = dnsVerificationResults.domainAge;
    dnsSettings.domainAgeValid = dnsVerificationResults.domainAgeValid;
    dnsSettings.updatedAt = new Date(); // Force update even if nothing changes to track refresh status

    await this.dnsSettingsRepository.upsert(dnsSettings);
  }
}
