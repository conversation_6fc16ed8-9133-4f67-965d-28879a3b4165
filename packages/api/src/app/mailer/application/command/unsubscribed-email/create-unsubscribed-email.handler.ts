import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Query<PERSON><PERSON>,
} from '@nestjs/cqrs'
import { ConflictException, Inject, NotFoundException } from '@nestjs/common'
import { CreateUnsubscribedEmailCommand } from './create-unsubscribed-email.command'
import {
  UNSUBSCRIBED_EMAIL_REPOSITORY_INTERFACE,
  UnsubscribedEmailRepositoryInterface,
} from '../../../domain/model/unsubscribed-email-repository.interface'
import { UnsubscribeTokenService } from '../../service/unsubscribe-token.service'
import { UnsubscribedEmailModel } from '../../../domain/model/unsubscribed-email.model'
import { FindAllowedDomainByIdQuery } from '../../query/find-allowed-domain/find-allowed-domain-by-id.query'
import { EMAIL_UNSUBSCRIBED_MESSAGES } from '../../../domain/enum/email-unsubscribed-responses'
import { UpdateContactStatusByE<PERSON><PERSON><PERSON><PERSON> } from '../../../../lead/application/command/contact/update-contact-status/update-contact-status-by-email.command'
import { LeadStatus } from '@getheroes/shared'
import { SequenceEvents } from '../../../../shared/domain/event/event.enum'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { SequenceContactUnsubscribedEvent } from '../../../../shared/domain/event/sequence/sequence-contact-unsubscribed.event'
import { FindSequenceContactBySequenceAndEmailQuery } from '../../../../sequence/application/query/find-sequence-contact-by-sequence-and-email/find-sequence-contact-by-sequence-and-email.query'
import { FindSequenceQuery } from '../../../../sequence/application/query/find-sequence/find-sequence.query'
import { UpdateSequenceKpiCommand } from '../../../../sequence/application/command/update-sequence-kpi/update-sequence-kpi.command'
import { SequenceModel } from '../../../../sequence/domain/model/sequence.model'

@CommandHandler(CreateUnsubscribedEmailCommand)
export class CreateUnsubscribedEmailHandler
  implements ICommandHandler<CreateUnsubscribedEmailCommand>
{
  constructor(
    @Inject(UNSUBSCRIBED_EMAIL_REPOSITORY_INTERFACE)
    private unsubscribedEmailRepository: UnsubscribedEmailRepositoryInterface,
    readonly unsubscribeTokenService: UnsubscribeTokenService,
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
    private eventEmitter: EventEmitter2
  ) {}

  async execute(command: CreateUnsubscribedEmailCommand): Promise<string> {
    const { token } = command
    const { allowedDomainId, email, sequenceId } =
      await this.unsubscribeTokenService.decodeUnsubscribeToken(token)

    if (!allowedDomainId || !sequenceId || !email) {
      throw new NotFoundException(EMAIL_UNSUBSCRIBED_MESSAGES.INVALID_TOKEN)
    }

    // get the allowed domain
    const allowedDomain = await this.queryBus.execute(
      new FindAllowedDomainByIdQuery(allowedDomainId, true)
    )

    if (!allowedDomain) {
      console.log(
        `Error : allowedDomain is not found with ID : ${allowedDomainId}`
      )

      // TODO: Return for fix in production.
      // Save even if empty fields to be done immediately afterwards
      return
    }

    // hash the email before store it
    const unsubscribedEmailHashed =
      await this.unsubscribeTokenService.hashUnsubscribedEmail(email)

    // check if the email is already unsubscribed
    const unsubscribedEmail =
      await this.unsubscribedEmailRepository.findByAllowedDomainAndEmailAndSequence(
        allowedDomain.domainName,
        unsubscribedEmailHashed,
        sequenceId
      )

    if (unsubscribedEmail) {
      throw new ConflictException(
        EMAIL_UNSUBSCRIBED_MESSAGES.EMAIL_ALREADY_UNSUBSCRIBED,
        EMAIL_UNSUBSCRIBED_MESSAGES.EMAIL_UNSUBSCRIBED_EXISTS
      )
    }

    const sequenceContactModel = await this.queryBus.execute(
      new FindSequenceContactBySequenceAndEmailQuery(sequenceId, email)
    )

    if (!sequenceContactModel) {
      console.log('unsubscribed sequence')
    }

    // create unsubscribed Email Model
    const unsubscribedEmailModel = new UnsubscribedEmailModel()
    unsubscribedEmailModel.unsubscribedEmailHashed = unsubscribedEmailHashed
    unsubscribedEmailModel.allowedDomainName = allowedDomain.domainName ?? null
    unsubscribedEmailModel.organizationId = allowedDomain.organizationId
    unsubscribedEmailModel.sequenceId = sequenceId
    unsubscribedEmailModel.contactId = sequenceContactModel.contactId

    await this.unsubscribedEmailRepository.create(unsubscribedEmailModel)

    // Update status of unsubscribed lead
    await this.commandBus.execute(
      new UpdateContactStatusByEmailCommand(
        email,
        LeadStatus.DISQUALIFIED,
        allowedDomain.organizationId
      )
    )

    const sequenceContactUnsubscribedEvent =
      new SequenceContactUnsubscribedEvent()
    sequenceContactUnsubscribedEvent.sequenceId = sequenceId
    sequenceContactUnsubscribedEvent.contactId = sequenceContactModel.contactId
    sequenceContactUnsubscribedEvent.sequenceContactId = sequenceContactModel.id
    sequenceContactUnsubscribedEvent.contactEmail = email
    this.eventEmitter.emit(
      SequenceEvents.SEQUENCE_CONTACT_UNSUBSCRIBED,
      sequenceContactUnsubscribedEvent
    )

    // get sequence by id then update nb of unsubsciptions
    const sequence: SequenceModel = await this.queryBus.execute(
      new FindSequenceQuery(sequenceId)
    )
    // get the count of unsuscribed emails form sequence
    const mailsUnsubscribed =
      await this.unsubscribedEmailRepository.countUnsubscribedEmailsBySequenceId(
        sequenceId
      )

    await this.commandBus.execute(
      new UpdateSequenceKpiCommand(sequence, {
        mailsUnsubscribed: mailsUnsubscribed,
      })
    )

    return EMAIL_UNSUBSCRIBED_MESSAGES.EMAIL_UNSUBSCRIBED_SUCCESSFULLY
  }
}
