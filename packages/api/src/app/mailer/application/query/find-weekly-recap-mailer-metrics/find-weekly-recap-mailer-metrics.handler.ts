import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { <PERSON>gger } from '@nestjs/common'

import { endOfWeek, startOfWeek } from 'date-fns'

import { CountWeeklySuccessContactsByAssignQuery } from '../../../../lead/application/query/contact/count-weekly-success-contacts-by-assign/count-weekly-success-contacts-by-assign.query'
import { LeadStatus, WeeklyRecapMetrics } from '@getheroes/shared'
import { FindWeeklyRecapMailerMetricsQuery } from './find-weekly-recap-mailer-metrics.query'
import { CountSentEmailsQuery } from '../count-sent-emails/count-sent-emails.query'
import { CountAnsweredEmailsQuery } from '../count-answered-emails/count-answered-emails.query'
import { CountOpenedEmailsQuery } from '../count-opened-emails/count-opened-emails.query'
import { CountSequenceEmailsQuery } from '../count-sequence-emails/count-sequence-emails.query'
import { CountCallsMadeQuery } from '../../../../dialer/application/query/count-calls-made/count-calls-made.query'
import { CountTasksCompletedQuery } from '../../../../journey/application/query/task/count-tasks-completed/count-tasks-completed.query'
import { FindConsecutiveWeeksActivityMetricByMemberQuery } from '../../../../organization/application/query/find-consecutive-weeks-activity-metric-by-member/find-consecutive-weeks-activity-metric-by-member.query'
import { CreateOrUpdateOrganizationMemberMetricsCommand } from '../../../../organization/application/command/create-or-update-organization-member-metrics/create-or-update-organization-member-metrics.command'
import { OrganizationMemberModel } from '../../../../organization/domain/organization-member.model'
import { FindOrganizationMemberByRelationQuery } from '../../../../organization/application/query/find-organization-member/find-organization-member-by-relation.query'

@QueryHandler(FindWeeklyRecapMailerMetricsQuery)
export class FindWeeklyRecapMailerMetricsHandler
  implements IQueryHandler<FindWeeklyRecapMailerMetricsQuery>
{
  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus
  ) {}

  private readonly logger = new Logger(FindWeeklyRecapMailerMetricsHandler.name)

  async execute({
    organizationId,
    userId,
  }: FindWeeklyRecapMailerMetricsQuery): Promise<WeeklyRecapMetrics> {
    try {
      // Monday is considered the first day of the week.
      // Ex: If today is a Wednesday,
      // this will return the Monday of the current week.
      const startDate = startOfWeek(new Date(), { weekStartsOn: 1 })
      const endDate = endOfWeek(new Date(), { weekStartsOn: 1 })

      const nbTotalEmailsSent = await this.queryBus.execute(
        new CountSentEmailsQuery(organizationId, userId, { startDate, endDate })
      )

      const nbAnswersToEmails = await this.queryBus.execute(
        new CountAnsweredEmailsQuery(organizationId, userId, {
          startDate,
          endDate,
        })
      )

      const nbSequencesEmailsSent = await this.queryBus.execute(
        new CountSequenceEmailsQuery(organizationId, userId, {
          startDate,
          endDate,
        })
      )

      const nbEmailsOpened = await this.queryBus.execute(
        new CountOpenedEmailsQuery(organizationId, userId, {
          startDate,
          endDate,
        })
      )

      const sequencesOpenRate =
        nbEmailsOpened !== 0
          ? Math.round((nbEmailsOpened / nbTotalEmailsSent) * 100)
          : 0

      const nbCallsMade = await this.queryBus.execute(
        new CountCallsMadeQuery(organizationId, userId, { startDate, endDate })
      )

      const nbTasksCompleted = await this.queryBus.execute(
        new CountTasksCompletedQuery(organizationId, userId, {
          startDate,
          endDate,
        })
      )

      const countWeeklySuccess: Record<LeadStatus, number> =
        await this.queryBus.execute(
          new CountWeeklySuccessContactsByAssignQuery(organizationId, userId)
        )

      const nbConsecutiveWeeksActive: number = await this.queryBus.execute(
        new FindConsecutiveWeeksActivityMetricByMemberQuery(
          organizationId,
          userId
        )
      )

      const nbManualEmailsSent = nbTotalEmailsSent - nbSequencesEmailsSent

      const nbMeetingsBooked = countWeeklySuccess?.meeting_booked || 0

      const organizationMemberModel: OrganizationMemberModel =
        await this.queryBus.execute(
          new FindOrganizationMemberByRelationQuery(organizationId, userId)
        )

      await this.commandBus.execute(
        new CreateOrUpdateOrganizationMemberMetricsCommand({
          organizationMember: organizationMemberModel,
          nbSequencesEmailsSent,
          sequencesOpenRate,
          nbManualEmailsSent,
          nbAnswersToEmails,
          nbCallsMade,
          nbTasksCompleted,
          nbMeetingsBooked,
          nbConsecutiveWeeksActive,
          nbAutomatedActions: 0,
          nbActionsMade: 0,
        })
      )

      return {
        nbSequencesEmailsSent,
        sequencesOpenRate: `${sequencesOpenRate}%`,
        nbManualEmailsSent,
        nbAnswersToEmails,
        nbCallsMade,
        nbTasksCompleted,
        nbMeetingsBooked,
        nbConsecutiveWeeksActive,
        nbAutomatedActions: 0,
        nbActionsMade: 0,
      }
    } catch (error) {
      this.logger.error(
        {
          error,
          data: {
            organizationId,
            userId,
          },
        },
        'Error while getting mailer weekly recap metrics'
      )
      throw error
    }
  }
}
