import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'

import {
  MESSAGE_REPOSITORY_INTERFACE,
  MessageRepositoryInterface,
} from '../../../domain/interface/repository/message-repository.interface'

import { CountOpenedEmailsQuery } from './count-opened-emails.query'

type CountOpenedEmails = number

@QueryHandler(CountOpenedEmailsQuery)
export class CountOpenedEmailsHandler
  implements IQueryHandler<CountOpenedEmailsQuery>
{
  constructor(
    @Inject(MESSAGE_REPOSITORY_INTERFACE)
    private readonly messageRepository: MessageRepositoryInterface
  ) {}

  async execute({
    organizationId,
    userId,
    dateRange,
  }: CountOpenedEmailsQuery): Promise<CountOpenedEmails> {
    return this.messageRepository.countSentByUserInDateRange(
      organizationId,
      userId,
      {
        ...dateRange,
        isOpenedDuringInterval: true,
      }
    )
  }
}
