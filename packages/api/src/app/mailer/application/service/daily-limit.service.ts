import { Injectable } from '@nestjs/common'
import { CacheService } from '../../../shared/infrastructure/service/cache.service'
import { DEFAULT_MAX_DAILY_LIMIT, EMAIL_DAILY_LIMIT } from '../../domain/global'
import { QueryBus } from '@nestjs/cqrs'
import { FindAllowedDomainByOrganizationAndNameQuery } from '../query/find-allowed-domain/find-allowed-domain-by-organization-name.query'
import { FindEmailSettingsByOrganizationAndAllowedDomainQuery } from '../query/find-email-settings/find-by-organization-and-allowed-domain.query'
import { AllowedDomainModel } from '../../domain/model/allowed-domain.model'
import { EmailJobTrackingService } from '../../infrastructure/service/email-job-tracking.service'
import { subHours } from 'date-fns'

@Injectable()
export class DailyLimitService {
  constructor(
    private readonly emailJobTrackingService: EmailJobTrackingService,
    private readonly queryBus: QueryBus,
    private readonly cacheManager: CacheService
  ) {}

  async getDailyLimit(email: string, organizationId: string): Promise<number> {
    const domain = email.split('@')[1]
    const cacheKey = `${EMAIL_DAILY_LIMIT}:${organizationId}-${domain}`
    let dailyLimit = await this.cacheManager.get(cacheKey)

    if (!dailyLimit) {
      const allowedDomain: AllowedDomainModel = await this.queryBus.execute(
        new FindAllowedDomainByOrganizationAndNameQuery(organizationId, domain)
      )
      const emailSettings = await this.queryBus.execute(
        new FindEmailSettingsByOrganizationAndAllowedDomainQuery(
          organizationId,
          allowedDomain.id
        )
      )
      dailyLimit = emailSettings?.dailyLimit ?? DEFAULT_MAX_DAILY_LIMIT
      if (emailSettings?.rampupMode?.enabled) {
        dailyLimit = emailSettings?.rampupMode?.currentRampupLimit
      }
      await this.cacheManager.set(cacheKey, dailyLimit?.toString())
    }

    return +dailyLimit
  }

  async getDailyCounter(email: string): Promise<number> {
    return this.emailJobTrackingService.countScheduledJobsSince(
      email,
      subHours(new Date(), 24)
    )
  }

  async remainingEmailCounter(
    email: string,
    organizationId: string
  ): Promise<number> {
    return (
      (await this.getDailyLimit(email, organizationId)) -
      (await this.getDailyCounter(email))
    )
  }
}
