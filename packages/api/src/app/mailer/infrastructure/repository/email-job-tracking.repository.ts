import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { Mapper } from '@automapper/core'
import { InjectMapper } from '@automapper/nestjs'
import { EmailJobTrackingEntity } from '../entity/email-job-tracking.entity'
import { JobTrackingRepository } from '../../../shared/infrastructure/repository/job-tracking.repository'
import { EmailJobTrackingModel } from '../../domain/model/email-job-tracking.model'
import { EmailJobTrackingRepositoryInterface } from '../../domain/repository/email-job-tracking.repository.interface'
import { JobTrackingStatus } from '../../../shared/domain/job-tracking-status.enum'

@Injectable()
export class EmailJobTrackingRepository
  extends JobTrackingRepository<EmailJobTrackingEntity, EmailJobTrackingModel>
  implements EmailJobTrackingRepositoryInterface
{
  constructor(
    @InjectRepository(EmailJobTrackingEntity)
    jobTrackingRepository: Repository<EmailJobTrackingEntity>,
    @InjectMapper() mapper: Mapper
  ) {
    super(
      jobTrackingRepository,
      mapper,
      EmailJobTrackingEntity,
      EmailJobTrackingModel
    )
  }

  async countScheduledJobsSince(email: string, since: Date): Promise<number> {
    return this.jobTrackingRepository
      .createQueryBuilder()
      .where('email = email', { email })
      .andWhere('created_at >= :since', { since })
      .andWhere('status IN (:...status)', {
        status: [
          JobTrackingStatus.PENDING,
          JobTrackingStatus.ACTIVE,
          JobTrackingStatus.DONE,
        ],
      })
      .getCount()
  }

  async findBySequenceContactIds(
    sequenceContactIds: string[]
  ): Promise<EmailJobTrackingModel[]> {
    const emailJobTrackingEntities = await this.jobTrackingRepository
      .createQueryBuilder()
      .where(`context ->> 'sequenceContactId' IN (:...sequenceContactIds)`, {
        sequenceContactIds,
      })
      .getMany()

    return this.mapper.mapArray(
      emailJobTrackingEntities,
      EmailJobTrackingEntity,
      EmailJobTrackingModel
    )
  }
}
