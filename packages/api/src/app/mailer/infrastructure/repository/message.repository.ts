import { InjectRepository } from '@nestjs/typeorm'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, In, Repository } from 'typeorm'
import { Mapper } from '@automapper/core'
import { InjectMapper } from '@automapper/nestjs'
import { MessageStatus } from '../../domain/enum/message-status.enum'
import { MessageDirection } from '../../domain/enum/message-direction.enum'
import { MessageModel } from '../../domain/model/message.model'
import {
  CountSentByUserInDateRangeOptions,
  MessageRepositoryInterface,
} from '../../domain/interface/repository/message-repository.interface'
import { MessageEntity } from '../entity/message.entity'
import { FindManyOptions } from 'typeorm/find-options/FindManyOptions'
import { encrypt } from '../../../shared/utils/crypto-util'
import { validate as uuidValidate } from 'uuid'
export class MessageRepository implements MessageRepositoryInterface {
  constructor(
    @InjectRepository(MessageEntity)
    private readonly entityRepository: Repository<MessageEntity>,
    @InjectMapper() private readonly mapper: Mapper
  ) {}

  async findAll(
    options?: FindManyOptions<MessageEntity>
  ): Promise<MessageModel[]> {
    const messageEntities = await this.entityRepository.find({
      ...options,
      relations: ['createdBy'],
    })
    return this.mapper.mapArray(messageEntities, MessageEntity, MessageModel)
  }

  async create(messageModel: MessageModel): Promise<MessageModel> {
    let messageEntity = this.mapper.map(
      messageModel,
      MessageModel,
      MessageEntity
    )

    messageEntity.encryptedTrackingId = encrypt(messageEntity.trackingId)

    try {
      messageEntity = await this.entityRepository.save(messageEntity)
    } catch (e) {
      console.log('save message', e)
    }

    return this.mapper.map(messageEntity, MessageEntity, MessageModel)
  }

  async update(messageModel: MessageModel): Promise<MessageModel> {
    let messageEntity = this.mapper.map(
      messageModel,
      MessageModel,
      MessageEntity
    )

    messageEntity = await this.entityRepository.save(messageEntity)
    return this.mapper.map(messageEntity, MessageEntity, MessageModel)
  }

  async delete(id: string): Promise<DeleteResult> {
    return this.entityRepository.delete(id)
  }

  async findById(id: string): Promise<MessageModel | null> {
    if (!id) return null
    const messageEntity = await this.entityRepository.findOne({
      relations: ['createdBy'],
      where: { id },
    })
    return messageEntity
      ? this.mapper.map(messageEntity, MessageEntity, MessageModel)
      : null
  }

  async findByResourceId(resourceId: string): Promise<MessageModel | null> {
    if (!resourceId) {
      return null
    }

    const messageEntity = await this.entityRepository.findOne({
      relations: ['createdBy'],
      where: {
        resourceId: resourceId,
      },
    })
    return messageEntity
      ? this.mapper.map(messageEntity, MessageEntity, MessageModel)
      : null
  }

  async findByTrackingId(trackingId: string): Promise<MessageModel | null> {
    if (!trackingId) {
      return null
    }

    const messageEntity = await this.entityRepository.findOne({
      relations: ['createdBy'],
      where: [
        {
          trackingId: uuidValidate(trackingId) ? trackingId : undefined,
        },
        { encryptedTrackingId: trackingId },
      ],
    })
    return messageEntity
      ? this.mapper.map(messageEntity, MessageEntity, MessageModel)
      : null
  }

  async findByTaskIdInDraft(taskId: string): Promise<MessageModel | null> {
    if (!taskId) {
      return null
    }

    const messageEntity = await this.entityRepository.findOne({
      relations: ['createdBy'],
      where: {
        taskId: taskId,
        status: MessageStatus.DRAFT,
      },
      order: {
        updatedAt: 'DESC',
      },
    })

    return messageEntity
      ? this.mapper.map(messageEntity, MessageEntity, MessageModel)
      : null
  }

  async findByResourceThreadId(
    resourceThreadId: string
  ): Promise<MessageModel | null> {
    const messageEntity = await this.entityRepository.find({
      relations: ['createdBy'],
      where: {
        resourceThreadId: resourceThreadId,
      },
      order: {
        messageDate: 'ASC',
      },
      take: 1,
    })

    return messageEntity
      ? this.mapper.map(messageEntity[0], MessageEntity, MessageModel)
      : null
  }

  async findMessagesByResourceThreadId(
    userId: string,
    resourceThreadId: string
  ): Promise<MessageModel[]> {
    const messageEntities = await this.entityRepository.find({
      relations: ['createdBy'],
      where: {
        createdBy: { id: userId },
        resourceThreadId: resourceThreadId,
      },
      order: {
        messageDate: 'ASC',
      },
    })

    return this.mapper.mapArray(messageEntities, MessageEntity, MessageModel)
  }

  async findThreadsByResourceThreadIds(
    userId: string,
    resourceThreadIds: string[]
  ): Promise<MessageModel[]> {
    const messageEntities = await this.entityRepository.find({
      relations: ['createdBy'],
      where: {
        createdBy: { id: userId },
        resourceThreadId: In(resourceThreadIds),
      },
    })
    return this.mapper.mapArray(messageEntities, MessageEntity, MessageModel)
  }

  async findByStepId(stepId: string): Promise<MessageModel | null> {
    const messageEntity = await this.entityRepository.findOne({
      where: { sequenceStep: { id: stepId } },
    })
    return messageEntity
      ? this.mapper.map(messageEntity, MessageEntity, MessageModel)
      : null
  }

  async getInitialMessageForStepAndSequenceContact(
    stepId: string,
    sequenceContactId: string
  ): Promise<MessageModel | null> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('m')
      .where('m.sequenceStepId = :stepId', { stepId })
      .andWhere('m.sequenceContactId = :sequenceContactId', {
        sequenceContactId,
      })
      .andWhere('m.status = :status', { status: MessageStatus.SENT })
      .orderBy('m.messageDate', 'ASC')

    const messageEntity = await queryBuilder.getOne()

    return this.mapper.map(messageEntity, MessageEntity, MessageModel)
  }

  async countContactsOpenedEmailsBySequenceId(
    sequenceId: string
  ): Promise<number> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('gm')
      .innerJoin('gm.sequenceStep', 'ss')
      .where('ss.sequenceId = :sequenceId', { sequenceId })
      .andWhere('gm.status != :status', { status: MessageStatus.BOUNCED })
      .andWhere('gm.openedDates IS NOT NULL')

    const result = await queryBuilder
      .select('COUNT(gm.contactId)', 'countOpenedEmails')
      .getRawOne()

    return parseInt(result.countOpenedEmails, 10) || 0
  }

  async countContactsClickedLinksBySequenceId(
    sequenceId: string
  ): Promise<number> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('gm')
      .innerJoin('gm.sequenceStep', 'ss')
      .where('ss.sequenceId = :sequenceId', { sequenceId })
      .andWhere('gm.linkClickedDates IS NOT NULL')

    const result = await queryBuilder
      .select('COUNT(gm.contactId)', 'countClickedLinks')
      .getRawOne()

    return parseInt(result.countClickedLinks, 10) || 0
  }

  async countContactsBouncedEmailsBySequenceId(
    sequenceId: string
  ): Promise<number> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('gm')
      .innerJoin('gm.sequenceStep', 'ss')
      .where('ss.sequenceId = :sequenceId', { sequenceId })
      .andWhere('gm.status = :status', { status: MessageStatus.BOUNCED })

    const result = await queryBuilder
      .select('COUNT(gm.contactId)', 'countBouncedEmails')
      .getRawOne()

    return parseInt(result.countBouncedEmails, 10) || 0
  }

  async countResourceThreadIdsBySequenceId(
    sequenceId: string
  ): Promise<number> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('gm')
      .innerJoin('gm.sequenceStep', 'ss')
      .where('ss.sequenceId = :sequenceId', { sequenceId })

    const result = await queryBuilder
      .select('COUNT(gm.resourceThreadId)', 'messageCount')
      .getRawOne()

    return parseInt(result.messageCount, 10) || 0
  }

  async countRepliedContactsBySequenceId(sequenceId: string): Promise<number> {
    const subquery = this.entityRepository
      .createQueryBuilder('gmSub')
      .innerJoin('gmSub.sequenceStep', 'ssSub')
      .select('gmSub.resourceThreadId')
      .where('ssSub.sequenceId = :sequenceId', { sequenceId })
      .groupBy('gmSub.resourceThreadId')
      .having('COUNT(*) >= 1')

    const result = await this.entityRepository
      .createQueryBuilder('gm')
      .select('COUNT(gm.sequenceContactId)', 'countRepliedContacts')
      .innerJoin('gm.sequenceStep', 'ss')
      .where('ss.sequenceId = :sequenceId', { sequenceId })
      .andWhere(`gm.resourceThreadId IN (${subquery.getQuery()})`)
      .andWhere('gm.direction = :direction', {
        direction: MessageDirection.INBOUND,
      })
      .setParameters(subquery.getParameters())
      .getRawOne()

    return parseInt(result.countRepliedContacts, 10) || 0
  }

  async countSentByUserInDateRange(
    organizationId: string,
    userId: string,
    {
      startDate,
      endDate,
      isOpenedDuringInterval = false,
      isSequenceOnly = false,
      isAnswered = false,
    }: CountSentByUserInDateRangeOptions
  ): Promise<number> {
    const query = this.entityRepository
      .createQueryBuilder('m')
      .select(isAnswered ? 'm.resource_thread_id' : 'm.id')
      .where('m.created_by_id = :userId', { userId })
      .andWhere('m.organization_id = :organizationId', { organizationId })
      .andWhere('m.status = :status', { status: MessageStatus.SENT })

    if (startDate) {
      query.andWhere('m.createdAt >= :startDate', { startDate })
    }
    if (endDate) {
      query.andWhere('m.createdAt <= :endDate', { endDate })
    }
    if (isOpenedDuringInterval) {
      query.andWhere(
        'EXISTS (SELECT 1 FROM unnest(m.openedDates) AS openDate WHERE openDate >= :startDate AND openDate <= :endDate)',
        { startDate, endDate }
      )
    }
    if (isSequenceOnly) {
      query.andWhere('m.sequence_step_id IS NOT NULL')
    }

    if (isAnswered) {
      query.andWhere('m.resource_id != m.resource_thread_id')
      query.groupBy('m.resource_thread_id')
    } else {
      query.andWhere('m.resource_id = m.resource_thread_id')
    }

    return query.getCount()
  }

  async countOpenedEmailsAndClickedEmailsBySequenceContactId(
    sequenceContactId: string
  ): Promise<{ mailsOpened: number; linksClicked: number }> {
    const result = await this.entityRepository
      .createQueryBuilder('m')
      .select(
        'SUM(COALESCE(array_length(m.opened_dates,1),0))::int',
        'countOpenedEmails'
      )
      .addSelect(
        'SUM(COALESCE(array_length(m.link_clicked_dates,1),0))::int',
        'countClickedLinks'
      )
      .where('m.sequenceContactId = :sequenceContactId', { sequenceContactId })
      .andWhere('m.status = :status', { status: MessageStatus.SENT })
      .groupBy('m.sequenceContactId')
      .getRawOne<{ countOpenedEmails: number; countClickedLinks: number }>()

    return {
      mailsOpened: result?.countOpenedEmails ?? 0,
      linksClicked: result?.countClickedLinks ?? 0,
    }
  }

  async findPreviousMessageSentBySequenceContact(
    sequenceContactId: string
  ): Promise<MessageModel | null> {
    const messageEntity = await this.entityRepository.findOne({
      where: {
        sequenceContactId,
        direction: MessageDirection.OUTBOUND,
        status: MessageStatus.SENT,
      },
      order: {
        messageDate: 'DESC',
      },
    })
    return messageEntity
      ? this.mapper.map(messageEntity, MessageEntity, MessageModel)
      : null
  }
}
