import { AutoMap } from '@automapper/classes'
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { UserEntity } from '../../../shared/infrastructure/entity/user.entity'
import { OrganizationInterface } from '../../../shared/domain/organization.interface'
import { TimableEntity } from '../../../shared/infrastructure/entity/timable.entity'
import { ContactInterface } from '../../../shared/domain/model/lead/contact.interface'
import { MessageStatus } from '../../domain/enum/message-status.enum'
import { MessageDirection } from '../../domain/enum/message-direction.enum'
import { MessageProvider } from '../../domain/enum/message-provider.enum'

export interface TaskInterface {
  id: string
}

export interface SequenceStepInterface {
  id: string
}

export interface SequenceContactInterface {
  id: string
}

// TODO: rename to mails
@Entity('messages')
@Index(['organizationId', 'externalId'], {
  unique: true,
  where: 'external_id IS NOT NULL',
})
@Index('IDX_messages_createdById_organizationId_status_createdAt', [
  'createdBy',
  'organizationId',
  'status',
  'createdAt',
])
@Index('IDX_messages_sequenceContactId_status', ['sequenceContactId'], {
  where: "status = 'SENT'",
})
@Index(['sequenceStepId', 'contactId'])
export class MessageEntity extends TimableEntity {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string

  @AutoMap(() => String)
  @Column('varchar')
  @Index()
  provider: MessageProvider

  @AutoMap(() => String)
  @Column('varchar')
  status: MessageStatus

  @AutoMap(() => [String])
  @Column('varchar', { array: true })
  to: string[] = []

  @AutoMap(() => [String])
  @Column('varchar', { array: true, nullable: true })
  cc: string[] = []

  @AutoMap(() => [String])
  @Column('varchar', { array: true, nullable: true })
  bcc: string[] = []

  @AutoMap()
  @Column('varchar', { nullable: true })
  from: string

  @AutoMap(() => String)
  @Column('varchar', { default: MessageDirection.OUTBOUND })
  direction: MessageDirection

  @AutoMap()
  @Column('varchar', { nullable: true })
  subject: string

  @AutoMap()
  @Column('varchar', { nullable: true })
  body: string

  @AutoMap()
  @Column('timestamptz', { nullable: true })
  messageDate?: Date

  @AutoMap()
  @Column('varchar', { nullable: true })
  @Index()
  resourceId: string

  @AutoMap()
  @Column('varchar', { nullable: true })
  @Index()
  resourceThreadId: string

  @AutoMap(() => UserEntity)
  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by_id' })
  createdBy: UserEntity

  @ManyToOne('contacts')
  @JoinColumn({ name: 'contact_id' })
  contact: ContactInterface

  @AutoMap()
  @Column({ name: 'contact_id', nullable: true })
  @Index()
  contactId?: string

  @ManyToOne('tasks')
  @JoinColumn({ name: 'task_id' })
  task: TaskInterface | null

  @AutoMap()
  @Column({ name: 'task_id', nullable: true })
  taskId: string | null

  @ManyToOne('sequences_step')
  @JoinColumn({ name: 'sequence_step_id' })
  sequenceStep: SequenceStepInterface | null

  @AutoMap()
  @Column({ name: 'sequence_step_id', nullable: true })
  @Index()
  sequenceStepId: string | null

  @ManyToOne('sequences_contact')
  @JoinColumn({ name: 'sequence_contact_id' })
  sequenceContact: SequenceContactInterface | null

  @AutoMap()
  @Column({ name: 'sequence_contact_id', nullable: true })
  sequenceContactId: string | null

  @ManyToOne('organizations')
  @JoinColumn({ name: 'organization_id' })
  organization: OrganizationInterface

  @AutoMap()
  @Column({ name: 'organization_id' })
  organizationId: string

  @AutoMap(() => [String])
  @Column('varchar', { array: true, nullable: true })
  attachments: string[]

  @AutoMap()
  @Column('uuid')
  @Index()
  trackingId: string

  @AutoMap()
  @Column('varchar', { nullable: true })
  encryptedTrackingId: string

  @AutoMap(() => Date)
  @Column('timestamptz', { array: true, nullable: true })
  openedDates: Date[]

  @AutoMap(() => Date)
  @Column('timestamptz', { array: true, nullable: true })
  linkClickedDates: Date[]

  @AutoMap()
  @Column('boolean', { nullable: false })
  addSignature: boolean

  @Column('varchar', { nullable: true })
  @AutoMap()
  externalId: string
}
