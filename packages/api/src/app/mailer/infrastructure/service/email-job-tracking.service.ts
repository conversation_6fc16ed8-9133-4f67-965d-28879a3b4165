import { Inject, Injectable } from '@nestjs/common'
import { JobTrackingService } from '../../../shared/infrastructure/service/job-tracking.service'
import {
  EmailJobTrackingContext,
  EmailJobTrackingModel,
} from '../../domain/model/email-job-tracking.model'
import { JobTrackingStatus } from '../../../shared/domain/job-tracking-status.enum'
import { MessageProvider } from '../../domain/enum/message-provider.enum'
import {
  EMAIL_JOB_TRACKING_REPOSITORY_INTERFACE,
  EmailJobTrackingRepositoryInterface,
} from '../../domain/repository/email-job-tracking.repository.interface'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { JobTrackingEvents } from '../../../shared/domain/event/event.enum'

@Injectable()
export class EmailJobTrackingService extends JobTrackingService<EmailJobTrackingModel> {
  constructor(
    @Inject(EMAIL_JOB_TRACKING_REPOSITORY_INTERFACE)
    protected readonly jobTrackingRepository: EmailJobTrackingRepositoryInterface,
    private readonly eventEmitter: EventEmitter2
  ) {
    super(jobTrackingRepository)
  }

  async create(
    organizationId: string,
    userId: string,
    internalJobId: string,
    provider: MessageProvider,
    email: string,
    context?: EmailJobTrackingContext
  ): Promise<void> {
    const jobTrackingModel = new EmailJobTrackingModel()
    jobTrackingModel.organizationId = organizationId
    jobTrackingModel.userId = userId
    jobTrackingModel.status = JobTrackingStatus.PENDING
    jobTrackingModel.internalJobId = internalJobId
    jobTrackingModel.provider = provider
    jobTrackingModel.email = email
    jobTrackingModel.context = context

    await this.jobTrackingRepository.create(jobTrackingModel)
  }

  async countScheduledJobsSince(email: string, since: Date): Promise<number> {
    const count = await this.jobTrackingRepository.countScheduledJobsSince(
      email,
      since
    )

    return count ?? 0
  }

  async cancelPendingJobBySequenceContactIds(
    sequenceContactIds: string[]
  ): Promise<void> {
    const jobTrackingModels =
      await this.jobTrackingRepository.findBySequenceContactIds(
        sequenceContactIds
      )

    // Exclude jobs that are only pending to keep daily counter up to date
    const jobTrackingModelsToCancel = jobTrackingModels.filter(job =>
      job.isPending()
    )

    if (!jobTrackingModelsToCancel.length) return

    const jobsByProvider = new Map<MessageProvider, string[]>()

    jobTrackingModelsToCancel.forEach(job => {
      const jobIds = jobsByProvider.get(job.provider) || []
      jobsByProvider.set(job.provider, [...jobIds, job.internalJobId])
    })

    jobsByProvider.forEach((jobIds, provider) => {
      this.eventEmitter.emit(JobTrackingEvents.JOB_CANCELED, {
        provider,
        jobIds,
      })
    })

    await this.jobTrackingRepository.deletePendingJobsByIds(
      jobTrackingModelsToCancel.map(job => job.id)
    )
  }
}
