import { Module } from '@nestjs/common'
import { MailerDnsSettingsService } from './application/service/dns-settings.service'
import { EMAIL_SETTINGS_REPOSITORY_INTERFACE } from './domain/model/email-settings-repository.interface'
import { TypeOrmModule } from '@nestjs/typeorm'
import { EmailSettingsEntity } from './infrastructure/entity/email-settings.entity'
import { CqrsModule } from '@nestjs/cqrs'
import { EmailSettingsRepository } from './infrastructure/repository/email-settings.respository'
import { EmailSettingsProfile } from './application/profile/email-settings.profile'
import { UpdateEmailSettingsHandler } from './application/command/update-email-settings/update-email-settings.handler'
import { IncrementRampupValueCronService } from './application/service/increment-rampup-value-cron.service'
import { DailyLimitService } from './application/service/daily-limit.service'
import { UnsubscribeTokenService } from './application/service/unsubscribe-token.service'
import { UnsubscribedEmailEntity } from './infrastructure/entity/unsubscribed-email.entity'
import { UnsubscribedEmailRepository } from './infrastructure/repository/unsubscribed-email.repository'
import { UNSUBSCRIBED_EMAIL_REPOSITORY_INTERFACE } from './domain/model/unsubscribed-email-repository.interface'
import { CreateUnsubscribedEmailHandler } from './application/command/unsubscribed-email/create-unsubscribed-email.handler'
import { UnsubscribedEmailProfile } from './application/profile/unsubscribed-email.profile'
import { FindEmailSettingsByOrganizationAndAllowedDomainHandler } from './application/query/find-email-settings/find-by-organization-and-allowed-domain.handler'
import { DeleteEmailSettingsByAllowedDomainHandler } from './application/command/delete-email-settings/delete-email-settings-by-allowed-domain.handler'
import { FindUnsubscribeLinkQueryHandler } from './application/query/unsubscribe-link/find-unsubscribe-link.handler'
import { CreateEmailBouncedHandler } from './application/command/create-email-bounced/create-email-bounced.handler'
import { EmailBouncedProfile } from './application/profile/email-bounced.profile'
import { EmailBouncedEntity } from './infrastructure/entity/email-bounced.entity'
import { EMAIL_BOUNCED_REPOSITORY_INTERFACE } from './domain/interface/email-bounced-repository.interface'
import { EmailBouncedRepository } from './infrastructure/repository/email-bounced.respository'
import { FindEmailBouncedHandler } from './application/query/find-email-bounced/find-by-email.handler'
import { FindAllowedDomainByOrganizationHandler } from './application/query/find-allowed-domain/find-allowed-domain-by-organization.handler'
import { FindAllowedDomainByOrganizationAndNameHandler } from './application/query/find-allowed-domain/find-allowed-domain-by-organization-name.handler'
import { FindAllowedDomainByIdHandler } from './application/query/find-allowed-domain/find-allowed-domain-by-id.handler'
import { CreateAllowedDomainHandler } from './application/command/allowed-domain/create-allowed-domain/create-allowed-domain.handler'
import { DeleteAllowedDomainHandler } from './application/command/allowed-domain/delete-allowed-domain/delete-allowed-domain.handler'
import { AllowedDomainProfile } from './application/profile/allowed-domain.profile'
import { ALLOWED_DOMAIN_REPOSITORY_INTERFACE } from './domain/interface/repository/allowed-domain-repository.interface'
import { AllowedDomainRepository } from './infrastructure/repository/allowed-domain.repository'
import { AllowedDomainEntity } from './infrastructure/entity/allowed-domain.entity'
import { MessageEntity } from './infrastructure/entity/message.entity'
import { MESSAGE_REPOSITORY_INTERFACE } from './domain/interface/repository/message-repository.interface'
import { MessageRepository } from './infrastructure/repository/message.repository'
import { FindMessageHandler } from './application/query/find-message/find-message.handler'
import { FindMessageByStepHandler } from './application/query/find-message-by-step/find-message-by-step.handler'
import { FindMessageByTaskHandler } from './application/query/find-message-by-task/find-message-by-task.handler'
import { FindMailsRepliedBySequenceIdHandler } from './application/query/find-mails-replied/find-mails-replied-by-sequence-id.handler'
import { FindMailsTotalBySequenceIdHandler } from './application/query/find-mails-total/find-mails-total-by-sequence-id.handler'
import { ListThreadHandler } from './application/query/list-thread/list-thread.handler'
import { UpdateMessageByTrackingHandler } from './application/command/update-message/update-message-by-tracking.handler'
import { ProviderService } from './application/service/provider.service'
import { UpdateMessageHandler } from './application/command/update-message/update-message.handler'
import { CreateMessageHandler } from './application/command/create-message/create-message.handler'
import { SendMessageHandler } from './application/command/send-message/send-message.handler'
import { TrackingMessageService } from './application/service/trackingMessage.service'
import { FindMessageByResourceIdHandler } from './application/query/find-message-by-resource-id/find-message-by-resource-id.handler'
import { UpdateMessageByParamsHandler } from './application/command/update-message/update-message-by-params.handler'
import { ReplyMessageHandler } from './application/command/reply-message/reply-message.handler'
import { ListThreadByThreadsHandler } from './application/query/list-thread/list-thread-by-threads.handler'
import { CountContactsBouncedBySequenceHandler } from './application/query/count-contacts-bounced-by-sequence/count-contacts-bounced-by-sequence.handler'
import { MailLinkClickedListener } from './application/listener/mail-link-clicked.listener'
import { MailOpenedListener } from './application/listener/mail-opened.listener'
import { MessageProfile } from './application/profile/message.profile'
import { FindInitialMessageByStepAndContactHandler } from './application/query/find-message-by-step-and-contact-id/find-initial-message-by-step-and-contact.handler'
import { FindResourceByResourceThreadIdHandler } from './application/query/find-resource-by-resource-thread-id/find-resource-by-resource-thread-id.handler'
import { FindMessagesHandler } from './application/query/find-messages/find-messages.handler'
import { DnsSettingsEntity } from './infrastructure/entity/dns-settings.entity'
import { DNS_SETTINGS_REPOSITORY_INTERFACE } from './domain/interface/repository/dns-settings-repository.interface'
import { DnsSettingsRepository } from './infrastructure/repository/dns-settings.repository'
import { UpdateDnsSettingsHandler } from './application/command/update-dns-settings/update-dns-settings.handler'
import { DnsSettingsProfile } from './application/profile/dns-settings.profile'
import { FindDnsSettingsHandler } from './application/query/find-dns-settings/find-dns-settings.handler'
import { RefreshDnsSettingsCron } from './application/cron/refresh-dns-settings.cron'
import { AttachmentService } from './application/service/attachement.service'
import { DeleteMessageHandler } from './application/command/delete-email/delete-message.handler'
import { EmailBouncedToProcessProfile } from './application/profile/email-bounced-to-process.profile'
import { EmailBouncedToProcessEntity } from './infrastructure/entity/email-bounced-to-process.entity'
import { EMAIL_BOUNCED_TO_PROCESS_REPOSITORY_INTERFACE } from './domain/interface/repository/email-bounced-to-process.interface'
import { EmailBouncedToProcessRepository } from './infrastructure/repository/email-bounced-to-process.repository'
import { CreateEmailBouncedToProcessHandler } from './application/command/create-email-bounced-to-process/create-email-bounced-to-process.handler'
import { FindWeeklyRecapMailerMetricsHandler } from './application/query/find-weekly-recap-mailer-metrics/find-weekly-recap-mailer-metrics.handler'
import { CountSentEmailsHandler } from './application/query/count-sent-emails/count-sent-emails.handler'
import { CountAnsweredEmailsHandler } from './application/query/count-answered-emails/count-answered-emails.handler'
import { CountSequenceEmailsHandler } from './application/query/count-sequence-emails/count-sequence-emails.handler'
import { BouncedEmailService } from './application/service/bounced-email.service'
import { CountOpenedEmailsHandler } from './application/query/count-opened-emails/count-opened-emails.handler'
import { GetMessagesKpisBySequenceHandler } from './application/query/get-messages-kpis-by-sequence/get-messages-kpis-by-sequence.handler'
import { GetMessagesKpisBySequenceContactHandler } from './application/query/get-messages-kpis-by-sequence-contact/get-messages-kpis-by-sequence-contact.handler'
import { FindPreviousMessageBySequenceContactHandler } from './application/query/find-previous-message-by-sequence-contact/find-previous-message-by-sequence-contact.handler'
import { EmailJobTrackingService } from './infrastructure/service/email-job-tracking.service'
import { EmailJobTrackingEntity } from './infrastructure/entity/email-job-tracking.entity'
import { EMAIL_JOB_TRACKING_REPOSITORY_INTERFACE } from './domain/repository/email-job-tracking.repository.interface'
import { EmailJobTrackingRepository } from './infrastructure/repository/email-job-tracking.repository'
import { EmailJobTrackingProfile } from './application/profile/email-job-tracking.profile'
import { SequenceContactDeletedListener } from './application/listener/sequence-contact-deleted.listener'

const services = [
  DailyLimitService,
  MailerDnsSettingsService,
  IncrementRampupValueCronService,
  ProviderService,
  TrackingMessageService,
  UnsubscribeTokenService,
  AttachmentService,
  BouncedEmailService,
  EmailJobTrackingService,
]

const commandHandlers = [
  CreateAllowedDomainHandler,
  DeleteAllowedDomainHandler,

  UpdateEmailSettingsHandler,
  DeleteEmailSettingsByAllowedDomainHandler,
  CreateUnsubscribedEmailHandler,
  CreateEmailBouncedHandler,

  CreateMessageHandler,
  UpdateMessageHandler,
  DeleteMessageHandler,
  UpdateMessageByTrackingHandler,
  UpdateMessageByParamsHandler,
  ReplyMessageHandler,
  SendMessageHandler,

  UpdateDnsSettingsHandler,
  CreateEmailBouncedToProcessHandler,

  FindWeeklyRecapMailerMetricsHandler,
]

const queryHandlers = [
  CountContactsBouncedBySequenceHandler,
  FindEmailSettingsByOrganizationAndAllowedDomainHandler,
  FindUnsubscribeLinkQueryHandler,
  FindEmailBouncedHandler,

  FindAllowedDomainByOrganizationHandler,
  FindAllowedDomainByOrganizationAndNameHandler,
  FindAllowedDomainByIdHandler,

  FindMessageHandler,
  FindMessageByResourceIdHandler,
  FindMessageByStepHandler,
  FindInitialMessageByStepAndContactHandler,
  FindMessageByTaskHandler,
  FindMailsRepliedBySequenceIdHandler,
  FindMailsTotalBySequenceIdHandler,
  FindResourceByResourceThreadIdHandler,
  ListThreadHandler,
  ListThreadByThreadsHandler,
  FindMessagesHandler,

  FindDnsSettingsHandler,

  CountSentEmailsHandler,
  CountAnsweredEmailsHandler,
  CountSequenceEmailsHandler,
  CountOpenedEmailsHandler,
  GetMessagesKpisBySequenceContactHandler,
  FindPreviousMessageBySequenceContactHandler,
]

const mapperProfiles = [
  EmailSettingsProfile,
  UnsubscribedEmailProfile,
  EmailBouncedProfile,
  MessageProfile,
  AllowedDomainProfile,
  DnsSettingsProfile,
  EmailBouncedToProcessProfile,
  EmailJobTrackingProfile,
]

const listeners = [
  MailLinkClickedListener,
  MailOpenedListener,
  SequenceContactDeletedListener,
]

const entities = [
  AllowedDomainEntity,
  EmailSettingsEntity,
  UnsubscribedEmailEntity,
  EmailBouncedEntity,
  MessageEntity,
  DnsSettingsEntity,
  EmailBouncedToProcessEntity,
  EmailJobTrackingEntity,
]

const crons = [RefreshDnsSettingsCron]

@Module({
  imports: [TypeOrmModule.forFeature(entities), CqrsModule],
  providers: [
    {
      provide: MESSAGE_REPOSITORY_INTERFACE,
      useClass: MessageRepository,
    },
    {
      provide: EMAIL_SETTINGS_REPOSITORY_INTERFACE,
      useClass: EmailSettingsRepository,
    },
    {
      provide: UNSUBSCRIBED_EMAIL_REPOSITORY_INTERFACE,
      useClass: UnsubscribedEmailRepository,
    },
    {
      provide: EMAIL_BOUNCED_REPOSITORY_INTERFACE,
      useClass: EmailBouncedRepository,
    },
    {
      provide: ALLOWED_DOMAIN_REPOSITORY_INTERFACE,
      useClass: AllowedDomainRepository,
    },
    {
      provide: DNS_SETTINGS_REPOSITORY_INTERFACE,
      useClass: DnsSettingsRepository,
    },
    {
      provide: EMAIL_BOUNCED_TO_PROCESS_REPOSITORY_INTERFACE,
      useClass: EmailBouncedToProcessRepository,
    },
    {
      provide: EMAIL_JOB_TRACKING_REPOSITORY_INTERFACE,
      useClass: EmailJobTrackingRepository,
    },
    ...commandHandlers,
    ...queryHandlers,
    ...mapperProfiles,
    ...services,
    ...listeners,
    ...crons,
    GetMessagesKpisBySequenceHandler,
  ],
  exports: [
    MailerDnsSettingsService,
    ProviderService,
    TrackingMessageService,
    DailyLimitService,
    AttachmentService,
    BouncedEmailService,
    EmailJobTrackingService,
  ],
})
export class MailerModule {}
