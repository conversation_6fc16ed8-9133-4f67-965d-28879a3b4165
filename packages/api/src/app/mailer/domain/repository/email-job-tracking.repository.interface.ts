import { JobTrackingRepositoryInterface } from '../../../shared/domain/repository/job-tracking.repository.interface'
import { EmailJobTrackingModel } from '../model/email-job-tracking.model'

export const EMAIL_JOB_TRACKING_REPOSITORY_INTERFACE =
  'EMAIL_JOB_TRACKING_REPOSITORY_INTERFACE'

export interface EmailJobTrackingRepositoryInterface
  extends JobTrackingRepositoryInterface<EmailJobTrackingModel> {
  create(emailJobTrackingModel: EmailJobTrackingModel): Promise<void>
  countScheduledJobsSince(email: string, since: Date): Promise<number>
  findBySequenceContactIds(
    sequenceContactIds: string[]
  ): Promise<EmailJobTrackingModel[]>
}
