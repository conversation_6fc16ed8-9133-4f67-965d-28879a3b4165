import { AutoMap } from '@automapper/classes'
import { ApiProperty } from '@nestjs/swagger'
import { Exclude, Expose } from 'class-transformer'
import { EXPOSE_GROUP } from '../../../shared/globals/expose-group.enum'
import { BaseModel } from '../../../shared/domain/model/base.model'
import { MessageStatus } from '../enum/message-status.enum'
import { MessageDirection } from '../enum/message-direction.enum'
import {
  ModelPermission,
  OWNER,
} from '../../../shared/domain/model-permission.interface'
import {
  MANAGER_HIERARCHY,
  USER_HIERARCHY,
} from '../../../shared/domain/organization-role.enum'
import { UserModel } from '../../../shared/domain/model/user.model'
import { v4 as uuidv4 } from 'uuid'
import { MessagePermission } from '../enum/message.permission'
import { MessageProvider } from '../enum/message-provider.enum'

export class AttachmentModel {
  @ApiProperty()
  @AutoMap()
  name: string

  @ApiProperty()
  @Exclude()
  @AutoMap()
  data?: string

  @ApiProperty()
  @Exclude()
  @AutoMap()
  url?: string

  @ApiProperty()
  @Exclude()
  @AutoMap()
  size?: number

  constructor(name: string) {
    this.name = name
  }
}

export class MessageModel extends BaseModel implements ModelPermission {
  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  id: string

  @ApiProperty({ enum: MessageProvider })
  @AutoMap(() => String)
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  provider: MessageProvider

  @ApiProperty({ enum: MessageStatus })
  @AutoMap(() => String)
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  status: MessageStatus

  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  isDraft: boolean

  @ApiProperty()
  @AutoMap(() => [String])
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  to: string[] = []

  @ApiProperty()
  @AutoMap(() => [String])
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  cc: string[] = []

  @ApiProperty()
  @AutoMap(() => [String])
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  bcc: string[] = []

  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  from: string

  @AutoMap(() => String)
  @ApiProperty({ enum: MessageDirection })
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  direction: MessageDirection

  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  subject: string

  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  body: string

  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  addSignature: boolean

  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  messageDate?: Date

  @ApiProperty()
  @AutoMap()
  @Exclude()
  resourceId: string

  @ApiProperty()
  @AutoMap()
  @Exclude()
  resourceThreadId: string

  @ApiProperty()
  @AutoMap(() => UserModel)
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  createdBy: UserModel

  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  contactId?: string | null

  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  taskId: string | null

  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  sequenceStepId: string | null

  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  sequenceContactId: string | null

  @ApiProperty()
  @AutoMap()
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  organizationId: string

  @ApiProperty()
  @AutoMap(() => [AttachmentModel])
  attachments: AttachmentModel[] = []

  @ApiProperty()
  @AutoMap()
  @Exclude()
  trackingId: string = uuidv4()

  @ApiProperty()
  @AutoMap()
  @Exclude()
  encryptedTrackingId: string

  @AutoMap(() => [Date])
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  openedDates: Date[]

  @AutoMap(() => [Date])
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  linkClickedDates: Date[]

  @AutoMap()
  @ApiProperty()
  externalId?: string

  isFromSequence?(): boolean {
    return (
      this.sequenceStepId !== null &&
      this.sequenceContactId !== null &&
      this.direction === MessageDirection.OUTBOUND
    )
  }

  getPermission?(): { [key: string]: string[] } {
    return {
      [MessagePermission.CREATE]: USER_HIERARCHY,
      [MessagePermission.VIEW]: [OWNER, ...MANAGER_HIERARCHY],
      [MessagePermission.UPDATE]: [OWNER, ...MANAGER_HIERARCHY],
    }
  }
}
