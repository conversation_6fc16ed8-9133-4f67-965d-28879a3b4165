import { Inject } from '@nestjs/common'
import { Que<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ueryHand<PERSON> } from '@nestjs/cqrs'

import { FindAllOnboardingStepsQuery } from './find-all-onboarding-steps.query'
import {
  ONBOARDING_STEP_REPOSITORY_INTERFACE,
  OnboardingStepRepositoryInterface,
} from '../../../domain/onboarding-step-repository.interface'
import { StepModel } from '../../../domain/model/step.model'
import { TryCatchLogger } from '../../../../shared/domain/decorator/try-catch-logger.decorator'
import { LogFeature } from '../../../../shared/logger.service'

@QueryHandler(FindAllOnboardingStepsQuery)
export class FindAllOnboardingStepsHandler
  implements IQueryHandler<FindAllOnboardingStepsQuery>
{
  constructor(
    @Inject(ONBOARDING_STEP_REPOSITORY_INTERFACE)
    private readonly onboardingStepRepository: OnboardingStepRepositoryInterface
  ) {}

  @TryCatchLogger({
    feature: LogFeature.ONBOARDING,
    message: 'Failed to find all onboarding steps',
  })
  async execute(): Promise<StepModel[]> {
    return this.onboardingStepRepository.findAll()
  }
}
