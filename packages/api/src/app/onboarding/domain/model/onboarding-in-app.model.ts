import { AutoMap } from '@automapper/classes'
import { ApiProperty } from '@nestjs/swagger'
import { Expose } from 'class-transformer'
import { EXPOSE_GROUP } from '../../../shared/globals/expose-group.enum'
import { OrganizationModel } from '../../../organization/domain/organization.model'
import { StepModel } from './step.model'

export class OnboardingInAppModel {
  @AutoMap()
  @ApiProperty()
  @Expose({ groups: [EXPOSE_GROUP.ME] })
  id: string

  @AutoMap()
  @ApiProperty()
  @Expose({ groups: [EXPOSE_GROUP.ME] })
  isCompleted: boolean

  @AutoMap()
  @ApiProperty()
  @Expose({ groups: [EXPOSE_GROUP.ME] })
  isUserNotified: boolean

  @AutoMap()
  @Expose({ groups: [EXPOSE_GROUP.ME] })
  createdAt: Date

  @AutoMap()
  @Expose({ groups: [EXPOSE_GROUP.ME] })
  updatedAt: Date

  @AutoMap(() => OrganizationModel)
  organization: OrganizationModel

  @AutoMap(() => StepModel)
  step: StepModel

  constructor(partial: Partial<OnboardingInAppModel>) {
    Object.assign(this, partial)
  }
}
