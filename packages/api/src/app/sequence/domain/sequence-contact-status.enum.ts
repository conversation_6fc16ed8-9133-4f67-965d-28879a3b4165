import {
  SequenceContactStatus,
  getSequenceContactErrorStatus,
} from '@getheroes/shared'

export function sequenceContactStatusDone(): string[] {
  return [
    SequenceContactStatus.DONE,
    SequenceContactStatus.CONTACT_ANSWERED,
    SequenceContactStatus.CONTACT_UNSUBSCRIBED,
    SequenceContactStatus.CONTACT_ARCHIVED,
  ]
}

export function sequenceContactStatusNotDone(): string[] {
  return [SequenceContactStatus.RUNNING, SequenceContactStatus.SCHEDULE]
}

export function sequenceContactStatusInErrorDueToMissingAttributes(): string[] {
  return [
    SequenceContactStatus.ERROR_MISSING_VARIABLE,
    SequenceContactStatus.ERROR_EMAIL_EMPTY,
    SequenceContactStatus.ERROR_LINKEDIN_URL_EMPTY,
    SequenceContactStatus.SDR_EMAIL_NOT_SYNC,
    SequenceContactStatus.SDR_LINKEDIN_NOT_SYNC,
    SequenceContactStatus.CONTACT_UNASSIGNED,
    SequenceContactStatus.ERROR,
    SequenceContactStatus.SDR_LINKEDIN_INVALID_COOKIE,
  ]
}
export function sequenceContactIsInError(status: string): boolean {
  return getSequenceContactErrorStatus().includes(status)
}
