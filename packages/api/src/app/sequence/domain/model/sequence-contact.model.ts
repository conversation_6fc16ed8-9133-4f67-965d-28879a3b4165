import { AutoMap } from '@automapper/classes'
import { ApiProperty } from '@nestjs/swagger'
import { Expose } from 'class-transformer'
import {
  SequenceContactReviewStatus,
  SequenceContactStatus,
  SequenceStepType,
} from '@getheroes/shared'
import { EXPOSE_GROUP } from '../../../shared/globals/expose-group.enum'
import { SequenceModel } from './sequence.model'
import { SequenceStepModel } from './sequence-step.model'
import { UserModel } from '../../../shared/domain/model/user.model'
import { SequenceStepStatus } from '../enum/sequence-step-status.enum'
import { ContactInterface } from '../../../shared/domain/model/lead/contact.interface'
import { sequenceContactStatusDone } from '../sequence-contact-status.enum'
import { SequenceContactReview } from '../value-object/sequence-contact-review'

export class SequenceContactModel {
  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap()
  @ApiProperty()
  id: string

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap()
  @ApiProperty()
  contactId: string

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap(() => Object)
  @ApiProperty()
  contact: ContactInterface

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap(() => UserModel)
  @ApiProperty()
  createdBy: UserModel

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap()
  @ApiProperty()
  customEmail: string | null

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap()
  @ApiProperty()
  sequenceId: string

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap(() => SequenceModel)
  @ApiProperty()
  sequence: SequenceModel

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap()
  @ApiProperty()
  stepId: string | null

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap(() => SequenceStepModel)
  @ApiProperty()
  step: SequenceStepModel | null

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC, EXPOSE_GROUP.SEQUENCES_WITH_STEPS],
  })
  @AutoMap(() => String)
  @ApiProperty()
  status: SequenceContactStatus | null

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap()
  @ApiProperty()
  createdAt: Date

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap()
  @ApiProperty()
  updatedAt: Date

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap()
  @ApiProperty()
  sequenceCreatedAt: Date | null

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap()
  @ApiProperty()
  nextStepId: string

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap(() => SequenceStepModel)
  @ApiProperty()
  nextStep: SequenceStepModel | null

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap(() => String)
  @ApiProperty()
  nextStepType: SequenceStepType | null

  @Expose({
    groups: [EXPOSE_GROUP.PUBLIC],
  })
  @AutoMap()
  @ApiProperty()
  nextStepDate: Date | null

  @Expose({ groups: [EXPOSE_GROUP.PUBLIC, EXPOSE_GROUP.SEQUENCES_WITH_STEPS] })
  @AutoMap(() => SequenceContactReview)
  @ApiProperty()
  review: SequenceContactReview | null

  public getNextStep(): SequenceStepModel | null {
    if (this.status === SequenceContactStatus.DONE) return null
    return this.nextStep
  }

  public getStepStatus(): SequenceStepStatus {
    const nextStep = this.getNextStep()
    if (!nextStep || this.step?.order < this.nextStep?.order) {
      return SequenceStepStatus.PAST
    } else if (this.step?.order > this.nextStep?.order) {
      return SequenceStepStatus.TOCOME
    } else {
      return SequenceStepStatus.NEXT
    }
  }

  public getNextExecutedStepOrder(): number {
    if (!this.nextStep) {
      return 0
    }

    if (this.status === SequenceContactStatus.DONE) {
      return null
    } else if (
      [
        SequenceContactStatus.ERROR,
        SequenceContactStatus.CONTACT_UNSUBSCRIBED,
      ].includes(this.status)
    ) {
      return this.step.order
    } else {
      return this.nextStep.order
    }
  }

  public isSkippable(): boolean {
    const skippableStepTypes = [
      SequenceStepType.CALL,
      SequenceStepType.MANUAL_EMAIL,
    ]
    return (
      !sequenceContactStatusDone().includes(this.status) &&
      (this.status !== SequenceContactStatus.SCHEDULE ||
        skippableStepTypes.includes(this.nextStepType))
    )
  }

  public isUnassigned(): boolean {
    return this.status === SequenceContactStatus.CONTACT_UNASSIGNED
  }

  public isInReview(): boolean {
    return this.review.status === SequenceContactReviewStatus.PENDING
  }

  getSequenceStepId(): string {
    if (this.status === SequenceContactStatus.CONTACT_BOUNCED) {
      return this.stepId
    } else {
      return this.nextStepId
    }
  }

  getSequenceStepType(): SequenceStepType {
    if (this.status === SequenceContactStatus.CONTACT_BOUNCED) {
      return this.step?.type
    } else {
      return this.nextStepType
    }
  }
}
