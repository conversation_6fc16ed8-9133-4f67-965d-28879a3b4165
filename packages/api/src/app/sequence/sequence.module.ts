import { Module } from '@nestjs/common'
import { CqrsModule } from '@nestjs/cqrs'
import { TypeOrmModule } from '@nestjs/typeorm'
import { SequenceProfile } from './application/profile/sequence.profile'
import { SEQUENCE_REPOSITORY_INTERFACE } from './domain/repository/sequence-repository.interface'
import { SequenceEntity } from './infrastructure/entity/sequence.entity'
import { SequenceRepository } from './infrastructure/repository/sequence.repository'
import { CreateSequenceHandler } from './application/command/create-sequence/create-sequence.handler'
import { FindSequenceHandler } from './application/query/find-sequence/find-sequence.handler'
import { DeleteSequenceHandler } from './application/command/delete-sequence/delete-sequence.handler'
import { PaginationService } from '../shared/infrastructure/service/pagination.service'
import { UpdateSequenceHandler } from './application/command/update-sequence/update-sequence.handler'
import { CreateSequenceContactHandler } from './application/command/create-sequence-contact/create-sequence-contact.handler'
import { CreateSequenceContactFromImportHandler } from './application/command/create-sequence-contact-from-import/create-sequence-contact-from-import.handler'
import { SequenceStepRepository } from './infrastructure/repository/sequence-step.repository'
import { SequenceContactRepository } from './infrastructure/repository/sequence-contact.repository'
import { SEQUENCE_CONTACT_REPOSITORY_INTERFACE } from './domain/repository/sequence-contact-repository.interface'
import { SEQUENCE_STEP_REPOSITORY_INTERFACE } from './domain/repository/sequence-step-repository.interface'
import { SequenceContactEntity } from './infrastructure/entity/sequence-contact.entity'
import { SequenceStepEntity } from './infrastructure/entity/sequence-step.entity'
import { FindSequenceContactHandler } from './application/query/find-sequence-contact/find-sequence-contact.handler'
import { FindSequenceByOrganizationHandler } from './application/query/find-sequence-by-organization/find-sequence-by-organization.handler'
import { DeleteSequenceStepHandler } from './application/command/delete-sequence-step/delete-sequence-step.handler'
import { UpdateSequenceStepHandler } from './application/command/update-sequence-step/update-sequence-step.handler'
import { CreateSequenceStepHandler } from './application/command/create-sequence-step/create-sequence-step.handler'
import { FindSequenceStepHandler } from './application/query/find-sequence-step/find-sequence-step.handler'
import { FindSequenceStepsBySequenceHandler } from './application/query/find-sequence-steps-by-sequence/find-sequence-steps-by-sequence.handler'
import { FindSequenceStepBySequenceHandler } from './application/query/find-sequence-step-by-sequence/find-sequence-step-by-sequence.handler'
import { ConfigDeleteLeadStatusService } from './application/service/config-delete-lead-status.service'
import { ConfigScheduleService } from './application/service/config-schedule.service'
import { DeleteSequenceContactHandler } from './application/command/delete-sequence-contact/delete-sequence-contact.handler'
import { FindSequenceCron } from './application/cron/find-sequence.cron'
import { BullModule } from '@nestjs/bull'
import { SEQUENCE_HANDLER_QUEUE } from './infrastructure/global'
import { SequenceLaunchConsumer } from './application/consumer/sequence-launch.consumer'
import { DailyLimitService } from '../mailer/application/service/daily-limit.service'
import { MailSentListener } from './application/listener/mail-sent.listener'
import { MailAnsweredListener } from './application/listener/mail-answered.listener'
import { CleanSequenceContactCron } from './application/cron/clean-sequence-contact.cron'
import { ContactArchivedListener } from './application/listener/contact-archived.listener'
import { FindSequenceContactBySequenceAndEmailHandler } from './application/query/find-sequence-contact-by-sequence-and-email/find-sequence-contact-by-sequence-and-email.handler'
import { UpdateSequenceKpiHandler } from './application/command/update-sequence-kpi/update-sequence-kpi.handler'
import { ScheduleEmailService } from './application/service/schedule-email.service'
import { LeadUnassignedListener } from './application/listener/lead/lead-unassigned.listener'
import { LeadAssignedListener } from './application/listener/lead/lead-assigned.listener'
import { SequenceContactUnsubscribedListener } from './application/listener/sequence-contact-unsubscribed.listener'
import { FindSequencesKpisHandler } from './application/query/find-sequences-kpis/find-sequences-kpis.handler'
import { FindSequenceContactsErrorsHandler } from './application/query/find-sequence-contacts-errors/find-sequence-contacts-errors.handler'
import { ContactOpenedListener } from './application/listener/activity/contact-opened.listener'
import { ContactClickedListener } from './application/listener/activity/contact-clicked.listener'
import { SequenceContactAddedListener } from './application/listener/activity/contact-added.listener'
import { MailSequenceAnsweredListener } from './application/listener/kpis/mail-sequence-answered.listener'
import { MailSequenceSentListener } from './application/listener/kpis/mail-sequence-sent.listener'
import { ContactStepValidatedListener } from './application/listener/activity/contact-step-validated.listener'
import { ActivityContactBouncedListener } from './application/listener/activity/activity-contact-bounced.listener'
import { SequenceContactBouncedListener } from './application/listener/sequence/sequence-contact-bounced.listener'
import { MailIntegratedListener } from './application/listener/mail-integrated.listener'
import { FindSequenceContactBySequenceHandler } from './application/query/find-sequence-contact-by-sequence/find-sequence-contact-by-sequence.handler'
import { UpdateSequenceContactHandler } from './application/command/update-sequence-contact/update-sequence-contact.handler'
import { FindSequenceContactsNotInOtherSequenceHandler } from './application/query/find-sequence-contacts-not-in-other-sequence/find-sequence-contacts-not-in-other-sequence.handler'
import { FindSequenceWithStepsByContactHandler } from './application/query/find-sequence-with-steps-by-contact/find-sequence-with-steps-by-contact.handler'
import { ContactErrorResolvedListener } from './application/listener/contact-error-resolved.listener'
import { FindSequenceContactErrorHandler } from './application/query/find-sequence-contact-error/find-sequence-contact-error.handler'
import { ProviderService } from '../mailer/application/service/provider.service'
import { SequenceContactErrorsService } from './application/service/sequence-contact-errors.service'
import { FindSequenceContactBySequenceAndContactIdHandler } from './application/query/find-sequence-contact-by-sequence-and-contact-id/find-sequence-contact-by-sequence-and-contact-id.handler'
import { SequenceCreatedListener } from './application/listener/sequence/sequence-created.listener'
import { FindSequencesByOrganizationHandler } from './application/query/find-sequences-by-organization/find-sequences-by-organization.handler'
import { FindSequenceContactPastActivitiesBySequenceAndContactsHandler } from './application/query/activities/find-sequence-past-activities-by-sequence-and-contacts/find-sequence-past-activities-by-sequence-and-contacts.handler'
import { StepExecutorFactory } from './application/consumer/step-executor.factory'
import { UpdateSequenceContactStatusHandler } from './application/command/update-sequence-contact-status /update-sequence-contact-satuts.handler'
import { LinkedinVisitProfileFailedListener } from './application/listener/linkedin/linkedin-visit-profil-failed.listener'
import { LinkedinVisitProfileSucceededListener } from './application/listener/linkedin/linkedin-visit-profil-succeeded.listener'
import { SequenceStepSucceededListener } from './application/listener/sequence-step-succeeded.listener'
import { LinkedinSendInvitationFailedListener } from './application/listener/linkedin/linkedin-send-invitation-failed.listener'
import { LinkedinMessageSentListener } from './application/listener/linkedin/linkedin-message-sent.listener'
import { LinkedinMessageReceivedListener } from './application/listener/linkedin/linkedin-message-received.listener'
import { LinkedinSendMessageFailedListener } from './application/listener/linkedin/linkedin-send-message-failed.listener'
import { LinkedinSendMessageSucceededListener } from './application/listener/linkedin/linkedin-send-message-succeeded.listener'
import { LinkedinNewConnectionRetreivedListener } from './application/listener/linkedin/linkedin-new-connection-retreived.listener'
import { CountSequencesHandler } from './application/query/count-sequences/count-sequences.handler'
import { DisableExtraSequencesHandler } from './application/command/disable-extra-sequences/disable-extra-sequences.handler'
import { LinkedinSendInvitationSucceededListener } from './application/listener/linkedin/linkedin-send-invitation-succeeded.listener'
import { LinkedinCookieRefreshedListener } from './application/listener/linkedin/linkedin-cookie-refreshed.listener'
import { MailErrorListener } from './application/listener/mail-error.listener'
import { IntegrationCaptainDataModule } from '../integration/captain-data/integration-captain-data.module'
import { MailerModule } from '../mailer/mailer.module'
import { FindSequenceContactByIdHandler } from './application/query/find-sequence-contact/find-sequence-contact-by-id.handler'
import { GetWeeklyRecapSequencesMetricsHandler } from './application/query/get-weekly-recap-sequences-metrics/get-weekly-recap-sequences-metrics.handler'
import { CreateSequenceActivityHandler } from './application/command/create-sequence-activity/create-sequence-activity.handler'
import { SEQUENCE_ACTIVITY_REPOSITORY_INTERFACE } from './domain/repository/sequence-activity-repository.interface'
import { SequenceActivityRepository } from './infrastructure/repository/sequence-activity.repository'
import { SequenceActivityEntity } from './infrastructure/entity/sequence-activity.entity'
import { FindSequenceActivityBySequenceContactAndStepAndTypeHandler } from './application/query/activities/find-sequence-activity-by-step-and-contact-and-type/find-sequence-activity-by-step-and-sequence-contact-and-type.handler'
import { FindSequenceContactActivityHandler } from './application/query/activities/find-sequence-contact-activity/find-sequence-contact-activity.handler'
import { SkipSequenceContactHandler } from './application/command/skip-sequence-contact/skip-sequence-contact.handler'
import { SequenceActivityService } from './application/service/sequence-activity.service'
import { SequenceContactUpdatedListener } from './application/listener/sequence-contact-updated.listener'
import { ScheduleLinkedinService } from './application/service/schedule-linkedin.service'
import { LeadUpdatedListener } from './application/listener/lead/lead-updated.listener'
import { CompanyUpdatedListener } from './application/listener/lead/company-updated.listener'
import { FindSequenceContactPaginatedHandler } from './application/query/find-sequence-contact/find-sequence-contact-paginated.handler'
import { ReviewSequenceContactHandler } from './application/command/review-sequence-contact/review-sequence-contact.handler'
import { SequenceContactReviewService } from './application/service/sequence-contact-review.service'
import { UpdateSequenceActivityHandler } from './application/command/update-sequence-activity/update-sequence-activity.handler'
import { TaskDoneListener } from './application/listener/task/task-done.listener'
import { FindSequenceContactActivityByTypeAndResourceHandler } from './application/query/activities/find-sequence-contact-activity-by-type-and-resource/find-sequence-contact-activity-by-type-and-resource.query.handler'
import { CallCreatedListener } from './application/listener/call/call-created.listener'
import { CallUpdatedListener } from './application/listener/call/call-updated.listener'
import { UpsertSequenceContactStepContentHandler } from './application/command/upsert-sequence-contact-step-content/upsert-sequence-contact-step-content.handler'
import { SEQUENCE_CONTACT_STEP_REPOSITORY_INTERFACE } from './domain/repository/sequence-contact-step-repository.interface'
import { SequenceContactStepRepository } from './infrastructure/repository/sequence-contact-step.repository'
import { SequenceContactStepEntity } from './infrastructure/entity/sequence-contact-step.entity'
import { DeleteSequenceContactStepContentHandler } from './application/command/delete-sequence-contact-step-content/delete-sequence-contact-step-content.handler'
import { GetAssignUsersAndCountContactsBySequenceHandler } from './application/query/get-assign-users-and-count-contacts-by-sequence/get-assign-users-and-count-contacts-by-sequence.handler'
import { SequenceStepService } from './application/service/sequence-step.service'
import { SequenceContactStepContentListener } from './application/listener/sequence/sequence-contact-step-content.listener'
import { LinkedinTaskFailedListener } from './application/listener/linkedin/linkedin-task-failed.listener'
import { GetLeadImportBySequenceHandler } from './application/query/get-lead-import-by-sequence/get-lead-import-by-sequence.handler'
import { SequenceService } from './application/service/sequence.service'
import { FindSequenceStepContentByContactHandler } from './application/query/find-sequence-step-content-by-contact/find-sequence-step-content-by-contact.handler'
import { DuplicateSequenceHandler } from './application/command/duplicate-sequence/duplicate-sequence.handler'
import { FindSequenceWithStepsByContactsHandler } from './application/query/find-sequence-with-steps-by-contacts/find-sequence-with-steps-by-contacts.handler'
import { FindSequenceStepsWithProgressionBySequenceHandler } from './application/query/find-sequence-steps-by-sequence/find-sequence-steps-with-progression-by-sequence.handler'
import { GetElasticSequencesInformationsByContactHandler } from './application/query/get-elastic-sequences-informations/get-elastic-sequences-informations-by-contact.handler'
import { GetElasticSequencesInformationsByContactsHandler } from './application/query/get-elastic-sequences-informations/get-elastic-sequences-informations-by-contacts.handler'
import { SequenceContactService } from './application/service/sequence-contact.service'
import { SequenceNextExecutionDateUpdatedListener } from './application/listener/sequence/sequence-next-execution-date-updated.listener'

const profiles = [SequenceProfile]

const commandHandlers = [
  // Sequence
  CreateSequenceHandler,
  UpdateSequenceHandler,
  DeleteSequenceHandler,
  UpdateSequenceKpiHandler,

  // Sequence Contact
  CreateSequenceContactHandler,
  CreateSequenceContactFromImportHandler,
  DeleteSequenceContactHandler,
  UpdateSequenceContactHandler,
  UpdateSequenceContactStatusHandler,
  DisableExtraSequencesHandler,
  SkipSequenceContactHandler,
  ReviewSequenceContactHandler,
  UpsertSequenceContactStepContentHandler,
  DeleteSequenceContactStepContentHandler,

  // Sequence Step
  CreateSequenceStepHandler,
  UpdateSequenceStepHandler,
  DeleteSequenceStepHandler,

  // Sequence Activity
  CreateSequenceActivityHandler,
  UpdateSequenceActivityHandler,
]

const entities = [
  SequenceEntity,
  SequenceContactEntity,
  SequenceStepEntity,
  SequenceActivityEntity,
  SequenceContactStepEntity,
]

const queryHandlers = [
  FindSequenceHandler,
  FindSequenceContactHandler,
  FindSequenceContactPaginatedHandler,
  FindSequenceContactByIdHandler,
  FindSequenceContactBySequenceHandler,
  FindSequenceByOrganizationHandler,
  FindSequenceStepHandler,
  FindSequenceStepBySequenceHandler,
  FindSequenceStepsBySequenceHandler,
  FindSequenceContactBySequenceAndContactIdHandler,
  FindSequenceContactBySequenceAndEmailHandler,
  FindSequencesKpisHandler,
  FindSequenceContactErrorHandler,
  FindSequenceContactsErrorsHandler,
  FindSequenceContactsNotInOtherSequenceHandler,
  FindSequenceWithStepsByContactHandler,
  FindSequenceContactPastActivitiesBySequenceAndContactsHandler,
  FindSequenceByOrganizationHandler,
  FindSequencesByOrganizationHandler,
  CountSequencesHandler,
  DisableExtraSequencesHandler,
  GetWeeklyRecapSequencesMetricsHandler,
  FindSequenceContactActivityHandler,
  FindSequenceActivityBySequenceContactAndStepAndTypeHandler,
  FindSequenceContactActivityByTypeAndResourceHandler,
  GetAssignUsersAndCountContactsBySequenceHandler,
  FindSequenceStepContentByContactHandler,
  DuplicateSequenceHandler,
  FindSequenceWithStepsByContactsHandler,
  FindSequenceStepsWithProgressionBySequenceHandler,
  GetLeadImportBySequenceHandler,
  GetElasticSequencesInformationsByContactsHandler,
  GetElasticSequencesInformationsByContactHandler,
]

const services = [
  PaginationService,
  ConfigDeleteLeadStatusService,
  ConfigScheduleService,
  DailyLimitService,
  ScheduleEmailService,
  ProviderService,
  SequenceContactErrorsService,
  StepExecutorFactory,
  SequenceActivityService,
  ScheduleLinkedinService,
  SequenceContactReviewService,
  SequenceStepService,
  SequenceService,
  SequenceContactService,
]

const listeners = [
  ContactArchivedListener,
  MailAnsweredListener,
  MailSentListener,
  SequenceContactUnsubscribedListener,
  ActivityContactBouncedListener,
  SequenceContactBouncedListener,
  ContactOpenedListener,
  ContactClickedListener,
  SequenceContactAddedListener,
  ContactStepValidatedListener,
  MailSequenceAnsweredListener,
  MailSequenceSentListener,
  MailIntegratedListener,
  MailErrorListener,
  ContactErrorResolvedListener,
  LeadUpdatedListener,
  LeadAssignedListener,
  LeadUnassignedListener,
  CompanyUpdatedListener,
  SequenceCreatedListener,
  SequenceStepSucceededListener,
  SequenceContactUpdatedListener,
  LinkedinVisitProfileSucceededListener,
  LinkedinVisitProfileFailedListener,
  LinkedinSendInvitationFailedListener,
  LinkedinSendMessageFailedListener,
  LinkedinSendMessageSucceededListener,
  LinkedinNewConnectionRetreivedListener,
  LinkedinSendInvitationSucceededListener,
  LinkedinMessageSentListener,
  LinkedinMessageReceivedListener,
  LinkedinCookieRefreshedListener,
  LinkedinTaskFailedListener,
  TaskDoneListener,
  CallCreatedListener,
  CallUpdatedListener,
  SequenceContactStepContentListener,
  SequenceNextExecutionDateUpdatedListener,
]

let consumers = []
if (process.env.IS_CONSUMER_INSTANCE === 'true') {
  consumers = [SequenceLaunchConsumer]
}

const cron = [FindSequenceCron, CleanSequenceContactCron]
@Module({
  imports: [
    TypeOrmModule.forFeature(entities),
    CqrsModule,
    BullModule.registerQueue({
      name: SEQUENCE_HANDLER_QUEUE,
    }),
    IntegrationCaptainDataModule,
    MailerModule,
  ],
  providers: [
    {
      provide: SEQUENCE_REPOSITORY_INTERFACE,
      useClass: SequenceRepository,
    },
    {
      provide: SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
      useClass: SequenceContactRepository,
    },
    {
      provide: SEQUENCE_STEP_REPOSITORY_INTERFACE,
      useClass: SequenceStepRepository,
    },
    {
      provide: SEQUENCE_ACTIVITY_REPOSITORY_INTERFACE,
      useClass: SequenceActivityRepository,
    },
    {
      provide: SEQUENCE_CONTACT_STEP_REPOSITORY_INTERFACE,
      useClass: SequenceContactStepRepository,
    },
    ...profiles,
    ...commandHandlers,
    ...queryHandlers,
    ...services,
    ...cron,
    ...consumers,
    ...listeners,
  ],
  exports: [ConfigScheduleService],
})
export class SequenceModule {}
