import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { FindSequenceContactsErrorsQuery } from './find-sequence-contacts-errors.query'
import { Inject } from '@nestjs/common'
import {
  SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
  SequenceContactRepositoryInterface,
} from '../../../domain/repository/sequence-contact-repository.interface'
import { ContactErrorObject } from '../../../domain/object/contact-error.object'
import { PaginationService } from '../../../../shared/infrastructure/service/pagination.service'
import { SequenceContactEntity } from '../../../infrastructure/entity/sequence-contact.entity'
import { SequenceContactModel } from '../../../domain/model/sequence-contact.model'
import { PaginationResultObject } from '../../../../shared/domain/object/pagination-result.object'
import { SequenceContactErrorsService } from '../../service/sequence-contact-errors.service'

@QueryHandler(FindSequenceContactsErrorsQuery)
export class FindSequenceContactsErrorsHandler
  implements IQueryHandler<FindSequenceContactsErrorsQuery>
{
  constructor(
    readonly queryBus: QueryBus,
    @Inject(SEQUENCE_CONTACT_REPOSITORY_INTERFACE)
    private readonly sequenceContactRepository: SequenceContactRepositoryInterface,
    private readonly paginationService: PaginationService,
    private readonly sequenceContactErrorsService: SequenceContactErrorsService
  ) {}

  async execute(
    query: FindSequenceContactsErrorsQuery
  ): Promise<PaginationResultObject<ContactErrorObject>> {
    const { sequenceId, organizationId, userId } = query

    const {
      commonCustomVariables,
      commonStandardVariables,
      contactsWithVariableInSteps,
    } = await this.sequenceContactErrorsService.getVariablesForSequence(
      organizationId,
      sequenceId,
      userId
    )

    const queryContactsWithEmptyFields =
      this.sequenceContactRepository.queryFindContactsWithEmptyFields(
        sequenceId,
        commonStandardVariables,
        commonCustomVariables,
        contactsWithVariableInSteps
      )

    const paginationResult = await this.paginationService.paginate(
      SequenceContactEntity,
      SequenceContactModel,
      queryContactsWithEmptyFields,
      query.paginationQueryObject
    )

    const formattedContactsWithEmptyFields = []
    paginationResult.items.forEach(contactWithEmptyFields => {
      const standardVariables =
        contactsWithVariableInSteps[contactWithEmptyFields.contact.id]?.standard

      const customVariables =
        contactsWithVariableInSteps[contactWithEmptyFields.contact.id]?.custom

      const contactError = new ContactErrorObject(
        contactWithEmptyFields.contact,
        standardVariables,
        customVariables,
        sequenceId,
        contactWithEmptyFields.id
      )
      formattedContactsWithEmptyFields.push(contactError)
    })

    return new PaginationResultObject(
      formattedContactsWithEmptyFields,
      paginationResult.meta
    )
  }
}
