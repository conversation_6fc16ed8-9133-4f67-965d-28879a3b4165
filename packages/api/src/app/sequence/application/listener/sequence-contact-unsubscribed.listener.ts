import { Inject, Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { SequenceEvents } from '../../../shared/domain/event/event.enum'
import {
  SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
  SequenceContactRepositoryInterface,
} from '../../domain/repository/sequence-contact-repository.interface'
import { SequenceContactUnsubscribedEvent } from '../../../shared/domain/event/sequence/sequence-contact-unsubscribed.event'
import { SequenceActivityModel } from '../../../shared/domain/model/activity/sequence.model'
import { ActivityModel } from '../../../lead/domain/model/activity.model'
import { ActivityType } from '../../../lead/domain/activity-type.enum'
import { CreateActivityCommand } from '../../../lead/application/command/activity/create-activity/create-activity.command'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { FindContactQuery } from '../../../lead/application/query/find-contact/find-contact.query'

@Injectable()
export class SequenceContactUnsubscribedListener {
  constructor(
    @Inject(SEQUENCE_CONTACT_REPOSITORY_INTERFACE)
    private sequenceContactRepository: SequenceContactRepositoryInterface,
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus
  ) {}

  @OnEvent(SequenceEvents.SEQUENCE_CONTACT_UNSUBSCRIBED)
  async handleSequenceContactUnsubscribedEvent(
    event: SequenceContactUnsubscribedEvent
  ) {
    const { sequenceId, contactId, sequenceContactId, contactEmail } = event

    await this.sequenceContactRepository.contactUnsubscribed(
      sequenceId,
      contactId
    )

    const sequenceContact = await this.sequenceContactRepository.findById(
      sequenceContactId
    )
    // get sequence
    const sequence = sequenceContact.sequence

    // get contact basic info
    const contact = await this.queryBus.execute(
      new FindContactQuery(sequenceContact.contactId)
    )
    // create activity when contact unsubscribed
    const sequenceActivityModel = new SequenceActivityModel()
    sequenceActivityModel.userId = sequenceContact.createdBy.id
    sequenceActivityModel.userName = sequenceContact.createdBy.getFullName()
    sequenceActivityModel.contactName = contact.fullNameDisplay
    sequenceActivityModel.contactEmail = contactEmail
    sequenceActivityModel.sequenceId = sequence.id
    sequenceActivityModel.sequenceName = sequence.name

    const activityModel = new ActivityModel()
    activityModel.resourceId = sequenceContactId
    activityModel.organizationId = sequence.organizationId
    activityModel.contactId = contactId
    activityModel.payload = sequenceActivityModel
    activityModel.type = ActivityType.SEQUENCE_CONTACT_UNSUBSCRIBED

    this.commandBus.execute(new CreateActivityCommand(activityModel))
  }
}
