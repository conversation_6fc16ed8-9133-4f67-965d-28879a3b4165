import { Inject, Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { SequenceEvents } from '../../../../shared/domain/event/event.enum'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { SequenceActivityModel } from '../../../../shared/domain/model/activity/sequence.model'
import { ActivityModel } from '../../../../lead/domain/model/activity.model'
import { ActivityType } from '../../../../lead/domain/activity-type.enum'
import { CreateActivityCommand } from '../../../../lead/application/command/activity/create-activity/create-activity.command'
import { SequenceContactBouncedEvent } from '../../../../shared/domain/event/sequence/sequence-contact-bounced.event'
import { FindContactQuery } from '../../../../lead/application/query/find-contact/find-contact.query'
import {
  SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
  SequenceContactRepositoryInterface,
} from '../../../domain/repository/sequence-contact-repository.interface'
import { Logger } from 'nestjs-pino'
import {
  SEQUENCE_ACTIVITY_REPOSITORY_INTERFACE,
  SequenceActivityRepositoryInterface,
} from '../../../domain/repository/sequence-activity-repository.interface'
import { SequenceActivityStepStatus } from '../../../domain/enum/sequence-activity-step-status.enum'

@Injectable()
export class ActivityContactBouncedListener {
  constructor(
    @Inject(SEQUENCE_CONTACT_REPOSITORY_INTERFACE)
    private sequenceContactRepository: SequenceContactRepositoryInterface,
    @Inject(SEQUENCE_ACTIVITY_REPOSITORY_INTERFACE)
    private readonly sequenceActivityRepository: SequenceActivityRepositoryInterface,
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
    private readonly logger: Logger
  ) {}

  @OnEvent(SequenceEvents.SEQUENCE_CONTACT_BOUNCED)
  async handleActivityContactBouncedEvent(event: SequenceContactBouncedEvent) {
    const { sequenceContactId, email } = event

    const sequenceContact =
      await this.sequenceContactRepository.findById(sequenceContactId)
    // get sequence
    const sequence = sequenceContact.sequence

    // get contact basic info
    const contact = await this.queryBus.execute(
      new FindContactQuery(sequenceContact.contactId)
    )

    // make sure the email is in contact's emails
    if (!contact.emails.includes(email)) {
      this.logger.error(`Email not found in contact's emails: ${email}`)
      return
    }

    // create activity when contact bounced
    const sequenceActivityModel = new SequenceActivityModel()
    sequenceActivityModel.userId = sequenceContact.createdBy.id
    sequenceActivityModel.userName = sequenceContact.createdBy.getFullName()
    sequenceActivityModel.contactName = contact.fullNameDisplay
    sequenceActivityModel.contactEmail = email
    sequenceActivityModel.sequenceId = sequence.id
    sequenceActivityModel.sequenceName = sequence.name

    const activityModel = new ActivityModel()
    activityModel.resourceId = sequenceContactId
    activityModel.organizationId = sequence.organizationId
    activityModel.contactId = contact.id
    activityModel.payload = sequenceActivityModel
    activityModel.type = ActivityType.SEQUENCE_CONTACT_BOUNCED

    this.commandBus.execute(new CreateActivityCommand(activityModel))

    // Update sequence activity status to bounced
    await this.sequenceActivityRepository.updateStatusBySequenceContactAndStepAndTo(
      sequenceContactId,
      sequenceContact.step.id,
      email,
      SequenceActivityStepStatus.BOUNCED
    )
  }
}
