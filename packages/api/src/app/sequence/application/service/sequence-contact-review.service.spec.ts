import { Test, TestingModule } from '@nestjs/testing'
import { QueryBus } from '@nestjs/cqrs'
import { SequenceContactReviewService } from './sequence-contact-review.service'
import {
  SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
  SequenceContactRepositoryInterface,
} from '../../domain/repository/sequence-contact-repository.interface'
import { SequenceContactModel } from '../../domain/model/sequence-contact.model'
import { ContactErrorObject } from '../../domain/object/contact-error.object'
import { FindSequenceQuery } from '../query/find-sequence/find-sequence.query'
import { FindSequenceContactPaginatedQuery } from '../query/find-sequence-contact/find-sequence-contact-paginated.query'
import { SequenceContactErrorsService } from './sequence-contact-errors.service'
import { PaginationResultObject } from '../../../shared/domain/object/pagination-result.object'
import { ContactInterface } from '../../../shared/domain/model/lead/contact.interface'
import { WebSocketService } from '../../../shared/infrastructure/service/websocket.service'

describe('SequenceContactReviewService', () => {
  let service: SequenceContactReviewService
  let queryBus: QueryBus
  let sequenceContactRepository: SequenceContactRepositoryInterface
  let sequenceContactErrorsService: SequenceContactErrorsService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SequenceContactReviewService,
        {
          provide: QueryBus,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: SEQUENCE_CONTACT_REPOSITORY_INTERFACE,
          useValue: {
            bulkUpdate: jest.fn(),
            findByContactId: jest.fn(),
            findById: jest.fn(),
          },
        },
        {
          provide: SequenceContactErrorsService,
          useValue: {
            getVariablesForSequence: jest.fn(),
            validateSequenceContactsForSequence: jest.fn(),
          },
        },
        {
          provide: WebSocketService,
          useValue: {
            send: jest.fn(),
          },
        },
      ],
    }).compile()

    service = module.get<SequenceContactReviewService>(
      SequenceContactReviewService
    )
    queryBus = module.get<QueryBus>(QueryBus)
    sequenceContactRepository = module.get(
      SEQUENCE_CONTACT_REPOSITORY_INTERFACE
    )
    sequenceContactErrorsService = module.get<SequenceContactErrorsService>(
      SequenceContactErrorsService
    )
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })

  describe('persistSequenceContactErrors', () => {
    it('should persist sequence contact errors', async () => {
      const errors: ContactErrorObject[] = [
        new ContactErrorObject(
          {
            id: 'contact-1',
            firstName: 'John',
            organizationId: 'org-1',
          } as ContactInterface,
          ['firstName', 'lastName'],
          ['custom1'],
          'seq-1',
          'seq-contact-1'
        ),
      ]

      await service['persistSequenceContactErrors'](errors)

      expect(sequenceContactRepository.bulkUpdate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'seq-contact-1',
            review: expect.objectContaining({
              data: {
                missing_variables: ['lastName', 'custom1'],
              },
            }),
          }),
        ])
      )
    })

    it('should handle empty errors array', async () => {
      await service['persistSequenceContactErrors']([])
      expect(sequenceContactRepository.bulkUpdate).not.toHaveBeenCalled()
    })
  })

  describe('reviewSequenceContactsByContact', () => {
    it('should review sequence contacts for a contact', async () => {
      const contactId = 'contact-1'
      const organizationId = 'org-1'

      const mockSequenceContacts = [
        {
          id: 'seq-contact-1',
          sequenceId: 'seq-1',
          sequence: {
            id: 'seq-1',
          },
        },
      ] as SequenceContactModel[]

      jest
        .spyOn(sequenceContactRepository, 'findByContactId')
        .mockResolvedValue(mockSequenceContacts)
      jest
        .spyOn(service, 'reviewSequenceContactById')
        .mockResolvedValue(
          new ContactErrorObject(
            {} as ContactInterface,
            [],
            [],
            'seq-1',
            'seq-contact-1'
          )
        )

      await service.reviewSequenceContactsByContact(
        contactId,
        organizationId,
        'user-1'
      )

      expect(sequenceContactRepository.findByContactId).toHaveBeenCalledWith(
        contactId
      )
      expect(service.reviewSequenceContactById).toHaveBeenCalledWith(
        'seq-contact-1',
        organizationId,
        'user-1'
      )
    })

    it('should handle no sequence contacts found', async () => {
      const contactId = 'contact-1'
      const organizationId = 'org-1'

      jest
        .spyOn(sequenceContactRepository, 'findByContactId')
        .mockResolvedValue([])

      jest.spyOn(service, 'reviewSequenceContactById')

      await service.reviewSequenceContactsByContact(contactId, organizationId)

      expect(sequenceContactRepository.findByContactId).toHaveBeenCalledWith(
        contactId
      )
      expect(service.reviewSequenceContactById).not.toHaveBeenCalled()
    })
  })

  describe('reviewSequenceContactsBySequence', () => {
    it('should review sequence contacts for a sequence', async () => {
      const sequenceId = 'seq-1'
      const mockSequence = {
        id: sequenceId,
        organizationId: 'org-1',
        createdBy: { id: 'user-1' },
      }

      const mockVariablesForSequence = {
        sequenceId,
        commonStandardVariables: ['firstName'],
        commonCustomVariables: ['custom1'],
        contactsWithVariableInSteps: {},
      }

      const mockPaginationResult = {
        items: [
          {
            id: 'seq-contact-1',
            contactId: 'contact-1',
          } as SequenceContactModel,
        ],
        meta: {
          hasNextPage: false,
          totalPages: 1,
        },
      } as PaginationResultObject<SequenceContactModel>

      jest.spyOn(queryBus, 'execute').mockImplementation(query => {
        if (query instanceof FindSequenceQuery) {
          return Promise.resolve(mockSequence)
        }
        if (query instanceof FindSequenceContactPaginatedQuery) {
          return Promise.resolve(mockPaginationResult)
        }
        return Promise.resolve([])
      })

      jest
        .spyOn(sequenceContactErrorsService, 'getVariablesForSequence')
        .mockResolvedValue(mockVariablesForSequence)
      jest
        .spyOn(
          sequenceContactErrorsService,
          'validateSequenceContactsForSequence'
        )
        .mockResolvedValue([
          new ContactErrorObject(
            {} as ContactInterface,
            [],
            [],
            sequenceId,
            'seq-contact-1'
          ),
        ])

      await service.reviewSequenceContactsBySequence(sequenceId)

      expect(queryBus.execute).toHaveBeenCalledWith(
        expect.any(FindSequenceQuery)
      )
      expect(
        sequenceContactErrorsService.getVariablesForSequence
      ).toHaveBeenCalledWith('org-1', sequenceId, 'user-1')
      expect(
        sequenceContactErrorsService.validateSequenceContactsForSequence
      ).toHaveBeenCalledWith(
        mockPaginationResult.items,
        mockVariablesForSequence
      )
    })

    it('should handle sequence not found', async () => {
      const sequenceId = 'seq-1'

      jest.spyOn(queryBus, 'execute').mockResolvedValue(null)

      await service.reviewSequenceContactsBySequence(sequenceId)

      expect(queryBus.execute).toHaveBeenCalledWith(
        expect.any(FindSequenceQuery)
      )
      expect(
        sequenceContactErrorsService.getVariablesForSequence
      ).not.toHaveBeenCalled()
    })
  })

  describe('reviewSequenceContactById', () => {
    it('should review a single sequence contact', async () => {
      const sequenceContactId = 'seq-contact-1'
      const organizationId = 'org-1'

      const mockSequenceContact = {
        id: sequenceContactId,
        sequenceId: 'seq-1',
        contactId: 'contact-1',
      } as SequenceContactModel

      const mockVariablesForSequence = {
        sequenceId: 'seq-1',
        commonStandardVariables: ['firstName'],
        commonCustomVariables: ['custom1'],
        contactsWithVariableInSteps: {},
      }

      const mockContactError = new ContactErrorObject(
        {} as ContactInterface,
        ['firstName'],
        ['custom1'],
        'seq-1',
        sequenceContactId
      )

      jest
        .spyOn(sequenceContactRepository, 'findById')
        .mockResolvedValue(mockSequenceContact)
      jest
        .spyOn(sequenceContactErrorsService, 'getVariablesForSequence')
        .mockResolvedValue(mockVariablesForSequence)
      jest
        .spyOn(
          sequenceContactErrorsService,
          'validateSequenceContactsForSequence'
        )
        .mockResolvedValue([mockContactError])

      const result = await service.reviewSequenceContactById(
        sequenceContactId,
        organizationId
      )

      expect(sequenceContactRepository.findById).toHaveBeenCalledWith(
        sequenceContactId
      )
      expect(
        sequenceContactErrorsService.getVariablesForSequence
      ).toHaveBeenCalledWith(organizationId, 'seq-1', 'contact-1')
      expect(
        sequenceContactErrorsService.validateSequenceContactsForSequence
      ).toHaveBeenCalledWith([mockSequenceContact], mockVariablesForSequence)
      expect(result).toEqual(mockContactError)
    })

    it('should handle sequence contact not found', async () => {
      const sequenceContactId = 'seq-contact-1'
      const organizationId = 'org-1'

      jest.spyOn(sequenceContactRepository, 'findById').mockResolvedValue(null)

      const result = await service.reviewSequenceContactById(
        sequenceContactId,
        organizationId
      )

      expect(sequenceContactRepository.findById).toHaveBeenCalledWith(
        sequenceContactId
      )
      expect(result).toBeUndefined()
    })
  })
})
