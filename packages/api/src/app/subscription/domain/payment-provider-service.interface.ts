import Stripe from 'stripe'
import { OrganizationModel } from '../../organization/domain/organization.model'
import { StripeCustomerMetadata } from './stripe-metadata.types'
import { CreateCheckoutSessionDto } from '../../../ui/api/subscription/dto/create-checkout-session.dto'
import { StripeBillingPortalConfigurationType } from '../infrastructure/service/stripe.service'
import { OrganizationServiceSubscriptionPlan } from '@getheroes/shared'
import { OrganizationSubscriptionInterval } from '../../shared/domain/organization-subscription-interval.enum'

export const PAYMENT_PROVIDER_SERVICE_INTERFACE =
  'PAYMENT_PROVIDER_SERVICE_INTERFACE'

export interface GetServiceSubscriptionUpgradeDiscountsResult {
  proratedDiscount: number
  promotionDiscount: number
  starterYearlyFirstMonthCoupon: Stripe.Coupon | null
}

export interface PaymentProviderServiceInterface {
  constructEventFromPayload(
    signature: string,
    payload: Buffer
  ): Promise<Stripe.Event>
  getCustomerById(customerId: string): Promise<{
    customer: Stripe.Customer
    metadata: StripeCustomerMetadata
  } | null>
  createCustomer(organizationModel: OrganizationModel): Promise<string>
  createCheckoutSession(
    organizationId: string,
    userId: string,
    createCheckoutSessionDto: CreateCheckoutSessionDto
  ): Promise<string>
  createBillingPortalSession(
    customerId: string,
    billingPortalConfig?: Stripe.BillingPortal.Configuration
  ): Promise<string>
  updateCreditSubscription(
    stripeSubscriptionId: string,
    updateSubscriptionDto: {
      quantity: number
    }
  ): Promise<Stripe.SubscriptionItem>
  getServiceSubscriptionUpgradeDiscounts(
    stripeSubscriptionId: string,
    updateSubscriptionDto: {
      plan: OrganizationServiceSubscriptionPlan
      interval: OrganizationSubscriptionInterval
    }
  ): Promise<GetServiceSubscriptionUpgradeDiscountsResult>
  updateServiceSubscription(
    stripeSubscriptionId: string,
    updateSubscriptionDto: {
      plan: OrganizationServiceSubscriptionPlan
      interval: OrganizationSubscriptionInterval
      coupon?: Stripe.Coupon
    }
  ): Promise<Stripe.SubscriptionItem>
  cancelSubscription(stripeSubscriptionId: string): Promise<void>
  cancelPendingInvoices(stripeSubscriptionId: string): Promise<void>
  getSubscriptionById(subscriptionId: string): Promise<Stripe.Subscription>
  getPriceById(priceId: string): Promise<Stripe.Price>
  getAmountFromTiers(stripePrice: Stripe.Price, quantity: number): number
  getBillingPortalConfiguration(
    configurationType: StripeBillingPortalConfigurationType
  ): Promise<Stripe.BillingPortal.Configuration | null>
  createBillingPortalConfiguration(
    configuration: Stripe.BillingPortal.ConfigurationCreateParams
  ): Promise<Stripe.BillingPortal.Configuration>
}
