import Stripe from 'stripe'
import { <PERSON><PERSON>us, CommandHandler, ICommandHandler } from '@nestjs/cqrs'
import {
  HandleStripeEventCommand,
  HandleStripeEventResult,
} from './handle-stripe-event.command'

import { HandleStripeSubscriptionEventCommand } from './subscription/handle-stripe-subscription-events/handle-stripe-subscription-event.command'
import { StripeWebhookEvents } from '../../../domain/stripe.event'
import { HandleStripeInvoiceEventCommand } from './handle-stripe-invoice-events/handle-stripe-invoice-event.command'
import { LogFeature } from '../../../../shared/logger.service'
import { TryCatchLogger } from '../../../../shared/domain/decorator/try-catch-logger.decorator'
import { HandleStripeSubscriptionCreatedEventCommand } from './subscription-created/handle-stripe-subscription-created-event.command'

@CommandHandler(HandleStripeEventCommand)
export class HandleStripeEventHandler
  implements ICommandHandler<HandleStripeEventCommand>
{
  constructor(private readonly commandBus: CommandBus) {}

  @TryCatchLogger({
    feature: LogFeature.SUBSCRIPTION,
    message: 'Error handling Stripe event',
  })
  async execute({
    event,
  }: HandleStripeEventCommand): Promise<HandleStripeEventResult> {
    const data = event.data.object as
      | Stripe.Subscription
      | Stripe.Invoice
      | Stripe.Checkout.Session

    const stripeCustomerId = data.customer as string

    try {
      switch (event.type) {
        // Subscription events
        case StripeWebhookEvents.CUSTOMER_SUBSCRIPTION_CREATED:
          await this.commandBus.execute(
            new HandleStripeSubscriptionCreatedEventCommand({
              event,
              stripeCustomerId,
              stripeSubscriptionData: data as Stripe.Subscription,
            })
          )
          return true

        case StripeWebhookEvents.CUSTOMER_SUBSCRIPTION_UPDATED:
        case StripeWebhookEvents.CUSTOMER_SUBSCRIPTION_DELETED:
        case StripeWebhookEvents.CUSTOMER_SUBSCRIPTION_PAUSED:
        case StripeWebhookEvents.CUSTOMER_SUBSCRIPTION_RESUMED:
          await this.commandBus.execute(
            new HandleStripeSubscriptionEventCommand({
              event,
              stripeCustomerId,
              stripeSubscriptionData: data as Stripe.Subscription,
            })
          )
          return true

        // Invoice events
        case StripeWebhookEvents.INVOICE_CREATED:
        case StripeWebhookEvents.INVOICE_PAYMENT_SUCCEEDED:
          await this.commandBus.execute(
            new HandleStripeInvoiceEventCommand({
              event,
              stripeCustomerId,
              stripeInvoiceData: data as Stripe.Invoice,
            })
          )
          return true

        // Customer events...
      }
    } catch {
      return false
    }

    return true
  }
}
