import Stripe from 'stripe'

export const paymentSucceededReceivedBeforeSubscriptionCreatedEvent = (
  stripeCustomerId: string,
  stripeSubscriptionId: string
) =>
  ({
    invoiceCreated: {
      id: 'evt_1QY7j6AcKR2MzNGOxV0klgeF',
      data: {
        object: {
          id: 'in_1QY7j2AcKR2MzNGOj5Ox4QtV',
          tax: 0,
          paid: true,
          lines: {
            url: '/v1/invoices/in_1QY7j2AcKR2MzNGOj5Ox4QtV/lines',
            data: [
              {
                id: 'il_1QY7j2AcKR2MzNGOfcgKR2lM',
                plan: {
                  id: 'price_1PP3obAcKR2MzNGOl9ov3qDY',
                  meter: null,
                  active: true,
                  amount: 5900,
                  object: 'plan',
                  created: **********,
                  product: 'prod_OiNZWI5u67eK0v',
                  currency: 'eur',
                  interval: 'month',
                  livemode: true,
                  metadata: {
                    plan: 'starter',
                    type: 'service',
                    creditsPerPeriod: '750',
                  },
                  nickname: 'SERVICE_STARTER_MONTHLY',
                  tiers_mode: null,
                  usage_type: 'licensed',
                  amount_decimal: '5900',
                  billing_scheme: 'per_unit',
                  interval_count: 1,
                  aggregate_usage: null,
                  transform_usage: null,
                  trial_period_days: null,
                },
                type: 'subscription',
                price: {
                  id: 'price_1PP3obAcKR2MzNGOl9ov3qDY',
                  type: 'recurring',
                  active: true,
                  object: 'price',
                  created: **********,
                  product: 'prod_OiNZWI5u67eK0v',
                  currency: 'eur',
                  livemode: true,
                  metadata: {
                    plan: 'starter',
                    type: 'service',
                    creditsPerPeriod: '750',
                  },
                  nickname: 'SERVICE_STARTER_MONTHLY',
                  recurring: {
                    meter: null,
                    interval: 'month',
                    usage_type: 'licensed',
                    interval_count: 1,
                    aggregate_usage: null,
                    trial_period_days: null,
                  },
                  lookup_key: 'SERVICE_STARTER_MONTHLY',
                  tiers_mode: null,
                  unit_amount: 5900,
                  tax_behavior: 'exclusive',
                  billing_scheme: 'per_unit',
                  custom_unit_amount: null,
                  transform_quantity: null,
                  unit_amount_decimal: '5900',
                },
                amount: 5900,
                object: 'line_item',
                period: { end: **********, start: ********** },
                invoice: 'in_1QY7j2AcKR2MzNGOj5Ox4QtV',
                currency: 'usd',
                livemode: true,
                metadata: {
                  plan: 'STARTER',
                  type: 'SERVICE',
                  userId: '01bc664c-983a-4405-8011-7cff861aeaa7',
                  interval: 'MONTH',
                  quantity: '1',
                  environment: 'prod',
                  organizationId: '00d350d5-3593-48eb-8048-fcf4889dac62',
                },
                quantity: 1,
                discounts: [],
                proration: false,
                tax_rates: [],
                description: '1 × Starter (at $59.00 / month)',
                tax_amounts: [
                  {
                    amount: 0,
                    tax_rate: 'txr_1OChqbAcKR2MzNGORnor0uvZ',
                    inclusive: false,
                    taxable_amount: 0,
                    taxability_reason: 'reverse_charge',
                  },
                ],
                discountable: true,
                subscription: stripeSubscriptionId,
                discount_amounts: [
                  { amount: 5900, discount: 'di_1QY7j2AcKR2MzNGO84zue1qR' },
                ],
                proration_details: { credited_items: null },
                subscription_item: 'si_RQziBCSfegMAKH',
                amount_excluding_tax: 5900,
                pretax_credit_amounts: [
                  {
                    type: 'discount',
                    amount: 5900,
                    discount: 'di_1QY7j2AcKR2MzNGO84zue1qR',
                  },
                ],
                unit_amount_excluding_tax: '5900',
              },
            ],
            object: 'list',
            has_more: false,
            total_count: 1,
          },
          quote: null,
          total: 0,
          charge: null,
          footer: null,
          issuer: { type: 'self' },
          number: 'ZELIQ2024-1567',
          object: 'invoice',
          status: 'paid',
          created: **********,
          currency: 'usd',
          customer: stripeCustomerId,
          discount: {
            id: 'di_1QY7j2AcKR2MzNGO84zue1qR',
            end: 1750431768,
            start: **********,
            coupon: {
              id: 'OOagUpf7',
              name: 'DISCOUNT INFLUENCER 6MONTH',
              valid: true,
              object: 'coupon',
              created: 1722005002,
              currency: null,
              duration: 'repeating',
              livemode: true,
              metadata: {},
              redeem_by: null,
              amount_off: null,
              percent_off: 100,
              times_redeemed: 8,
              max_redemptions: null,
              duration_in_months: 6,
            },
            object: 'discount',
            invoice: null,
            customer: stripeCustomerId,
            invoice_item: null,
            subscription: stripeSubscriptionId,
            promotion_code: 'promo_1QX3THAcKR2MzNGOBjjVzA53',
            checkout_session: null,
            subscription_item: null,
          },
          due_date: null,
          livemode: true,
          metadata: {},
          subtotal: 5900,
          attempted: true,
          discounts: ['di_1QY7j2AcKR2MzNGO84zue1qR'],
          rendering: {
            pdf: null,
            template: null,
            template_version: null,
            amount_tax_display: 'include_inclusive_tax',
          },
          amount_due: 0,
          period_end: **********,
          test_clock: null,
          amount_paid: 0,
          application: null,
          description: null,
          invoice_pdf:
            'https://pay.stripe.com/invoice/acct_1NuhkJAcKR2MzNGO/live_YWNjdF8xTnVoa0pBY0tSMk16TkdPLF9SUXppOTB3VjNPVmE0S1doWGtmazJxZmIzN3lvQUl4LDEyNTI0Nzc3Mg0200EvdyzEvY/pdf?s=ap',
          account_name: 'GetHeroes',
          auto_advance: false,
          effective_at: **********,
          from_invoice: null,
          on_behalf_of: null,
          period_start: **********,
          subscription: stripeSubscriptionId,
          attempt_count: 0,
          automatic_tax: {
            status: 'complete',
            enabled: true,
            liability: { type: 'self' },
            disabled_reason: null,
          },
          custom_fields: null,
          customer_name: 'Sıtkı Can Timağur ',
          shipping_cost: null,
          transfer_data: null,
          billing_reason: 'subscription_create',
          customer_email: '<EMAIL>',
          customer_phone: null,
          default_source: null,
          ending_balance: 0,
          payment_intent: null,
          receipt_number: null,
          account_country: 'FR',
          account_tax_ids: null,
          amount_shipping: 0,
          latest_revision: null,
          amount_remaining: 0,
          customer_address: {
            city: 'İzmir',
            line1:
              'Bahriye Üçok Mahallesi Latife Hanım sokak İlhan apartmanı No:50/8 İzmir/Karşıyaka',
            line2: null,
            state: 'İzmir',
            country: 'TR',
            postal_code: '35580',
          },
          customer_tax_ids: [{ type: 'tr_tin', value: '**********' }],
          paid_out_of_band: false,
          payment_settings: {
            default_mandate: null,
            payment_method_types: null,
            payment_method_options: {
              card: { request_three_d_secure: 'automatic' },
              konbini: null,
              acss_debit: null,
              bancontact: null,
              sepa_debit: null,
              us_bank_account: null,
              customer_balance: null,
            },
          },
          shipping_details: null,
          starting_balance: 0,
          collection_method: 'charge_automatically',
          customer_shipping: null,
          default_tax_rates: [],
          rendering_options: { amount_tax_display: 'include_inclusive_tax' },
          total_tax_amounts: [
            {
              amount: 0,
              tax_rate: 'txr_1OChqbAcKR2MzNGORnor0uvZ',
              inclusive: false,
              taxable_amount: 0,
              taxability_reason: 'reverse_charge',
            },
          ],
          hosted_invoice_url:
            'https://invoice.stripe.com/i/acct_1NuhkJAcKR2MzNGO/live_YWNjdF8xTnVoa0pBY0tSMk16TkdPLF9SUXppOTB3VjNPVmE0S1doWGtmazJxZmIzN3lvQUl4LDEyNTI0Nzc3Mg0200EvdyzEvY?s=ap',
          status_transitions: {
            paid_at: **********,
            voided_at: null,
            finalized_at: **********,
            marked_uncollectible_at: null,
          },
          customer_tax_exempt: 'none',
          total_excluding_tax: 0,
          next_payment_attempt: null,
          statement_descriptor: null,
          subscription_details: {
            metadata: {
              plan: 'STARTER',
              type: 'SERVICE',
              userId: '01bc664c-983a-4405-8011-7cff861aeaa7',
              interval: 'MONTH',
              quantity: '1',
              environment: 'prod',
              organizationId: '00d350d5-3593-48eb-8048-fcf4889dac62',
            },
          },
          webhooks_delivered_at: null,
          application_fee_amount: null,
          default_payment_method: null,
          subtotal_excluding_tax: 5900,
          total_discount_amounts: [
            { amount: 5900, discount: 'di_1QY7j2AcKR2MzNGO84zue1qR' },
          ],
          last_finalization_error: null,
          automatically_finalizes_at: null,
          total_pretax_credit_amounts: [
            {
              type: 'discount',
              amount: 5900,
              discount: 'di_1QY7j2AcKR2MzNGO84zue1qR',
            },
          ],
          pre_payment_credit_notes_amount: 0,
          post_payment_credit_notes_amount: 0,
        },
      },
      type: 'invoice.created',
      object: 'event',
      created: **********,
      request: { id: null, idempotency_key: null },
      livemode: true,
      api_version: '2023-08-16',
      pending_webhooks: 3,
    },
    invoicePaymentSucceeded: {
      id: 'evt_1QY7j7AcKR2MzNGOC2zaHRin',
      data: {
        object: {
          id: 'in_1QY7j2AcKR2MzNGOj5Ox4QtV',
          tax: 0,
          paid: true,
          lines: {
            url: '/v1/invoices/in_1QY7j2AcKR2MzNGOj5Ox4QtV/lines',
            data: [
              {
                id: 'il_1QY7j2AcKR2MzNGOfcgKR2lM',
                plan: {
                  id: 'price_1PP3obAcKR2MzNGOl9ov3qDY',
                  meter: null,
                  active: true,
                  amount: 5900,
                  object: 'plan',
                  created: **********,
                  product: 'prod_OiNZWI5u67eK0v',
                  currency: 'eur',
                  interval: 'month',
                  livemode: true,
                  metadata: {
                    plan: 'starter',
                    type: 'service',
                    creditsPerPeriod: '750',
                  },
                  nickname: 'SERVICE_STARTER_MONTHLY',
                  tiers_mode: null,
                  usage_type: 'licensed',
                  amount_decimal: '5900',
                  billing_scheme: 'per_unit',
                  interval_count: 1,
                  aggregate_usage: null,
                  transform_usage: null,
                  trial_period_days: null,
                },
                type: 'subscription',
                price: {
                  id: 'price_1PP3obAcKR2MzNGOl9ov3qDY',
                  type: 'recurring',
                  active: true,
                  object: 'price',
                  created: **********,
                  product: 'prod_OiNZWI5u67eK0v',
                  currency: 'eur',
                  livemode: true,
                  metadata: {
                    plan: 'starter',
                    type: 'service',
                    creditsPerPeriod: '750',
                  },
                  nickname: 'SERVICE_STARTER_MONTHLY',
                  recurring: {
                    meter: null,
                    interval: 'month',
                    usage_type: 'licensed',
                    interval_count: 1,
                    aggregate_usage: null,
                    trial_period_days: null,
                  },
                  lookup_key: 'SERVICE_STARTER_MONTHLY',
                  tiers_mode: null,
                  unit_amount: 5900,
                  tax_behavior: 'exclusive',
                  billing_scheme: 'per_unit',
                  custom_unit_amount: null,
                  transform_quantity: null,
                  unit_amount_decimal: '5900',
                },
                amount: 5900,
                object: 'line_item',
                period: { end: **********, start: ********** },
                invoice: 'in_1QY7j2AcKR2MzNGOj5Ox4QtV',
                currency: 'usd',
                livemode: true,
                metadata: {
                  plan: 'STARTER',
                  type: 'SERVICE',
                  userId: '01bc664c-983a-4405-8011-7cff861aeaa7',
                  interval: 'MONTH',
                  quantity: '1',
                  environment: 'prod',
                  organizationId: '00d350d5-3593-48eb-8048-fcf4889dac62',
                },
                quantity: 1,
                discounts: [],
                proration: false,
                tax_rates: [],
                description: '1 × Starter (at $59.00 / month)',
                tax_amounts: [
                  {
                    amount: 0,
                    tax_rate: 'txr_1OChqbAcKR2MzNGORnor0uvZ',
                    inclusive: false,
                    taxable_amount: 0,
                    taxability_reason: 'reverse_charge',
                  },
                ],
                discountable: true,
                subscription: stripeSubscriptionId,
                discount_amounts: [
                  { amount: 5900, discount: 'di_1QY7j2AcKR2MzNGO84zue1qR' },
                ],
                proration_details: { credited_items: null },
                subscription_item: 'si_RQziBCSfegMAKH',
                amount_excluding_tax: 5900,
                pretax_credit_amounts: [
                  {
                    type: 'discount',
                    amount: 5900,
                    discount: 'di_1QY7j2AcKR2MzNGO84zue1qR',
                  },
                ],
                unit_amount_excluding_tax: '5900',
              },
            ],
            object: 'list',
            has_more: false,
            total_count: 1,
          },
          quote: null,
          total: 0,
          charge: null,
          footer: null,
          issuer: { type: 'self' },
          number: 'ZELIQ2024-1567',
          object: 'invoice',
          status: 'paid',
          created: **********,
          currency: 'usd',
          customer: stripeCustomerId,
          discount: {
            id: 'di_1QY7j2AcKR2MzNGO84zue1qR',
            end: 1750431768,
            start: **********,
            coupon: {
              id: 'OOagUpf7',
              name: 'DISCOUNT INFLUENCER 6MONTH',
              valid: true,
              object: 'coupon',
              created: 1722005002,
              currency: null,
              duration: 'repeating',
              livemode: true,
              metadata: {},
              redeem_by: null,
              amount_off: null,
              percent_off: 100,
              times_redeemed: 8,
              max_redemptions: null,
              duration_in_months: 6,
            },
            object: 'discount',
            invoice: null,
            customer: stripeCustomerId,
            invoice_item: null,
            subscription: stripeSubscriptionId,
            promotion_code: 'promo_1QX3THAcKR2MzNGOBjjVzA53',
            checkout_session: null,
            subscription_item: null,
          },
          due_date: null,
          livemode: true,
          metadata: {},
          subtotal: 5900,
          attempted: true,
          discounts: ['di_1QY7j2AcKR2MzNGO84zue1qR'],
          rendering: {
            pdf: null,
            template: null,
            template_version: null,
            amount_tax_display: 'include_inclusive_tax',
          },
          amount_due: 0,
          period_end: **********,
          test_clock: null,
          amount_paid: 0,
          application: null,
          description: null,
          invoice_pdf:
            'https://pay.stripe.com/invoice/acct_1NuhkJAcKR2MzNGO/live_YWNjdF8xTnVoa0pBY0tSMk16TkdPLF9SUXppOTB3VjNPVmE0S1doWGtmazJxZmIzN3lvQUl4LDEyNTI0Nzc3Mw02009n0dX5eK/pdf?s=ap',
          account_name: 'GetHeroes',
          auto_advance: false,
          effective_at: **********,
          from_invoice: null,
          on_behalf_of: null,
          period_start: **********,
          subscription: stripeSubscriptionId,
          attempt_count: 0,
          automatic_tax: {
            status: 'complete',
            enabled: true,
            liability: { type: 'self' },
            disabled_reason: null,
          },
          custom_fields: null,
          customer_name: 'Sıtkı Can Timağur ',
          shipping_cost: null,
          transfer_data: null,
          billing_reason: 'subscription_create',
          customer_email: '<EMAIL>',
          customer_phone: null,
          default_source: null,
          ending_balance: 0,
          payment_intent: null,
          receipt_number: null,
          account_country: 'FR',
          account_tax_ids: null,
          amount_shipping: 0,
          latest_revision: null,
          amount_remaining: 0,
          customer_address: {
            city: 'İzmir',
            line1:
              'Bahriye Üçok Mahallesi Latife Hanım sokak İlhan apartmanı No:50/8 İzmir/Karşıyaka',
            line2: null,
            state: 'İzmir',
            country: 'TR',
            postal_code: '35580',
          },
          customer_tax_ids: [{ type: 'tr_tin', value: '**********' }],
          paid_out_of_band: false,
          payment_settings: {
            default_mandate: null,
            payment_method_types: null,
            payment_method_options: {
              card: { request_three_d_secure: 'automatic' },
              konbini: null,
              acss_debit: null,
              bancontact: null,
              sepa_debit: null,
              us_bank_account: null,
              customer_balance: null,
            },
          },
          shipping_details: null,
          starting_balance: 0,
          collection_method: 'charge_automatically',
          customer_shipping: null,
          default_tax_rates: [],
          rendering_options: { amount_tax_display: 'include_inclusive_tax' },
          total_tax_amounts: [
            {
              amount: 0,
              tax_rate: 'txr_1OChqbAcKR2MzNGORnor0uvZ',
              inclusive: false,
              taxable_amount: 0,
              taxability_reason: 'reverse_charge',
            },
          ],
          hosted_invoice_url:
            'https://invoice.stripe.com/i/acct_1NuhkJAcKR2MzNGO/live_YWNjdF8xTnVoa0pBY0tSMk16TkdPLF9SUXppOTB3VjNPVmE0S1doWGtmazJxZmIzN3lvQUl4LDEyNTI0Nzc3Mw02009n0dX5eK?s=ap',
          status_transitions: {
            paid_at: **********,
            voided_at: null,
            finalized_at: **********,
            marked_uncollectible_at: null,
          },
          customer_tax_exempt: 'none',
          total_excluding_tax: 0,
          next_payment_attempt: null,
          statement_descriptor: null,
          subscription_details: {
            metadata: {
              plan: 'STARTER',
              type: 'SERVICE',
              userId: '01bc664c-983a-4405-8011-7cff861aeaa7',
              interval: 'MONTH',
              quantity: '1',
              environment: 'prod',
              organizationId: '00d350d5-3593-48eb-8048-fcf4889dac62',
            },
          },
          webhooks_delivered_at: null,
          application_fee_amount: null,
          default_payment_method: null,
          subtotal_excluding_tax: 5900,
          total_discount_amounts: [
            { amount: 5900, discount: 'di_1QY7j2AcKR2MzNGO84zue1qR' },
          ],
          last_finalization_error: null,
          automatically_finalizes_at: null,
          total_pretax_credit_amounts: [
            {
              type: 'discount',
              amount: 5900,
              discount: 'di_1QY7j2AcKR2MzNGO84zue1qR',
            },
          ],
          pre_payment_credit_notes_amount: 0,
          post_payment_credit_notes_amount: 0,
        },
      },
      type: 'invoice.payment_succeeded',
      object: 'event',
      created: **********,
      request: { id: null, idempotency_key: null },
      livemode: true,
      api_version: '2023-08-16',
      pending_webhooks: 3,
    },
    customerSubscriptionCreated: {
      id: 'evt_1QY7j8AcKR2MzNGOjaLyo7dh',
      data: {
        object: {
          id: stripeSubscriptionId,
          plan: {
            id: 'price_1PP3obAcKR2MzNGOl9ov3qDY',
            meter: null,
            active: true,
            amount: 5900,
            object: 'plan',
            created: **********,
            product: 'prod_OiNZWI5u67eK0v',
            currency: 'eur',
            interval: 'month',
            livemode: true,
            metadata: {
              plan: 'starter',
              type: 'service',
              creditsPerPeriod: '750',
            },
            nickname: 'SERVICE_STARTER_MONTHLY',
            tiers_mode: null,
            usage_type: 'licensed',
            amount_decimal: '5900',
            billing_scheme: 'per_unit',
            interval_count: 1,
            aggregate_usage: null,
            transform_usage: null,
            trial_period_days: null,
          },
          items: {
            url: '/v1/subscription_items?subscriptionstripeSubscriptionId',
            data: [
              {
                id: 'si_RQziBCSfegMAKH',
                plan: {
                  id: 'price_1PP3obAcKR2MzNGOl9ov3qDY',
                  meter: null,
                  active: true,
                  amount: 5900,
                  object: 'plan',
                  created: **********,
                  product: 'prod_OiNZWI5u67eK0v',
                  currency: 'eur',
                  interval: 'month',
                  livemode: true,
                  metadata: {
                    plan: 'starter',
                    type: 'service',
                    creditsPerPeriod: '750',
                  },
                  nickname: 'SERVICE_STARTER_MONTHLY',
                  tiers_mode: null,
                  usage_type: 'licensed',
                  amount_decimal: '5900',
                  billing_scheme: 'per_unit',
                  interval_count: 1,
                  aggregate_usage: null,
                  transform_usage: null,
                  trial_period_days: null,
                },
                price: {
                  id: 'price_1PP3obAcKR2MzNGOl9ov3qDY',
                  type: 'recurring',
                  active: true,
                  object: 'price',
                  created: **********,
                  product: 'prod_OiNZWI5u67eK0v',
                  currency: 'eur',
                  livemode: true,
                  metadata: {
                    plan: 'starter',
                    type: 'service',
                    creditsPerPeriod: '750',
                  },
                  nickname: 'SERVICE_STARTER_MONTHLY',
                  recurring: {
                    meter: null,
                    interval: 'month',
                    usage_type: 'licensed',
                    interval_count: 1,
                    aggregate_usage: null,
                    trial_period_days: null,
                  },
                  lookup_key: 'SERVICE_STARTER_MONTHLY',
                  tiers_mode: null,
                  unit_amount: 5900,
                  tax_behavior: 'exclusive',
                  billing_scheme: 'per_unit',
                  custom_unit_amount: null,
                  transform_quantity: null,
                  unit_amount_decimal: '5900',
                },
                object: 'subscription_item',
                created: **********,
                metadata: {},
                quantity: 1,
                discounts: [],
                tax_rates: [],
                subscription: stripeSubscriptionId,
                billing_thresholds: null,
              },
            ],
            object: 'list',
            has_more: false,
            total_count: 1,
          },
          object: 'subscription',
          status: 'active',
          created: **********,
          currency: 'usd',
          customer: stripeCustomerId,
          discount: {
            id: 'di_1QY7j2AcKR2MzNGO84zue1qR',
            end: 1750431768,
            start: **********,
            coupon: {
              id: 'OOagUpf7',
              name: 'DISCOUNT INFLUENCER 6MONTH',
              valid: true,
              object: 'coupon',
              created: 1722005002,
              currency: null,
              duration: 'repeating',
              livemode: true,
              metadata: {},
              redeem_by: null,
              amount_off: null,
              percent_off: 100,
              times_redeemed: 8,
              max_redemptions: null,
              duration_in_months: 6,
            },
            object: 'discount',
            invoice: null,
            customer: stripeCustomerId,
            invoice_item: null,
            subscription: stripeSubscriptionId,
            promotion_code: 'promo_1QX3THAcKR2MzNGOBjjVzA53',
            checkout_session: null,
            subscription_item: null,
          },
          ended_at: null,
          livemode: true,
          metadata: {
            plan: 'STARTER',
            type: 'SERVICE',
            userId: '01bc664c-983a-4405-8011-7cff861aeaa7',
            interval: 'MONTH',
            quantity: '1',
            environment: 'prod',
            organizationId: '00d350d5-3593-48eb-8048-fcf4889dac62',
          },
          quantity: 1,
          schedule: null,
          cancel_at: null,
          discounts: ['di_1QY7j2AcKR2MzNGO84zue1qR'],
          trial_end: null,
          start_date: **********,
          test_clock: null,
          application: null,
          canceled_at: null,
          description: null,
          trial_start: null,
          on_behalf_of: null,
          automatic_tax: {
            enabled: true,
            liability: { type: 'self' },
            disabled_reason: null,
          },
          transfer_data: null,
          days_until_due: null,
          default_source: null,
          latest_invoice: 'in_1QY7j2AcKR2MzNGOj5Ox4QtV',
          pending_update: null,
          trial_settings: {
            end_behavior: { missing_payment_method: 'create_invoice' },
          },
          invoice_settings: { issuer: { type: 'self' }, account_tax_ids: null },
          pause_collection: null,
          payment_settings: {
            payment_method_types: null,
            payment_method_options: {
              card: { network: null, request_three_d_secure: 'automatic' },
              konbini: null,
              acss_debit: null,
              bancontact: null,
              sepa_debit: null,
              us_bank_account: null,
              customer_balance: null,
            },
            save_default_payment_method: 'off',
          },
          collection_method: 'charge_automatically',
          default_tax_rates: [],
          billing_thresholds: null,
          current_period_end: **********,
          billing_cycle_anchor: **********,
          cancel_at_period_end: false,
          cancellation_details: { reason: null, comment: null, feedback: null },
          current_period_start: **********,
          pending_setup_intent: null,
          default_payment_method: 'pm_1QY7iyAcKR2MzNGO8PL5Claf',
          application_fee_percent: null,
          billing_cycle_anchor_config: null,
          pending_invoice_item_interval: null,
          next_pending_invoice_item_invoice: null,
        },
      },
      type: 'customer.subscription.created',
      object: 'event',
      created: **********,
      request: { id: null, idempotency_key: null },
      livemode: true,
      api_version: '2023-08-16',
      pending_webhooks: 4,
    },
  }) as unknown as Record<string, Stripe.Event>
