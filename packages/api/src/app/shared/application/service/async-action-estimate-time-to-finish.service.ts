import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { AsyncActionModel } from '../../domain/model/asyncAction/async-action.model'
import {
  AsyncActionCategoryEnum,
  LeadImportSourceEnum,
} from '@getheroes/shared'
import { AsyncActionEstimateTimeToFinishServiceInterface } from '../../domain/interface/async-action-estimate-time-to-finish-service.interface'
import { QueryBus } from '@nestjs/cqrs'
import {
  EstimateEnrichmentTimeOfCompletionQuery,
  EstimateEnrichmentTimeOfCompletionQueryResult,
} from '../../../lead/application/query/enrichment/estimate-enrichment-time-of-completion/estimate-enrichment-time-of-completion.query'

import { EstimateEnrichmentHubTimeOfCompletionQuery } from '../../../lead/application/query/enrichment/estimate-enrichment-hub-time-of-completion/estimate-enrichment-hub-time-of-completion.query'
import { EstimateLeadsExportTimeOfCompletionQuery } from '../../../lead/application/query/leads-export/estimate-leads-export-time-of-completion/estimate-leads-export-time-of-completion.query'
import { LogFeature, LoggerService } from '../../logger.service'
import { GetOneLeadImportByIdQuery } from '../../../lead-import/application/query/get-one-lead-import-by-id/get-one-lead-import-by-id.query'

@Injectable()
export class AsyncActionEstimateTimeToFinishService
  implements AsyncActionEstimateTimeToFinishServiceInterface
{
  constructor(
    private readonly queryBus: QueryBus,
    private readonly configService: ConfigService
  ) {}

  private readonly logger = new LoggerService({
    context: AsyncActionEstimateTimeToFinishService.name,
    feature: LogFeature.ASYNC_ACTIONS,
  })

  /**
   * Estimates the time of completion for the given async action.
   *
   * @param {AsyncActionModel} asyncActionModel - The async action model.
   * @return {Promise<Date>} - A Promise that resolves to the estimated time of completion.
   * @throws {Error} - If the async action category is not valid.
   */
  async estimateTimeOfCompletion(
    asyncActionModel: Pick<
      AsyncActionModel,
      'category' | 'internalRelationId' | 'organizationId'
    >
  ): Promise<Date> {
    switch (asyncActionModel.category) {
      case AsyncActionCategoryEnum.ENRICHMENT:
        return await this.queryBus.execute<
          EstimateEnrichmentTimeOfCompletionQuery,
          EstimateEnrichmentTimeOfCompletionQueryResult
        >(
          new EstimateEnrichmentTimeOfCompletionQuery(
            asyncActionModel.internalRelationId
          )
        )

      case AsyncActionCategoryEnum.ENRICHMENT_HUB:
        return await this.queryBus.execute(
          new EstimateEnrichmentHubTimeOfCompletionQuery({
            enrichmentHubId: asyncActionModel.internalRelationId,
          })
        )

      case AsyncActionCategoryEnum.LEAD_EXPORT:
        return await this.queryBus.execute(
          new EstimateLeadsExportTimeOfCompletionQuery(
            asyncActionModel.internalRelationId
          )
        )

      case AsyncActionCategoryEnum.LEAD_IMPORT:
        return await this.handleLeadImport(
          asyncActionModel.organizationId,
          asyncActionModel.internalRelationId
        )

      default:
        this.logger.error({
          message: `invalid category ${asyncActionModel.category}`,
          data: { asyncActionModel },
        })
        throw new Error(`invalid category ${asyncActionModel.category}`)
    }
  }

  private async handleLeadImport(
    organizationId: string,
    internalRelationId: string
  ): Promise<Date> {
    try {
      const leadImportModel = await this.queryBus.execute(
        new GetOneLeadImportByIdQuery(internalRelationId, organizationId)
      )

      if (!leadImportModel) {
        this.logger.error({
          feature: LogFeature.LEAD_IMPORT,
          message: `No lead import found for id ${internalRelationId}`,
          data: { organizationId, internalRelationId },
        })
        throw new Error('No lead import found')
      }

      const estimatedMsToImportOneLeadBySource = this.configService.get<
        Record<LeadImportSourceEnum, number>
      >('async-actions.estimatedMsToImportOneLeadBySource')

      const estimatedMsPerLead =
        estimatedMsToImportOneLeadBySource?.[leadImportModel.source] || 0
      const totalEstimatedMs = leadImportModel.nbLeadsTotal * estimatedMsPerLead

      return new Date(Date.now() + totalEstimatedMs)
    } catch (error) {
      this.logger.error({
        error,
        feature: LogFeature.LEAD_IMPORT,
        message: `Error estimating time of completion for lead import: ${error.message}`,
        data: { organizationId, internalRelationId },
      })
      return new Date()
    }
  }
}
