import { describe, expect, it } from '@jest/globals'
import { TestBed } from '@automock/jest'
import { SearchService } from './search.service'

describe('SearchService', () => {
  let service: SearchService

  beforeEach(() => {
    const { unit } = TestBed.create(SearchService).compile()
    service = unit
  })

  describe('removeEmptyValues', () => {
    beforeEach(() => {
      jest.spyOn(service, 'isValueEmpty').mockImplementation(value => {
        if (value === null || value === undefined) return true
        if (typeof value === 'number' && Number.isNaN(value)) return true
        if (typeof value === 'object' && value instanceof Date) return false
        if (typeof value === 'string' && value.length === 0) return true
        if (typeof value === 'object' && Object.keys(value).length === 0)
          return true
        return false
      })
    })

    it('should remove null and undefined values', () => {
      const input = { a: 1, b: null, c: undefined, d: 'hello' }
      const expected = { a: 1, d: 'hello' }
      expect(service.removeEmptyValues(input)).toEqual(expected)
    })

    it('should remove empty objects and arrays', () => {
      const input = { a: {}, b: [], c: { d: 1 }, e: [1] }
      const expected = { c: { d: 1 }, e: [1] }
      expect(service.removeEmptyValues(input)).toEqual(expected)
    })

    it('should handle nested objects', () => {
      const input = { a: { b: { c: null, d: 1 } }, e: { f: {} } }
      const expected = { a: { b: { d: 1 } } }
      expect(service.removeEmptyValues(input)).toEqual(expected)
    })

    it('should preserve non-empty falsy values', () => {
      const input = { a: 0, b: false, c: '', d: NaN }
      const expected = { a: 0, b: false }
      expect(service.removeEmptyValues(input)).toEqual(expected)
    })

    it('should return undefined for completely empty objects', () => {
      const input = { a: null, b: undefined, c: {}, d: [] }
      expect(service.removeEmptyValues(input)).toBeUndefined()
    })

    it('should handle non-object inputs', () => {
      expect(service.removeEmptyValues(0)).toBe(0)
      expect(service.removeEmptyValues(1)).toBe(1)
      expect(service.removeEmptyValues(-1)).toBe(-1)
      expect(service.removeEmptyValues(Infinity)).toBe(Infinity)
      expect(service.removeEmptyValues(-Infinity)).toBe(-Infinity)
      expect(service.removeEmptyValues(NaN)).toBeUndefined()
    })

    it('should not modify arrays', () => {
      const input = [1, null, 2, undefined, 3]
      expect(service.removeEmptyValues(input)).toEqual(input)
    })

    it('should handle date objects', () => {
      const date = new Date('2024-09-21T09:18:06.817Z')
      const input = {
        name: 'John DOE',
        birthDate: date,
      }
      const expected = {
        name: 'John DOE',
        birthDate: date,
      }
      expect(service.removeEmptyValues(input)).toEqual(expected)
    })

    afterEach(() => {
      jest.restoreAllMocks()
    })
  })

  describe('isValueEmpty', () => {
    describe('numbers', () => {
      it('should return false for non-NaN numbers', () => {
        expect(service.isValueEmpty(0)).toBe(false)
        expect(service.isValueEmpty(1)).toBe(false)
        expect(service.isValueEmpty(-1)).toBe(false)
        expect(service.isValueEmpty(Infinity)).toBe(false)
        expect(service.isValueEmpty(-Infinity)).toBe(false)
      })

      it('should return true for NaN', () => {
        expect(service.isValueEmpty(NaN)).toBe(true)
      })
    })

    describe('strings', () => {
      it('should return true for empty strings', () => {
        expect(service.isValueEmpty('')).toBe(true)
      })

      it('should return false for non-empty strings', () => {
        expect(service.isValueEmpty('hello')).toBe(false)
        expect(service.isValueEmpty(' ')).toBe(false)
        expect(service.isValueEmpty('0')).toBe(false)
      })
    })

    describe('null and undefined', () => {
      it('should return true for null', () => {
        expect(service.isValueEmpty(null)).toBe(true)
      })

      it('should return true for undefined', () => {
        expect(service.isValueEmpty(undefined)).toBe(true)
      })
    })

    describe('objects', () => {
      it('should return true for empty objects', () => {
        expect(service.isValueEmpty({})).toBe(true)
      })

      it('should return false for non-empty objects', () => {
        expect(service.isValueEmpty({ a: 1 })).toBe(false)
      })

      it('should return false for valid Date objects', () => {
        expect(service.isValueEmpty(new Date())).toBe(false)
      })
    })

    describe('arrays', () => {
      it('should return true for empty arrays', () => {
        expect(service.isValueEmpty([])).toBe(true)
      })

      it('should return false for non-empty arrays', () => {
        expect(service.isValueEmpty([1, 2, 3])).toBe(false)
      })
    })

    describe('booleans', () => {
      it('should return false for both true and false', () => {
        expect(service.isValueEmpty(true)).toBe(false)
        expect(service.isValueEmpty(false)).toBe(false)
      })
    })

    describe('functions', () => {
      it('should return false for functions', () => {
        expect(service.isValueEmpty(() => {})).toBe(false)
      })
    })

    describe('symbols', () => {
      it('should return false for symbols', () => {
        expect(service.isValueEmpty(Symbol('test'))).toBe(false)
      })
    })
  })

  describe('transformData', () => {
    it('should transform string values to lowercase and remove empty values', () => {
      const input = { name: 'John DOE', email: '<EMAIL>', empty: '' }
      const expected = { name: 'john doe', email: '<EMAIL>' }
      expect(service.transformData(input)).toEqual(expected)
    })

    it('should not transform non-string values and remove null/undefined', () => {
      const input = {
        age: 30,
        isActive: true,
        isNotActive: false,
        score: 9.5,
        nullValue: null,
        undefinedValue: undefined,
        date: new Date('2024-09-21T09:18:06.817Z'),
        nan: NaN,
      }
      const expected = {
        age: 30,
        isActive: true,
        isNotActive: false,
        score: 9.5,
        date: new Date('2024-09-21T09:18:06.817Z'),
      }
      expect(service.transformData(input)).toEqual(expected)
    })

    it('should handle nested objects', () => {
      const input = {
        user: {
          name: 'Alice SMITH',
          contact: {
            email: '<EMAIL>',
            phone: '************',
            fax: null,
          },
        },
      }
      const expected = {
        user: {
          name: 'alice smith',
          contact: {
            email: '<EMAIL>',
            phone: '************',
          },
        },
      }
      expect(service.transformData(input)).toEqual(expected)
    })

    it('should not transform values for ignored keys but still remove empty values', () => {
      const input = {
        name: 'John DOE',
        labels: ['IMPORTANT', 'URGENT', ''],
        emptyLabel: '',
      }
      const expected = {
        name: 'john doe',
        labels: ['IMPORTANT', 'URGENT', ''],
      }
      expect(service.transformData(input)).toEqual(expected)
    })

    it('should return undefined for empty input after removing empty values', () => {
      expect(
        service.transformData({ a: null, b: undefined, c: '' })
      ).toBeUndefined()
    })

    it('should handle arrays', () => {
      const input = {
        names: ['John', 'ALICE', 'BOB'],
        scores: [1, 2, null, 3],
      }
      const expected = {
        names: ['john', 'alice', 'bob'],
        scores: [1, 2, null, 3],
      }
      expect(service.transformData(input)).toEqual(expected)
    })
  })
})
