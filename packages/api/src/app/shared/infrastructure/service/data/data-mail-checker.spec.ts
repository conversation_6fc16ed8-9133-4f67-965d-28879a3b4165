import { ConfigService } from '@nestjs/config'
import { Test, TestingModule } from '@nestjs/testing'
import { ContactInterface } from '../../../domain/model/lead/contact.interface'
import { CheckEmailsResponse } from '../../../domain/type/data-mail-checker.type'
import { LoggerService } from '../../../logger.service'
import { CacheService } from '../cache.service'
import { HttpService } from '../http.service'
import { DataMailCheckerService } from './data-mail-checker.service'

// Mock HttpService
const mockHttpService = {
  fetchWithTimeout: jest.fn(),
}

// Mock CacheService
const mockCacheService = {
  get: jest.fn(),
  set: jest.fn(),
  delete: jest.fn(),
}

// Mock ConfigService
const mockConfigService = {
  get: jest.fn((key: string) => {
    // Provide default mock values for config keys used in the service
    switch (key) {
      case 'data-mail-check-service.mailCheckGooglePrivateKey':
        return '-----BEGIN PRIVATE KEY-----\nMOCK_PRIVATE_KEY\n-----END PRIVATE KEY-----'
      case 'data-mail-check-service.mailCheckGoogleClientEmail':
        return '<EMAIL>'
      case 'data-mail-check-service.mailCheckUrl':
        return 'https://mock-mail-checker.example.com/api/v2'
      default:
        return null
    }
  }),
}

describe('DataMailCheckerService', () => {
  let service: DataMailCheckerService

  beforeEach(async () => {
    // Reset mocks before each test
    jest.clearAllMocks()

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DataMailCheckerService,
        { provide: HttpService, useValue: mockHttpService },
        { provide: CacheService, useValue: mockCacheService },
        { provide: ConfigService, useValue: mockConfigService },
        LoggerService, // Provide the original (mocked via jest.mock)
      ],
    }).compile()

    service = module.get<DataMailCheckerService>(DataMailCheckerService)

    mockCacheService.get.mockResolvedValue('mock-token')
  })

  describe('checkEmailsAPIV2', () => {
    it('should filter blacklisted domains and not call API for them', async () => {
      const testContacts: Partial<ContactInterface>[] = [
        {
          id: 'contact-1',
          emails: ['<EMAIL>', '<EMAIL>', '<EMAIL>'], // One valid, 2 blacklisted
        },
        {
          id: 'contact-2',
          emails: ['<EMAIL>'], // Blacklisted
        },
        {
          id: 'contact-3',
          emails: ['<EMAIL>'], // Valid
        },
      ]

      // Mock the successful API response for the valid emails
      const mockApiResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        json: jest.fn().mockResolvedValue({
          result: [
            {
              email: '<EMAIL>',
              reason: 'deliverable',
              is_pro: true,
              is_valid: true,
            },
          ],
        }),
      }
      // Use any type assertion because the mock shape doesn't perfectly match Response
      mockHttpService.fetchWithTimeout.mockResolvedValue(mockApiResponse as any)

      const results: CheckEmailsResponse =
        await service.checkEmailsAPIV2(testContacts)

      // Verify fetchWithTimeout was called only for non-blacklisted domains
      expect(mockHttpService.fetchWithTimeout).toHaveBeenCalledTimes(2) // 1 call per contact

      // Verify the first call <NAME_EMAIL>
      expect(mockHttpService.fetchWithTimeout).toHaveBeenNthCalledWith(
        1,
        new URL('https://mock-mail-checker.example.com/api/v2'), // URL from mock ConfigService
        expect.objectContaining({
          body: expect.stringContaining('"emails":["<EMAIL>"]'),
          headers: expect.objectContaining({
            Authorization: 'Bearer mock-token',
          }),
          method: 'POST',
        }),
        35_000 // Timeout
      )

      // Verify the second call <NAME_EMAIL>
      expect(mockHttpService.fetchWithTimeout).toHaveBeenNthCalledWith(
        2,
        new URL('https://mock-mail-checker.example.com/api/v2'), // URL from mock ConfigService
        expect.objectContaining({
          body: expect.stringContaining('"emails":["<EMAIL>"]'),
          headers: expect.objectContaining({
            Authorization: 'Bearer mock-token',
          }),
          method: 'POST',
        }),
        35_000 // Timeout
      )

      // Check that the results array contains entries only for contacts where valid emails were processed
      expect(results).toHaveLength(2) // Contact-1 (due to good-domain.com) and Contact-3
      expect(results.map(r => r.context.id)).toEqual(['contact-1', 'contact-3'])

      // Ensure the mocked json function inside the mock response was called for each successful fetch
      expect(mockApiResponse.json).toHaveBeenCalledTimes(2)
    })

    it('should return an empty array if all emails are blacklisted', async () => {
      const testContacts: Partial<ContactInterface>[] = [
        {
          id: 'contact-1',
          emails: ['<EMAIL>', '<EMAIL>'], // Both blacklisted
        },
      ]

      const results: CheckEmailsResponse =
        await service.checkEmailsAPIV2(testContacts)

      expect(mockHttpService.fetchWithTimeout).not.toHaveBeenCalled()
      expect(results).toEqual([])
    })

    it('should return an empty array if no contacts are provided', async () => {
      const results: CheckEmailsResponse = await service.checkEmailsAPIV2([])
      expect(mockHttpService.fetchWithTimeout).not.toHaveBeenCalled()
      expect(results).toEqual([])
    })

    it('should return an empty array if contacts have no emails', async () => {
      const testContacts: Partial<ContactInterface>[] = [
        { id: 'contact-1', emails: [] },
        { id: 'contact-2' }, // emails property is undefined
      ]
      const results: CheckEmailsResponse =
        await service.checkEmailsAPIV2(testContacts)
      expect(mockHttpService.fetchWithTimeout).not.toHaveBeenCalled()
      expect(results).toEqual([])
    })
  })
})
