import { errors } from '@elastic/elasticsearch'
import { BulkOperation } from '@elastic/elasticsearch/api/types'
import { Injectable, OnApplicationBootstrap } from '@nestjs/common'
import { ElasticsearchService } from '@nestjs/elasticsearch'
import { objectValuesTransformer } from '../../../../ui/api/shared/infrastructure/helper/object.helper'
import { LeadIndexName } from '../../../lead/domain/lead-index.enum'
import { FieldModel } from '../../../lead/domain/model/field.model'
import { SearchOperation } from '../../domain/search.enum'
import {
  SearchBulkDeleteItem,
  SearchBulkIndexItem,
  SearchFilter,
  SearchQuery,
  SearchQueryParams,
  SearchResponse,
  SearchResult,
} from '../../domain/search.interface'
import { SortType } from '../../domain/sort-type.enum'
import { LogFeature, LoggerService } from '../../logger.service'
import { luceneEscapeSpecials } from '../helper/lucene-escape.helper'
import {
  buildSortQuery,
  searchConvertTextToKeywordHelper,
  searchGroup,
  searchJoinWithOr,
  searchQueryFiltersProcessors,
  searchQueryMultiValueFiltersProcessors,
  searchSurroundWithWildcards,
  searchTransformSpaceToAnd,
} from '../helper/search.helper'
import { IndicesPutTemplate } from '@elastic/elasticsearch/api/requestParams'

@Injectable()
export class SearchService implements OnApplicationBootstrap {
  /**
   * A contact with 1 company + 1 enrich is ~3KB
   * 3KB * 2500 = 7.5 MB, ES recommends 7MB to 15MB per payload
   * ! NOTE, that value must be EVEN because the indexing payloads work in pair (index meta followed by the actual doc, so 2 items each time)
   */
  public static readonly BULK_OPERATIONS_CHUNK_SIZE = 2500

  private readonly loggerService: LoggerService

  constructor(private readonly esService: ElasticsearchService) {
    this.loggerService = new LoggerService({
      context: SearchService.name,
      feature: LogFeature.INDEXATION,
    })
  }

  async onApplicationBootstrap() {
    //create templates if not exists
    await this.upsertTemplate(
      {
        name: 'company_template',
        body: {
          index_patterns: [`${LeadIndexName.COMPANY}*`],
          version: 1,
          settings: {
            number_of_shards: '1',
            number_of_replicas: '1',
            index: {
              analysis: {
                analyzer: {
                  default: {
                    type: 'custom',
                    tokenizer: 'whitespace',
                    filter: ['lowercase', 'asciifolding'],
                  },
                },
              },
            },
          },
        },
      },
      {
        updateMappingMode: 'none',
      }
    )

    await this.upsertTemplate(
      {
        name: 'lead_template',
        body: {
          index_patterns: [`${LeadIndexName.CONTACT}*`],
          version: 1,
          settings: {
            number_of_shards: '1',
            number_of_replicas: '1',
            index: {
              analysis: {
                analyzer: {
                  default: {
                    type: 'custom',
                    tokenizer: 'whitespace',
                    filter: ['lowercase', 'asciifolding'],
                  },
                },
              },
            },
          },
          mappings: {
            properties: {
              sequences: {
                type: 'nested',
                properties: {
                  id: { type: 'keyword' },
                  name: { type: 'text' },
                  reviewStatus: { type: 'keyword' },
                },
              },
            },
          },
        },
      },
      {
        updateMappingMode: 'update',
      }
    )
  }

  private async upsertTemplate(
    template: IndicesPutTemplate<Record<string, any>>,
    options: {
      updateMappingMode: 'reindex' | 'update' | 'none'
    }
  ) {
    try {
      const { body: templateFound } = await this.esService.indices.getTemplate({
        name: template.name,
      })

      const currentVersion = templateFound[template.name]?.version ?? 0
      const newVersion = template.body?.version ?? 0

      if (currentVersion < newVersion) {
        await this.esService.indices.put_template(template)
        this.loggerService.log({
          message: `Template ${template.name} updated from version ${currentVersion} to ${newVersion}`,
        })

        switch (options.updateMappingMode) {
          case 'reindex':
            await this.updateMappingWithReindex(template)
            break
          case 'update':
            await this.updateMapping(template)
            break
          case 'none':
            break
        }
      }
    } catch (error) {
      if (error instanceof errors.ResponseError && error.statusCode === 404) {
        await this.esService.indices.put_template(template)
        this.loggerService.log({
          message: `Template ${template.name} created`,
        })
      } else {
        this.loggerService.error({
          message: `Error occurred while upserting template ${template.name}`,
          error,
        })
      }
    }
  }

  private async updateMapping(
    template: IndicesPutTemplate<Record<string, any>>
  ) {
    const { body: catIndices } = await this.esService.cat.indices<
      {
        index: string
      }[]
    >({
      format: 'json',
      index: template.body.index_patterns,
    })

    for (const { index } of catIndices) {
      await this.esService.indices.putMapping({
        index,
        body: template.body,
      })
    }
  }

  private async updateMappingWithReindex(
    _: IndicesPutTemplate<Record<string, any>>
  ) {
    /**
     * TODO :
     * - Get all indexes from pattern
     * - For each index, create a new index with the new settings and mappings
     * - Reindex the data from the old index to the new index
     * - Update the alias to point to the new index
     * - Delete the old index
     *  */
    throw new Error('Not implemented because not necessary for now')
  }

  /**
   * Transform and cleanall the values before indexing
   * @param data
   */
  transformData(data: unknown) {
    const cloneData = structuredClone(data)
    const cleanData = this.removeEmptyValues(cloneData)
    if (!cleanData) {
      return undefined
    }
    return objectValuesTransformer({
      obj: cleanData,
      fn: value => {
        if (typeof value === 'string') {
          return value.toLowerCase()
        }
        return value
      },
      ignoredKeys: ['labels'],
    })
  }

  isValueEmpty(value: unknown): boolean {
    if (typeof value === 'number') {
      return Number.isNaN(value)
    }

    return (
      value === null ||
      value === undefined ||
      value === '' ||
      (typeof value === 'object' &&
        Object.keys(value).length === 0 &&
        Object.prototype.toString.call(value) !== '[object Date]')
    )
  }

  removeEmptyValues<T>(obj: T): T | undefined {
    if (obj === null || obj === undefined) {
      return undefined
    }

    if (typeof obj !== 'object') {
      return this.isValueEmpty(obj) ? undefined : obj
    }

    if (
      obj instanceof Date ||
      Object.prototype.toString.call(obj) === '[object Date]' ||
      Array.isArray(obj)
    ) {
      return obj as T
    }

    const result = Object.fromEntries(
      Object.entries(obj)
        .map(([k, v]) => [k, this.removeEmptyValues(v)])
        .filter(([, v]) => !this.isValueEmpty(v))
    )
    return Object.keys(result).length ? (result as T) : undefined
  }

  /**
   * Create or update a document in the index
   * @param index
   * @param document
   * @param id -  the id of the document to update or create if not exists
   * @param refresh - if true, the index will be refreshed after the operation
   */
  index(
    index: string,
    document: unknown,
    id: string,
    refresh: 'wait_for' | boolean = false
  ) {
    return this.esService
      .index({ index, body: document, id, refresh: refresh })
      .catch(error => {
        this.loggerService.error({
          message: 'Error occurred while indexing document',
          error,
        })
      })
  }

  delete(index: string, id: string, refresh: 'wait_for' | boolean = false) {
    return this.esService.delete({ index, id, refresh: refresh })
  }

  /**
   * Delete an entire Elasticsearch index
   * @param index - The name of the index to delete
   */
  async deleteIndex(index: string) {
    try {
      const response = await this.esService.indices.delete({ index })
      this.loggerService.log({
        message: `Successfully deleted index: ${index}`,
      })
      return response
    } catch (error) {
      if (error.statusCode === 404) {
        this.loggerService.log({
          message: `Index ${index} does not exist, skipping deletion`,
        })
        return null
      }
      this.loggerService.error({
        message: `Error occurred while deleting index: ${index}`,
        error,
      })
      throw error
    }
  }

  async executeBulkOperations(
    operations: BulkOperation[],
    refresh: 'wait_for' | boolean = false
  ) {
    if (!Array.isArray(operations) || operations.length === 0) {
      return []
    }

    const chunks: BulkOperation[][] = []
    const results = []
    for (
      let i = 0;
      i < operations.length;
      i += SearchService.BULK_OPERATIONS_CHUNK_SIZE
    ) {
      chunks.push(
        operations.slice(i, i + SearchService.BULK_OPERATIONS_CHUNK_SIZE)
      )
    }

    try {
      for (let i = 0; i < chunks.length; i++) {
        const currentOperationsChunk = chunks[i]
        results.push(
          await this.esService.bulk({
            // ? only force refresh on last chunk and if refresh is truthy
            refresh: refresh && i === chunks.length - 1,
            body: currentOperationsChunk,
          })
        )
      }
      return results
    } catch (e) {
      this.loggerService.error({
        message: 'Error occurred while executing bulk operations',
        error: e,
      })
      return []
    }
  }

  bulkIndex(
    documents: SearchBulkIndexItem[],
    refresh: 'wait_for' | boolean = false
  ) {
    const operations: BulkOperation[] = documents.flatMap(doc => [
      { index: { _index: doc.index, _id: doc.id } },
      doc.document,
    ])

    return this.executeBulkOperations(operations, refresh)
  }

  bulkDelete(
    documents: SearchBulkDeleteItem[],
    refresh: 'wait_for' | boolean = false
  ) {
    const operations = documents.flatMap(doc => [
      { delete: { _index: doc.index, _id: doc.id } },
    ])

    return this.esService.bulk({ refresh: refresh, body: operations })
  }

  buildDistinctQuery(
    searchQueryParams: SearchQueryParams,
    fieldModels: FieldModel[],
    distinctField: string
  ): SearchQuery {
    const query = this.buildSearchQuery(searchQueryParams, fieldModels)
    const fieldKey = searchConvertTextToKeywordHelper(
      distinctField,
      fieldModels.find(f => f.id === distinctField)
    )
    return {
      ...query,
      size: 0,
      aggs: {
        distinct_values: {
          terms: {
            field: fieldKey,
            size: searchQueryParams.limit || 10,
            order: {
              _key:
                searchQueryParams.sort?.find(v => v.field === distinctField)
                  ?.order || SortType.ASC,
            },
          },
        },
        distinct_enriched_values: {
          terms: {
            field: `enrichmentResult.${fieldKey}`,
            size: searchQueryParams.limit || 10,
            order: {
              _key:
                searchQueryParams.sort?.find(v => v.field === distinctField)
                  ?.order || SortType.ASC,
            },
          },
        },
      },
    }
  }

  buildSearchQuery(
    searchQueryParams: SearchQueryParams,
    fieldModels: FieldModel[],
    withEnrichedFields = false
  ): SearchQuery {
    const queryItems = []

    searchQueryParams.searchText = (searchQueryParams.searchText || '').trim()
    //build main query
    const querySearchText = searchGroup(
      searchSurroundWithWildcards(
        searchTransformSpaceToAnd(
          luceneEscapeSpecials(
            searchQueryParams.searchText.toLowerCase(),
            false
          )
        )
      )
    )

    if (!querySearchText) {
      //querySearchText = '*'
    }

    if (querySearchText) {
      queryItems.push(querySearchText)
    }

    //build filters query
    const excludedEnrichedFields = ['archived', 'availableInWorkspace']
    for (let i = 0; i < searchQueryParams.filters.length; i++) {
      const filterItem = searchQueryParams.filters[i]
      filterItem.field = luceneEscapeSpecials(filterItem.field, true)

      let filtersQueryItem = null
      if (filterItem.from || filterItem.to) {
        const operatorProcessor =
          searchQueryMultiValueFiltersProcessors[filterItem.operator]
        if (!operatorProcessor) {
          continue
        }
        let fromValue = null
        if (filterItem.from) {
          fromValue = searchTransformSpaceToAnd(
            luceneEscapeSpecials(filterItem.from.trim().toLowerCase(), false)
          )
        }
        let toValue = null
        if (filterItem.to) {
          toValue = searchTransformSpaceToAnd(
            luceneEscapeSpecials(filterItem.to.trim().toLowerCase(), false)
          )
        }
        filtersQueryItem = operatorProcessor(
          filterItem.field,
          fromValue,
          toValue
        )

        //add enriched fields to the query
        if (
          withEnrichedFields &&
          !excludedEnrichedFields.includes(filterItem.field)
        ) {
          const enrichedFiltersQueryItem = operatorProcessor(
            `enrichmentResult.${filterItem.field}`,
            fromValue,
            toValue
          )
          filtersQueryItem = searchGroup(
            searchJoinWithOr([filtersQueryItem, enrichedFiltersQueryItem])
          )
        }
      } else {
        const operatorProcessor =
          searchQueryFiltersProcessors[filterItem.operator]
        if (!operatorProcessor) {
          continue
        }
        let operatorValue = null
        if (filterItem.values && filterItem.values.length > 0) {
          operatorValue = filterItem.values.map(filterItemValue =>
            searchGroup(
              searchTransformSpaceToAnd(
                luceneEscapeSpecials(
                  filterItemValue.trim().toLowerCase(),
                  false
                )
              )
            )
          )
        } else if (filterItem.value) {
          operatorValue = searchTransformSpaceToAnd(
            luceneEscapeSpecials(filterItem.value.trim().toLowerCase(), false)
          )
        }

        filtersQueryItem = operatorProcessor(filterItem.field, operatorValue)

        //add enriched fields to the query
        if (
          withEnrichedFields &&
          !excludedEnrichedFields.includes(filterItem.field)
        ) {
          const enrichedFiltersQueryItem = operatorProcessor(
            `enrichmentResult.${filterItem.field}`,
            operatorValue
          )
          filtersQueryItem = searchGroup(
            searchJoinWithOr([filtersQueryItem, enrichedFiltersQueryItem])
          )
        }
      }

      if (filtersQueryItem) {
        queryItems.push(filtersQueryItem)
      }
    }

    //convert fields searchQueryParams.fields
    const fields = []
    const customSortingOrders = {}

    //TODO remove this part after analyzer setup
    //build fields with combination of field + field.keyword if possible
    for (let i = 0; i < searchQueryParams.fields.length; i++) {
      const field = searchQueryParams.fields[i]
      fields.push(field)
      const fieldModel = fieldModels.find(f => f.id === field)
      if (!fieldModel) {
        continue
      }
      const keywordField = searchConvertTextToKeywordHelper(field, fieldModel)
      if (keywordField !== field) {
        fields.push(keywordField)
      }

      if (fieldModel.customSortingOrder) {
        customSortingOrders[field] = fieldModel.customSortingOrder
      }
    }

    if (withEnrichedFields) {
      // fields.push(...fields.map(f => `enrichmentResult.${f}`))
      if (withEnrichedFields) {
        for (let i = fields.length - 1; i >= 0; i--) {
          const field = fields[i]
          if (!excludedEnrichedFields.includes(field)) {
            fields.push(`enrichmentResult.${field}`)
          }
        }
      }
    }

    //convert sort searchQueryParams.sort
    searchQueryParams.sort = searchQueryParams.sort.map(sort => {
      const fieldModel = fieldModels.find(
        f => f.id === sort.field || `enrichmentResult.${f.id}` === sort.field
      )
      if (!fieldModel) {
        return sort
      }
      return {
        ...sort,
        field: searchConvertTextToKeywordHelper(sort.field, fieldModel),
      }
    })

    const sort = searchQueryParams.sort || []

    let esQuery: any
    if (searchQueryParams.rawFilters.length > 0) {
      esQuery = {
        query: {
          bool: {
            filter: searchQueryParams.rawFilters.map(
              this.rawFilterToEsFilter.bind(this)
            ),
          },
        },
      }
      esQuery.query.bool.must = [
        {
          query_string: {
            query: queryItems.length > 0 ? queryItems.join(' AND ') : '*',
            fields: fields ?? [],
          },
        },
      ]
    } else {
      esQuery = {
        query: queryItems.join(' AND '),
      }
    }
    return {
      query: esQuery.query,
      source: searchQueryParams.source || [],
      sort: buildSortQuery(sort, customSortingOrders),
      fields: !querySearchText ? [] : fields,
      from: searchQueryParams.offset || 0,
      size: searchQueryParams.limit || 10,
      aggs: this.getExtraTotalsAggs(),
    }
  }

  rawFilterToEsFilter(rawSearchFilter: SearchFilter): object {
    const { field, value } = rawSearchFilter

    switch (rawSearchFilter.operator) {
      case SearchOperation.EMPTY:
        return {
          bool: {
            must_not: {
              exists: { field },
            },
          },
        }
      case SearchOperation.NOT_EMPTY:
        return {
          bool: {
            must: {
              exists: { field },
            },
          },
        }
      case SearchOperation.EQUALS_OR_EMPTY:
        return {
          bool: {
            should: [
              {
                term: {
                  [field]: value,
                },
              },
              {
                bool: {
                  must_not: {
                    exists: { field },
                  },
                },
              },
            ],
            minimum_should_match: 1,
          },
        }
      case SearchOperation.EQUALS:
        return {
          term: {
            [field]: value,
          },
        }
    }
  }

  getExtraTotalsAggs() {
    return {
      extra_total: {
        global: {},
        aggs: {
          total_not_archived: {
            filter: {
              bool: {
                filter: [
                  { term: { archived: false } },
                  {
                    bool: {
                      should: [
                        { term: { availableInWorkspace: true } },
                        {
                          bool: {
                            must_not: {
                              exists: { field: 'availableInWorkspace' },
                            },
                          },
                        },
                      ],
                      minimum_should_match: 1,
                    },
                  },
                ],
              },
            },
          },
        },
      },
    }
  }

  async search<T>(
    index: string,
    searchQuery: SearchQuery
  ): Promise<SearchResult<T>> {
    const aggs = searchQuery.aggs || {}
    const response = await this.esService
      .search<SearchResponse<T>>({
        index,
        body: {
          _source: searchQuery.source,
          sort: searchQuery.sort,
          track_total_hits: true,
          query:
            typeof searchQuery.query === 'string' // ? to stay backward compatible with non-optimized queries
              ? {
                  query_string: {
                    query: searchQuery.query,
                    fields: searchQuery.fields,
                    //fuzzy_prefix_length: 0,
                  },
                }
              : searchQuery.query,
          aggs,
          from: searchQuery.from,
          size: searchQuery.size,
        },
      })
      .catch(error => {
        this.loggerService.error({
          message: `Error occurred while searching in index ${index}`,
          data: {
            rawQuery: searchQuery,
          },
          error,
        })
        if (
          error instanceof errors.ResponseError &&
          (error?.statusCode === 404 || error?.statusCode === 400)
        ) {
          return {
            body: {
              took: 0,
              hits: { total: { value: 0 }, hits: [] },
              aggregations: {
                extra_total: {
                  doc_count: 0,
                  total_not_archived: { doc_count: 0 },
                },
              },
            },
          }
        } else {
          // ? non-recoverable error, so rethrow
          throw error
        }
      })

    this.loggerService.log({
      message: `[search.service][opensearch] - ran search query in ${response.body.took} ms`,
      data: {
        tookMs: response.body.took,
        rawQuery: searchQuery,
        returnedHits: response.body.hits.hits.length,
      },
    })

    return {
      took: response.body.took,
      total: response.body.hits.total.value ?? 0,
      totalGlobal: response.body.aggregations?.extra_total?.doc_count ?? 0,
      totalGlobalNotArchived:
        response.body.aggregations?.extra_total?.total_not_archived
          ?.doc_count ?? 0,
      data:
        response.body.hits.hits.map(v => {
          const data = { ...v._source }
          data.score = v._score
          return data
        }) ?? [],
      aggs: response.body.aggregations,
    }
  }
}
