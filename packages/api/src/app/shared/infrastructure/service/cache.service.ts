import { Inject, Injectable } from '@nestjs/common'
import Redis from 'ioredis'

@Injectable()
export class CacheService {
  constructor(@Inject('REDIS_CACHE') private readonly redis: Redis) {}

  async getValueFromHash<T>(key: string, field: string) {
    return this.redis.hget(key, field)
  }

  async get(key: string): Promise<string | null> {
    return this.redis.get(key)
  }

  async exists(key: string): Promise<number> {
    return this.redis.exists(key)
  }

  async set(key: string, value: string | number, ttl?: number): Promise<void> {
    if (ttl !== undefined) {
      await this.redis.set(key, value, 'EX', ttl)
    } else {
      await this.redis.set(key, value)
    }
  }

  find(keyPattern: string): Promise<string[]> {
    return this.redis.keys(keyPattern)
  }

  async delete(key): Promise<number> {
    return this.redis.del(key)
  }

  async incr(key: string): Promise<number> {
    return this.redis.incr(key)
  }

  async incrBy(key: string, increment: number): Promise<number> {
    return this.redis.incrby(key, increment)
  }

  async decr(key: string): Promise<number> {
    return this.redis.decr(key)
  }

  async decrBy(key: string, decrement: number): Promise<number> {
    return this.redis.decrby(key, decrement)
  }
}
