import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { LogFeature, LoggerService } from '../../logger.service'
import { GitlabConfig } from '../../../../config/gitlab.config'
import {
  GitlabFeatureFlag,
  GitlabFeatureFlagStrategy,
  EnvironmentScopes,
} from '@getheroes/shared'
import { FeatureFlagRepository } from '../../application/ports/feature-flag.repository'
import { FeatureFlagModel } from '../../domain/model/feature-flag/feature-flag.model'
import { ALL_COMPANIES } from '../../application/service/feature-flag.service'

@Injectable()
export class GitlabService implements FeatureFlagRepository {
  private readonly logger: LoggerService
  private readonly gitlabConfig: GitlabConfig

  constructor(private readonly configService: ConfigService) {
    this.logger = new LoggerService({
      context: GitlabService.name,
      feature: LogFeature.FEATURE_FLAG,
    })

    this.gitlabConfig = this.configService.get<GitlabConfig>('gitlab')
  }

  /**
   * Fetch a specific feature flag from GitLab
   * @param flagName The name of the feature flag to fetch
   * @returns The feature flag or null if not found
   */
  async getFeatureFlag(flagName: string): Promise<FeatureFlagModel | null> {
    if (!this.gitlabConfig.projectId || !this.gitlabConfig.token) {
      throw new Error('GitLab project ID or token is not configured')
    }

    try {
      const url = `${this.gitlabConfig.apiUrl}/projects/${this.gitlabConfig.projectId}/feature_flags/${flagName}`
      const response = await fetch(url, {
        headers: {
          'PRIVATE-TOKEN': this.gitlabConfig.token,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        if (response.status === 404) {
          return null
        }
        throw new Error(`Failed to fetch feature flag: ${flagName}`)
      }

      const data: GitlabFeatureFlag = await response.json()
      return new FeatureFlagModel(
        data.name,
        data.strategies
          .filter(this.filterByScope)
          .flatMap(
            strategy =>
              strategy.user_list?.user_xids?.split(',') ?? [ALL_COMPANIES]
          ),
        data.active
      )
    } catch (error) {
      this.logger.error({
        message: `Error fetching feature flag ${flagName} from GitLab`,
        error,
      })
      return null
    }
  }

  private filterByScope(strategy: GitlabFeatureFlagStrategy): boolean {
    let scopes: EnvironmentScopes[] = []

    switch (process.env.ENVIRONMENT) {
      case 'production':
        scopes = [
          EnvironmentScopes.PRODUCTION,
          EnvironmentScopes.PROD_DASHBOARD_FRONTEND,
        ]
        break
      case 'dev':
        scopes = [
          EnvironmentScopes.DEV_DASHBOARD_API,
          EnvironmentScopes.DEV_DASHBOARD_API_CORRECT,
          EnvironmentScopes.DEV_DASHBOARD_CLI,
          EnvironmentScopes.DEV_DASHBOARD,
          EnvironmentScopes.DEV_DASHBOARD_FRONTEND,
        ]
        break
      case 'staging':
        scopes = [EnvironmentScopes.STAGING_DASHBOARD_FRONTEND]
        break
      default:
        scopes = []
        break
    }

    return strategy.scopes.some(scope =>
      scopes.includes(scope.environment_scope)
    )
  }
}
