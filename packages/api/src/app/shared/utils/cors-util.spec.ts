import { getOrigins } from './cors-util'

describe('getOrigins', () => {
  it('should return "*" when NODE_ENV is "development"', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'

    const result = getOrigins('dummy')
    expect(result).toBe('*')

    process.env.NODE_ENV = originalEnv
  })

  it('should throw an error if rawOrigins is not defined', () => {
    expect(() => getOrigins('')).toThrow(
      new Error('CORS_ORIGIN is not defined')
    )
  })

  it('should return an array if rawOrigins is a valid JSON array', () => {
    const rawOrigins = '["https://example.com", "http://localhost:3000"]'
    const result = getOrigins(rawOrigins)

    expect(result).toEqual(['https://example.com', 'http://localhost:3000'])
  })

  it('should parse rawOrigins as a comma-separated string', () => {
    const rawOrigins = 'https://example.com, http://localhost:3000,example.org'
    const result = getOrigins(rawOrigins)

    expect(result).toHaveLength(3)
    expect(result[0]).toBe('https://example.com')
    expect(result[1]).toBe('http://localhost:3000')
    expect(result[2]).toBeInstanceOf(RegExp)
    expect(result[2].toString()).toBe('/^https:\\/\\/(.+\\.)?example\\.org$/i')
  })

  it('should handle invalid JSON and proceed with manual parsing', () => {
    const rawOrigins = 'invalid-json,http://localhost:5000'
    const result = getOrigins(rawOrigins)

    expect(result).toHaveLength(2)
    expect(result[0]).toBeInstanceOf(RegExp)
    expect(result[1]).toBe('http://localhost:5000')
  })

  it('should allow subdomains when parsed as domain names', () => {
    const rawOrigins = 'example.com'
    const result = getOrigins(rawOrigins)

    expect(result).toHaveLength(1)
    expect(result[0]).toBeInstanceOf(RegExp)
    expect(result[0].toString()).toBe('/^https:\\/\\/(.+\\.)?example\\.com$/i')
  })

  it('should trim spaces around origins', () => {
    const rawOrigins = '  https://example.com  ,   example.org   '
    const result = getOrigins(rawOrigins)

    expect(result).toHaveLength(2)
    expect(result[0]).toBe('https://example.com')
    expect(result[1]).toBeInstanceOf(RegExp)
    expect(result[1].toString()).toBe('/^https:\\/\\/(.+\\.)?example\\.org$/i')
  })

  it('should correctly match valid origins against generated RegExp patterns', () => {
    const rawOrigins = 'example.com'
    const result = getOrigins(rawOrigins)

    const regex = result[0] as RegExp
    expect(regex.test('https://example.com')).toBe(true)
    expect(regex.test('https://subdomain.example.com')).toBe(true)
    expect(regex.test('https://sub.sub.example.com')).toBe(true)

    expect(regex.test('http://example.com')).toBe(false) // Not https
    expect(regex.test('https://example.org')).toBe(false) // Wrong TLD
    expect(regex.test('https://examplexcom')).toBe(false) // Not matching format
  })

  it('should match valid origins for multiple regex patterns', () => {
    const rawOrigins = 'example.org,test.com'
    const result = getOrigins(rawOrigins)

    const regex1 = result[0] as RegExp
    const regex2 = result[1] as RegExp

    expect(regex1.test('https://example.org')).toBe(true)
    expect(regex1.test('https://sub.example.org')).toBe(true)
    expect(regex2.test('https://test.com')).toBe(true)
    expect(regex2.test('https://api.test.com')).toBe(true)
  })
})
