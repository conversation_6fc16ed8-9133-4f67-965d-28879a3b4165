import { IsUUID, validateSync } from 'class-validator'

export class CsvImportCreatedEvent {
  @IsUUID()
  readonly organizationId: string

  @IsUUID()
  readonly csvImportId: string

  constructor(payload: { organizationId: string; csvImportId: string }) {
    this.organizationId = payload.organizationId
    this.csvImportId = payload.csvImportId

    if (validateSync(this).length) {
      throw new Error('Invalid event')
    }
  }
}
