export enum LeadEvents {
  ACTIVITY_CREATED = 'activity.created',
  CONTACT_CREATED = 'contact.created',
  COMPANY_CREATED = 'company.created',
  CONTACTS_CREATED = 'contacts.created',
  COMPANIES_CREATED = 'companies.created',
  CONTACT_ARCHIVED = 'contact.archived',
  CONTACT_ASSIGNED = 'contact.assigned',
  CONTACT_UNASSIGNED = 'contact.unassigned',
  CONTACT_ATTRIBUT_UPDATED = 'contact_attribut.updated',
  CONTACT_STATUS_UPDATED = 'contact_status.updated',
  CONTACT_STATS_CALL_UPDATED = 'contact_stats_call.updated',
  STRATEGY_GENERATED = 'strategy.generated',
  CONTACT_NEW_DATA_AVAILABLE = 'contact.new_data_available',
  COMPANY_ATTRIBUT_UPDATED = 'company_attribut.updated',
  CONTACT_EMAILS_DEBOUNCE_REQUESTED = 'contact.emails_debounce_requested',
}

export enum CallEvents {
  AIRCALL_CALL_CREATED = 'aircall.call.created',
  AIRCALL_CALL_UPDATED = 'aircall.call.updated',
  AIRCALL_INTEGRATED = 'aircall.integrated',
  RINGOVER_CALL_CREATED = 'ringover.call.created',
  RINGOVER_CALL_UPDATED = 'ringover.call.updated',
  RINGOVER_RECORD_AVAILABLE = 'ringover.record.available',
  RINGOVER_INTEGRATED = 'ringover.integrated',
  CALL_CREATED = 'call.created',
  CALL_UPDATED = 'call.updated',
  CALL_MISSED = 'call.missed',
}

export enum NoteEvents {
  NOTE_CREATED = 'note.created',
  NOTE_UPDATED = 'note.updated',
}

export enum TaskEvents {
  TASK_CREATED = 'task.created',
  TASK_SNOOZED = 'task.snoozed',
  TASK_UPDATED = 'task.updated',
  TASK_DELETED = 'task.deleted',
  TASK_DONE = 'task.done',
}

export enum MailEvents {
  MAIL_INTEGRATED = 'mail.integrated',
  MAIL_CREATED = 'mail.created',
  MAIL_UPDATED = 'mail.updated',
  MAIL_SENT = 'mail.sent',
  MAIL_REPLY = 'mail.reply',
  MAIL_ANSWERED = 'mail.answered',
  MAIL_SEQUENCE_SENT = 'mail.sequence.sent',
  MAIL_SEQUENCE_ANSWERED = 'mail.sequence.answered',
  MAIL_LINK_CLICKED = 'mail.link.clicked',
  MAIL_OPENED = 'mail.opened',
  MAIL_ERROR = 'mail.error',
}

export enum AllowedDomainEvents {
  ALLOWED_DOMAIN_DELETED = 'allowed_domain.deleted',
}

export enum CaptainDataJobEvents {
  JOB_SUCCEEDED = 'captain_data.job.succeeded',
  JOB_FAILED = 'captain_data.job.failed',
  JOB_VISIT_PROFILE_SUCCEEDED = 'captain_data.job.visit_profile.succeeded',
  JOB_SEND_INVITATION_SUCCEEDED = 'captain_data.job.send_invitation.succeeded',
  JOB_EXTRACT_CONNECTIONS_SUCCEEDED = 'captain_data.job.extract_connections.succeeded',
  JOB_SEND_MESSAGE_SUCCEEDED = 'captain_data.job.send_message.succeeded',
  JOB_EXTRACT_MESSAGES_SUCCEEDED = 'captain_data.job.extract_messages.succeeded',
  JOB_SEARCH_LINKEDIN_PEOPLE_SUCCEEDED = 'captain_data.job.search.linkedin.people.succeeded',
  JOB_SEARCH_LINKEDIN_COMPANIES_SUCCEEDED = 'captain_data.job.search.linkedin.companies.succeeded',
  JOB_EXTRACT_LINKEDIN_POST_LIKERS_SUCCEEDED = 'captain_data.job.extract.linkedin.post.likers.succeeded',
  JOB_EXTRACT_LINKEDIN_POST_COMMENTERS_SUCCEEDED = 'captain_data.job.extract.linkedin.post.commenters.succeeded',
}

export enum LinkedinEvents {
  ACCOUNT_COOKIE_REFRESHED = 'linkedin.account.cookie_refreshed',
  ACCOUNT_CONNECTED = 'linkedin.account.connected',
  ACCOUNT_DISCONNECTED = 'linkedin.account.disconnected',
  VISIT_PROFILE_SUCCEEDED = 'linkedin.visit_profile.succeeded',
  VISIT_PROFILE_FAILED = 'linkedin.visit_profile.failed',
  SEND_INVITATION_FAILED = 'linkedin.send_invitation.failed',
  SEND_INVITATION_SUCCEEDED = 'linkedin.send_invitation.succeeded',
  NEW_CONNECTION_RETREIVED = 'linkedin.new_connection.retreived',
  SEND_MESSAGE_FAILED = 'linkedin.send_message.failed',
  SEND_MESSAGE_SUCCEEDED = 'linkedin.send_message.succeeded',
  EXTRACT_MESSAGES_FAILED = 'linkedin.extract_messages.failed',
  MESSAGE_SENT = 'linkedin.message.sent',
  MESSAGE_RECEIVED = 'linkedin.message.received',
  EXTRACT_POST_LIKERS_AND_COMMENTERS_EXTRACTION_SCHEDULED = 'linkedin.post_likers_and_commenters_extraction.scheduled',
  SEARCH_SCHEDULED = 'linkedin.search.scheduled',
  PEOPLE_SEARCH_RECEIVED = 'linkedin.people.search.received',
  PEOPLE_SEARCH_FAILED = 'linkedin.people.search.failed',
  COMPANIES_SEARCH_RECEIVED = 'linkedin.companies.search.received',
  COMPANIES_SEARCH_FAILED = 'linkedin.companies.search.failed',
  POST_LIKERS_EXTRACTION_RECEIVED = 'linkedin.post.likers.extraction.received',
  POST_COMMENTERS_EXTRACTION_RECEIVED = 'linkedin.post.commenters.extraction.received',
  POST_LIKERS_AND_COMMENTERS_DEDUPLICATION_ENABLED = 'linkedin.post.likers.and.commenters.deduplication.enabled',
  POST_LIKERS_EXTRACTION_FAILED = 'linkedin.post.likers.extraction.failed',
  POST_COMMENTERS_EXTRACTION_FAILED = 'linkedin.post.commenters.extraction.failed',
  SEARCH_LEADS_INDEXED = 'linkedin.search.leads.indexed',
  POST_LIKERS_LEADS_INDEXED = 'linkedin.post.likers.leads.indexed',
  POST_COMMENTERS_LEADS_INDEXED = 'linkedin.post.commenters.leads.indexed',
  TASK_FAILED = 'linkedin.task.failed',
}

export enum ActivityEvents {
  ACTIVITY_CREATED = 'activity.created',
  ACTIVITY_UPDATED = 'activity.updated',
}

export enum TrackingEvents {
  TRACKING_INTEGRATION = 'TRACKING_INTEGRATION',
}

export enum UserEvents {
  USER_CREATED = 'USER_CREATED',
  USER_UPDATED = 'USER_UPDATED',
}

export enum SequenceEvents {
  SEQUENCE_CONTACT_ANSWERED = 'sequence_contact.answered',
  SEQUENCE_CONTACT_UNSUBSCRIBED = 'sequence_contact.unsubscribed',
  SEQUENCE_CONTACT_BOUNCED = 'sequence_contact.bounced',
  SEQUENCE_CONTACT_CLICKED = 'sequence_contact.clicked',
  SEQUENCE_CONTACT_OPENED = 'sequence_contact.opened',
  SEQUENCE_CONTACT_ADDED = 'sequence_contact.added',
  SEQUENCE_CONTACT_UPDATED = 'sequence_contact.updated',
  SEQUENCE_CONTACT_DELETED = 'sequence_contact.deleted',
  SEQUENCE_CONTACT_STEP_VALIDATED = 'sequence_contact.step.validated',
  SEQUENCE_STARTED = 'sequence.started',
  SEQUENCE_DELETED = 'sequence.deleted',
  SEQUENCE_PAUSED = 'sequence.paused',
  SEQUENCE_RESTARTED = 'sequence.restarted',
  SEQUENCE_STEP_SUCCEEDED = 'sequence-step.succeeded',
  SEQUENCE_CONTACT_STEP_CONTENT_UPDATED = 'sequence-contact-step-content.updated',
  SEQUENCE_NEXT_EXECUTION_DATE_UPDATED = 'sequence.next_execution_date_updated',
}

export enum OrganizationEvents {
  ORGANIZATION_CREATED = 'organization.created',
}

export enum HubspotEvents {
  HUBSPOT_INTEGRATED = 'hubspot.integrated',
}

export enum NotifyOrganizationEvents {
  BY_WEBSOCKET = 'notify_organization.websocket',
}

export enum EnrichmentHubEvents {
  START_ENRICHMENT_PROCESS = 'enrichment_hub.start_enrichment_process',
  READY_FOR_NEXT_ENRICHMENT_BATCH = 'enrichment_hub.ready_for_next_enrichment_batch',
  ENRICHMENT_HUB_CREATED = 'enrichment_hub.created',
  ENRICHMENT_HUB_SUCCESSFUL = 'enrichment_hub.successful',
  ENRICHMENT_HUB_FAILED = 'enrichment_hub.failed',
  ENRICHMENT_HUB_DELETED = 'enrichment_hub.deleted',
  ENRICHMENT_HUB_RELAUNCHED = 'enrichment_hub.relaunched',
}

export enum EnrichmentEvents {
  ENRICHMENT_STATUS_UPDATED = 'enrichment.status_updated',
  ENRICHMENT_BATCH_COMPLETED_SUCCESSFULLY = 'enrichment.batch_completed_successfully',
  ENRICHMENT_BATCH_FAILED = 'enrichment.batch_failed',
}

export enum SubscriptionEvents {
  SUBSCRIPTIONS_UPDATED = 'subscriptions.updated',
  SERVICE_SUBSCRIPTION_UPDATED = 'subscription.service_subscription_updated',
}

export enum LeadExportEvents {
  LEAD_EXPORT_CREATED = 'lead_export.created',
  LEAD_EXPORT_STATUS_UPDATED = 'lead_export.status_updated',
  LEAD_EXPORT_DONE = 'lead_export.done',
  LEAD_EXPORT_ERROR = 'lead_export.error',
}

export enum AsyncActionEvents {
  ASYNC_ACTION_CREATED = 'async_action.created',
  ASYNC_ACTION_UPDATED = 'async_action.updated',
  ASYNC_ACTION_DELETED = 'async_action.deleted',
}

export enum LeadImportEvents {
  CSV_IMPORT_CREATED = 'csv_import.created',

  /* lead import has been created in DB with a DRAFT status */
  LEAD_IMPORT_CREATED = 'lead_import.created',

  LEAD_IMPORT_UPDATED = 'lead_import.updated',
  LEAD_IMPORT_COUNTERS_UPDATED = 'lead_import.counters_updated',
  LEAD_IMPORT_DONE = 'lead_import.done',

  /* the lead import has been processed and leads are imported in DB */
  LEAD_IMPORT_SOURCE_PROCESSING_FINISHED = 'lead_import.source_processing_finished',

  /* the lead import has been processed and leads are imported in DB */
  LEAD_IMPORT_REVIEW_LEADS_EXCLUDED = 'lead_import.review_leads_excluded',

  /* the lead import has been processed and leads are imported in DB */
  LEAD_IMPORT_REVIEW_SOME_DUPLICATES_RESOLVED = 'lead_import.some_duplicates_resolved',

  /* lead import has failed during one of the steps */
  LEAD_IMPORT_ERROR = 'lead_import.error',

  /* contact.leads_in_import_id has been updated in DB and elastic search re-indexation is done */
  LEADS_IN_IMPORT_UPDATED = 'lead_import.leads_in_import_updated',

  /* lead import status has been updated */
  LEAD_IMPORT_STATUS_UPDATED = 'lead_import.status_updated',

  LEAD_IMPORT_LEADS_EXCLUDED_AND_INDEXED = 'lead_import.leads_excluded_and_indexed',
}

export enum MatchingEvents {
  LEAD_IMPORT_MATCHING_READY = 'lead.import.matching.ready',
  LEAD_IMPORT_MATCHING_ERROR = 'lead.import.matching.error',
}

export enum JobTrackingEvents {
  JOB_CANCELED = 'job.canceled',
}
