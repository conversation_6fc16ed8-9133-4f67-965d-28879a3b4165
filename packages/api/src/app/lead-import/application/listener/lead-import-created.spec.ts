import { Test, TestingModule } from '@nestjs/testing'
import { QueryBus } from '@nestjs/cqrs'
import { LeadImportCreatedEvent } from '../../../shared/domain/event/lead-import/lead-import-created.event'
import { GetOneLeadImportByIdQuery } from '../query/get-one-lead-import-by-id/get-one-lead-import-by-id.query'
import {
  AsyncActionStatusEnum,
  AsyncActionCategoryEnum,
  LeadImportSourceEnum,
} from '@getheroes/shared'
import { LeadImportFactoryMock } from '../../domain/factory/lead-import.factory.mock'
import { LeadImportCreatedListener } from './lead-import-created.listener'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { jest } from '@jest/globals'
import { ASYNC_ACTION_SERVICE_INTERFACE } from '../../../shared/domain/interface/async-action-service.interface'
import { CreateAsyncActionDto } from '../../../shared/domain/dto/create-async-action.dto'
import { AsyncActionMockModule } from '../../../shared/tests/async-action.mock.module'
import { MockAsyncActionService } from '../../../shared/tests/async-action-mock.service'
import { LEAD_IMPORT_LEAD_COUNTERS_SERVICE_INTERFACE } from '../../domain/interface/lead-import-lead-counters-service.interface'
import { NotifyOrganizationEvents } from '../../../shared/domain/event/event.enum'
import { NotifyOrganizationEvent } from '../../../shared/domain/event/websocket/notify-organization.event'
import { LeadImportSourceCsvFilePropsMinimal } from '../../../shared/domain/interface/lead-import.interface'

describe('LeadImportCreatedListener', () => {
  let listener: LeadImportCreatedListener
  let mockQueryBus: QueryBus
  let mockAsyncActionService: MockAsyncActionService
  let mockEventEmitter: EventEmitter2
  let mockLeadImportLeadCountersService: any

  const mockCsvSourceSpecificProps: LeadImportSourceCsvFilePropsMinimal = {
    spaceId: 'space-123',
    spaceName: 'Test Space',
    spaceNamespace: 'test-space',
    spaceMetadata: {},
    spaceEnvironmentId: 'env-123',
    sheetId: 'sheet-123',
    sheetName: 'Test Sheet',
    sheetFields: [
      { key: 'firstName', label: 'First Name', type: 'string' },
      { key: 'lastName', label: 'Last Name', type: 'string' },
      { key: 'email', label: 'Email', type: 'string' },
    ],
    sheetRecordCounts: {
      total: 100,
      valid: 90,
      error: 10,
    },
  }

  beforeEach(async () => {
    mockLeadImportLeadCountersService = {
      computeNbLeadsTotal: jest.fn().mockResolvedValue(42 as never),
    }

    mockEventEmitter = {
      emit: jest.fn().mockImplementation(() => true),
    } as unknown as EventEmitter2

    const module: TestingModule = await Test.createTestingModule({
      imports: [AsyncActionMockModule],
      providers: [
        LeadImportCreatedListener,
        {
          provide: QueryBus,
          useValue: { execute: jest.fn() },
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: LEAD_IMPORT_LEAD_COUNTERS_SERVICE_INTERFACE,
          useValue: mockLeadImportLeadCountersService,
        },
      ],
    }).compile()

    listener = module.get<LeadImportCreatedListener>(LeadImportCreatedListener)
    mockQueryBus = module.get<QueryBus>(QueryBus)
    mockAsyncActionService = module.get<MockAsyncActionService>(
      ASYNC_ACTION_SERVICE_INTERFACE
    )

    jest.spyOn(mockAsyncActionService, 'create').mockResolvedValue(null)
  })

  it('should create an async action for a CONTACT lead import', async () => {
    // Arrange
    const nbLeadsTotal = 42
    const leadImport = LeadImportFactoryMock.create({
      nbLeadsTotal,
    })
    jest.spyOn(mockQueryBus, 'execute').mockResolvedValue(leadImport)
    // Build the event
    const event = new LeadImportCreatedEvent({
      leadImportId: leadImport.id,
      organizationId: leadImport.organizationId,
      context: leadImport.context,
    })

    // Act
    await listener.updateCounters(event)

    // Assert
    // 1) We must fetch the LeadImport with the correct query
    expect(mockQueryBus.execute).toHaveBeenCalledWith(
      new GetOneLeadImportByIdQuery(leadImport.id, leadImport.organizationId)
    )

    // 2) We must create an AsyncAction in PRELOADING state, category LEAD_IMPORT
    expect(mockAsyncActionService.create).toHaveBeenCalledTimes(1)
    expect(mockAsyncActionService.create).toHaveBeenCalledWith(
      new CreateAsyncActionDto({
        status: AsyncActionStatusEnum.PRELOADING,
        organizationId: leadImport.organizationId,
        createdById: leadImport.createdByUserId,
        category: AsyncActionCategoryEnum.LEAD_IMPORT,
        internalRelationId: leadImport.id,
      })
    )
  })

  it('should send websocket notification when lead import is created', async () => {
    // Arrange
    const leadImport = LeadImportFactoryMock.create()
    jest.spyOn(mockQueryBus, 'execute').mockResolvedValueOnce(leadImport)

    const event = new LeadImportCreatedEvent({
      leadImportId: leadImport.id,
      organizationId: leadImport.organizationId,
      context: leadImport.context,
    })

    // Act
    await listener.sendWebsocketNotification(event)

    // Assert
    // 1) We must fetch the LeadImport with the correct query
    expect(mockQueryBus.execute).toHaveBeenCalledWith(
      new GetOneLeadImportByIdQuery(leadImport.id, leadImport.organizationId)
    )

    // 2) We must emit a websocket notification with the correct event
    expect(mockEventEmitter.emit).toHaveBeenCalledWith(
      NotifyOrganizationEvents.BY_WEBSOCKET,
      expect.any(NotifyOrganizationEvent)
    )

    // 3) Check that the emit was called with the correct event type
    expect(mockEventEmitter.emit).toHaveBeenCalledWith(
      NotifyOrganizationEvents.BY_WEBSOCKET,
      expect.objectContaining({
        organizationId: leadImport.organizationId,
        stringifiedWebsocketEvent: expect.any(String),
      })
    )
  })

  it('should call both createAsyncAction and getNumberLeadsToImport when handling LEAD_IMPORT_CREATED event', async () => {
    // Arrange
    const leadImport = LeadImportFactoryMock.create()
    jest.spyOn(mockQueryBus, 'execute').mockResolvedValue(leadImport)

    const event = new LeadImportCreatedEvent({
      leadImportId: leadImport.id,
      organizationId: leadImport.organizationId,
      context: leadImport.context,
    })

    // Spy on the private methods
    const createAsyncActionSpy = jest.spyOn(
      listener as any,
      'createAsyncAction'
    )
    const getNumberLeadsToImportSpy = jest.spyOn(
      listener as any,
      'getNumberLeadsToImport'
    )

    // Act
    await listener.updateCounters(event)

    // Assert
    expect(getNumberLeadsToImportSpy).toHaveBeenCalledWith(event)
    expect(createAsyncActionSpy).toHaveBeenCalledWith(event)
  })

  it('should compute nbLeadsTotal for ZELIQ_ORGANIZATION_LEADS source', async () => {
    // Arrange
    const leadImport = LeadImportFactoryMock.create({
      source: LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS,
      nbLeadsTotal: 0,
    })
    jest.spyOn(mockQueryBus, 'execute').mockResolvedValueOnce(leadImport)

    const event = new LeadImportCreatedEvent({
      leadImportId: leadImport.id,
      organizationId: leadImport.organizationId,
      context: leadImport.context,
    })

    // Act
    await (listener as any).getNumberLeadsToImport(event)

    // Assert
    expect(mockQueryBus.execute).toHaveBeenCalledWith(
      new GetOneLeadImportByIdQuery(leadImport.id, leadImport.organizationId)
    )
    expect(
      mockLeadImportLeadCountersService.computeNbLeadsTotal
    ).toHaveBeenCalledWith(leadImport)
  })

  it('should not compute nbLeadsTotal for CSV_FILE source', async () => {
    // Arrange
    const leadImport = LeadImportFactoryMock.create({
      source: LeadImportSourceEnum.CSV_FILE,
      sourceSpecificProps: { ...mockCsvSourceSpecificProps },
      nbLeadsTotal: 0,
    })
    jest.spyOn(mockQueryBus, 'execute').mockResolvedValueOnce(leadImport)

    const event = new LeadImportCreatedEvent({
      leadImportId: leadImport.id,
      organizationId: leadImport.organizationId,
      context: leadImport.context,
    })

    // Act
    await (listener as any).getNumberLeadsToImport(event)

    // Assert
    expect(mockQueryBus.execute).toHaveBeenCalledWith(
      new GetOneLeadImportByIdQuery(leadImport.id, leadImport.organizationId)
    )
    expect(
      mockLeadImportLeadCountersService.computeNbLeadsTotal
    ).not.toHaveBeenCalled()
  })
})
