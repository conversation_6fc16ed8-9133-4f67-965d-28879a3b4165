import { Test, TestingModule } from '@nestjs/testing'
import { CommandBus } from '@nestjs/cqrs'
import { LeadsInImportUpdatedListener } from './leads-in-import-updated.listener'
import { LeadsInImportUpdatedEvent } from '../../../shared/domain/event/lead-import/leads-in-import-updated.event'
import { UpdateLeadImportCountersCommand } from '../command/update-lead-import-counters/update-lead-import-counters.command'
import { v4 as uuidv4 } from 'uuid'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { jest } from '@jest/globals'

describe('LeadsInImportUpdatedListener', () => {
  let listener: LeadsInImportUpdatedListener
  let mockCommandBus: CommandBus

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LeadsInImportUpdatedListener,
        {
          provide: CommandBus,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
          },
        },
      ],
    }).compile()

    listener = module.get<LeadsInImportUpdatedListener>(
      LeadsInImportUpdatedListener
    )
    mockCommandBus = module.get<CommandBus>(CommandBus)
  })

  it('should call UpdateLeadImportCountersCommand when LEADS_IN_IMPORT_UPDATED event is fired', async () => {
    // Arrange
    const leadImportId = uuidv4()
    const organizationId = uuidv4()

    const event = new LeadsInImportUpdatedEvent({
      leadImportId,
      organizationId,
    })

    // Act
    await listener.updateLeadImportCounters(event)

    // Assert
    expect(mockCommandBus.execute).toHaveBeenCalledTimes(1)
    expect(mockCommandBus.execute).toHaveBeenCalledWith(
      new UpdateLeadImportCountersCommand({
        leadImportId,
        organizationId,
      })
    )
  })
})
