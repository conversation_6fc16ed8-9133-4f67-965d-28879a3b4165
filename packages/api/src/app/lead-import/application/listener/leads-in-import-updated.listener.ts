import { Injectable } from '@nestjs/common'
import { CommandBus } from '@nestjs/cqrs'
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter'

import {
  LeadImportEvents,
  NotifyOrganizationEvents,
} from '../../../shared/domain/event/event.enum'
import { LeadsInImportUpdatedEvent } from '../../../shared/domain/event/lead-import/leads-in-import-updated.event'

import { BaseEvent, NameEvent } from '@getheroes/shared'
import { NotifyOrganizationEvent } from '../../../shared/domain/event/websocket/notify-organization.event'
import { UpdateLeadImportCountersCommand } from '../command/update-lead-import-counters/update-lead-import-counters.command'

@Injectable()
export class LeadsInImportUpdatedListener {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly eventEmitter: EventEmitter2
  ) {}

  @OnEvent(LeadImportEvents.LEADS_IN_IMPORT_UPDATED)
  @OnEvent(LeadImportEvents.LEAD_IMPORT_SOURCE_PROCESSING_FINISHED)
  async updateLeadImportCounters(event: LeadsInImportUpdatedEvent) {
    const { leadImportId, organizationId } = event

    await this.commandBus.execute(
      new UpdateLeadImportCountersCommand({
        leadImportId,
        organizationId,
      })
    )

    const baseEvent = new BaseEvent(NameEvent.LEAD_IMPORT_LEADS_UPDATED, {
      leadImportId,
    })
    this.eventEmitter.emit(
      NotifyOrganizationEvents.BY_WEBSOCKET,
      new NotifyOrganizationEvent(organizationId, JSON.stringify(baseEvent))
    )
  }
}
