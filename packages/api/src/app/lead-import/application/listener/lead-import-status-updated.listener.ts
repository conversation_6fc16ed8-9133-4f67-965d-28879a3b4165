import { Inject, Injectable } from '@nestjs/common'
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter'
import { QueryBus } from '@nestjs/cqrs'
import {
  AsyncActionStatusEnum,
  BaseEvent,
  LeadImportStatusEnum,
  NameEvent,
} from '@getheroes/shared'
import {
  LeadImportEvents,
  NotifyOrganizationEvents,
} from '../../../shared/domain/event/event.enum'
import { LeadImportStatusUpdatedEvent } from '../../../shared/domain/event/lead-import/lead-import-status-updated.event'
import { LogFeature, LoggerService } from '../../../shared/logger.service'
import { GetOneLeadImportByIdQuery } from '../query/get-one-lead-import-by-id/get-one-lead-import-by-id.query'
import { TryCatchLogger } from '../../../shared/domain/decorator/try-catch-logger.decorator'
import {
  ASYNC_ACTION_SERVICE_INTERFACE,
  AsyncActionServiceInterface,
} from '../../../shared/domain/interface/async-action-service.interface'
import { NotifyOrganizationEvent } from '../../../shared/domain/event/websocket/notify-organization.event'
import { plainToInstance } from 'class-transformer'
import { LeadImportDTO } from '../dto/lead-import.dto'
import { LeadImportLeadsExcludedAndIndexedEvent } from '../../../shared/domain/event/lead-import/lead-import-leads-excluded-and-indexed.event'

@Injectable()
export class LeadImportStatusUpdatedListener {
  private readonly logger = new LoggerService({
    feature: LogFeature.LEAD_IMPORT,
  })

  constructor(
    private readonly queryBus: QueryBus,
    @Inject(ASYNC_ACTION_SERVICE_INTERFACE)
    private readonly asyncActionService: AsyncActionServiceInterface,
    private readonly eventEmitter: EventEmitter2
  ) {}

  private mapStatusToAsyncAction(
    leadImportStatus: LeadImportStatusEnum
  ): AsyncActionStatusEnum {
    const statusMap: Record<LeadImportStatusEnum, AsyncActionStatusEnum> = {
      [LeadImportStatusEnum.DRAFT]: AsyncActionStatusEnum.PRELOADING,
      [LeadImportStatusEnum.IMPORTING]: AsyncActionStatusEnum.IN_PROGRESS,
      [LeadImportStatusEnum.LEADS_IMPORTED]:
        AsyncActionStatusEnum.ACTION_REQUIRED,
      [LeadImportStatusEnum.LEADS_EXCLUDED]:
        AsyncActionStatusEnum.ACTION_REQUIRED,
      [LeadImportStatusEnum.ERROR]: AsyncActionStatusEnum.ERROR,
      [LeadImportStatusEnum.SUCCESS]: AsyncActionStatusEnum.DONE,
      [LeadImportStatusEnum.EXECUTE_ACTIONS]: AsyncActionStatusEnum.DONE,
    }

    return statusMap[leadImportStatus] || AsyncActionStatusEnum.IN_PROGRESS
  }

  @TryCatchLogger({
    feature: LogFeature.LEAD_IMPORT,
    message: 'Failed to update async action after lead import status updated',
    stopErrorPropagation: true,
  })
  @OnEvent(LeadImportEvents.LEAD_IMPORT_STATUS_UPDATED)
  async updateAsyncAction(event: LeadImportStatusUpdatedEvent): Promise<void> {
    const { leadImportId, organizationId } = event

    const leadImport = await this.queryBus.execute(
      new GetOneLeadImportByIdQuery(leadImportId, organizationId)
    )

    if (!leadImport) {
      this.logger.error({
        message: 'Lead import not found',
        data: { leadImportId, organizationId },
      })
      return
    }

    const asyncAction =
      await this.asyncActionService.getByLeadImportId(leadImportId)

    if (!asyncAction) {
      this.logger.error({
        message: 'Async action not found for lead import',
        data: { leadImportId, organizationId },
      })
      return
    }

    await this.asyncActionService.updateStatus(
      asyncAction.id,
      this.mapStatusToAsyncAction(leadImport.status)
    )

    const leadImportDTO = plainToInstance(LeadImportDTO, leadImport, {
      excludeExtraneousValues: true,
    })
    const baseEvent = new BaseEvent(NameEvent.LEAD_IMPORT_UPDATED, {
      ...leadImportDTO,
      userId: leadImport?.createdBy?.id,
    })
    this.eventEmitter.emit(
      NotifyOrganizationEvents.BY_WEBSOCKET,
      new NotifyOrganizationEvent(organizationId, JSON.stringify(baseEvent))
    )
  }

  @OnEvent(LeadImportEvents.LEAD_IMPORT_STATUS_UPDATED)
  triggerMatchingService(event: LeadImportStatusUpdatedEvent) {
    const { status, leadImportId, organizationId } = event

    if (status === LeadImportStatusEnum.LEADS_EXCLUDED) {
      this.eventEmitter.emit(
        LeadImportEvents.LEAD_IMPORT_LEADS_EXCLUDED_AND_INDEXED,
        new LeadImportLeadsExcludedAndIndexedEvent(organizationId, leadImportId)
      )
    }
  }
}
