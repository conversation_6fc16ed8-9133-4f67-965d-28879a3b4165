import { Inject, Injectable } from '@nestjs/common'
import { QueryBus } from '@nestjs/cqrs'
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter'
import {
  BaseEvent,
  LeadImportSourceEnum,
  NameEvent,
  AsyncActionStatusEnum,
  AsyncActionCategoryEnum,
} from '@getheroes/shared'
import {
  LeadImportEvents,
  NotifyOrganizationEvents,
} from '../../../shared/domain/event/event.enum'
import { LeadImportModel } from '../../domain/models/lead-import.model'
import { GetOneLeadImportByIdQuery } from '../query/get-one-lead-import-by-id/get-one-lead-import-by-id.query'
import { LeadImportCreatedEvent } from '../../../shared/domain/event/lead-import/lead-import-created.event'
import { NotifyOrganizationEvent } from '../../../shared/domain/event/websocket/notify-organization.event'
import { CreateAsyncActionDto } from '../../../shared/domain/dto/create-async-action.dto'
import {
  ASYNC_ACTION_SERVICE_INTERFACE,
  AsyncActionServiceInterface,
} from '../../../shared/domain/interface/async-action-service.interface'
import {
  LEAD_IMPORT_LEAD_COUNTERS_SERVICE_INTERFACE,
  LeadImportLeadCountersServiceInterface,
} from '../../domain/interface/lead-import-lead-counters-service.interface'

@Injectable()
export class LeadImportCreatedListener {
  constructor(
    private readonly queryBus: QueryBus,
    private readonly eventEmitter: EventEmitter2,
    @Inject(ASYNC_ACTION_SERVICE_INTERFACE)
    private readonly asyncActionService: AsyncActionServiceInterface,
    @Inject(LEAD_IMPORT_LEAD_COUNTERS_SERVICE_INTERFACE)
    private readonly leadImportLeadCountersService: LeadImportLeadCountersServiceInterface
  ) {}

  @OnEvent(LeadImportEvents.LEAD_IMPORT_CREATED)
  async sendWebsocketNotification({
    leadImportId,
    organizationId,
  }: LeadImportCreatedEvent) {
    const leadImportModel: LeadImportModel = await this.queryBus.execute(
      new GetOneLeadImportByIdQuery(leadImportId, organizationId)
    )

    const baseEvent = new BaseEvent(NameEvent.LEAD_IMPORT_CREATED, {
      ...leadImportModel,
      userId: leadImportModel.createdBy.id,
    })
    this.eventEmitter.emit(
      NotifyOrganizationEvents.BY_WEBSOCKET,
      new NotifyOrganizationEvent(
        leadImportModel.organizationId,
        JSON.stringify(baseEvent)
      )
    )
  }

  @OnEvent(LeadImportEvents.LEAD_IMPORT_CREATED)
  async updateCounters(event: LeadImportCreatedEvent) {
    await this.createAsyncAction(event)
    await this.getNumberLeadsToImport(event)
  }

  private async createAsyncAction({
    leadImportId,
    organizationId,
  }: LeadImportCreatedEvent) {
    const leadImportModel: LeadImportModel = await this.queryBus.execute(
      new GetOneLeadImportByIdQuery(leadImportId, organizationId)
    )

    const createAsyncActionDto = new CreateAsyncActionDto({
      status: AsyncActionStatusEnum.PRELOADING,
      organizationId,
      createdById: leadImportModel.createdByUserId,
      category: AsyncActionCategoryEnum.LEAD_IMPORT,
      internalRelationId: leadImportId,
    })

    await this.asyncActionService.create(createAsyncActionDto)
  }

  private async getNumberLeadsToImport({
    leadImportId,
    organizationId,
  }: LeadImportCreatedEvent) {
    const leadImportModel: LeadImportModel = await this.queryBus.execute(
      new GetOneLeadImportByIdQuery(leadImportId, organizationId)
    )

    // for some sources, the props that allow to know the number of leads
    // to import are not known at the creation of the lead import
    const canEstimateNbLeadsToImport = [
      LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS,
    ].includes(leadImportModel.source)

    if (!canEstimateNbLeadsToImport) {
      return
    }

    await this.leadImportLeadCountersService.computeNbLeadsTotal(
      leadImportModel
    )
  }
}
