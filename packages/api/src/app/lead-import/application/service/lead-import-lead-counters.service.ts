import { Inject, Injectable } from '@nestjs/common'
import { QueryBus } from '@nestjs/cqrs'
import { LeadImportSourceEnum } from '@getheroes/shared'
import { LeadImportModel } from '../../domain/models/lead-import.model'
import { LeadImportLeadCountersServiceInterface } from '../../domain/interface/lead-import-lead-counters-service.interface'
import {
  LEAD_IMPORT_REPOSITORY_INTERFACE,
  LeadImportRepositoryInterface,
} from '../../domain/interface/lead-import-repository.interface'
import {
  LeadImportSourceCsvFilePropsMinimal,
  LeadImportSourceZeliqOrganizationProps,
} from '../../../shared/domain/interface/lead-import.interface'
import { PaginationQueryDto } from '../../../../ui/api/shared/infrastructure/dto/pagination-query.dto'
import { SortType } from '../../../shared/domain/sort-type.enum'
import { GetSearchPaginationQueryObjectsQuery } from '../../../lead/application/query/search/get-search-pagination-search-query-objects/get-search-pagination-query-objects.query'
import { SearchIndexContactQuery } from '../../../lead/application/query/search-contact/search-index-contact.query'
import { TryCatchLogger } from '../../../shared/domain/decorator/try-catch-logger.decorator'
import { LogFeature, LoggerService } from '../../../shared/logger.service'
import { LeadImportEvents } from '../../../shared/domain/event/event.enum'
import { LeadImportLeadCountersUpdatedEvent } from '../../../shared/domain/event/lead-import/lead-import-lead-counters-updated.event'
import { EventEmitter2 } from '@nestjs/event-emitter'

@Injectable()
export class LeadImportLeadCountersService
  implements LeadImportLeadCountersServiceInterface
{
  private readonly logger = new LoggerService({
    context: LeadImportLeadCountersService.name,
    feature: LogFeature.LEAD_IMPORT,
  })

  constructor(
    private readonly queryBus: QueryBus,
    @Inject(LEAD_IMPORT_REPOSITORY_INTERFACE)
    private readonly leadImportRepository: LeadImportRepositoryInterface,
    private readonly eventEmitter: EventEmitter2
  ) {}

  @TryCatchLogger({
    feature: LogFeature.LEAD_IMPORT,
    message: 'Failed to compute number of leads total',
    stopErrorPropagation: true,
  })
  async computeNbLeadsTotal(leadImportModel: LeadImportModel): Promise<number> {
    let nbLeadsTotal = 0

    switch (leadImportModel.source) {
      case LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS:
        nbLeadsTotal =
          await this.computeNbLeadsTotalForZeliqOrganizationLeads(
            leadImportModel
          )
        break
      case LeadImportSourceEnum.CSV_FILE:
        nbLeadsTotal = this.computeNbLeadsTotalForCSV(leadImportModel)
        break
      default:
        this.logger.warn({
          message: `Computation of nbLeadsTotal not implemented for source ${leadImportModel.source}`,
        })
        break
    }

    if (!nbLeadsTotal) {
      return nbLeadsTotal
    }

    await this.leadImportRepository.updateCounters({
      id: leadImportModel.id,
      nbLeadsTotal,
    })

    this.logger.log({
      message: 'Computed total number of leads to import for lead import',
      data: {
        leadImportModel,
        nbLeadsTotal,
      },
    })

    this.eventEmitter.emit(
      LeadImportEvents.LEAD_IMPORT_COUNTERS_UPDATED,
      new LeadImportLeadCountersUpdatedEvent({
        organizationId: leadImportModel.organizationId,
        leadImportId: leadImportModel.id,
        nbLeadsTotal,
      })
    )

    return nbLeadsTotal
  }

  @TryCatchLogger({
    feature: LogFeature.LEAD_IMPORT,
    message:
      'Failed to compute number of leads total for Zeliq organization leads',
  })
  private computeNbLeadsTotalForCSV(leadImportModel: LeadImportModel): number {
    const importProps = leadImportModel.getCsvImportProps()

    // Conditional chaining because sourceSpecificProps not defined on CSV import creation
    return (importProps as LeadImportSourceCsvFilePropsMinimal)
      ?.sheetRecordCounts?.valid
  }

  @TryCatchLogger({
    feature: LogFeature.LEAD_IMPORT,
    message:
      'Failed to compute number of leads total for Zeliq organization leads',
  })
  private async computeNbLeadsTotalForZeliqOrganizationLeads(
    leadImportModel: LeadImportModel
  ): Promise<number> {
    const importProps = leadImportModel.getAllLeadsImportProps()
    let nbLeadsTotal = 0

    if (importProps.leadsIds) {
      nbLeadsTotal = importProps.leadsIds.length
    } else if (importProps.filters) {
      nbLeadsTotal = await this.computeNbLeadsTotalFromFilters(
        leadImportModel,
        importProps
      )
    }

    return nbLeadsTotal
  }

  @TryCatchLogger({
    feature: LogFeature.LEAD_IMPORT,
    message: 'Failed to compute number of leads total from filters',
  })
  private async computeNbLeadsTotalFromFilters(
    leadImportModel: LeadImportModel,
    importProps: LeadImportSourceZeliqOrganizationProps
  ): Promise<number> {
    const { filters, searchText } = importProps
    const pagination: PaginationQueryDto = new PaginationQueryDto()
    Object.assign(pagination, {
      limitPerPage: 1, // Just to get the total matches
      page: 1,
      order: SortType.ASC,
      orderBy: 'createdAt',
    })

    const { paginationQueryObject, searchQueryObject } =
      await this.queryBus.execute(
        new GetSearchPaginationQueryObjectsQuery(
          { searchText, filters },
          pagination,
          leadImportModel.createdBy
        )
      )

    const response = await this.queryBus.execute(
      new SearchIndexContactQuery(
        leadImportModel.organizationId,
        paginationQueryObject,
        searchQueryObject
      )
    )

    return response.meta.totalItems
  }
}
