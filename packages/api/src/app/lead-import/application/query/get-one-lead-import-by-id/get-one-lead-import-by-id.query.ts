import { IsUUID } from 'class-validator'
import { Query } from '@nestjs/cqrs'
import { LeadImportModel } from '../../../domain/models/lead-import.model'

export class GetOneLeadImportByIdQuery extends Query<LeadImportModel> {
  @IsUUID()
  readonly leadImportId: string

  @IsUUID()
  readonly organizationId: string

  constructor(leadImportId: string, organizationId: string) {
    super()
    this.leadImportId = leadImportId
    this.organizationId = organizationId
  }
}
