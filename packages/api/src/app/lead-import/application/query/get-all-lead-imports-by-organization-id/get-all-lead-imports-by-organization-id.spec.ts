import { beforeEach, describe, expect, it, jest } from '@jest/globals'
import { Test, TestingModule } from '@nestjs/testing'
import { GetAllLeadImportsByOrganizationIdQuery } from './get-all-lead-imports-by-organization-id.query'
import {
  LEAD_IMPORT_REPOSITORY_INTERFACE,
  LeadImportRepositoryInterface,
} from '../../../domain/interface/lead-import-repository.interface'
import { LeadImportModel } from '../../../domain/models/lead-import.model'
import { v4 as uuidv4 } from 'uuid'
import { GetAllLeadImportsByOrganizationIdHandler } from './get-all-lead-imports-by-organization-id.handler'
import { Logger } from '@nestjs/common'
import { LeadImportStatusEnum } from '@getheroes/shared'

describe('GetAllLeadImportsByOrganizationIdHandler', () => {
  let handler: GetAllLeadImportsByOrganizationIdHandler
  let repository: LeadImportRepositoryInterface

  const mockLeadImportRepository = () => ({
    findByOrganizationId: jest.fn(),
  })

  jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {})
  jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => {})
  jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {})
  jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => {})

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GetAllLeadImportsByOrganizationIdHandler,
        {
          provide: LEAD_IMPORT_REPOSITORY_INTERFACE,
          useFactory: mockLeadImportRepository,
        },
      ],
    }).compile()

    handler = module.get<GetAllLeadImportsByOrganizationIdHandler>(
      GetAllLeadImportsByOrganizationIdHandler
    )
    repository = module.get<LeadImportRepositoryInterface>(
      LEAD_IMPORT_REPOSITORY_INTERFACE
    )
  })

  it('should return lead imports when organizationId matches', async () => {
    const organizationId = uuidv4()
    const query = new GetAllLeadImportsByOrganizationIdQuery(organizationId)

    const leadImportModels: LeadImportModel[] = [
      {
        id: 'leadImport-1',
        organizationId: organizationId,
        createdBy: { id: uuidv4() } as never, // Simplified for test
        // ...other properties
      } as unknown as LeadImportModel,
      {
        id: 'leadImport-2',
        organizationId: organizationId,
        createdBy: { id: uuidv4() } as never,
        // ...other properties
      } as unknown as LeadImportModel,
    ]

    jest
      .spyOn(repository, 'findByOrganizationId')
      .mockResolvedValue(leadImportModels)

    const result = await handler.execute(query)

    expect(result).toEqual(leadImportModels)
    expect(repository.findByOrganizationId).toHaveBeenCalledWith(
      organizationId,
      {}
    )
  })

  it('should throw an error if validation fails', async () => {
    const query = new GetAllLeadImportsByOrganizationIdQuery('invalid-uuid')

    try {
      await handler.execute(query)
      fail('Expected to throw error')
    } catch (error) {
      expect(error).toBeDefined()
    }
  })

  it('should return lead imports from the repository', async () => {
    const organizationId = uuidv4()
    const query = new GetAllLeadImportsByOrganizationIdQuery(organizationId)

    const filteredLeadImportModels: LeadImportModel[] = [
      {
        id: 'leadImport-1',
        organizationId: organizationId,
        createdBy: { id: 'user-1' } as never,
        // ...other properties
      } as unknown as LeadImportModel,
      {
        id: 'leadImport-3',
        organizationId: organizationId,
        createdBy: { id: 'user-3' } as never,
        // ...other properties
      } as unknown as LeadImportModel,
    ]

    jest
      .spyOn(repository, 'findByOrganizationId')
      .mockResolvedValue(filteredLeadImportModels)

    const result = await handler.execute(query)

    expect(result).toEqual(filteredLeadImportModels)
    expect(repository.findByOrganizationId).toHaveBeenCalledWith(
      organizationId,
      {}
    )
  })

  it('should return the filtered lead imports from the repository', async () => {
    const organizationId = uuidv4()
    const query = new GetAllLeadImportsByOrganizationIdQuery(organizationId)

    const filteredLeadImportModels: LeadImportModel[] = [
      {
        id: 'leadImport-2',
        organizationId: organizationId,
        createdBy: { id: 'user-2' } as never,
        status: LeadImportStatusEnum.ERROR,
        createdAt: new Date(), // Recent ERROR
      } as unknown as LeadImportModel,
      {
        id: 'leadImport-3',
        organizationId: organizationId,
        createdBy: { id: 'user-3' } as never,
        status: LeadImportStatusEnum.SUCCESS,
        createdAt: new Date(),
      } as unknown as LeadImportModel,
    ]

    jest
      .spyOn(repository, 'findByOrganizationId')
      .mockResolvedValue(filteredLeadImportModels)

    const result = await handler.execute(query)

    // Should return exactly what the repository returned
    expect(result).toEqual(filteredLeadImportModels)
    expect(repository.findByOrganizationId).toHaveBeenCalledWith(
      organizationId,
      {}
    )
  })
})
