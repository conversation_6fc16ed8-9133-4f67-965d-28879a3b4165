import {
  <PERSON><PERSON>rray,
  Is<PERSON><PERSON>,
  IsOptional,
  IsUUID,
  ValidateNested,
  validateSync,
} from 'class-validator'
import { Type } from 'class-transformer'
import { LeadReviewAutomaticallyResolve } from '@getheroes/shared'
import { LogFeature, LoggerService } from '../../../../shared/logger.service'
import { ResolveContactDuplicateDTO } from '../../dto/resolve-contact-duplicate.dto'
import { ResolveLeadImportDuplicatesDto } from '../../dto/resolve-lead-import-duplicates.dto'
import { UserModel } from '../../../../shared/domain/model/user.model'

export class ResolveLeadImportDuplicatesCommand {
  private readonly logger = new LoggerService({
    context: ResolveLeadImportDuplicatesCommand.name,
    feature: LogFeature.LEAD_IMPORT,
  })

  @IsUUID()
  organizationId: string

  @Type(() => UserModel)
  user: UserModel

  @IsUUID()
  leadImportId: string

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ResolveContactDuplicateDTO)
  leadsToUpdate?: ResolveContactDuplicateDTO[]

  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  leadIdsToRemove?: string[]

  @IsOptional()
  @IsEnum(LeadReviewAutomaticallyResolve)
  automaticallyResolveByKeeping?: LeadReviewAutomaticallyResolve

  constructor(
    readonly props: {
      dto: ResolveLeadImportDuplicatesDto
      leadImportId: string
      organizationId: string
      user: UserModel
    }
  ) {
    this.organizationId = props.organizationId
    this.leadImportId = props.leadImportId
    this.user = props.user
    this.automaticallyResolveByKeeping = props.dto.automaticallyResolveByKeeping
    this.leadsToUpdate = props.dto.leadsToUpdate
    this.leadIdsToRemove = props.dto.leadIdsToRemove

    const validationErrors = validateSync(this)

    if (validationErrors.length > 0) {
      this.logger.error({
        data: {
          command: this,
          validationErrors,
        },
        message: 'Validation failed for ResolveLeadImportDuplicatesCommand',
      })
      throw new Error(
        'Validation failed for ResolveLeadImportDuplicatesCommand'
      )
    }
  }
}
