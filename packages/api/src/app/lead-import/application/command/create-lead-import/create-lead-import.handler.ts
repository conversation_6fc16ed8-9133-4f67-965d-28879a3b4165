import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON><PERSON>ler, QueryBus } from '@nestjs/cqrs'
import { Inject, Injectable } from '@nestjs/common'

import { validateOrReject } from 'class-validator'
import { CreateLeadImportCommand } from './create-lead-import.command'
import {
  LEAD_IMPORT_REPOSITORY_INTERFACE,
  LeadImportRepositoryInterface,
} from '../../../domain/interface/lead-import-repository.interface'
import { LeadImportModel } from '../../../domain/models/lead-import.model'
import {
  AsyncActionCategoryEnum,
  AsyncActionStatusEnum,
  LeadImportSourceEnum,
  LeadImportStatusEnum,
} from '@getheroes/shared'
import { GetUserQuery } from '../../../../user/application/query/get-user/get-user.query'
import { LogFeature, LoggerService } from '../../../../shared/logger.service'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { LeadImportEvents } from '../../../../shared/domain/event/event.enum'
import { LeadImportCreatedEvent } from '../../../../shared/domain/event/lead-import/lead-import-created.event'
import {
  LeadImportInvalidPropsError,
  LeadImportLimitExceededError,
} from './lead-import-invalid-props.error'
import { UserModel } from '../../../../shared/domain/model/user.model'
import { CreateLeadImportDTO } from '../../../domain/dto/create-lead-import.dto'

import {
  LEAD_IMPORT_SERVICE,
  LeadImportServiceInterface,
} from '../../../domain/interface/lead-import-service.interface'
import {
  LEAD_IMPORT_LEAD_COUNTERS_SERVICE_INTERFACE,
  LeadImportLeadCountersServiceInterface,
} from '../../../domain/interface/lead-import-lead-counters-service.interface'
import { LeadImportSourceCsvFilePropsMinimal } from '../../../../shared/domain/interface/lead-import.interface'
import { CreateAsyncActionDto } from '../../../../shared/domain/dto/create-async-action.dto'
import {
  ASYNC_ACTION_SERVICE_INTERFACE,
  AsyncActionServiceInterface,
} from '../../../../shared/domain/interface/async-action-service.interface'

@Injectable()
@CommandHandler(CreateLeadImportCommand)
export class CreateLeadImportHandler
  implements ICommandHandler<CreateLeadImportCommand>
{
  private readonly logger = new LoggerService({
    context: CreateLeadImportHandler.name,
    feature: LogFeature.LEAD_IMPORT,
  })

  constructor(
    @Inject(LEAD_IMPORT_REPOSITORY_INTERFACE)
    private readonly leadImportRepository: LeadImportRepositoryInterface,
    private readonly queryBus: QueryBus,
    private readonly eventEmitter: EventEmitter2,
    @Inject(ASYNC_ACTION_SERVICE_INTERFACE)
    private readonly asyncActionService: AsyncActionServiceInterface,
    @Inject(LEAD_IMPORT_SERVICE)
    private readonly leadImportService: LeadImportServiceInterface,
    @Inject(LEAD_IMPORT_LEAD_COUNTERS_SERVICE_INTERFACE)
    private readonly leadImportLeadCountersService: LeadImportLeadCountersServiceInterface
  ) {}

  async execute(command: CreateLeadImportCommand): Promise<LeadImportModel> {
    try {
      await validateOrReject(command)
      this.leadImportService.validateImportLimits({
        source: command.source,
        sourceSpecificProps: command.sourceSpecificProps,
        context: command.context,
      })

      const {
        organizationId,
        createdByUserId,
        source,
        leadType,
        labels,
        context,
        sourceSpecificProps,
        contextSpecificProps,
      } = command

      const createdBy: UserModel = await this.queryBus.execute(
        new GetUserQuery(createdByUserId)
      )

      // For CSV_FILE imports, set nbLeadsTotal directly from sourceSpecificProps
      let nbLeadsTotal = 0
      if (source === LeadImportSourceEnum.CSV_FILE) {
        nbLeadsTotal =
          (sourceSpecificProps as LeadImportSourceCsvFilePropsMinimal)
            ?.sheetRecordCounts?.valid ?? 0
      }

      const createLeadImportDTO = new CreateLeadImportDTO({
        createdBy,
        organizationId,
        status: LeadImportStatusEnum.DRAFT,
        sourceSpecificProps,
        nbLeadsTotal,
        nbLeadsImported: 0,
        source,
        leadType,
        labels,
        context,
        contextSpecificProps,
        isAvailableInWorkspace: false,
      })

      const createdLeadImport =
        await this.leadImportRepository.create(createLeadImportDTO)

      await this.asyncActionService.create(
        new CreateAsyncActionDto({
          status: AsyncActionStatusEnum.PRELOADING,
          organizationId,
          createdById: createdLeadImport.createdBy.id,
          category: AsyncActionCategoryEnum.LEAD_IMPORT,
          internalRelationId: createdLeadImport.id,
        })
      )

      await this.leadImportService.attemptToStartImportProcess(
        createdLeadImport
      )

      this.eventEmitter.emit(
        LeadImportEvents.LEAD_IMPORT_CREATED,
        new LeadImportCreatedEvent({
          organizationId: createdLeadImport.organizationId,
          leadImportId: createdLeadImport.id,
          context: createdLeadImport.context,
          nbLeadsTotal: createdLeadImport.nbLeadsTotal,
        })
      )

      await this.leadImportLeadCountersService.computeNbLeadsTotal(
        createdLeadImport
      )

      this.logger.log({
        data: {
          command: {
            ...command,
          },
          leadImport: createdLeadImport,
          leadImportId: createdLeadImport.id,
        },
        message: `Lead import created with status ${createdLeadImport.status} for source ${createdLeadImport.source} and context ${createdLeadImport.context}`,
      })

      return createdLeadImport
    } catch (error) {
      if (
        !(
          error instanceof LeadImportInvalidPropsError ||
          error instanceof LeadImportLimitExceededError
        )
      ) {
        // don't log an error if user provided invalid props
        this.logger.error({
          data: { command },
          message: 'Failed to create lead import',
          error,
        })

        throw new Error('Failed to create the LeadImport.')
      }

      throw error
    }
  }
}
