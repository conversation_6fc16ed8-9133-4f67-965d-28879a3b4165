import { beforeEach, describe, expect, it, jest } from '@jest/globals'
import { Test, TestingModule } from '@nestjs/testing'
import { CreateLeadImportHandler } from './create-lead-import.handler'
import { CreateLeadImportCommand } from './create-lead-import.command'
import {
  LEAD_IMPORT_REPOSITORY_INTERFACE,
  LeadImportRepositoryInterface,
} from '../../../domain/interface/lead-import-repository.interface'
import {
  LeadImportContextEnum,
  LeadImportSourceEnum,
  LeadImportStatusEnum,
  LeadImportTypeEnum,
} from '@getheroes/shared'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { Logger } from '@nestjs/common'
import { MockLeadImportRepository } from '../../../infrastructure/repository/lead-import.repository.mock'
import { LeadImportModel } from '../../../domain/models/lead-import.model'
import { UserModel } from '../../../../shared/domain/model/user.model'
import { QueryBus } from '@nestjs/cqrs'
import { ConfigService } from '@nestjs/config'
import { Queue } from 'bull'
import { PROCESS_LEAD_IMPORT_QUEUE } from '../../../infrastructure/queues'
import { LeadImportEvents } from '../../../../shared/domain/event/event.enum'
import { LeadImportCreatedEvent } from '../../../../shared/domain/event/lead-import/lead-import-created.event'
import { LeadImportLimitExceededError } from './lead-import-invalid-props.error'
import { v4 as uuidv4 } from 'uuid'
import { getQueueToken } from '@nestjs/bull'
import { LEAD_IMPORT_SERVICE } from '../../../domain/interface/lead-import-service.interface'
import { LeadImportService } from '../../../domain/service/lead-import.service'
import { CSV_IMPORT_REPOSITORY_INTERFACE } from '../../../domain/interface/csv-import-repository.interface'
import { MockCSVImportRepository } from '../../../infrastructure/repository/csv-import.repository.mock'
import { LEAD_IMPORT_LEAD_COUNTERS_SERVICE_INTERFACE } from '../../../domain/interface/lead-import-lead-counters-service.interface'
import { LeadImportSourceCsvFilePropsMinimal } from '../../../../shared/domain/interface/lead-import.interface'
import { LeadImportFactoryMock } from '../../../domain/factory/lead-import.factory.mock'
import { ASYNC_ACTION_SERVICE_INTERFACE } from '../../../../shared/domain/interface/async-action-service.interface'
import { MockAsyncActionService } from '../../../../shared/tests/async-action-mock.service'
import { MockAsyncActionRepository } from '../../../../shared/infrastructure/repository/async-action.mock.repository'

describe('CreateLeadImportHandler', () => {
  let handler: CreateLeadImportHandler
  let mockRepo: LeadImportRepositoryInterface
  let eventEmitter: EventEmitter2
  let queryBus: QueryBus
  let configService: ConfigService
  let mockQueue: Queue
  let mockUser: UserModel
  let mockLeadImportLeadCountersService: any

  // Create a reusable mock for CSV source specific props
  const mockCsvSourceSpecificProps: LeadImportSourceCsvFilePropsMinimal = {
    spaceId: 'space-123',
    spaceName: 'Test Space',
    spaceNamespace: 'test-space',
    spaceMetadata: {},
    spaceEnvironmentId: 'env-123',
    sheetId: 'sheet-123',
    sheetName: 'Test Sheet',
    sheetFields: [
      { key: 'firstName', label: 'First Name', type: 'string' },
      { key: 'lastName', label: 'Last Name', type: 'string' },
      { key: 'email', label: 'Email', type: 'string' },
    ],
    sheetRecordCounts: {
      total: 100,
      valid: 90,
      error: 10,
    },
  }

  jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {})
  jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {})

  beforeEach(async () => {
    mockLeadImportLeadCountersService = {
      computeNbLeadsTotal: jest.fn().mockResolvedValue(90 as never),
    }

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateLeadImportHandler,
        {
          provide: LEAD_IMPORT_REPOSITORY_INTERFACE,
          useClass: MockLeadImportRepository,
        },
        {
          provide: CSV_IMPORT_REPOSITORY_INTERFACE,
          useClass: MockCSVImportRepository,
        },
        {
          provide: LEAD_IMPORT_SERVICE,
          useClass: LeadImportService,
        },
        {
          provide: QueryBus,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue(100),
          },
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
          },
        },
        {
          provide: getQueueToken(PROCESS_LEAD_IMPORT_QUEUE),
          useValue: { add: jest.fn() },
        },
        {
          provide: LEAD_IMPORT_LEAD_COUNTERS_SERVICE_INTERFACE,
          useValue: mockLeadImportLeadCountersService,
        },
        MockAsyncActionRepository,
        {
          provide: ASYNC_ACTION_SERVICE_INTERFACE,
          useClass: MockAsyncActionService,
        },
      ],
    }).compile()

    handler = module.get<CreateLeadImportHandler>(CreateLeadImportHandler)
    mockRepo = module.get<LeadImportRepositoryInterface>(
      LEAD_IMPORT_REPOSITORY_INTERFACE
    )
    eventEmitter = module.get<EventEmitter2>(EventEmitter2)
    queryBus = module.get<QueryBus>(QueryBus)
    configService = module.get<ConfigService>(ConfigService)
    mockQueue = module.get<Queue>(getQueueToken(PROCESS_LEAD_IMPORT_QUEUE))

    mockUser = new UserModel()
    mockUser.id = uuidv4()
    jest.spyOn(queryBus, 'execute').mockResolvedValueOnce(mockUser) // for GetUserQuery
  })

  it('should create a lead import successfully and enqueue a job', async () => {
    // Arrange
    const commandProps = {
      organizationId: uuidv4(),
      createdByUserId: mockUser.id,
      source: LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS,
      context: LeadImportContextEnum.SEQUENCES,
      leadType: LeadImportTypeEnum.CONTACT,
      sourceSpecificProps: {
        leadsIds: [uuidv4(), uuidv4()],
      },
    }
    const command = new CreateLeadImportCommand(commandProps)

    // Act
    const result: LeadImportModel = await handler.execute(command)
    // Assert
    // 1. The repo should now contain a newly created lead import
    const dataInRepo = (mockRepo as MockLeadImportRepository).getData()
    expect(dataInRepo.length).toBe(1)
    const createdItem = dataInRepo[0]
    expect(createdItem.organizationId).toBe(commandProps.organizationId)
    expect(createdItem.status).toBe(LeadImportStatusEnum.DRAFT)

    // 2. The returned model is the same
    expect(result.id).toEqual(createdItem.id)
    expect(result.source).toBe(LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS)

    // 3. The event was emitted (expect 2 calls: LEAD_IMPORT_CREATED + ASYNC_ACTION_CREATED)
    expect(eventEmitter.emit).toHaveBeenCalledTimes(2)
    expect(eventEmitter.emit).toHaveBeenCalledWith(
      LeadImportEvents.LEAD_IMPORT_CREATED,
      expect.any(LeadImportCreatedEvent)
    )

    // Find the LEAD_IMPORT_CREATED event call
    const leadImportCreatedCall = (
      eventEmitter.emit as jest.Mock
    ).mock.calls.find(call => call[0] === LeadImportEvents.LEAD_IMPORT_CREATED)
    expect(leadImportCreatedCall).toBeDefined()
    const eventArg = leadImportCreatedCall[1] as {
      leadImportId: string
      organizationId: string
    }
    expect(eventArg.organizationId).toBe(commandProps.organizationId)
    expect(eventArg.leadImportId).toBe(result.id)
  })

  it('should throw LeadImportLimitExceededError if leadsIds exceed limit', async () => {
    // Arrange
    jest.spyOn(configService, 'get').mockReturnValue(1)

    const command = new CreateLeadImportCommand({
      organizationId: uuidv4(),
      createdByUserId: uuidv4(),
      source: LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS,
      leadType: LeadImportTypeEnum.CONTACT,
      context: LeadImportContextEnum.SEQUENCES,
      sourceSpecificProps: {
        leadsIds: [uuidv4(), uuidv4()], // 2 leads => above the limit of 1
      },
    })

    // Act & Assert
    await expect(handler.execute(command)).rejects.toThrowError(
      LeadImportLimitExceededError
    )
    // Ensure nothing is added to the repo or queue
    expect((mockRepo as MockLeadImportRepository).getData().length).toBe(0)
    expect(mockQueue.add).not.toHaveBeenCalled()
    expect(eventEmitter.emit).not.toHaveBeenCalled()
  })

  it('should apply the sequences context limit when context is SEQUENCES', async () => {
    // Arrange
    // Mock the config service to return different values based on the config key
    jest.spyOn(configService, 'get').mockImplementation((key: string) => {
      if (key === 'lead-import.maxLeadsForSequencesContext') {
        return 1000 // Sequences limit
      } else if (key === 'lead-import.maxLeadsFromZeliqOrganizationLeads') {
        return 10000 // Default limit
      }
      return 100 // Default for other keys
    })

    // Create a command with 1500 leads (exceeds sequences limit but not default limit)
    const leadsIds = Array(1500)
      .fill(0)
      .map(() => uuidv4())

    const command = new CreateLeadImportCommand({
      organizationId: uuidv4(),
      createdByUserId: uuidv4(),
      source: LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS,
      leadType: LeadImportTypeEnum.CONTACT,
      context: LeadImportContextEnum.SEQUENCES,
      sourceSpecificProps: {
        leadsIds,
      },
    })

    // Act & Assert
    await expect(handler.execute(command)).rejects.toThrowError(
      LeadImportLimitExceededError
    )
    // Error message should mention the sequences limit
    await expect(handler.execute(command)).rejects.toThrow(/must be <= 1000/)
  })

  it('should apply the default limit when context is ALL_LEADS', async () => {
    // Arrange
    // Mock the config service to return different values based on the config key
    jest.spyOn(configService, 'get').mockImplementation((key: string) => {
      if (key === 'lead-import.maxLeadsForSequencesContext') {
        return 1000 // Sequences limit
      } else if (key === 'lead-import.maxLeadsFromZeliqOrganizationLeads') {
        return 10000 // Default limit
      }
      return 100 // Default for other keys
    })

    // Create a command with 1500 leads (exceeds sequences limit but not default limit)
    const leadsIds = Array(1500)
      .fill(0)
      .map(() => uuidv4())

    const command = new CreateLeadImportCommand({
      organizationId: uuidv4(),
      createdByUserId: uuidv4(),
      source: LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS,
      leadType: LeadImportTypeEnum.CONTACT,
      context: LeadImportContextEnum.ENRICHMENT_HUB, // Different context
      sourceSpecificProps: {
        leadsIds,
      },
    })

    // Act
    // Should not throw an error since 1500 is below the default limit of 10000
    await handler.execute(command)

    // Assert
    // Verify that the lead import was created
    expect((mockRepo as MockLeadImportRepository).getData().length).toBe(1)
  })

  it('should throw a generic error if a non-LeadImportInvalidPropsError occurs', async () => {
    // Arrange
    // Suppose the user query fails with an unexpected error
    jest.spyOn(mockRepo, 'create').mockRejectedValueOnce(new Error('DB error'))

    const command = new CreateLeadImportCommand({
      organizationId: uuidv4(),
      createdByUserId: uuidv4(),
      source: LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS,
      leadType: LeadImportTypeEnum.CONTACT,
      context: LeadImportContextEnum.SEQUENCES,
      sourceSpecificProps: {
        leadsIds: [uuidv4(), uuidv4()],
      },
    })

    // Act & Assert
    await expect(handler.execute(command)).rejects.toThrowError()
    // Check logs or side effects
    expect(mockQueue.add).not.toHaveBeenCalled()
    expect(eventEmitter.emit).not.toHaveBeenCalled()
  })

  it('should create a lead import with CSV_FILE source successfully', async () => {
    // Arrange
    const commandProps = {
      organizationId: uuidv4(),
      createdByUserId: mockUser.id,
      source: LeadImportSourceEnum.CSV_FILE,
      context: LeadImportContextEnum.SEQUENCES,
      leadType: LeadImportTypeEnum.CONTACT,
      sourceSpecificProps: { ...mockCsvSourceSpecificProps },
    }
    const command = new CreateLeadImportCommand(commandProps)

    // Act
    const result: LeadImportModel = await handler.execute(command)

    // Assert
    // 1. The repo should now contain a newly created lead import
    const dataInRepo = (mockRepo as MockLeadImportRepository).getData()
    expect(dataInRepo.length).toBe(1)
    const createdItem = dataInRepo[0]
    expect(createdItem.organizationId).toBe(commandProps.organizationId)
    expect(createdItem.status).toBe(LeadImportStatusEnum.DRAFT)

    // 2. The returned model is the same
    expect(result.id).toEqual(createdItem.id)
    expect(result.source).toBe(LeadImportSourceEnum.CSV_FILE)

    // 3. The event was emitted
    expect(eventEmitter.emit).toHaveBeenCalledWith(
      LeadImportEvents.LEAD_IMPORT_CREATED,
      expect.any(LeadImportCreatedEvent)
    )

    // 4. The lead counters service was called
    expect(
      mockLeadImportLeadCountersService.computeNbLeadsTotal
    ).toHaveBeenCalledWith(result)
  })

  it('should compute nbLeadsTotal for CSV_FILE source', async () => {
    // Arrange
    const commandProps = {
      organizationId: uuidv4(),
      createdByUserId: mockUser.id,
      source: LeadImportSourceEnum.CSV_FILE,
      context: LeadImportContextEnum.SEQUENCES,
      leadType: LeadImportTypeEnum.CONTACT,
      sourceSpecificProps: { ...mockCsvSourceSpecificProps },
    }
    const command = new CreateLeadImportCommand(commandProps)

    // Act
    await handler.execute(command)

    // Assert
    expect(
      mockLeadImportLeadCountersService.computeNbLeadsTotal
    ).toHaveBeenCalled()
  })

  it('should apply CSV file import limits correctly', async () => {
    // Arrange
    // Mock the config service to return different values based on the config key
    jest.spyOn(configService, 'get').mockImplementation((key: string) => {
      if (key === 'lead-import.maxLeadsFromCsvFile') {
        return 50 // CSV file limit
      }
      return 100 // Default for other keys
    })

    // Create CSV props with too many records
    const csvPropsWithTooManyRecords = {
      ...mockCsvSourceSpecificProps,
      sheetRecordCounts: {
        total: 100,
        valid: 80, // Above the limit of 50
        error: 20,
      },
    }

    const command = new CreateLeadImportCommand({
      organizationId: uuidv4(),
      createdByUserId: mockUser.id,
      source: LeadImportSourceEnum.CSV_FILE,
      context: LeadImportContextEnum.SEQUENCES,
      leadType: LeadImportTypeEnum.CONTACT,
      sourceSpecificProps: csvPropsWithTooManyRecords,
    })

    // Mock the repository create method
    const createSpy = jest
      .spyOn(mockRepo, 'create')
      .mockImplementation(async () => {
        return LeadImportFactoryMock.create({
          id: uuidv4(),
          createdAt: new Date(),
          updatedAt: new Date(),
          createdByUserId: mockUser.id,
          organizationId: command.organizationId,
          source: command.source,
          context: command.context,
          leadType: command.leadType,
          status: LeadImportStatusEnum.DRAFT,
          sourceSpecificProps: command.sourceSpecificProps,
          nbLeadsTotal: 0,
          nbLeadsImported: 0,
          isAvailableInWorkspace: false,
          createdBy: mockUser,
        })
      })

    // Mock the lead import service to throw an error
    jest
      .spyOn(handler['leadImportService'], 'validateImportLimits')
      .mockImplementation(() => {
        throw new LeadImportLimitExceededError('CSV file import limit exceeded')
      })

    // Act & Assert
    await expect(handler.execute(command)).rejects.toThrowError(
      LeadImportLimitExceededError
    )
    expect(createSpy).not.toHaveBeenCalled()
  })
})
