import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ICommand<PERSON>andler,
  QueryBus,
} from '@nestjs/cqrs'
import { Injectable, NotImplementedException } from '@nestjs/common'
import { ImportFromZeliqOrganizationLeadsCommand } from './import-from-zeliq-organization-leads.command'
import { LeadImportContextEnum, LeadImportTypeEnum } from '@getheroes/shared'
import { UpdateContactsBulkLeadImportIdsCommand } from '../../../../../lead/application/command/contact/update-bulk-contacts-lead-import-ids/update-contacts-bulk-lead-import-ids.command'
import { LeadImportModel } from '../../../../domain/models/lead-import.model'
import { ConfigService } from '@nestjs/config'
import { PaginationQueryDto } from '../../../../../../ui/api/shared/infrastructure/dto/pagination-query.dto'
import { SortType } from '../../../../../shared/domain/sort-type.enum'
import {
  GetSearchPaginationQueryObjectsQuery,
  GetSearchPaginationQueryObjectsResult,
} from '../../../../../lead/application/query/search/get-search-pagination-search-query-objects/get-search-pagination-query-objects.query'
import { PaginationResultObject } from '../../../../../shared/domain/object/pagination-result.object'
import { ContactModel } from '../../../../../lead/domain/model/contact.model'
import { SearchIndexContactQuery } from '../../../../../lead/application/query/search-contact/search-index-contact.query'
import { TryCatchLogger } from '../../../../../shared/domain/decorator/try-catch-logger.decorator'
import { LogFeature } from '../../../../../shared/logger.service'
import { LeadImportSourceZeliqOrganizationProps } from '../../../../../shared/domain/interface/lead-import.interface'

@Injectable()
@CommandHandler(ImportFromZeliqOrganizationLeadsCommand)
export class ImportFromZeliqOrganizationLeadsHandler
  implements ICommandHandler<ImportFromZeliqOrganizationLeadsCommand>
{
  constructor(
    private readonly configService: ConfigService,
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus
  ) {}

  @TryCatchLogger({
    feature: LogFeature.LEAD_IMPORT,
    message: 'Failed to import leads from Zeliq organization leads',
  })
  async execute({
    leadImportModel,
  }: ImportFromZeliqOrganizationLeadsCommand): Promise<void> {
    const importProps = leadImportModel.getAllLeadsImportProps()

    if (importProps.leadsIds) {
      await this.importArrayOfLeadsIds(importProps.leadsIds, leadImportModel)
    }

    if (importProps.filters) {
      await this.importLeadsFromSearchFilters(leadImportModel)
    }
  }

  @TryCatchLogger({
    feature: LogFeature.LEAD_IMPORT,
    message: 'Failed to import array of leads ids',
  })
  private async importArrayOfLeadsIds(
    leadsIds: string[],
    leadImportModel: LeadImportModel
  ) {
    if (leadImportModel.leadType === LeadImportTypeEnum.CONTACT) {
      await this.commandBus.execute(
        new UpdateContactsBulkLeadImportIdsCommand({
          contactsIds: leadsIds,
          leadImportId: leadImportModel.id,
          operation: 'add',
          organizationId: leadImportModel.organizationId,
        })
      )
    } else {
      throw new NotImplementedException()
    }
  }

  @TryCatchLogger({
    feature: LogFeature.LEAD_IMPORT,
    message: 'Failed to import leads from search',
  })
  private async importLeadsFromSearchFilters(leadImportModel: LeadImportModel) {
    const { filters, searchText } =
      leadImportModel.sourceSpecificProps as LeadImportSourceZeliqOrganizationProps
    const batchSize: number = this.configService.get(
      'lead-import.OrganizationLeadsImportBatchSize'
    )
    const maxLimit: number =
      leadImportModel.context === LeadImportContextEnum.SEQUENCES
        ? this.configService.get('lead-import.maxLeadsForSequencesContext')
        : this.configService.get(
            'lead-import.maxLeadsFromZeliqOrganizationLeads'
          )

    const pagination: PaginationQueryDto = new PaginationQueryDto()
    Object.assign(pagination, {
      limitPerPage: batchSize,
      page: 1,
      order: SortType.ASC,
      orderBy: 'createdAt',
    })

    let hasReachedEndOfPagination = false
    let nbLeadsIncluded = 0
    while (!hasReachedEndOfPagination && nbLeadsIncluded <= maxLimit) {
      const {
        paginationQueryObject,
        searchQueryObject,
      }: GetSearchPaginationQueryObjectsResult = await this.queryBus.execute(
        new GetSearchPaginationQueryObjectsQuery(
          { searchText, filters },
          pagination,
          leadImportModel.createdBy
        )
      )

      const response: PaginationResultObject<ContactModel> =
        await this.queryBus.execute(
          new SearchIndexContactQuery(
            leadImportModel.organizationId,
            paginationQueryObject,
            searchQueryObject
          )
        )

      if (response.meta.hasNextPage) {
        Object.assign(pagination, {
          page: pagination.page + 1,
        })
      } else {
        hasReachedEndOfPagination = true
      }

      const nbLeadsToImport = Math.min(
        response.items.length,
        maxLimit - nbLeadsIncluded
      )
      nbLeadsIncluded += nbLeadsToImport

      await this.importArrayOfLeadsIds(
        response.items.slice(0, nbLeadsToImport).map(contact => contact.id),
        leadImportModel
      )
    }
  }
}
