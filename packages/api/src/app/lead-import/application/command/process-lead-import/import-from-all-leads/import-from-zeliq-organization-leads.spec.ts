import { beforeEach, describe, expect, it, jest } from '@jest/globals'
import { Test, TestingModule } from '@nestjs/testing'
import { ImportFromZeliqOrganizationLeadsHandler } from './import-from-zeliq-organization-leads.handler'
import { ImportFromZeliqOrganizationLeadsCommand } from './import-from-zeliq-organization-leads.command'
import { ConfigService } from '@nestjs/config'
import { QueryBus, CommandBus } from '@nestjs/cqrs'
import { Logger } from '@nestjs/common'
import {
  LeadImportTypeEnum,
  LeadImportSourceEnum,
  LeadImportStatusEnum,
  LeadImportContextEnum,
} from '@getheroes/shared'
import { v4 as uuidv4 } from 'uuid'
import { UpdateContactsBulkLeadImportIdsCommand } from '../../../../../lead/application/command/contact/update-bulk-contacts-lead-import-ids/update-contacts-bulk-lead-import-ids.command'
import { SearchIndexContactQuery } from '../../../../../lead/application/query/search-contact/search-index-contact.query'
import {
  GetSearchPaginationQueryObjectsQuery,
  GetSearchPaginationQueryObjectsResult,
} from '../../../../../lead/application/query/search/get-search-pagination-search-query-objects/get-search-pagination-query-objects.query'
import { PaginationResultObject } from '../../../../../shared/domain/object/pagination-result.object'
import { ContactModel } from '../../../../../lead/domain/model/contact.model'
import { LeadImportFactoryMock } from '../../../../domain/factory/lead-import.factory.mock'
import { SearchFilterItemDto } from '../../../../../../ui/api/shared/infrastructure/dto/search-filter-item.dto'
import { PaginationResultMetaObject } from '../../../../../shared/domain/object/pagination-result-meta.object'
import { LeadImportSourceZeliqOrganizationProps } from '../../../../../shared/domain/interface/lead-import.interface'

describe('ImportFromZeliqOrganizationLeadsHandler', () => {
  let handler: ImportFromZeliqOrganizationLeadsHandler
  let mockConfig: ConfigService
  let mockQueryBus: QueryBus
  let mockCommandBus: CommandBus

  jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {})
  jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {})

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImportFromZeliqOrganizationLeadsHandler,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: QueryBus,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: CommandBus,
          useValue: {
            execute: jest.fn(),
          },
        },
      ],
    }).compile()

    handler = module.get<ImportFromZeliqOrganizationLeadsHandler>(
      ImportFromZeliqOrganizationLeadsHandler
    )
    mockConfig = module.get<ConfigService>(ConfigService)
    mockQueryBus = module.get<QueryBus>(QueryBus)
    mockCommandBus = module.get<CommandBus>(CommandBus)
  })

  it('should import an array of leadsIds if leadType=CONTACT', async () => {
    // Arrange
    const leadImportModel = LeadImportFactoryMock.create({
      status: LeadImportStatusEnum.DRAFT,
      source: LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS,
      leadType: LeadImportTypeEnum.CONTACT,
      sourceSpecificProps: {
        leadsIds: [uuidv4(), uuidv4()],
      },
    })
    const command = new ImportFromZeliqOrganizationLeadsCommand(leadImportModel)

    // Act
    await handler.execute(command)

    // Assert
    // We expect a call to UpdateContactsBulkLeadImportIdsCommand with operation=add
    expect(mockCommandBus.execute).toHaveBeenCalledTimes(1)
    expect(mockCommandBus.execute).toHaveBeenCalledWith(
      new UpdateContactsBulkLeadImportIdsCommand({
        contactsIds: (
          leadImportModel.sourceSpecificProps as LeadImportSourceZeliqOrganizationProps
        ).leadsIds,
        leadImportId: leadImportModel.id,
        operation: 'add',
        organizationId: leadImportModel.organizationId,
      })
    )
  })

  it('[SEQUENCE] should import leads from filters with pagination, stopping after no next page', async () => {
    // Arrange
    const leadImportModel = LeadImportFactoryMock.create({
      status: LeadImportStatusEnum.DRAFT,
      source: LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS,
      leadType: LeadImportTypeEnum.CONTACT,
      sourceSpecificProps: {
        filters: [
          {
            field: 'source',
            operator: 'ANY_OF_VALUES',
            values: ['ENRICHMENT_HUB'],
          } as SearchFilterItemDto,
        ],
      },
    })
    const command = new ImportFromZeliqOrganizationLeadsCommand(leadImportModel)

    // Mock config for batchSize, maxLimit
    jest.spyOn(mockConfig, 'get').mockImplementation((key: string) => {
      if (key === 'lead-import.OrganizationLeadsImportBatchSize') return 10
      if (key === 'lead-import.maxLeadsForSequencesContext') return 50
    })

    // First page has 5 items, hasNextPage=true
    const contactItemsPage1: ContactModel[] = Array.from({ length: 5 }, () => {
      const contact = new ContactModel()
      contact.id = uuidv4()
      return contact
    })
    const paginationResultPage1: PaginationResultObject<ContactModel> = {
      items: contactItemsPage1,
      meta: {
        page: 1,
        hasNextPage: true,
        totalItems: 5,
      } as PaginationResultMetaObject,
    }

    // Second page has 3 items, hasNextPage=false
    const contactItemsPage2: ContactModel[] = Array.from({ length: 3 }, () => {
      const contact = new ContactModel()
      contact.id = uuidv4()
      return contact
    })
    const paginationResultPage2: PaginationResultObject<ContactModel> = {
      items: contactItemsPage2,
      meta: {
        page: 2,
        hasNextPage: false,
        totalItems: 8,
      } as PaginationResultMetaObject,
    }

    // The first getSearchPaginationQueryObjectsQuery call returns an object with the pagination & search
    // The second call will get the next page, etc.
    let currentPage = 0
    jest.spyOn(mockQueryBus, 'execute').mockImplementation(async query => {
      if (query instanceof GetSearchPaginationQueryObjectsQuery) {
        currentPage++
        return {
          paginationQueryObject: {
            page: currentPage,
          },
          searchQueryObject: {},
        } as GetSearchPaginationQueryObjectsResult
      } else if (query instanceof SearchIndexContactQuery) {
        if ((query as { pagination: { page: number } }).pagination.page === 1) {
          return paginationResultPage1
        } else {
          return paginationResultPage2
        }
      }
      return null
    })

    // Act
    await handler.execute(command)

    // Assert
    // We expect multiple calls to:
    //   1) GetSearchPaginationQueryObjectsQuery
    //   2) SearchIndexContactQuery (twice, for 2 pages)
    //   3) importArrayOfLeadsIds -> calls UpdateContactsBulkLeadImportIdsCommand
    // On the first page: 5 leads
    // On the second page: 3 leads
    // => total 8 leads
    // (The detail is black box, so let's check the final calls on commandBus at least.)
    // The sum of nbLeadsToImport is 8.
    // The calls to commandBus.execute for each page's leads:
    expect(mockCommandBus.execute).toHaveBeenCalledTimes(2)
    expect(mockQueryBus.execute).toHaveBeenCalledTimes(4)
    // Combine the IDs from both pages
    const first5Ids = contactItemsPage1.map(c => c.id)
    const second3Ids = contactItemsPage2.map(c => c.id)

    // Check first call
    expect(mockCommandBus.execute).toHaveBeenNthCalledWith(
      1,
      new UpdateContactsBulkLeadImportIdsCommand({
        contactsIds: first5Ids,
        leadImportId: leadImportModel.id,
        operation: 'add',
        organizationId: leadImportModel.organizationId,
      })
    )
    // Check second call
    expect(mockCommandBus.execute).toHaveBeenNthCalledWith(
      2,
      new UpdateContactsBulkLeadImportIdsCommand({
        contactsIds: second3Ids,
        leadImportId: leadImportModel.id,
        operation: 'add',
        organizationId: leadImportModel.organizationId,
      })
    )
  })

  it('[ENRICHMENT_HUB] should import leads from filters with pagination, stopping after no next page', async () => {
    // Arrange
    const leadImportModel = LeadImportFactoryMock.create({
      status: LeadImportStatusEnum.DRAFT,
      source: LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS,
      leadType: LeadImportTypeEnum.CONTACT,
      context: LeadImportContextEnum.ENRICHMENT_HUB,
      sourceSpecificProps: {
        filters: [
          {
            field: 'source',
            operator: 'ANY_OF_VALUES',
            values: ['ENRICHMENT_HUB'],
          } as SearchFilterItemDto,
        ],
      },
    })
    const command = new ImportFromZeliqOrganizationLeadsCommand(leadImportModel)

    // Mock config for batchSize, maxLimit
    jest.spyOn(mockConfig, 'get').mockImplementation((key: string) => {
      if (key === 'lead-import.OrganizationLeadsImportBatchSize') return 10
      if (key === 'lead-import.maxLeadsFromZeliqOrganizationLeads') return 50
    })

    // First page has 5 items, hasNextPage=true
    const contactItemsPage1: ContactModel[] = Array.from({ length: 5 }, () => {
      const contact = new ContactModel()
      contact.id = uuidv4()
      return contact
    })
    const paginationResultPage1: PaginationResultObject<ContactModel> = {
      items: contactItemsPage1,
      meta: {
        page: 1,
        hasNextPage: true,
        totalItems: 5,
      } as PaginationResultMetaObject,
    }

    // Second page has 3 items, hasNextPage=false
    const contactItemsPage2: ContactModel[] = Array.from({ length: 3 }, () => {
      const contact = new ContactModel()
      contact.id = uuidv4()
      return contact
    })
    const paginationResultPage2: PaginationResultObject<ContactModel> = {
      items: contactItemsPage2,
      meta: {
        page: 2,
        hasNextPage: false,
        totalItems: 8,
      } as PaginationResultMetaObject,
    }

    // The first getSearchPaginationQueryObjectsQuery call returns an object with the pagination & search
    // The second call will get the next page, etc.
    let currentPage = 0
    jest.spyOn(mockQueryBus, 'execute').mockImplementation(async query => {
      if (query instanceof GetSearchPaginationQueryObjectsQuery) {
        currentPage++
        return {
          paginationQueryObject: {
            page: currentPage,
          },
          searchQueryObject: {},
        } as GetSearchPaginationQueryObjectsResult
      } else if (query instanceof SearchIndexContactQuery) {
        if ((query as { pagination: { page: number } }).pagination.page === 1) {
          return paginationResultPage1
        } else {
          return paginationResultPage2
        }
      }
      return null
    })

    // Act
    await handler.execute(command)

    // Assert
    // We expect multiple calls to:
    //   1) GetSearchPaginationQueryObjectsQuery
    //   2) SearchIndexContactQuery (twice, for 2 pages)
    //   3) importArrayOfLeadsIds -> calls UpdateContactsBulkLeadImportIdsCommand
    // On the first page: 5 leads
    // On the second page: 3 leads
    // => total 8 leads
    // (The detail is black box, so let's check the final calls on commandBus at least.)
    // The sum of nbLeadsToImport is 8.
    // The calls to commandBus.execute for each page's leads:
    expect(mockCommandBus.execute).toHaveBeenCalledTimes(2)
    expect(mockQueryBus.execute).toHaveBeenCalledTimes(4)
    // Combine the IDs from both pages
    const first5Ids = contactItemsPage1.map(c => c.id)
    const second3Ids = contactItemsPage2.map(c => c.id)

    // Check first call
    expect(mockCommandBus.execute).toHaveBeenNthCalledWith(
      1,
      new UpdateContactsBulkLeadImportIdsCommand({
        contactsIds: first5Ids,
        leadImportId: leadImportModel.id,
        operation: 'add',
        organizationId: leadImportModel.organizationId,
      })
    )
    // Check second call
    expect(mockCommandBus.execute).toHaveBeenNthCalledWith(
      2,
      new UpdateContactsBulkLeadImportIdsCommand({
        contactsIds: second3Ids,
        leadImportId: leadImportModel.id,
        operation: 'add',
        organizationId: leadImportModel.organizationId,
      })
    )
  })
})
