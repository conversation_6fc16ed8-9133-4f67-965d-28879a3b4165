import { LeadImportModel } from '../models/lead-import.model'
import { LeadImportContextEnum, LeadImportSourceEnum } from '@getheroes/shared'
import { LeadImportSourceSpecificProps } from '../../../shared/domain/interface/lead-import.interface'

export const LEAD_IMPORT_SERVICE = 'LEAD_IMPORT_SERVICE'

export interface LeadImportServiceInterface {
  /**
   * Evaluates if the lead-import is ready to be processed in the import-queue.
   * Depending on the source and context, the value is either true or false.
   * Some sources depend on asynchronous processes and require additional properties to be set before the lead import can be processed.
   *
   * @param leadImportModel
   */
  isReadyForProcessing(leadImportModel: LeadImportModel): Promise<boolean>

  /**
   * Validate the number of leads that are about to be imported, agains the configured limits.
   * Each source has its own limits, and the lead-import is not allowed to be processed if the number of leads is too high.
   * @throws {LeadImportInvalidPropsError} If the number of leads is too high
   */
  validateImportLimits(props: {
    source: LeadImportSourceEnum
    sourceSpecificProps: LeadImportSourceSpecificProps
    context?: LeadImportContextEnum
  }): void

  attemptToStartImportProcess(leadImportModel: LeadImportModel): Promise<void>
}
