import { LeadImportContextEnum, LeadImportSourceEnum } from '@getheroes/shared'
import { InjectQueue } from '@nestjs/bull'
import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { Queue } from 'bull'
import { TryCatchLogger } from '../../../shared/domain/decorator/try-catch-logger.decorator'
import {
  LeadImportSourceCsvFileProps,
  LeadImportSourceCsvFilePropsMinimal,
  LeadImportSourceHubspotProps,
  LeadImportSourceSpecificProps,
  LeadImportSourceZeliqOrganizationProps,
} from '../../../shared/domain/interface/lead-import.interface'
import { LogFeature, LoggerService } from '../../../shared/logger.service'
import {
  LeadImportInvalidPropsError,
  LeadImportLimitExceededError,
} from '../../application/command/create-lead-import/lead-import-invalid-props.error'
import { PROCESS_LEAD_IMPORT_QUEUE } from '../../infrastructure/queues'
import { LeadEnrichmentQueueJobData } from '../interface/lead-enrichment-queue-job-data.interface'
import { LeadImportServiceInterface } from '../interface/lead-import-service.interface'
import { LeadImportModel } from '../models/lead-import.model'
import { LeadImportValidator } from '../validator/lead-import.validator'

@Injectable()
export class LeadImportService implements LeadImportServiceInterface {
  private readonly logger = new LoggerService({
    context: LeadImportService.name,
    feature: LogFeature.LEAD_IMPORT,
  })

  constructor(
    private readonly configService: ConfigService,
    @InjectQueue(PROCESS_LEAD_IMPORT_QUEUE)
    private readonly leadImportQueue: Queue
  ) {}

  @TryCatchLogger({
    feature: LogFeature.LEAD_IMPORT,
    message: 'Failed to check if lead-import is ready to be processed',
  })
  async isReadyForProcessing(
    leadImportModel: LeadImportModel
  ): Promise<boolean> {
    switch (leadImportModel.source) {
      case LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS:
        return true

      case LeadImportSourceEnum.CSV_FILE:
        try {
          LeadImportValidator.validateCsvImportSourceSpecificProps(
            leadImportModel.getCsvImportProps()
          )
          return true
        } catch {
          return false
        }

      case LeadImportSourceEnum.HUBSPOT:
      case LeadImportSourceEnum.LINKED_IN_POST_LIKERS_AND_COMMENTERS:
      case LeadImportSourceEnum.ZELIQ_SEARCH:
      case LeadImportSourceEnum.LINKED_IN_SEARCH:
        return true

      default:
        return false
    }
  }

  @TryCatchLogger({
    feature: LogFeature.LEAD_IMPORT,
    message: 'Failed to validate lead-import limits',
  })
  validateImportLimits(props: {
    source: LeadImportSourceEnum
    sourceSpecificProps: LeadImportSourceSpecificProps
    context?: LeadImportContextEnum
  }): void {
    const isSequencesContext = props.context === LeadImportContextEnum.SEQUENCES
    const sequencesLimit = isSequencesContext
      ? this.configService.get('lead-import.maxLeadsForSequencesContext')
      : null

    if (props.source === LeadImportSourceEnum.ZELIQ_ORGANIZATION_LEADS) {
      const importProps =
        props.sourceSpecificProps as LeadImportSourceZeliqOrganizationProps

      if (importProps.leadsIds) {
        // Use sequences limit if context is SEQUENCES, otherwise use the default limit
        const orgaLeadsImportLimit: number =
          sequencesLimit ||
          this.configService.get(
            'lead-import.maxLeadsFromZeliqOrganizationLeads'
          )

        if (importProps.leadsIds.length > orgaLeadsImportLimit) {
          throw new LeadImportLimitExceededError(
            `LeadsIds array is too long (must be <= ${orgaLeadsImportLimit})`
          )
        }
      }
    }

    if (props.source === LeadImportSourceEnum.CSV_FILE) {
      const importProps = props.sourceSpecificProps
      if (
        !(importProps as LeadImportSourceCsvFileProps).sheet &&
        !(importProps as LeadImportSourceCsvFilePropsMinimal).sheetId
      ) {
        // This prop is set after the creation of the lead-import, when Flatfile calls the webook
        return
      }

      // Use sequences limit if context is SEQUENCES, otherwise use the default limit
      const csvImportLimit: number =
        sequencesLimit ?? this.configService.get('lead-import.maxLeadsFromCsv')

      const legacyNbValidLeads = (importProps as LeadImportSourceCsvFileProps)
        ?.sheet?.recordCounts?.valid
      const minimalNbValidLeads = (
        importProps as LeadImportSourceCsvFilePropsMinimal
      )?.sheetRecordCounts?.valid
      const nbValidLeads = legacyNbValidLeads || minimalNbValidLeads
      if (nbValidLeads > csvImportLimit) {
        throw new LeadImportLimitExceededError(
          `Too many valid leads (${nbValidLeads}) in the CSV (must be <= ${csvImportLimit})`
        )
      }
    }

    if (props.source === LeadImportSourceEnum.HUBSPOT) {
      const importProps =
        props.sourceSpecificProps as LeadImportSourceHubspotProps

      // Use sequences limit if context is SEQUENCES, otherwise use the default limit
      const hubspotImportLimit: number =
        sequencesLimit ||
        this.configService.get('lead-import.maxLeadsFromHubspot')

      if (importProps.listIds.length > hubspotImportLimit) {
        throw new LeadImportInvalidPropsError(
          `ListIds array is too long (must be <= ${hubspotImportLimit})`
        )
      }
    }
  }

  @TryCatchLogger({
    feature: LogFeature.LEAD_IMPORT,
    message: 'Failed to attempt to start the lead-import process',
  })
  async attemptToStartImportProcess(
    leadImportModel: LeadImportModel
  ): Promise<void> {
    const isReadyForProcessing =
      await this.isReadyForProcessing(leadImportModel)

    if (isReadyForProcessing) {
      const importLeadsJobData: LeadEnrichmentQueueJobData = {
        leadImportId: leadImportModel.id,
        organizationId: leadImportModel.organizationId,
      }
      await this.leadImportQueue.add(importLeadsJobData)
      this.logger.log({
        data: { importLeadsJobData, leadImportModel },
        message: 'Lead import process job added to import queue',
      })
    } else {
      this.logger.log({
        data: { leadImportModel },
        message:
          'Lead import process job not added to import queue because not ready for processing',
      })
    }
  }
}
