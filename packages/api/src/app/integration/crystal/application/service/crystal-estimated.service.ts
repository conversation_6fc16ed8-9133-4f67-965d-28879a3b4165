import { ConflictException, Injectable } from '@nestjs/common'
import { QueryBus } from '@nestjs/cqrs'
import { PERSONALITY_COST } from '@getheroes/shared'
import { GetProfileDto } from '../../../../../ui/api/integration/crystal/dto/get-profile.dto'
import { EstimatedProfileObject } from '../../../../../ui/api/integration/crystal/object/estimated-profile.object'
import { FindContactQuery } from '../../../../lead/application/query/find-contact/find-contact.query'
import { FindProfilesQuery } from '../query/find-profiles/find-profiles.query'
import { CreditTransactionModel } from '../../../../credit/domain/model/credit-transaction.model'
import { FindTransactionsByOrganizationAndResourcesIdQuery } from '../../../../credit/application/query/find-transactions-by-organization-and-resources-id/find-transactions-by-organization-and-resources-id.query'
import { CrystalProfileModel } from '../../domain/model/crystal-profile.model'
import { TryCatchLogger } from '../../../../shared/domain/decorator/try-catch-logger.decorator'
import { LogFeature } from '../../../../shared/logger.service'

@Injectable()
export class CrystalEstimatedService {
  constructor(private readonly queryBus: QueryBus) {}

  @TryCatchLogger({
    feature: LogFeature.CRYSTAL_PROFILE_GENERATION,
    message: 'Failed to estimated crystal profile generation',
  })
  async getEstimatedProfile(
    organizationId: string,
    getProfileDto: GetProfileDto
  ): Promise<EstimatedProfileObject> {
    const contactModel = await this.queryBus.execute(
      new FindContactQuery(getProfileDto.contactId)
    )

    if (!contactModel.linkedinUrlProfileOrEnrichment) {
      throw new ConflictException('Linkedin Url not accepted')
    }

    const existingProfilesModel: CrystalProfileModel[] =
      await this.queryBus.execute(new FindProfilesQuery(contactModel))

    if (existingProfilesModel.length > 0) {
      const existingProfilesModelIds = existingProfilesModel.map(
        existingProfileModel => existingProfileModel.id
      )

      const creditTransactions: CreditTransactionModel[] =
        await this.queryBus.execute(
          new FindTransactionsByOrganizationAndResourcesIdQuery(
            organizationId,
            existingProfilesModelIds
          )
        )

      const existingCreditTransactionForCrystalProfile =
        creditTransactions.length > 0

      const estimatedProfileObject = new EstimatedProfileObject()

      estimatedProfileObject.available =
        !!existingCreditTransactionForCrystalProfile
      estimatedProfileObject.cost = existingCreditTransactionForCrystalProfile
        ? 0
        : PERSONALITY_COST

      return estimatedProfileObject
    }

    const estimatedProfileObject = new EstimatedProfileObject()

    estimatedProfileObject.available = false
    estimatedProfileObject.cost = PERSONALITY_COST

    return estimatedProfileObject
  }
}
