import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs'
import { GetEstimatedProfilesQuery } from './get-estimated-profiles.query'
import { EstimatedProfileObject } from '../../../../../../ui/api/integration/crystal/object/estimated-profile.object'
import { CrystalEstimatedService } from '../../service/crystal-estimated.service'

@QueryHandler(GetEstimatedProfilesQuery)
export class GetEstimatedProfilesHandler
  implements IQueryHandler<GetEstimatedProfilesQuery>
{
  constructor(
    private readonly crystalEstimatedProfileService: CrystalEstimatedService
  ) {}

  execute({
    organizationId,
    getProfileDto,
  }: GetEstimatedProfilesQuery): Promise<EstimatedProfileObject> {
    return this.crystalEstimatedProfileService.getEstimatedProfile(
      organizationId,
      getProfileDto
    )
  }
}
