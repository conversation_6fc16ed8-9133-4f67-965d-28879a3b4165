import { AutoMap } from '@automapper/classes'
import { ModelPermission } from '../../../../shared/domain/model-permission.interface'
import { UserModel } from '../../../../shared/domain/model/user.model'
import {
  ADMIN_HIERARCHY,
  USER_HIERARCHY,
} from '../../../../shared/domain/organization-role.enum'
import { AuthorizationModel } from '../../../common/domain/model/authorization.model'
import { RingoverPermission } from '../enums/ringover-permission.enum'

export class RingoverAuthorizationModel
  extends AuthorizationModel
  implements ModelPermission
{
  @AutoMap()
  organizationId: string

  @AutoMap()
  webhookToken?: string

  @AutoMap()
  createdBy: UserModel

  getPermission?(): { [key: string]: string[] } {
    return {
      [RingoverPermission.AUTH]: ADMIN_HIERARCHY,
      [RingoverPermission.VIEW]: USER_HIERARCHY,
    }
  }
}
