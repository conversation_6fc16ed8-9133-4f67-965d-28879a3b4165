import { AutoMap } from '@automapper/classes'
import { AuthorizationModel } from '../../../common/domain/model/authorization.model'
import { UserModel } from '../../../../shared/domain/model/user.model'
import { Exclude, Expose } from 'class-transformer'
import { EXPOSE_GROUP } from '../../../../shared/globals/expose-group.enum'
import { MailerAuthorizationInterface } from '../../../common/domain/interface/mailer-authorization.interface'

export class OutlookAuthorizationModel
  extends AuthorizationModel
  implements MailerAuthorizationInterface
{
  @AutoMap()
  @Expose({ groups: [EXPOSE_GROUP.PUBLIC] })
  organizationId: string

  @AutoMap()
  @Exclude()
  refreshToken: string

  @AutoMap()
  @Expose({ groups: [EXPOSE_GROUP.PUBLIC] })
  email: string

  @AutoMap()
  @Expose({ groups: [EXPOSE_GROUP.PUBLIC] })
  user: UserModel

  @AutoMap()
  @Exclude()
  homeAccountId: string | null

  @AutoMap()
  @Expose({ groups: [EXPOSE_GROUP.PUBLIC] })
  lastHistoryId: string

  @AutoMap()
  @Exclude()
  webhookId: string

  @AutoMap()
  @Exclude()
  webhookExpiration: Date

  @AutoMap(() => [String])
  @Expose({ groups: [EXPOSE_GROUP.PUBLIC] })
  alias: string[]

  @AutoMap()
  @Expose({ groups: [EXPOSE_GROUP.PUBLIC] })
  signature: string

  @AutoMap()
  @Expose({ groups: [EXPOSE_GROUP.PUBLIC] })
  senderName: string

  @AutoMap()
  @Expose({ groups: [EXPOSE_GROUP.PUBLIC] })
  defaultSenderName: string

  @AutoMap()
  account?(): string {
    return this.email
  }
}
