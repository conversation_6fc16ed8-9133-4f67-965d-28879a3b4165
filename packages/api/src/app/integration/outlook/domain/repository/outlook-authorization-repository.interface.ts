import { DeleteResult } from 'typeorm'
import { OAuthAuthorizationStatus } from '../../../common/domain/oauth-authorization-status.enum'
import { OutlookAuthorizationModel } from '../model/outlook-authorization.model'
import { FindManyOptions } from 'typeorm/find-options/FindManyOptions'
import { OutlookAuthorizationEntity } from '../../infrastructure/entity/outlook-authorization.entity'

export const OUTLOOK_AUTHORIZATION_REPOSITORY_INTERFACE =
  'OUTLOOK_AUTHORIZATION_REPOSITORY_INTERFACE'

export interface OutlookAuthorizationRepositoryInterface {
  create(
    outlookAuthorizationModel: OutlookAuthorizationModel
  ): Promise<OutlookAuthorizationModel>
  update(
    outlookAuthorizationModel: OutlookAuthorizationModel
  ): Promise<OutlookAuthorizationModel>
  findGrantedByEmail(email: string): Promise<OutlookAuthorizationModel>
  findGrantedByOrganizationAndDomain(
    organizationId: string,
    domainName: string
  ): Promise<OutlookAuthorizationModel[]>
  findByUserId(userId: string): Promise<OutlookAuthorizationModel>
  findGrantedByIdAndUserId(
    id: string,
    userId: string
  ): Promise<OutlookAuthorizationModel>
  findByUserIdAndStatus(
    userId: string,
    status: OAuthAuthorizationStatus
  ): Promise<OutlookAuthorizationModel>
  findByState(state: string): Promise<OutlookAuthorizationModel>
  findGrantedByState(state: string): Promise<OutlookAuthorizationModel>
  findAllGranted(): Promise<OutlookAuthorizationModel[]>
  delete(outlookAuthorizationModelId: string): Promise<DeleteResult>
  findExpiringWebhooks(inDays: number): Promise<OutlookAuthorizationModel[]>
  count(options?: FindManyOptions<OutlookAuthorizationEntity>): Promise<number>
}
