import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'
import { UpdateAuthorizationCommand } from './update-authorization.command'
import {
  INTEGRATION_AUTHORIZATION_REPOSITORY_INTERFACE,
  IntegrationAuthorizationRepositoryInterface,
} from '../../../domain/repository/integration-authorization-repository.interface'
import { IntegrationAuthorizationModel } from '../../../domain/model/integration-authorization.model'

@CommandHandler(UpdateAuthorizationCommand)
export class UpdateAuthorizationHandler
  implements ICommandHandler<UpdateAuthorizationCommand>
{
  constructor(
    @Inject(INTEGRATION_AUTHORIZATION_REPOSITORY_INTERFACE)
    private integrationAuthorizationRepository: IntegrationAuthorizationRepositoryInterface
  ) {}

  async execute(
    updateAuthorizationCommand: UpdateAuthorizationCommand
  ): Promise<IntegrationAuthorizationModel> {
    return this.integrationAuthorizationRepository.update(
      updateAuthorizationCommand.integrationAuthorizationModel
    )
  }
}
