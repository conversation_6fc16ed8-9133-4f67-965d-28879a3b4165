import { Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { SequenceEvents } from '../../../../../shared/domain/event/event.enum'
import { CaptainDataJobService } from '../../service/captain-data-job.service'

@Injectable()
export class SequenceContactDeletedListener {
  constructor(private readonly captainDataJobService: CaptainDataJobService) {}

  @OnEvent(SequenceEvents.SEQUENCE_CONTACT_DELETED)
  async handleSequenceContactDeletedEvent(event: {
    sequenceContactIds: string[]
  }) {
    if (event.sequenceContactIds.length) {
      await this.captainDataJobService.cancelPendingJobBySequenceIdAndSequenceContactIds(
        event.sequenceContactIds,
        true
      )
    }
  }
}
