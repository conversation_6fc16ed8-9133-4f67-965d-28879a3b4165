import { Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { SequenceEvents } from '../../../../../shared/domain/event/event.enum'
import { CaptainDataJobService } from '../../service/captain-data-job.service'
import { SequenceContactUnsubscribedEvent } from '../../../../../shared/domain/event/sequence/sequence-contact-unsubscribed.event'

@Injectable()
export class SequenceContactUnsubscribedListener {
  constructor(private readonly captainDataJobService: CaptainDataJobService) {}

  @OnEvent(SequenceEvents.SEQUENCE_CONTACT_UNSUBSCRIBED)
  async handleSequenceContactUnsubscribedEvent(
    event: SequenceContactUnsubscribedEvent
  ) {
    await this.captainDataJobService.cancelPendingJobBySequenceContactId(
      event.sequenceContactId
    )
  }
}
