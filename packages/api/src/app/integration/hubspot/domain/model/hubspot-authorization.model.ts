import { AutoMap } from '@automapper/classes'
import { UserModel } from '../../../../shared/domain/model/user.model'
import { ModelPermission } from '../../../../shared/domain/model-permission.interface'
import { HubspotPermission } from '../hubspot-permission.enum'
import {
  ADMIN_HIERARCHY,
  MANAGER_HIERARCHY,
  USER_HIERARCHY,
} from '../../../../shared/domain/organization-role.enum'
import { AuthorizationModel } from '../../../common/domain/model/authorization.model'
import { SearchQueryFilterItemObject } from '../../../../shared/domain/object/search-query-filter-item.object'
import { MappingHubspotObject } from '../object/mapping-hubspot.object'

export class HubspotAuthorizationModel
  extends AuthorizationModel
  implements ModelPermission
{
  @AutoMap()
  name: string

  @AutoMap()
  organizationId: string

  @AutoMap()
  refreshToken: string

  @AutoMap()
  createdBy: UserModel

  @AutoMap()
  isSynced: boolean

  @AutoMap()
  isSyncedAllContacts: boolean

  @AutoMap(() => [Object])
  exclusionRules: SearchQueryFilterItemObject[]

  @AutoMap(() => [String])
  push: string[]

  @AutoMap(() => MappingHubspotObject)
  mapping: MappingHubspotObject

  @AutoMap(() => [Number])
  contactListIds: number[]

  @AutoMap(() => [Number])
  contactListIdsLegacy: number[]

  @AutoMap()
  portalId: string

  @AutoMap()
  uiDomain: string

  getPermission?(): { [key: string]: string[] } {
    return {
      [HubspotPermission.AUTH]: ADMIN_HIERARCHY,
      [HubspotPermission.MANAGE]: MANAGER_HIERARCHY,
      [HubspotPermission.VIEW]: USER_HIERARCHY,
    }
  }
}
