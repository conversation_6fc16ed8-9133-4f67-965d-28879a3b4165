import { HubspotContactListModel } from './hubspot-contact-list.model'
import { AutoMap } from '@automapper/classes'
import { UserModel } from '../../../../shared/domain/model/user.model'

export class HubspotSyncedContactListModel extends HubspotContactListModel {
  @AutoMap()
  id: string

  @AutoMap()
  lastSyncedAt: Date

  @AutoMap()
  lastSyncExternalContactId: number

  @AutoMap()
  isSyncing: boolean

  @AutoMap(() => UserModel)
  createdBy: UserModel

  @AutoMap()
  organizationId: string

  @AutoMap()
  createdAt: Date

  @AutoMap()
  updatedAt: Date
}
