import { DeleteResult } from 'typeorm'
import { GmailAuthorizationModel } from './gmail-authorization.model'
import { OAuthAuthorizationStatus } from '../../../common/domain/oauth-authorization-status.enum'

export const GMAIL_AUTHORIZATION_REPOSITORY_INTERFACE =
  'GMAIL_AUTHORIZATION_REPOSITORY_INTERFACE'

export interface GmailAuthorizationRepositoryInterface {
  create(
    gmailAuthorizationModel: GmailAuthorizationModel
  ): Promise<GmailAuthorizationModel>
  update(
    gmailAuthorizationModel: GmailAuthorizationModel
  ): Promise<GmailAuthorizationModel>
  findGrantedByEmail(email: string): Promise<GmailAuthorizationModel>
  findGrantedByOrganization(
    organizationId: string
  ): Promise<GmailAuthorizationModel[]>
  findGrantedByOrganizationAndDomain(
    organizationId: string,
    domainName: string
  ): Promise<GmailAuthorizationModel[]>
  findByUserId(userId: string): Promise<GmailAuthorizationModel>
  findGrantedByIdAndUserId(
    id: string,
    userId: string
  ): Promise<GmailAuthorizationModel>
  findByUserIdAndStatus(
    userId: string,
    status: OAuthAuthorizationStatus
  ): Promise<GmailAuthorizationModel>
  findByState(state: string): Promise<GmailAuthorizationModel>
  findGrantedByState(state: string): Promise<GmailAuthorizationModel>
  findAllGranted(): Promise<GmailAuthorizationModel[]>
  delete(gmailAuthorizationModelId: string): Promise<DeleteResult>
  findExpiringWebhooks(inDays: number): Promise<GmailAuthorizationModel[]>
}
