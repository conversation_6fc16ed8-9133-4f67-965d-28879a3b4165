import {
  ConflictException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common'
import { OAuth2Client } from 'google-auth-library'
import * as CryptoJS from 'crypto-js'
import {
  GMAIL_AUTHORIZATION_REPOSITORY_INTERFACE,
  GmailAuthorizationRepositoryInterface,
} from '../../domain/model/gmail-authorization-repository.interface'
import { GmailAuthorizationModel } from '../../domain/model/gmail-authorization.model'
import { CacheService } from '../../../../shared/infrastructure/service/cache.service'
import { OAuthAuthorizationStatus } from '../../../common/domain/oauth-authorization-status.enum'
import { EventBus } from '@nestjs/cqrs'
import { gmail_v1, google } from 'googleapis'
import { AuthGrantedEvent } from '../../domain/event/auth-granted.event'
import { ProviderService } from '../../../../mailer/application/service/provider.service'
import { GmailInvalidGrantException } from '../../domain/exception/gmail-invalid-grant.exception'
import { GmailInvalidClientException } from '../../domain/exception/gmail-invalid-client.exception'

const SCOPES = [
  'https://www.googleapis.com/auth/userinfo.email',
  'https://www.googleapis.com/auth/userinfo.profile',
  'https://www.googleapis.com/auth/gmail.send',
  'https://www.googleapis.com/auth/gmail.readonly',
]
const TOKEN_TTL = 3600

export const CACHE_TOKEN_PREFIX = 'gmail-authorization'

const GOOGLE_URL_GET_USER =
  'https://www.googleapis.com/oauth2/v1/userinfo?alt=json'

@Injectable()
export class GmailAuthorizationService {
  private readonly logger = new Logger(GmailAuthorizationService.name)

  private oauth2Client: OAuth2Client

  constructor(
    @Inject(GMAIL_AUTHORIZATION_REPOSITORY_INTERFACE)
    private readonly gmailAuthorizationRepository: GmailAuthorizationRepositoryInterface,
    private readonly cacheManager: CacheService,
    private readonly eventBus: EventBus,
    private readonly providerService: ProviderService
  ) {
    this.oauth2Client = new OAuth2Client({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      redirectUri: `${process.env.API_URL}/integrations/gmail/callback`,
    })
  }

  async authorize(
    gmailAuthorizationModel: GmailAuthorizationModel
  ): Promise<string> {
    // TODO : Replace by method
    const gmailAuthorizationExisting =
      await this.gmailAuthorizationRepository.findByUserIdAndStatus(
        gmailAuthorizationModel.user.id,
        OAuthAuthorizationStatus.GRANTED
      )

    if (gmailAuthorizationExisting !== null) {
      throw new ConflictException(
        'Gmail authorization already exists',
        'already_authorized'
      )
    }

    const provider = await this.providerService.getConnected(
      gmailAuthorizationModel.user.id
    )
    if (null !== provider) {
      throw new ConflictException(
        'One integration authorization already exists',
        'already_authorized'
      )
    }

    await this.gmailAuthorizationRepository.create(gmailAuthorizationModel)

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      prompt: 'consent',
      // If you only need one scope you can pass it as a string
      scope: SCOPES,
      state: gmailAuthorizationModel.state,
    })
  }

  async createToken(
    gmailAuthorizationModel: GmailAuthorizationModel,
    code: string
  ): Promise<boolean> {
    try {
      const authResponse = await this.oauth2Client.getToken(code)

      if (authResponse.tokens !== null) {
        const { access_token: accessToken, refresh_token: refreshToken } =
          authResponse.tokens

        const encryptedByte = CryptoJS.AES.encrypt(
          refreshToken,
          process.env.ENCRYPTION_KEY
        ).toString()

        const email = await this.fetchUserEmail(accessToken)
        gmailAuthorizationModel.email = email

        // check if the account isn't already linked to another user
        const gmailAuthorizationExisting =
          await this.gmailAuthorizationRepository.findGrantedByEmail(email)
        if (gmailAuthorizationExisting !== null) {
          throw new ConflictException(
            'Gmail account already linked to another user',
            'gmail_account_already_linked'
          )
        }
        gmailAuthorizationModel.status = OAuthAuthorizationStatus.GRANTED
        gmailAuthorizationModel.refreshToken = encryptedByte
        const userInfo = await this.getUserInfo(accessToken)
        gmailAuthorizationModel.defaultSenderName = userInfo.name
        await this.gmailAuthorizationRepository.update(gmailAuthorizationModel)
        await this.cacheManager.set(
          `${CACHE_TOKEN_PREFIX}-${gmailAuthorizationModel.user.id}`,
          accessToken,
          TOKEN_TTL
        )
        this.eventBus.publish(new AuthGrantedEvent(gmailAuthorizationModel))

        return true
      }
      return false
    } catch (e: any) {
      if (e instanceof ConflictException) {
        throw e
      }
      this.logger.error(
        e,
        `Error while creating token for gmailAuthorization ${gmailAuthorizationModel.id}`
      )
      return false
    }
  }

  async createWebhook(gmailAuthorizationModel: GmailAuthorizationModel) {
    this.logger.log(
      `[Expiring Webhook] Gmail - ${gmailAuthorizationModel.id} renewed - expiration date: ${gmailAuthorizationModel.webhookExpiration}`
    )

    const gmail = await this.getGmailClient(gmailAuthorizationModel.user.id)

    if (!gmail) {
      await this.desynchronize(gmailAuthorizationModel)

      const error = new GmailInvalidClientException()
      this.logger.error(
        error,
        `Error while creating webhook for gmailAuthorization ${gmailAuthorizationModel.id}}`
      )
      throw error
    }

    const webhookResponse = await gmail.users.watch({
      userId: 'me',
      requestBody: {
        labelIds: ['INBOX', 'SENT'],
        topicName: `projects/${process.env.GOOGLE_PROJECT_NAME}/topics/gmail-mailbox-update`,
      },
    })

    gmailAuthorizationModel.webhookExpiration = new Date(
      +webhookResponse.data.expiration
    )

    await this.gmailAuthorizationRepository.update(gmailAuthorizationModel)
  }

  async disconnect(userId: string): Promise<boolean> {
    const gmailAuthorizationModel =
      await this.gmailAuthorizationRepository.findByUserIdAndStatus(
        userId,
        OAuthAuthorizationStatus.GRANTED
      )
    if (gmailAuthorizationModel === null) {
      throw new ForbiddenException()
    }
    const accessToken = await this.fetchToken(userId)
    const oAuth2Client = new OAuth2Client()
    oAuth2Client.setCredentials({
      access_token: accessToken,
    })
    try {
      await oAuth2Client.revokeToken(accessToken)
      // eslint-disable-next-line no-empty
    } catch (e) {}
    await this.gmailAuthorizationRepository.delete(gmailAuthorizationModel.id)
    await this.cacheManager.delete(`${CACHE_TOKEN_PREFIX}-${userId}`)
    return true
  }

  async getGmailClient(userId: string): Promise<gmail_v1.Gmail> {
    const accessToken = await this.fetchToken(userId)

    if (accessToken) {
      const oAuth2Client = new google.auth.OAuth2()
      oAuth2Client.setCredentials({
        access_token: accessToken,
      })

      return google.gmail({ version: 'v1', auth: oAuth2Client })
    }

    return null
  }

  async refreshToken(
    gmailAuthorizationModel: GmailAuthorizationModel
  ): Promise<string | null> {
    if (gmailAuthorizationModel.refreshToken === null) {
      return null
    }

    const decryptedRefreshToken = CryptoJS.AES.decrypt(
      gmailAuthorizationModel.refreshToken,
      process.env.ENCRYPTION_KEY
    ).toString(CryptoJS.enc.Utf8)

    try {
      const refreshOAuthClient = new OAuth2Client({
        clientId: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        redirectUri: `${process.env.API_URL}/integrations/gmail/callback`,
      })
      refreshOAuthClient.setCredentials({
        refresh_token: decryptedRefreshToken,
      })
      const tokenResponse = await refreshOAuthClient.refreshAccessToken()

      if (tokenResponse.credentials !== null) {
        await this.cacheManager.set(
          `${CACHE_TOKEN_PREFIX}-${gmailAuthorizationModel.user.id}`,
          tokenResponse.credentials.access_token,
          TOKEN_TTL
        )

        const refreshToken = tokenResponse.credentials.refresh_token

        if (refreshToken) {
          const encryptedByte = CryptoJS.AES.encrypt(
            refreshToken,
            process.env.ENCRYPTION_KEY
          ).toString()

          gmailAuthorizationModel.refreshToken = encryptedByte

          await this.gmailAuthorizationRepository.update(
            gmailAuthorizationModel
          )
        }

        return tokenResponse.credentials.access_token
      }
      return null
    } catch (e) {
      this.logger.error(
        e,
        `Error while refreshing token for gmailAuthorization ${gmailAuthorizationModel.id}`
      )
      if (e.code === 400 && e.message === 'invalid_grant')
        throw new GmailInvalidGrantException()
      return null
    }
  }

  async fetchToken(userId: string): Promise<string> {
    let accessToken = await this.cacheManager.get(
      `${CACHE_TOKEN_PREFIX}-${userId}`
    )

    if (accessToken === null) {
      const gmailAuthorizationModel =
        await this.gmailAuthorizationRepository.findByUserIdAndStatus(
          userId,
          OAuthAuthorizationStatus.GRANTED
        )

      if (gmailAuthorizationModel === null) {
        throw new GmailInvalidClientException()
      }

      accessToken = await this.refreshToken(gmailAuthorizationModel)
    }

    return accessToken
  }

  async fetchUserEmail(accessToken: string): Promise<string> {
    const oAuth2Client = new OAuth2Client()
    oAuth2Client.setCredentials({
      access_token: accessToken,
    })
    const gmailClient = google.gmail({ version: 'v1', auth: oAuth2Client })
    const users = await gmailClient.users.getProfile({ userId: 'me' })

    return users.data.emailAddress
  }

  async getUserInfo(accessToken: string): Promise<any> {
    const url = GOOGLE_URL_GET_USER
    const headers = {
      Authorization: `Bearer ${accessToken}`,
    }

    try {
      const response = await fetch(url, { headers })
      if (response.ok) {
        return await response.json()
      } else {
        throw new Error(`Error while fetching user info : ${response.status}`)
      }
    } catch (error) {
      throw new Error(`Error while fetching user info : ${error}`)
    }
  }

  async desynchronize(gmailAuthorizationModel: GmailAuthorizationModel) {
    gmailAuthorizationModel.status = OAuthAuthorizationStatus.DESYNCHRONIZED
    await this.gmailAuthorizationRepository.update(gmailAuthorizationModel)
  }
}
