import {
  OnQ<PERSON><PERSON><PERSON><PERSON>,
  OnQueue<PERSON>ompleted,
  OnQueue<PERSON>rror,
  OnQueueFailed,
  Process,
  Processor,
} from '@nestjs/bull'
import { Injectable, Logger } from '@nestjs/common'
import { GMAIL_MESSAGE_QUEUE } from '../../domain/global'
import { Job } from 'bull'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { MailEvents } from '../../../../shared/domain/event/event.enum'
import { MessageStatus } from '../../../../mailer/domain/enum/message-status.enum'
import { MessageModel } from '../../../../mailer/domain/model/message.model'
import { MessageEvent } from '../../../../shared/domain/event/mail/message.event'
import { UpdateMessageCommand } from '../../../../mailer/application/command/update-message/update-message.command'
import { GmailMessageService } from '../service/gmail-message.service'
import { FindMessageQuery } from '../../../../mailer/application/query/find-message/find-message.query'
import { MailErrorEvent } from '../../../../shared/domain/event/mail/mail-error.event'
import { ConfigScheduleService } from '../../../../sequence/application/service/config-schedule.service'
import { SequenceStatus } from '../../../../sequence/domain/sequence-status.enum'
import { DeleteMessageCommand } from '../../../../mailer/application/command/delete-email/delete-message.command'
import { UpdateSequenceContactCommand } from '../../../../sequence/application/command/update-sequence-contact/update-sequence-contact.command'
import { FindSequenceContactByIdQuery } from '../../../../sequence/application/query/find-sequence-contact/find-sequence-contact-by-id.query'
import { SequenceContactModel } from '../../../../sequence/domain/model/sequence-contact.model'
import { DailyLimitService } from '../../../../mailer/application/service/daily-limit.service'
import { SequenceContactStatus } from '@getheroes/shared'
import { SequenceMessageService } from '../../../../shared/application/service/sequence-message.service'
import { EmailJobTrackingService } from '../../../../mailer/infrastructure/service/email-job-tracking.service'

@Injectable()
@Processor(GMAIL_MESSAGE_QUEUE)
export class GmailMessageConsumer {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
    private readonly eventEmitter: EventEmitter2,
    private readonly gmailMessageService: GmailMessageService,
    private readonly configScheduleService: ConfigScheduleService,
    private readonly dailyLimitService: DailyLimitService,
    private readonly sequenceMessageService: SequenceMessageService,
    private readonly emailJobTrackingService: EmailJobTrackingService
  ) {}

  private readonly logger = new Logger(GmailMessageConsumer.name)

  @Process()
  async sendMessage(
    job: Job<{
      isReply: boolean
      messageModel: MessageModel
      addUnsubscribeLink: boolean
      addSignature: boolean
      messageToReplyId?: string
      responseMessageId?: string
    }>
  ): Promise<void> {
    const messageToReplyId = job.data.messageToReplyId
    const responseMessageId = job.data.responseMessageId
    const isReply: boolean =
      (job.data.isReply && !!messageToReplyId && !!responseMessageId) ?? false
    const messageModel: MessageModel = job.data.messageModel

    const addUnsubscribeLink: boolean = job.data.addUnsubscribeLink ?? false
    const addSignature: boolean = job.data.addSignature ?? true

    if (!messageModel) {
      this.logger.error(`Message ${responseMessageId} not found`)
      return
    }

    if (messageModel && MessageStatus.TO_SEND !== messageModel.status) {
      return
    }

    const isSentFromSequence = messageModel.sequenceContactId
    if (isSentFromSequence) {
      const isVerified = await this.verifySequencePreconditions(messageModel)
      if (!isVerified) {
        return
      }
      await this.sequenceMessageService.updateMessageContentForSequence(
        messageModel
      )
    }

    try {
      const sendMessage: MessageModel = isReply
        ? await this.gmailMessageService.sendReply(
            messageToReplyId,
            messageModel,
            addSignature,
            addUnsubscribeLink
          )
        : await this.gmailMessageService.sendMessage(
            messageModel,
            addUnsubscribeLink,
            addSignature
          )
      const messageEvent = new MessageEvent(sendMessage)
      messageEvent.messageModel = sendMessage
      messageEvent.messageId = sendMessage.id
      messageEvent.provider = sendMessage.provider
      messageEvent.isReply = isReply

      this.eventEmitter.emit(MailEvents.MAIL_SENT, messageEvent)
    } catch (error) {
      if (this.gmailMessageService.isGmailAuthorizationInvalid(error)) {
        const messageId = job.data.isReply
          ? job.data.responseMessageId
          : job.data.messageModel.id
        await this.handleError(job.id.toString(), messageId, error)
        return
      }
      throw error
    }
  }

  @OnQueueActive()
  async onActive(job: Job) {
    await this.emailJobTrackingService.updateToActiveByInternalJobId(
      job.id.toString()
    )
  }

  @OnQueueCompleted()
  async onCompleted(job: Job) {
    await this.emailJobTrackingService.updateToDoneByInternalJobId(
      job.id.toString()
    )
  }

  @OnQueueFailed()
  async onFailed(job: Job, error: Error) {
    if (job.attemptsMade < job.opts.attempts) {
      await job.log(
        `Job ${job.id} failed but will be retried (attempt ${job.attemptsMade + 1}/${job.opts.attempts})`
      )
      return
    }

    const messageId: string = job.data.isReply
      ? job.data.responseMessageId
      : job.data.messageModel.id

    await this.handleError(job.id.toString(), messageId, error)
  }

  @OnQueueError()
  onError(error: Error) {
    this.logger.error(
      error,
      'Unexpected error on queue %s',
      GMAIL_MESSAGE_QUEUE
    )
  }

  private async handleError(jobId: string, messageId: string, error: Error) {
    this.logger.error(
      error,
      `Error while processing gmail message ${messageId}`
    )

    await this.emailJobTrackingService.updateToFailedByInternalJobId(jobId)

    const messageModel = await this.queryBus.execute<
      FindMessageQuery,
      MessageModel
    >(new FindMessageQuery(messageId))

    if (!messageModel) return

    messageModel.status = MessageStatus.FAILED
    await this.commandBus.execute(new UpdateMessageCommand(messageModel))

    const event = new MailErrorEvent()
    event.organizationId = messageModel.organizationId
    event.sequenceContactId = messageModel.sequenceContactId
    event.provider = messageModel.provider
    event.messageId = messageModel.id
    event.from = messageModel.from
    event.contactId = messageModel.contactId
    event.error = error

    this.eventEmitter.emit(MailEvents.MAIL_ERROR, event)
  }

  private async verifySequencePreconditions(
    messageModel: MessageModel
  ): Promise<boolean> {
    const sequenceContactModel = await this.queryBus.execute<
      FindSequenceContactByIdQuery,
      SequenceContactModel
    >(new FindSequenceContactByIdQuery(messageModel.sequenceContactId))

    if (!sequenceContactModel?.sequence) {
      // Sequence contact or sequence don't exist anymore
      await this.commandBus.execute(new DeleteMessageCommand(messageModel.id))
      return false
    }

    const sequenceModel = sequenceContactModel.sequence
    if (sequenceModel.status !== SequenceStatus.IN_PROGRESS) {
      // Sequence is paused
      await this.commandBus.execute(new DeleteMessageCommand(messageModel.id))
      await this.commandBus.execute(
        new UpdateSequenceContactCommand(sequenceContactModel.id, {
          status: SequenceContactStatus.RUNNING,
        })
      )
      return false
    }

    const isAvailableToExecute =
      this.configScheduleService.isAvailableToExecute(
        sequenceModel.scheduleDays,
        sequenceModel.scheduleTimeStart,
        sequenceModel.scheduleTimeEnd,
        sequenceModel.scheduleTimezone
      )
    if (!isAvailableToExecute) {
      // Out of sequence schedule
      await this.commandBus.execute(new DeleteMessageCommand(messageModel.id))
      await this.commandBus.execute(
        new UpdateSequenceContactCommand(sequenceContactModel.id, {
          status: SequenceContactStatus.RUNNING,
        })
      )

      return false
    }

    const remainingEmailCounter =
      await this.dailyLimitService.remainingEmailCounter(
        messageModel.from,
        messageModel.organizationId
      )
    if (remainingEmailCounter <= 0) {
      await this.commandBus.execute(new DeleteMessageCommand(messageModel.id))
      await this.commandBus.execute(
        new UpdateSequenceContactCommand(sequenceContactModel.id, {
          status: SequenceContactStatus.RUNNING,
        })
      )
      return false
    }
    return true
  }
}
