import { Injectable } from '@nestjs/common'

import { ConfigService } from '@nestjs/config'

import { FlatfileClient } from '@flatfile/api'
import * as Flatfile from '@flatfile/api/api'
import { FlatfileConfig } from '../../../../../config/flatfile.config'
import { streamToString } from '../../../../lead-import/utils/streamToString'
import { LogFeature, LoggerService } from '../../../../shared/logger.service'
import { FileServiceInterface } from './file-service.interface'

@Injectable()
export class FileService implements FileServiceInterface {
  private readonly flatfileConfig: FlatfileConfig
  private readonly flatfileClient: FlatfileClient

  constructor(private readonly configService: ConfigService) {
    this.flatfileConfig = this.configService.get<FlatfileConfig>('flatfile')
    this.flatfileClient = new FlatfileClient({
      token: this.flatfileConfig.apiKey,
    })
  }

  private readonly logger = new LoggerService({
    feature: LogFeature.LEAD_EXPORT,
    context: FileService.name,
  })

  /**
   *
   * @param spaceId
   */
  public async listFiles(spaceId: string): Promise<Flatfile.File_[]> {
    try {
      const files = await this.flatfileClient.files.list({ spaceId })

      if (!files.data || files.data.length === 0) {
        this.logger.warn({
          message: 'No files found in the space',
          data: { spaceId },
        })
      }

      return files.data
    } catch (error) {
      this.logger.error({
        error,
        message: 'Failed to list files from Flatfile',
        data: { spaceId },
      })
      throw error
    }
  }

  /**
   *
   * @param fileId
   */
  public async downloadFileInSpace(fileId: string): Promise<string> {
    try {
      const fileDataStream = await this.flatfileClient.files.download(fileId)

      return streamToString(fileDataStream)
    } catch (error) {
      this.logger.error({
        error,
        message: 'Failed to download data of file from Flatfile',
        data: { fileId },
      })
      throw error
    }
  }

  public async getSheetColumns(
    spaceId: string,
    sheetId: string
  ): Promise<string[]> {
    try {
      const files = await this.flatfileClient.workbooks.list({
        spaceId,
      })

      // Find the file labeled as "file" (the source file)
      const sourceFile = files.data.find(file => file.labels.includes('file'))

      // Get the first (and likely only) sheet from that file
      const sourceSheet = sourceFile?.sheets[0]

      // Extract column keys from the sheet config fields
      const originalCsvColumns =
        sourceSheet?.config?.fields?.map(field => field.key) ?? []

      this.logger.log({
        message: 'originalCsvColumns',
        data: { files: originalCsvColumns },
      })

      return originalCsvColumns
    } catch (error) {
      this.logger.error({
        error,
        message: 'Failed to retrieve original file column headers',
        data: { sheetId },
      })
      throw error
    }
  }

  public async getMapping(spaceId: string): Promise<Flatfile.ProgramsResponse> {
    const space = await this.flatfileClient.spaces.get(spaceId)

    return this.flatfileClient.mapping.listMappingPrograms({
      pageSize: 1,
      pageNumber: 1,
      environmentId: space.data.environmentId,
    })
  }

  public async getColumnsMapping(
    spaceId: string
  ): Promise<Record<string, string>> {
    const space = await this.flatfileClient.spaces.get(spaceId)
    const columnsMapped = {}

    const mappings = await this.flatfileClient.mapping.listMappingPrograms({
      pageSize: 1,
      pageNumber: 1,
      environmentId: space.data.environmentId,
    })

    // Create a mapping object: destinationField -> sourceField
    if (mappings.data[0]?.rules) {
      mappings.data[0].rules
        .filter(rule => rule.createdBy !== 'ai')
        .forEach(rule => {
          if (rule.config.destinationField && rule.config.sourceField) {
            columnsMapped[rule.config.destinationField] =
              rule.config.sourceField
          }
        })
    }

    return columnsMapped
  }
}
