import { EnrichmentType } from '@getheroes/shared'
import { ConfigService } from '@nestjs/config'
import { Test, TestingModule } from '@nestjs/testing'
import { PersonResponse } from 'peopledatalabs'
import { PersonEnrichmentParams } from 'peopledatalabs/dist/types/enrichment-types'
import { ContactModel } from '../../../../domain/model/contact.model'
import { DataProviderPdlService } from './data-provider-pdl.service'
import { PdlEmailEnrichmentStrategy } from './pdl-email-enrichment.strategy'
import { PdlPhoneAndEmailStrategy } from './pdl-phone-and-email.strategy'
import { PdlPhoneEnrichmentStrategy } from './pdl-phone-enrichment.strategy'

let pdlEnrichmentMock = jest.fn()

jest.mock('peopledatalabs', () => {
  return function () {
    return {
      person: {
        enrichment: pdlEnrichmentMock,
      },
    }
  }
})

describe('Enrich with peopledatalabs', () => {
  let service: DataProviderPdlService

  beforeEach(async () => {
    pdlEnrichmentMock = jest.fn().mockReturnValue(
      Promise.resolve({
        status: 200,
        data: {
          full_name: 'momolcageot',
          linkedin_url: 'https://linkedin.com/momo',
          gender: 'male',
          job_title: 'Software Engineer',
          job_company_name: 'Test Company',
          job_company_website: 'https://testcompany.com',
          location_name: 'City, State',
          phone_numbers: ['+16132978347'],
          work_email: '<EMAIL>',
        },
      })
    )
    const mockConfigService = {
      get: jest.fn(key => {
        const config = {
          pdl: {
            pdlPhoneApiKey: 'test-phone-api-key',
            pdlEmailApiKey: 'test-email-api-key',
            limitPerPage: 100,
          },
        }
        return config[key]
      }),
    }
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DataProviderPdlService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile()

    service = module.get<DataProviderPdlService>(DataProviderPdlService)
  })

  describe('when performing enrichment', () => {
    it('should query the PDL API with required "work_mail" given an enrichment of type EMAIL', async () => {
      const enrichmentType = EnrichmentType.EMAIL
      const personEnrichmentQueryParams: PersonEnrichmentParams = {
        name: 'Jane Doe',
        email: '<EMAIL>',
      }

      const result = service.getEnrichmentExecutor(enrichmentType)
      await result.enrich(personEnrichmentQueryParams)
      expect(result).toBeInstanceOf(PdlEmailEnrichmentStrategy)
      expect(pdlEnrichmentMock).toHaveBeenCalledWith(
        expect.objectContaining({ required: 'work_email' })
      )
    })

    it('should query the PDL API with required "phone_numbers" given an enrichment of type PHONE', async () => {
      const enrichmentType = EnrichmentType.PHONE
      const personEnrichmentQueryParams: PersonEnrichmentParams = {
        name: 'Jane Doe',
        email: '<EMAIL>',
      }
      const result = service.getEnrichmentExecutor(enrichmentType)
      expect(result).toBeInstanceOf(PdlPhoneEnrichmentStrategy)
      await result.enrich(personEnrichmentQueryParams)
      expect(pdlEnrichmentMock).toHaveBeenCalledWith(
        expect.objectContaining({ required: 'phone_numbers' })
      )
    })

    it('should query the PDL API with required "phone_numbers and work_email" given an enrichment of type PHONE+EMAIL', async () => {
      const enrichmentType = EnrichmentType.ADVANCED
      const personEnrichmentQueryParams: PersonEnrichmentParams = {
        name: 'Jane Doe',
        email: '<EMAIL>',
      }
      const result = service.getEnrichmentExecutor(enrichmentType)
      expect(result).toBeInstanceOf(PdlPhoneAndEmailStrategy)
      await result.enrich(personEnrichmentQueryParams)
      expect(pdlEnrichmentMock).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({ required: 'work_email' })
      )
      expect(pdlEnrichmentMock).toHaveBeenNthCalledWith(
        2,
        expect.objectContaining({ required: 'phone_numbers' })
      )
    })

    it('should throw an error given an invalid enrichment type', () => {
      expect(() =>
        service.getEnrichmentExecutor('INVALID' as EnrichmentType)
      ).toThrow('Invalid enrichment type')
    })
  })

  describe('when transforming a PersonPreviewResponse to ContactModel', () => {
    it('should correctly transform given a valid PersonResponse', () => {
      const personResponse: PersonResponse = {
        full_name: 'John Doe',
        linkedin_url: 'https://linkedin.com/johndoe',
        gender: 'male',
        job_title: 'Software Engineer',
        job_company_name: 'Test Company',
        job_company_website: 'https://testcompany.com',
        location_name: 'City, State',
        location_country: 'Country',
        location_street_address: 'Street Address',
        location_postal_code: '12345',
        location_region: 'Region',
        id: '12345',
      }

      const result = service.personPreviewResponseToContactModel(personResponse)

      expect(result).toBeInstanceOf(ContactModel)
      expect(result.firstName).toBe('John')
      expect(result.lastName).toBe('Doe')
      expect(result.linkedinUrl).toBe('https://linkedin.com/johndoe')
      expect(result.gender).toBe(1)
      expect(result.title).toBe('Software Engineer')
      expect(result.company.name).toBe('Test Company')
      expect(result.company.website).toBe('https://testcompany.com')
      expect(result.company.city).toBe('City')
      expect(result.externalId).toBe('12345')
    })
  })

  describe('when transforming a PersonResponse to ContactModel', () => {
    it('should generate ContactModel with additional properties', () => {
      const personResponse: PersonResponse = {
        full_name: 'John Doe',
        linkedin_url: 'https://linkedin.com/johndoe',
        gender: 'male',
        job_title: 'Software Engineer',
        mobile_phone: '+1234567890',
        work_email: '<EMAIL>',
        location_name: 'City, State, Country',
        id: '12345',
      }

      const result = service.personResponseToContactModel(personResponse)

      expect(result).toBeInstanceOf(ContactModel)
      expect(result.phones).toContain('+1234567890')
      expect(result.emails).toContain('<EMAIL>')
    })

    it('should populate phones array when no mobile phone is provided but other phone numbers exist', () => {
      const personResponse: PersonResponse = {
        phone_numbers: ['+1234567890', '+0987654321'],
        full_name: 'John Doe',
        location_name: 'City, State',
      }

      const result = service.personResponseToContactModel(personResponse)

      expect(result.phones).toContain('+1234567890')
      expect(result.phones).toContain('+0987654321')
    })

    it('should return empty phones and undefined emails when no valid phone numbers or emails are provided', () => {
      const personResponse: PersonResponse = {
        full_name: 'John Doe',
        location_name: 'City, State',
      }

      const result = service.personResponseToContactModel(personResponse)

      expect(result.phones).toEqual([])
      expect(result.emails).toBe(undefined)
    })
  })
})
