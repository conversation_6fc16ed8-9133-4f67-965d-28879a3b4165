import { AutoMap } from '@automapper/classes'
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { UserEntity } from '../../../shared/infrastructure/entity/user.entity'
import { SearchQueryFilterItemObject } from '../../../shared/domain/object/search-query-filter-item.object'
import { OrganizationInterface } from '../../../shared/domain/organization.interface'
import { TimableEntity } from '../../../shared/infrastructure/entity/timable.entity'

@Entity('views')
@Index(['name', 'createdBy', 'organizationId', 'default'], { unique: true })
export class ViewEntity extends TimableEntity {
  @PrimaryGeneratedColumn('uuid')
  @AutoMap()
  id: string

  @Column('varchar')
  @AutoMap()
  name: string

  @Column({ type: 'jsonb', nullable: true })
  @AutoMap(() => SearchQueryFilterItemObject)
  filters: [SearchQueryFilterItemObject]

  @Column({ default: false })
  @AutoMap()
  default: boolean

  @ManyToOne('organizations')
  @JoinColumn({ name: 'organization_id' })
  organization: OrganizationInterface

  @AutoMap()
  @Column({ name: 'organization_id' })
  organizationId: string

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by_id' })
  @AutoMap(() => UserEntity)
  createdBy: UserEntity

  @AutoMap()
  @Column({ name: 'created_by_id' })
  createdById: string
}
