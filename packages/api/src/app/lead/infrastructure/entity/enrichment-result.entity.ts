import { AutoMap } from '@automapper/classes'
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { UserEntity } from '../../../shared/infrastructure/entity/user.entity'
import { TimableEntity } from '../../../shared/infrastructure/entity/timable.entity'
import { EnrichmentType } from '@getheroes/shared'
import { OrganizationInterface } from '../../../shared/domain/organization.interface'
import { EnrichmentEntity } from './enrichment.entity'

@Entity('enrichments_results')
@Index(['enrichmentId', 'contactId', 'companyId'], { unique: true })
@Index('IDX_NEWPHONEOREMAILFOUND_CREATEDBYID', [
  'hasNewEmail',
  'hasNewPhone',
  'createdBy.id',
])
export class EnrichmentResultEntity extends TimableEntity {
  @PrimaryGeneratedColumn('uuid')
  @AutoMap()
  id: string

  @ManyToOne('organizations')
  @JoinColumn({ name: 'organization_id' })
  organization: OrganizationInterface

  @AutoMap()
  @Column({ name: 'organization_id' })
  organizationId: string

  @ManyToOne(() => EnrichmentEntity)
  @JoinColumn({ name: 'enrichment_id' })
  @AutoMap(() => EnrichmentEntity)
  enrichment: EnrichmentEntity

  @AutoMap()
  @Column({ name: 'enrichment_id' })
  @Index()
  enrichmentId: string

  @Column('varchar', { default: EnrichmentType.GENERAL })
  @AutoMap(() => String)
  enrichmentType: EnrichmentType

  @Column({ type: 'varchar', nullable: true })
  @AutoMap(() => String)
  contactId: string

  @Column({ type: 'varchar', nullable: true })
  @AutoMap(() => String)
  companyId: string

  @Column({ type: 'jsonb', nullable: true })
  @AutoMap(() => Object)
  queryResult: object

  @Column({ type: 'varchar', nullable: true })
  @AutoMap(() => String)
  error: string

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by_id' })
  @AutoMap(() => UserEntity)
  createdBy: UserEntity

  @Column({ type: 'decimal', default: 0 })
  @Index()
  @AutoMap()
  creditUsed: number

  @Column({ type: 'boolean', default: false })
  @Index()
  @AutoMap()
  phoneFound: boolean

  @Column({ type: 'boolean', default: false })
  @Index()
  @AutoMap()
  emailFound: boolean

  @Column({ type: 'boolean', default: false })
  @AutoMap()
  hasNewEmail: boolean

  @Column({ type: 'boolean', default: false })
  @AutoMap()
  hasNewPhone: boolean

  @Column({ type: 'boolean', default: false })
  @Index()
  @AutoMap()
  success: boolean

  @Column({ type: 'boolean', default: false })
  @Index()
  @AutoMap()
  partialSuccess: boolean

  @Column({ type: 'boolean', default: false })
  @Index()
  @AutoMap()
  partialEmailSuccess: boolean

  @Column({ type: 'boolean', default: false })
  @Index()
  @AutoMap()
  partialPhoneSuccess: boolean

  @Column({ type: 'boolean', default: false })
  @Index()
  @AutoMap()
  failed: boolean
}
