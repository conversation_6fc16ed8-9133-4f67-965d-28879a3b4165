import { Mapper } from '@automapper/core'
import { InjectMapper } from '@automapper/nestjs'
import {
  EnrichmentStatus,
  EnrichmentType,
  LeadEngagementScoringMetadataKey,
  LeadEngagementScoringPriority,
  LeadSource,
  LeadStatus,
} from '@getheroes/shared'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import {
  Brackets,
  In,
  Repository,
  SelectQueryBuilder,
  UpdateResult,
} from 'typeorm'
import { FindManyOptions } from 'typeorm/find-options/FindManyOptions'
import { ReadStream } from 'typeorm/platform/PlatformTools'
import { MessageDirection } from '../../../mailer/domain/enum/message-direction.enum'
import { PaginationQueryObject } from '../../../shared/domain/object/pagination-query.object'
import { LeadImportIdOperation } from '../../domain/interface/contact.interface'
import {
  ContactRepositoryInterface,
  FindContactIdsByLeadImportIdQuery,
} from '../../domain/model/contact-repository.interface'
import { ContactModel } from '../../domain/model/contact.model'
import { CustomFieldModel } from '../../domain/model/custom-field.model'
import { ContactEntity } from '../entity/contact.entity'
@Injectable()
export class ContactRepository implements ContactRepositoryInterface {
  constructor(
    @InjectRepository(ContactEntity)
    private readonly entityRepository: Repository<ContactEntity>,
    @InjectMapper() private readonly mapper: Mapper
  ) {}

  async hasNewEnrichmentDataAvailable(
    organizationId: string,
    startDate?: Date
  ): Promise<boolean> {
    if (!organizationId) return false
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .where('c.organization_id = :organizationId', { organizationId })
      .andWhere('c.enrichment_new_data_available = :newDataAvailable', {
        newDataAvailable: true,
      })
      .andWhere(`c.available_in_workspace IS TRUE`)

    if (startDate) {
      queryBuilder.andWhere(
        'c.enrichment_new_data_available_updated_at >= :startDate',
        {
          startDate,
        }
      )
    }
    queryBuilder.limit(1)

    const count = await queryBuilder.getCount()
    return count > 0
  }

  async count(options?: FindManyOptions<ContactEntity>): Promise<number> {
    return this.entityRepository.count(options)
  }

  async create(contactModel: ContactModel): Promise<ContactModel> {
    const contactEntity = this.mapper.map(
      contactModel,
      ContactModel,
      ContactEntity
    )
    await this.entityRepository.save(contactEntity)
    return this.mapper.map(contactEntity, ContactEntity, ContactModel)
  }

  async update(contactModel: ContactModel): Promise<ContactModel> {
    const contactEntity = this.mapper.map(
      contactModel,
      ContactModel,
      ContactEntity
    )

    await this.entityRepository.save(contactEntity)
    return this.mapper.map(contactEntity, ContactEntity, ContactModel)
  }

  async deleteByIds(contactIds: string[]): Promise<void> {
    if (contactIds.length === 0) {
      return
    }
    await this.entityRepository.delete({ id: In(contactIds) })
  }

  async updateMany(
    contacts: Array<ContactModel>
  ): Promise<Array<ContactModel>> {
    if (contacts.length === 0) {
      return []
    }
    const updateContacts = await this.entityRepository.save(contacts)
    return this.mapper.mapArray(updateContacts, ContactEntity, ContactModel)
  }

  async updateLeadImportIds(
    contactsIds: string[],
    leadImportId: string,
    operation: LeadImportIdOperation
  ): Promise<void> {
    if (!contactsIds || contactsIds.length === 0 || !leadImportId) {
      return
    }

    if (operation === 'add') {
      await this.entityRepository
        .createQueryBuilder()
        .update()
        .set({
          leadImportIds: () => `array_append(lead_import_ids, :leadImportId)`,
        })
        .where('id IN (:...contactsIds)', { contactsIds })
        .andWhere('NOT lead_import_ids @> ARRAY[:leadImportId]::uuid[]', {
          leadImportId,
        })
        .execute()
    }

    if (operation === 'remove') {
      await this.entityRepository
        .createQueryBuilder()
        .update()
        .set({
          leadImportIds: () => `array_remove(lead_import_ids, :leadImportId)`,
        })
        .where('id IN (:...contactsIds)', { contactsIds })
        .andWhere('lead_import_ids @> ARRAY[:leadImportId]::uuid[]', {
          leadImportId,
        })
        .execute()
    }
  }

  async findById(
    id: string,
    withDeleted = false
  ): Promise<ContactModel | null> {
    if (!id) return null
    const contactEntity = await this.entityRepository.findOne({
      where: { id },
      relations: {
        company: true,
        assignUser: true,
        createdBy: true,
        archivedBy: true,
      },
      withDeleted: withDeleted,
    })
    return contactEntity
      ? this.mapper.map(contactEntity, ContactEntity, ContactModel)
      : null
  }

  async findByPhone(
    organizationId: string,
    phone: string,
    withDeleted = false
  ): Promise<ContactModel | null> {
    if (!organizationId || !phone) return null

    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .leftJoinAndSelect('c.assignUser', 'a')
      .where('c.organizationId = :organizationId', {
        organizationId: organizationId,
      })
      .andWhere(`c.availableInWorkspace IS TRUE`)
      .andWhere(`phones @> ARRAY[:phone]::varchar[]`, { phone })
      .orderBy(`CASE WHEN c.archivedAt IS NULL THEN 0 ELSE 1 END`, 'ASC')
      .addOrderBy('c.archivedAt', 'DESC')

    if (true === withDeleted) {
      queryBuilder.withDeleted()
    }

    const contactEntity = await queryBuilder.getOne()

    return contactEntity
      ? this.mapper.map(contactEntity, ContactEntity, ContactModel)
      : null
  }

  async findByIds(
    ids: string[],
    withDeleted = false,
    relations: {
      company?: boolean
      assignUser?: boolean
      createdBy?: boolean
      archivedBy?: boolean
      updatedBy?: boolean
    } = {
      company: true,
      assignUser: true,
      createdBy: true,
      archivedBy: true,
      updatedBy: true,
    }
  ): Promise<ContactModel[]> {
    if (ids.length === 0) return []
    const contactEntity = await this.entityRepository.find({
      where: { id: In(ids) },
      relations,
      withDeleted: withDeleted,
    })
    return this.mapper.mapArray(contactEntity, ContactEntity, ContactModel)
  }

  //TODO: do something better
  async findByIdsForHubspotSync(
    ids: string[],
    withDeleted = false
  ): Promise<ContactModel[]> {
    if (ids.length === 0) return []
    const contactEntity = await this.entityRepository.find({
      where: { id: In(ids), duplicateGroup: null },
      relations: {
        company: true,
        assignUser: true,
        createdBy: true,
      },
      withDeleted: withDeleted,
    })
    return this.mapper.mapArray(contactEntity, ContactEntity, ContactModel)
  }

  async findByOrganizationAndIds(
    organizationId: string,
    ids: string[],
    withDeleted = false
  ): Promise<ContactModel[]> {
    if (ids.length === 0) return []
    const contactEntity = await this.entityRepository.find({
      where: { id: In(ids), organizationId: organizationId },
      relations: {
        company: true,
        assignUser: true,
        createdBy: true,
        archivedBy: true,
        updatedBy: true,
      },
      withDeleted: withDeleted,
    })
    return this.mapper.mapArray(contactEntity, ContactEntity, ContactModel)
  }

  async findByOrganizationAndId(
    organizationId: string,
    id: string,
    withDeleted = false
  ): Promise<ContactModel | null> {
    const contactEntity = await this.entityRepository.findOne({
      where: { id, organizationId },
      relations: {
        company: true,
        assignUser: true,
        createdBy: true,
      },
      withDeleted: withDeleted,
    })
    return contactEntity
      ? this.mapper.map(contactEntity, ContactEntity, ContactModel)
      : null
  }

  async getStreamByUpdatedAt(
    organizationId: string,
    afterDate?: Date,
    beforeDate?: Date,
    withJoins = true
  ): Promise<ReadStream> {
    const queryBuilder = this.entityRepository.createQueryBuilder('c')

    if (withJoins) {
      queryBuilder
        .leftJoinAndSelect('c.createdBy', 'u')
        .leftJoinAndSelect('c.company', 'company')
    }

    queryBuilder
      .where('c.organizationId = :organizationId', { organizationId })
      .andWhere(`c.availableInWorkspace IS TRUE`)

    if (afterDate) {
      queryBuilder.andWhere('c.updatedAt > :afterDate', { afterDate })
    }

    if (beforeDate) {
      queryBuilder.andWhere('c.updatedAt < :beforeDate', { beforeDate })
    }
    queryBuilder.orderBy('c.updatedAt', 'ASC').addOrderBy('c.createdAt', 'ASC')

    return queryBuilder.stream()
  }

  async findByOrganizationAndEmail(
    organizationId: string,
    email: string
  ): Promise<ContactModel[] | null> {
    if (!email || !organizationId) return null

    const contactEntities = await this.entityRepository
      .createQueryBuilder('c')
      .leftJoinAndSelect('c.updatedBy', 'u')
      .where('c.organizationId = :organizationId', { organizationId })
      .andWhere(`c.availableInWorkspace IS TRUE`)
      .andWhere(`emails @> ARRAY[:email]::varchar[]`, { email })
      .getMany()

    return contactEntities
      ? this.mapper.mapArray(contactEntities, ContactEntity, ContactModel)
      : null
  }

  async findByUserAndEmail(
    userId: string,
    email: string
  ): Promise<ContactModel | null> {
    if (!email || !userId) return null
    const contactEntity = await this.entityRepository
      .createQueryBuilder('c')
      .leftJoinAndSelect('c.assignUser', 'a')
      .where('a.id = :userId', { userId })
      .andWhere(`c.availableInWorkspace IS TRUE`)
      .andWhere(`emails @> ARRAY[:email]::varchar[]`, { email })
      .getOne()

    return contactEntity
      ? this.mapper.map(contactEntity, ContactEntity, ContactModel)
      : null
  }

  async findByOrganizationAndUser(
    organizationId: string,
    assignId: string,
    id: string,
    withDeleted = false
  ): Promise<ContactModel | null> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .leftJoinAndSelect('c.assignUser', 'a')
      .where('c.id = :id', {
        id: id,
      })
      .andWhere('a.id = :assignId', {
        assignId: assignId,
      })
      .andWhere('c.organizationId = :organizationId', {
        organizationId: organizationId,
      })

    if (true === withDeleted) {
      queryBuilder.withDeleted()
    }

    const contactEntity = await queryBuilder.getOne()

    return contactEntity
      ? this.mapper.map(contactEntity, ContactEntity, ContactModel)
      : null
  }

  queryFindByOrganization(
    organizationId: string,
    userId: string,
    filter: PaginationQueryObject
  ): SelectQueryBuilder<ContactEntity> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .where('c.organizationId = :organizationId', {
        organizationId: organizationId,
      })
      .andWhere(`c.availableInWorkspace IS TRUE`)

    if (filter.order) {
      queryBuilder.orderBy('c.createdAt', filter.order)
    }

    // TODO: Add user id to query
    return queryBuilder
  }

  async createMany(contactModels: ContactModel[]): Promise<ContactModel[]> {
    if (contactModels.length === 0) {
      return []
    }

    const contactEntities = this.mapper.mapArray(
      contactModels,
      ContactModel,
      ContactEntity
    )
    const contactModelsAdded = await this.entityRepository.save(
      contactEntities,
      {
        transaction: false,
        chunk: 250,
      }
    )

    return this.mapper.mapArray(contactModelsAdded, ContactEntity, ContactModel)
  }

  async archive(contactModel: ContactModel) {
    let contactEntity = this.mapper.map(
      contactModel,
      ContactModel,
      ContactEntity
    )
    contactEntity = await this.entityRepository.softRemove(contactEntity)
    return this.mapper.map(contactEntity, ContactEntity, ContactModel)
  }

  async unarchive(contactModel: ContactModel) {
    let contactEntity = this.mapper.map(
      contactModel,
      ContactModel,
      ContactEntity
    )
    contactEntity = await this.entityRepository.recover(contactEntity)
    return this.mapper.map(contactEntity, ContactEntity, ContactModel)
  }

  deleteCustomField(customFieldModel: CustomFieldModel): Promise<UpdateResult> {
    //remove a field from a jsonb column
    return this.entityRepository
      .createQueryBuilder()
      .update()
      .set({
        customFields: () => `customFields - '${customFieldModel.fieldId}'`,
      })
      .where('organizationId = :organizationId', {
        organizationId: customFieldModel.organizationId,
      })
      .execute()
  }

  async findIdsByExternalIds(
    organizationId: string,
    externalIds?: string[],
    withDeleted = false
  ): Promise<{ [key: string]: string }> {
    if (!externalIds || externalIds.length === 0) return {}

    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .select(['c.id', 'c.externalId'])
      .where(
        'c.organizationId = :organizationId AND c.externalId IN (:...externalIds)',
        {
          organizationId: organizationId,
          externalIds: externalIds,
        }
      )

    if (true === withDeleted) {
      queryBuilder.withDeleted()
    }
    const results = await queryBuilder.execute()
    return Object.fromEntries(
      results.map(result => [result.c_external_id, result.c_id])
    )
  }

  async findIdsByLinkedinIds(
    organizationId: string,
    linkedinIds?: string[],
    withDeleted = false
  ): Promise<{ [key: string]: string }> {
    if (!linkedinIds || linkedinIds.length === 0) return {}
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .select(['c.id', 'c.linkedinId'])
      .where('c.organizationId = :organizationId', { organizationId })
      .andWhere('c.linkedinId IN (:...linkedinIds)', { linkedinIds })
      .andWhere('c.availableInWorkspace IS TRUE')

    if (true === withDeleted) {
      queryBuilder.withDeleted()
    }

    const results = await queryBuilder.getRawMany()
    return Object.fromEntries(
      results.map(result => {
        return [result.c_linkedin_id, result.c_id]
      })
    )
  }

  async findByExternalIds(
    organizationId: string,
    externalIds?: string[],
    withDeleted = false
  ): Promise<ContactModel[]> {
    const contactEntities = await this.entityRepository.find({
      where: { organizationId: organizationId, externalId: In(externalIds) },
      withDeleted,
    })
    return this.mapper.mapArray(contactEntities, ContactEntity, ContactModel)
  }

  async findOneByExternalId(
    organizationId: string,
    externalId: string,
    withDeleted = false
  ): Promise<ContactModel> {
    if (!externalId) return null

    const contactEntity = await this.entityRepository.findOne({
      where: { organizationId: organizationId, externalId: externalId },
      withDeleted,
    })
    return this.mapper.map(contactEntity, ContactEntity, ContactModel)
  }

  async primitiveUpdate(contactModel: Partial<ContactModel>): Promise<boolean> {
    if (!contactModel.id) {
      return false
    }

    try {
      await this.entityRepository.update(contactModel.id, contactModel)
      return true
    } catch (error) {
      return false
    }
  }

  async updateAvailableInWorkspace(leadImportId: string): Promise<string[]> {
    if (!leadImportId) {
      throw new Error('LeadImport id is required')
    }

    const result = await this.entityRepository
      .createQueryBuilder()
      .update()
      .where('contacts.lead_import_ids @> ARRAY[:leadImportId]::uuid[]', {
        leadImportId,
      })
      .set({ availableInWorkspace: true })
      .returning('id')
      .execute()
    return result?.raw?.map(r => r.id) ?? null
  }

  /**
   * @param duplicateGroup
   * @param partialContactModel
   * @returns ids of the updated contacts or null if no duplicateGroup is provided
   */
  async updateByDuplicateGroup(
    duplicateGroup: string,
    partialContactModel: Partial<Omit<ContactModel, 'id'>>
  ): Promise<string[] | null> {
    if (!duplicateGroup) {
      return null
    }

    const result = await this.entityRepository
      .createQueryBuilder()
      .update()
      .where('duplicateGroup = :duplicateGroup', {
        duplicateGroup: duplicateGroup,
      })
      .set(partialContactModel)
      .returning('id')
      .execute()
    return result?.raw?.map(r => r.id) ?? null
    /*try {
      await this.entityRepository.update(
        { duplicateGroup: duplicateGroup },
        partialContactModel
      )
      return true
    } catch (error) {
      return false
    }*/
  }

  async findByOrganizationAndDuplicateGroup(
    organizationId: string,
    duplicateGroup: string,
    limit = 10
  ): Promise<ContactModel[]> {
    const query = this.entityRepository
      .createQueryBuilder('c')
      .where('c.organizationId = :organizationId', {
        organizationId: organizationId,
      })
      .andWhere(`c.duplicateGroup = :duplicateGroup`, {
        duplicateGroup: duplicateGroup,
      })
      .andWhere(`c.availableInWorkspace IS TRUE`)

    if (limit) {
      query.limit(limit)
    }
    const entities = await query.getMany()
    return this.mapper.mapArray(entities, ContactEntity, ContactModel)
  }

  async findIdsByCompany(
    companyId: string,
    unAssignedOnly?: boolean
  ): Promise<string[] | null> {
    if (!companyId) {
      return null
    }
    let queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .select(['c.id as id'])
      .where('c.company_id = :companyId', {
        companyId: companyId,
      })
      .andWhere(`c.availableInWorkspace IS TRUE`)

    if (unAssignedOnly) {
      queryBuilder = queryBuilder.andWhere('c.assign_user_id IS NULL')
    }

    const results = await queryBuilder.execute()
    return results.map(result => result.id)
  }

  async findIdsByCompanies(
    companiesIds: string[],
    unAssignedOnly?: boolean
  ): Promise<string[]> {
    if (!companiesIds || companiesIds.length === 0) {
      return []
    }
    let queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .select(['c.id as id'])
      .where('c.company_id IN(:...companiesIds)', {
        companiesIds: companiesIds,
      })
      .andWhere(`c.availableInWorkspace IS TRUE`)

    if (unAssignedOnly) {
      queryBuilder = queryBuilder.andWhere('c.assign_user_id IS NULL')
    }

    const results = await queryBuilder.execute()
    return results.map(result => result.id)
  }

  async countNbAvailableForEnrichment(
    contactsIds: string[],
    enrichmentMaxDate: Date
  ): Promise<number> {
    if (!contactsIds || contactsIds.length === 0) {
      return 0
    }
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .where('c.id IN(:...contactsIds)', {
        contactsIds: contactsIds,
      })
      .andWhere(
        '(c.enrichmentAdvancedDate IS NULL OR c.enrichmentAdvancedDate < :enrichmentMaxDate)',
        { enrichmentMaxDate: enrichmentMaxDate }
      )

    return queryBuilder.getCount()
  }

  async countNbLeadsInLeadImport(
    organizationId: string,
    leadImportId: string
  ): Promise<number> {
    if (!organizationId || !leadImportId) {
      return 0
    }
    const contactCountResult = await this.entityRepository
      .createQueryBuilder('c')
      .select('COUNT(c.id)', 'contactCount')
      .where('c.organization_id = :organizationId', { organizationId })
      .andWhere('c.leadImportIds @> ARRAY[:leadImportId]::uuid[]', {
        leadImportId,
      })
      .getRawOne()
    return parseInt(contactCountResult.contactCount, 10)
  }

  async findIdsAndReindexRequired(
    limit = 100,
    withDeleted = true
  ): Promise<string[]> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .select('c.id')
      .andWhere('c.reindexRequired = :reindexRequired', {
        reindexRequired: true,
      })
      .limit(limit)
      .orderBy('c.updatedAt', 'ASC')

    if (true === withDeleted) {
      queryBuilder.withDeleted()
    }

    const results = await queryBuilder.getRawMany()

    return results.map(result => result.c_id)
  }

  async countReindexRequired(withDeleted = false): Promise<number> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .select('COUNT(c.id)', 'count')
      .where('c.reindex_required = :reindexRequired', {
        reindexRequired: true,
      })

    if (withDeleted) {
      queryBuilder.withDeleted()
    }

    const result = await queryBuilder.getRawOne()
    return result.count
  }

  async updateBulkReindexRequired(
    ids: string[],
    reindexRequired: boolean
  ): Promise<void> {
    if (ids.length === 0) {
      return
    }

    await this.entityRepository
      .createQueryBuilder()
      .update()
      .set({ reindexRequired })
      .where('id IN (:...ids)', { ids })
      .execute()
  }

  async updateBulkEnrichmentData(
    ids: string[],
    status: EnrichmentStatus,
    type?: EnrichmentType
  ): Promise<void> {
    if (0 >= ids.length) {
      return
    }
    const setQuery: Partial<ContactModel> = {
      enrichmentStatus: status,
    }

    if (type) {
      setQuery.enrichmentType = type

      if (type === EnrichmentType.EMAIL) {
        setQuery.enrichmentEmailStatus = status
      } else if (type === EnrichmentType.PHONE) {
        setQuery.enrichmentPhoneStatus = status
      } else if (type === EnrichmentType.ADVANCED) {
        setQuery.enrichmentAdvancedStatus = status
      }
    }

    const queryBuilder = this.entityRepository
      .createQueryBuilder()
      .update()
      .set(setQuery)
      .where('id IN (:...ids)', { ids: ids })

    await queryBuilder.execute()
  }

  async countNbByCompany(companyId: string): Promise<number> {
    if (!companyId) {
      return null
    }
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .where('c.company_id = :companyId', {
        companyId: companyId,
      })
      .andWhere(`c.availableInWorkspace IS TRUE`)

    return queryBuilder.getCount()
  }

  async getStreamByContactZeliqIdAndEnrichmentDate(
    contactZeliqId: string,
    enrichmentDate: Date
  ): Promise<ReadStream | null> {
    if (!contactZeliqId || !enrichmentDate) {
      return null
    }

    const queryBuilder = this.entityRepository
      .createQueryBuilder()
      .select(['id', 'enrichment_result'])
      .where(`enrichment_result->>'id' = :contactZeliqId`, {
        contactZeliqId,
      })
      .andWhere(
        '(enrichment_advanced_date < :enrichmentDate OR enrichment_email_date < :enrichmentDate OR enrichment_phone_date < :enrichmentDate)',
        { enrichmentDate }
      )
      .andWhere(
        `(enrichment_new_data_available_updated_at < :enrichmentDate OR enrichment_new_data_available_updated_at IS NULL)`,
        { enrichmentDate }
      )
      .andWhere(`available_in_workspace IS TRUE`)

    return queryBuilder.stream()
  }

  async getStreamByLinkedinIdAndEnrichmentDate(
    linkedinId: string,
    enrichmentDate: Date
  ): Promise<ReadStream | null> {
    if (!linkedinId || !enrichmentDate) {
      return null
    }

    const queryBuilder = this.entityRepository
      .createQueryBuilder()
      .select(['id', 'enrichment_result'])
      .where(`linkedin_id = :linkedinId`, { linkedinId })
      .andWhere(
        '(enrichment_advanced_date < :enrichmentDate OR enrichment_email_date < :enrichmentDate OR enrichment_phone_date < :enrichmentDate)',
        { enrichmentDate }
      )
      .andWhere(
        `(enrichment_new_data_available_updated_at < :enrichmentDate OR enrichment_new_data_available_updated_at IS NULL)`,
        { enrichmentDate }
      )
      .andWhere(`available_in_workspace IS TRUE`)

    return queryBuilder.stream()
  }

  async updateEngagementScoringMetaForTaskDate(
    engagementScoring: LeadEngagementScoringPriority,
    startTaskDate?: Date,
    endTaskDate?: Date
  ): Promise<void> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder()
      .update()
      .set({
        engagementScoringPriority: engagementScoring,
        reindexRequired: true,
        engagementScoringMetadata: () =>
          ` engagement_scoring_metadata ?? jsonb_build_object('${LeadEngagementScoringMetadataKey.NEXT_TASK_DATE}', nextTaskDate, '${LeadEngagementScoringMetadataKey.NEXT_TASK_ID}', nextTaskId)`,
        // `jsonb_set(engagement_scoring_metadata, '{${LeadEngagementScoringMetadataKey.NEXT_TASK_DATE}}', next_task_date, true)`,
      })
      .where("coalesce(engagementScoringPriority, '') = :engagementScoring", {
        engagementScoring: engagementScoring,
      })

    if (startTaskDate) {
      queryBuilder.andWhere('nextTaskDate >= :startDate', {
        startDate: startTaskDate,
      })
    }

    if (endTaskDate) {
      queryBuilder.andWhere('nextTaskDate <= :endDate', {
        endDate: endTaskDate,
      })
    }

    queryBuilder.execute()
  }

  async updateEngagementScoringForTaskDate(
    engagementScoring: LeadEngagementScoringPriority,
    startTaskDate?: Date,
    endTaskDate?: Date
  ): Promise<void> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder()
      .update()
      .set({
        engagementScoringPriority: engagementScoring,
        reindexRequired: true,
        engagementScoringMetadata: () =>
          `json_build_object('${LeadEngagementScoringMetadataKey.NEXT_TASK_DATE}', nextTaskDate, '${LeadEngagementScoringMetadataKey.NEXT_TASK_ID}', nextTaskId)`,
      })
      .where("coalesce(engagementScoringPriority, '') <> :engagementScoring", {
        engagementScoring: engagementScoring,
      })

    if (startTaskDate) {
      queryBuilder.andWhere('nextTaskDate >= :startDate', {
        startDate: startTaskDate,
      })
    }

    if (endTaskDate) {
      queryBuilder.andWhere('nextTaskDate <= :endDate', {
        endDate: endTaskDate,
      })
    }

    queryBuilder.execute()
  }

  async updateEngagementScoringForAssignmentDate(
    engagementScoring: LeadEngagementScoringPriority,
    assignmentDate: Date
  ): Promise<void> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder()
      .update()
      .set({
        engagementScoringPriority: engagementScoring,
        reindexRequired: true,
        engagementScoringMetadata: () =>
          `json_build_object('${LeadEngagementScoringMetadataKey.ASSIGNATION_OVERDUE_DATE}', assignmentDate)`,
      })
      .where(
        "coalesce(engagementScoringPriority, '') <> :engagementScoring AND status = :status AND assignmentDate < :assignmentDate",
        {
          engagementScoring: engagementScoring,
          status: LeadStatus.NEW,
          assignmentDate: assignmentDate,
        }
      )

    queryBuilder.execute()
  }

  async updateEngagementScoringMetaForAssignmentDate(
    engagementScoring: LeadEngagementScoringPriority,
    assignmentDate: Date
  ): Promise<void> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder()
      .update()
      .set({
        engagementScoringPriority: engagementScoring,
        reindexRequired: true,
        engagementScoringMetadata: () =>
          `engagement_scoring_metadata ?? json_build_object('${LeadEngagementScoringMetadataKey.ASSIGNATION_OVERDUE_DATE}', assignmentDate)`,
      })
      .where(
        "coalesce(engagementScoringPriority, '') = :engagementScoring AND status = :status AND assignmentDate < :assignmentDate",
        {
          engagementScoring: engagementScoring,
          status: LeadStatus.NEW,
          assignmentDate: assignmentDate,
        }
      )

    queryBuilder.execute()
  }

  async updateEngagementScoringForCallToFollowUp(
    engagementScoring: LeadEngagementScoringPriority,
    startCallDate: Date,
    endCallDate: Date
  ): Promise<void> {
    this.entityRepository.query(
      `WITH RankedRows AS (SELECT id,
                                  contact_id,
                                  created_at,
                                  answered_at,
                                  ROW_NUMBER() OVER (PARTITION BY contact_id ORDER BY created_at DESC) AS RowNum
                           FROM calls
                           WHERE created_at > $1)
       UPDATE contacts
       SET reindex_required = true,
           engagement_scoring_metadata = CASE
                                           WHEN engagement_scoring_priority = '${engagementScoring}' THEN
                                             engagement_scoring_metadata ??
                                             jsonb_build_object('${LeadEngagementScoringMetadataKey.FOLLOW_UP_DATE}', RankedRows.created_at)
                                           ELSE jsonb_build_object('${LeadEngagementScoringMetadataKey.FOLLOW_UP_DATE}', RankedRows.created_at) END,
           engagement_scoring_priority = $2
       FROM RankedRows
       WHERE contacts.id = RankedRows.contact_id
         AND RankedRows.RowNum = 1
         AND RankedRows.answered_at IS NULL
         AND RankedRows.created_at > $3
         AND RankedRows.created_at < $4;
      `,
      [startCallDate, engagementScoring, startCallDate, endCallDate]
    )
  }

  async updateEngagementScoringForMailToFollowUp(
    engagementScoring: LeadEngagementScoringPriority,
    startMailDate: Date,
    endMailDate: Date
  ): Promise<void> {
    this.entityRepository.query(
      `WITH RankedRows AS (SELECT id,
                                  contact_id,
                                  created_at,
                                  direction,
                                  ROW_NUMBER() OVER (PARTITION BY contact_id ORDER BY created_at DESC) AS RowNum
                           FROM messages
                           WHERE created_at > $1)
       UPDATE contacts
       SET reindex_required = true,
           engagement_scoring_metadata = CASE
                                           WHEN engagement_scoring_priority = '${engagementScoring}' THEN
                                             engagement_scoring_metadata ??
                                             jsonb_build_object('${LeadEngagementScoringMetadataKey.FOLLOW_UP_DATE}', RankedRows.created_at)
                                           ELSE jsonb_build_object('${LeadEngagementScoringMetadataKey.FOLLOW_UP_DATE}', RankedRows.created_at) END,
           engagement_scoring_priority = $2
       FROM RankedRows
       WHERE contacts.id = RankedRows.contact_id
         AND RankedRows.RowNum = 1
         AND RankedRows.direction = '${MessageDirection.OUTBOUND}'
         AND RankedRows.created_at > $3
         AND RankedRows.created_at < $4;`,
      [startMailDate, engagementScoring, startMailDate, endMailDate]
    )
  }

  async findContactIdsByLeadImport({
    leadImportId,
    organizationId,
    paginationObj,
    withNewDataAvailableOnly,
  }: FindContactIdsByLeadImportIdQuery): Promise<string[]> {
    if (!leadImportId || !organizationId) return []

    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .select('c.id')
      .where('c.organizationId = :organizationId', { organizationId })
      // Using the GIN index for array containment check
      .andWhere('c.leadImportIds @> ARRAY[:leadImportId]::uuid[]', {
        leadImportId,
      })
    if (withNewDataAvailableOnly) {
      queryBuilder.andWhere('c.enrichmentNewDataAvailable IS TRUE')
    }
    queryBuilder.orderBy('c.id')
    if (paginationObj) {
      const { limit, skip } = paginationObj
      queryBuilder.skip(skip).take(limit)
    }

    const contactIds = await queryBuilder.getMany()
    return contactIds.map(contact => contact.id)
  }

  async findCompaniesIdsByOrganizationIdAndContactIds(
    organizationId: string,
    contactIds: string[]
  ): Promise<string[]> {
    const result = await this.entityRepository
      .createQueryBuilder()
      .select('company_id')
      .where('id IN (:...contactIds)', { contactIds })
      .andWhere('organization_id = :organizationId', { organizationId })
      .execute()
    return result.map(r => r.company_id)
  }

  async countByOrganizationInDateRange(
    organizationId: string,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    return this.entityRepository
      .createQueryBuilder('c')
      .where('c.organizationId = :organizationId', { organizationId })
      .andWhere(`c.source = '${LeadSource.SEARCH}'`)
      .andWhere('c.createdAt >= :startDate', { startDate })
      .andWhere('c.createdAt <= :endDate', { endDate })
      .getCount()
  }

  async countCreatedByUserInDateRange(
    organizationId: string,
    userId: string,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    return this.entityRepository
      .createQueryBuilder('c')
      .where('c.organizationId = :organizationId', { organizationId })
      .andWhere('c.created_by_id = :userId', {
        userId,
      })
      .andWhere(`c.availableInWorkspace IS TRUE`)
      .andWhere('c.createdAt >= :startDate', { startDate })
      .andWhere('c.createdAt <= :endDate', { endDate })
      .getCount()
  }

  async countContactsByOrganizationAndAssign(
    organizationId: string,
    assignId: string
  ): Promise<object> {
    const urgentPriorityQuery = this.entityRepository
      .createQueryBuilder('c')
      .where('c.organizationId = :organizationId', { organizationId })
      .andWhere('c.assign_user_id = :assignId', { assignId })
      .andWhere('c.engagementScoringPriority = :priority', {
        priority: LeadEngagementScoringPriority.URGENT,
      })
      .getCount()

    const highPriorityQuery = this.entityRepository
      .createQueryBuilder('c')
      .where('c.organizationId = :organizationId', { organizationId })
      .andWhere('c.assign_user_id = :assignId', { assignId })
      .andWhere('c.engagementScoringPriority = :priority', {
        priority: LeadEngagementScoringPriority.HIGH,
      })
      .getCount()

    const poolPriorityQuery = this.entityRepository
      .createQueryBuilder('c')
      .where('c.organizationId = :organizationId', { organizationId })
      .andWhere('c.assign_user_id = :assignId', { assignId })
      .andWhere('c.engagementScoringPriority = :priority', {
        priority: LeadEngagementScoringPriority.POOL,
      })
      .getCount()

    const standbyPriorityQuery = this.entityRepository
      .createQueryBuilder('c')
      .where('c.organizationId = :organizationId', { organizationId })
      .andWhere('c.assign_user_id = :assignId', { assignId })
      .andWhere('c.engagementScoringPriority = :priority', {
        priority: LeadEngagementScoringPriority.STANDBY,
      })
      .getCount()

    const [urgent, high, pool, standby] = await Promise.all([
      urgentPriorityQuery,
      highPriorityQuery,
      poolPriorityQuery,
      standbyPriorityQuery,
    ])

    return {
      urgent,
      high,
      pool,
      standby,
    }
  }

  async countContactsByStatusInDateRange(
    organizationId: string,
    assignId: string,
    startDate: Date,
    endDate: Date
  ): Promise<Record<LeadStatus, number>> {
    const statusCounts = await this.entityRepository
      .createQueryBuilder('contact')
      .select('contact.status', 'status')
      .addSelect('COUNT(contact.id)', 'count')
      .where('contact.organizationId = :organizationId', { organizationId })
      .andWhere('contact.assignUser.id = :assignId', { assignId })
      .andWhere(
        new Brackets(qb => {
          qb.where(
            '(contact.statusUpdatedAt IS NOT NULL AND contact.statusUpdatedAt >= :startDate AND contact.statusUpdatedAt <= :endDate)',
            { startDate, endDate }
          ).orWhere(
            '(contact.statusUpdatedAt IS NULL AND contact.createdAt >= :startDate AND contact.createdAt <= :endDate)',
            { startDate, endDate }
          )
        })
      )
      .groupBy('contact.status')
      .getRawMany()

    const statusCountMap: Record<LeadStatus, number> = Object.values(
      LeadStatus
    ).reduce(
      (acc, status) => {
        acc[status] = 0
        return acc
      },
      {} as Record<LeadStatus, number>
    )

    for (const statusCount of statusCounts) {
      const status = statusCount.status.toLowerCase() as LeadStatus
      if (status in statusCountMap) {
        statusCountMap[status] = parseInt(statusCount.count, 10)
      }
    }

    return statusCountMap
  }

  async countByLeadImportIdWithNewDataAvailable(
    organizationId: string,
    leadImportId: string
  ): Promise<number> {
    const contactCountResult = await this.entityRepository
      .createQueryBuilder('c')
      .select('COUNT(c.id)', 'contactCount')
      .where('c.organization_id = :organizationId', { organizationId })
      .andWhere('c.leadImportIds @> ARRAY[:leadImportId]::uuid[]', {
        leadImportId,
      })
      .andWhere('c.enrichmentNewDataAvailable IS TRUE')
      .getRawOne()
    return parseInt(contactCountResult.contactCount, 10)
  }

  async countNbEnrichedByLeadImportId(
    organizationId: string,
    leadImportId: string
  ): Promise<number> {
    const contactCountResult = await this.entityRepository
      .createQueryBuilder('c')
      .select('COUNT(c.id)', 'contactCount')
      .where('c.organization_id = :organizationId', { organizationId })
      .andWhere('c.leadImportIds @> ARRAY[:leadImportId]::uuid[]', {
        leadImportId,
      })
      .andWhere('c.enrichmentStatus = :enrichmentStatus', {
        enrichmentStatus: EnrichmentStatus.COMPLETED,
      })
      .getRawOne()
    return parseInt(contactCountResult.contactCount, 10)
  }

  async countByAssignUserId(
    organizationId: string,
    assignId: string
  ): Promise<number> {
    const contactCountResult = await this.entityRepository
      .createQueryBuilder('c')
      .select('COUNT(c.id)', 'contactCount')
      .where('c.organization_id = :organizationId', { organizationId })
      .andWhere('c.assign_user_id = :assignId', { assignId })
      .andWhere('c.available_in_workspace IS TRUE')
      .getRawOne()

    return parseInt(contactCountResult.contactCount, 10)
  }

  async updateExternalUrlTemplate(
    organizationId: string,
    newExternalUrlTemplate: string
  ): Promise<void> {
    if (!organizationId) {
      return
    }

    //raw query to avoid updating updatedAt field
    await this.entityRepository.query(
      `UPDATE contacts
       SET external_url_template = $1
       WHERE organization_id = $2
         AND source = $3
         AND external_url_template IS NULL`,
      [newExternalUrlTemplate, organizationId, 'CRM']
    )
  }

  async getContactsToUpdate(contactIds: string[]): Promise<string[]> {
    const contacts = await this.entityRepository
      .createQueryBuilder('contact')
      .select('contact.id')
      .where('contact.id IN (:...contactIds)', { contactIds })
      .andWhere(
        `(contact.enrichmentId IS NULL OR
          contact.enrichmentStatus = '${EnrichmentStatus.IN_PROGRESS}' OR
          contact.enrichmentEmailStatus = '${EnrichmentStatus.IN_PROGRESS}' OR
          contact.enrichmentPhoneStatus = '${EnrichmentStatus.IN_PROGRESS}' OR
          contact.enrichmentAdvancedStatus = '${EnrichmentStatus.IN_PROGRESS}')`
      )
      .getMany()

    return contacts.map(contact => contact.id)
  }

  async getIdsForOneLeadImportNotAvailableInWorkspace(
    organizationId: string,
    leadImportId: string
  ): Promise<string[]> {
    const contacts = await this.entityRepository
      .createQueryBuilder('contact')
      .select('contact.id')
      .where('contact.organization_id = :organizationId', { organizationId })
      .andWhere('contact.available_in_workspace = false')
      .andWhere('cardinality(contact.lead_import_ids) = 1')
      .andWhere('contact.lead_import_ids[1] = :leadImportId', { leadImportId })
      .getMany()

    return contacts.map(contact => contact.id)
  }

  async getUniqueCustomFieldKeys(
    leadImportId: string,
    organizationId: string
  ): Promise<string[]> {
    const result = await this.entityRepository.query(
      `SELECT DISTINCT key
       FROM contacts,
            LATERAL jsonb_object_keys(custom_fields) AS key
       WHERE $1 = ANY (lead_import_ids)
         AND organization_id = $2`,
      [leadImportId, organizationId]
    )

    return result.map(row => row.key)
  }

  async findByLinkedinId(linkedinId: string): Promise<ContactModel[]> {
    const contacts = await this.entityRepository.find({
      where: { linkedinId },
      relations: ['company'],
    })
    return contacts.map(contact =>
      this.mapper.map(contact, ContactEntity, ContactModel)
    )
  }

  async updateOptOutStatus(
    contactIds: string[],
    optOut: boolean
  ): Promise<void> {
    if (contactIds.length === 0) return

    await this.entityRepository
      .createQueryBuilder()
      .update(ContactEntity)
      .set({
        optOut,
        enrichmentNewDataAvailable: false,
        enrichmentNewDataAvailableUpdatedAt: null,
        enrichmentNewDataAvailableDetails: null,
      })
      .where('id IN (:...contactIds)', { contactIds })
      .execute()
  }

  async findByOrganizationAndLeadImport(
    organizationId: string,
    leadImportId: string
  ): Promise<ContactModel[]> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .where('c.organization_id = :organizationId', { organizationId })
      .andWhere(':leadImportId = ANY(c.leadImportIds)', {
        leadImportId,
      })

    const contacts = await queryBuilder.getMany()

    return this.mapper.mapArray(contacts, ContactEntity, ContactModel)
  }
}
