import { Mapper } from '@automapper/core'
import { InjectMapper } from '@automapper/nestjs'
import { EnrichmentStatus, LeadSource } from '@getheroes/shared'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import {
  Brackets,
  FindOptionsWhere,
  In,
  Repository,
  SelectQueryBuilder,
  UpdateResult,
} from 'typeorm'
import { FindManyOptions } from 'typeorm/find-options/FindManyOptions'
import { PaginationQueryObject } from '../../../shared/domain/object/pagination-query.object'
import { LeadImportIdOperation } from '../../domain/interface/contact.interface'
import { CompanyRepositoryInterface } from '../../domain/model/company-repository.interface'
import { CompanyModel } from '../../domain/model/company.model'
import { CustomFieldModel } from '../../domain/model/custom-field.model'
import { CompanyEntity } from '../entity/company.entity'

@Injectable()
export class CompanyRepository implements CompanyRepositoryInterface {
  constructor(
    @InjectRepository(CompanyEntity)
    private readonly entityRepository: Repository<CompanyEntity>,
    @InjectMapper() private readonly mapper: Mapper
  ) {}

  async create(companyModel: CompanyModel): Promise<CompanyModel> {
    const companyEntity = this.mapper.map(
      companyModel,
      CompanyModel,
      CompanyEntity
    )
    await this.entityRepository.save(companyEntity)
    return this.mapper.map(companyEntity, CompanyEntity, CompanyModel)
  }

  async count(options?: FindManyOptions<CompanyEntity>): Promise<number> {
    return this.entityRepository.count(options)
  }

  async update(companyModel: CompanyModel): Promise<CompanyModel> {
    const companyEntity = this.mapper.map(
      companyModel,
      CompanyModel,
      CompanyEntity
    )

    await this.entityRepository.save(companyEntity)
    return this.mapper.map(companyEntity, CompanyEntity, CompanyModel)
  }

  async findById(
    id: string,
    withDeleted = false
  ): Promise<CompanyModel | null> {
    const companyEntity = await this.entityRepository.findOne({
      where: { id },
      relations: {
        assignUsers: {
          user: true,
        },
        createdBy: true,
        archivedBy: true,
      },
      withDeleted: withDeleted,
    })
    return companyEntity
      ? this.mapper.map(companyEntity, CompanyEntity, CompanyModel)
      : null
  }

  queryFindByOrganizationAndNameOrDomain(
    organizationId: string,
    name: string | null = null,
    domain: string | null = null
  ): SelectQueryBuilder<CompanyEntity> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .where('c.organizationId = :organizationId', {
        organizationId,
      })
    const filterCriterias = { domain, name }
    queryBuilder.andWhere(
      new Brackets(qb => {
        Object.keys(filterCriterias).forEach(key => {
          if (filterCriterias[key] !== null) {
            qb.orWhere(`c.${key} = :${key}`, {
              [key]: filterCriterias[key],
            })
          }
        })
      })
    )
    return queryBuilder
  }

  async findByOrganizationAndNameOrDomain(
    organizationId: string,
    name: string,
    domain: string
  ): Promise<CompanyModel[]> {
    const queryBuilder = this.queryFindByOrganizationAndNameOrDomain(
      organizationId,
      name,
      domain
    )
    const commpaniesEntities = await (
      await queryBuilder.getRawAndEntities()
    ).entities
    return this.mapper.mapArray(commpaniesEntities, CompanyEntity, CompanyModel)
  }

  async findByIdAndOrganizationId(
    organizationId: string,
    id: string,
    withDeleted = false
  ): Promise<CompanyModel | null> {
    const companyEntity = await this.entityRepository.findOne({
      where: { id, organizationId },
      withDeleted: withDeleted,
    })

    return companyEntity
      ? this.mapper.map(companyEntity, CompanyEntity, CompanyModel)
      : null
  }

  async findIdsByLeadImportId(
    organizationId: string,
    leadImportId: string
  ): Promise<string[]> {
    const companies = await this.entityRepository
      .createQueryBuilder('company')
      .select('company.id')
      .where('company.organizationId = :organizationId', { organizationId })
      // Using the GIN index for array containment check
      .andWhere('company.leadImportIds @> ARRAY[:leadImportId]::uuid[]', {
        leadImportId,
      })
      .getMany()

    return companies.map(company => company.id)
  }

  queryFindByOrganization(
    organizationId: string,
    userId: string,
    filter: PaginationQueryObject
  ): SelectQueryBuilder<CompanyEntity> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .where('c.organizationId = :organizationId', {
        organizationId: organizationId,
      })

    if (filter.order) {
      queryBuilder.orderBy('c.createdAt', filter.order)
    }

    // TODO: Add user id to query
    return queryBuilder
  }

  async createMany(companiesModels: CompanyModel[]): Promise<CompanyModel[]> {
    if (companiesModels.length === 0) {
      return []
    }

    const companiesEntities = this.mapper.mapArray(
      companiesModels,
      CompanyModel,
      CompanyEntity
    )

    await this.entityRepository.save(companiesEntities, {
      transaction: false,
      chunk: 250,
    })
    return this.mapper.mapArray(companiesEntities, CompanyEntity, CompanyModel)
  }

  async findByIds(ids: string[], withDeleted = false): Promise<CompanyModel[]> {
    if (ids.length === 0) return []
    const companiesEntities = await this.entityRepository.find({
      where: { id: In(ids) },
      relations: {
        assignUsers: {
          user: true,
        },
        createdBy: true,
        archivedBy: true,
      },
      withDeleted: withDeleted,
    })
    return this.mapper.mapArray(companiesEntities, CompanyEntity, CompanyModel)
  }

  async findByIdsForHubspotSync(
    ids: string[],
    withDeleted = false
  ): Promise<CompanyModel[]> {
    if (ids.length === 0) return []
    const companiesEntities = await this.entityRepository.find({
      where: { id: In(ids) },
      relations: {
        assignUsers: {
          user: true,
        },
        createdBy: true,
      },
      withDeleted: withDeleted,
    })
    return this.mapper.mapArray(companiesEntities, CompanyEntity, CompanyModel)
  }

  async archive(companyModel: CompanyModel) {
    let companyEntity = this.mapper.map(
      companyModel,
      CompanyModel,
      CompanyEntity
    )
    companyEntity = await this.entityRepository.softRemove(companyEntity)
    return this.mapper.map(companyEntity, CompanyEntity, CompanyModel)
  }

  async unarchive(companyModel: CompanyModel) {
    let companyEntity = this.mapper.map(
      companyModel,
      CompanyModel,
      CompanyEntity
    )
    companyEntity = await this.entityRepository.recover(companyEntity)
    return this.mapper.map(companyEntity, CompanyEntity, CompanyModel)
  }

  deleteCustomField(customFieldModel: CustomFieldModel): Promise<UpdateResult> {
    //remove a field from a jsonb column
    return this.entityRepository
      .createQueryBuilder()
      .update()
      .set({
        customFields: () => `customFields - '${customFieldModel.fieldId}'`,
      })
      .where('organizationId = :organizationId', {
        organizationId: customFieldModel.organizationId,
      })
      .execute()
  }

  async find(
    organizationId: string,
    params: {
      names?: string[]
      domains?: string[]
      externalIds?: string[]
    }
  ): Promise<CompanyModel[]> {
    const whereConditions = []
    if (params.names != undefined && params.names.length > 0) {
      whereConditions.push({ name: In(params.names), organizationId })
    }
    if (params.domains != undefined && params.domains.length > 0) {
      whereConditions.push({ domain: In(params.domains), organizationId })
    }
    if (params.externalIds != undefined && params.externalIds.length > 0) {
      whereConditions.push({
        externalId: In(params.externalIds),
        organizationId,
      })
    }
    if (whereConditions.length === 0) {
      return []
    }

    const companiesEntities = await this.entityRepository.find({
      where: whereConditions,
      order: { createdAt: 'ASC' },
    })
    return this.mapper.mapArray(companiesEntities, CompanyEntity, CompanyModel)
  }

  async findIdsByExternalIds(
    organizationId: string,
    externalIds?: string[],
    withDeleted = false
  ): Promise<{ [key: string]: string }> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .select(['c.id', 'c.externalId'])
      .where(
        'c.organizationId = :organizationId AND c.externalId IN (:...externalIds)',
        {
          organizationId: organizationId,
          externalIds: externalIds,
        }
      )
    if (true === withDeleted) {
      queryBuilder.withDeleted()
    }
    const results = await queryBuilder.execute()
    return Object.fromEntries(
      results.map(result => [result.c_external_id, result.c_id])
    )
  }

  async findByExternalIdsOrNamesOrDomains(
    organizationId: string,
    externalIds?: string[],
    names?: string[],
    domains?: string[],
    withDeleted = false
  ): Promise<CompanyModel[]> {
    const whereConditions: FindOptionsWhere<CompanyEntity>[] = []
    if (domains && domains.length > 0) {
      whereConditions.push({
        organizationId: organizationId,
        domain: In(domains),
      })
    }

    if (externalIds && externalIds.length > 0) {
      whereConditions.push({
        organizationId: organizationId,
        externalId: In(externalIds),
      })
    }

    if (names && names.length > 0) {
      whereConditions.push({
        organizationId: organizationId,
        name: In(names),
      })
    }

    if (whereConditions.length === 0) {
      return []
    }

    const companiesEntities = await this.entityRepository.find({
      where: whereConditions,
      withDeleted,
    })
    return this.mapper.mapArray(companiesEntities, CompanyEntity, CompanyModel)
  }

  async findIdsByExternalIdsOrNames(
    organizationId: string,
    externalIds?: string[],
    names?: string[],
    withDeleted = false
  ): Promise<{ [key: string]: string }> {
    if (
      !externalIds ||
      !names ||
      externalIds.length === 0 ||
      names.length === 0
    ) {
      return {}
    }
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .select(['c.id', 'c.externalId', 'c.name'])
      .where(
        'c.organizationId = :organizationId AND c.externalId IN (:...externalIds)',
        {
          organizationId: organizationId,
          externalIds: externalIds,
        }
      )
      .orWhere('c.organizationId = :organizationId AND c.name IN (:...names)', {
        organizationId: organizationId,
        names: names,
      })
    if (true === withDeleted) {
      queryBuilder.withDeleted()
    }

    const results = await queryBuilder.execute()
    return Object.fromEntries(
      results
        .map(result => [result.c_external_id, result.c_id])
        .concat(results.map(result => [result.c_name, result.c_id]))
    )
  }

  async findByExternalIds(
    organizationId: string,
    externalIds?: string[],
    withDeleted = false
  ): Promise<CompanyModel[]> {
    const companiesEntities = await this.entityRepository.find({
      where: { organizationId: organizationId, externalId: In(externalIds) },
      withDeleted,
    })
    return this.mapper.mapArray(companiesEntities, CompanyEntity, CompanyModel)
  }

  async updateLeadImportIds(
    companiesIds: string[],
    leadImportId: string,
    operation: LeadImportIdOperation
  ): Promise<void> {
    if (!companiesIds || companiesIds.length === 0 || !leadImportId) {
      return
    }

    if (operation === 'add') {
      await this.entityRepository
        .createQueryBuilder()
        .update()
        .set({
          leadImportIds: () => `array_append(lead_import_ids, :leadImportId)`,
        })
        .where('id IN (:...companiesIds)', { companiesIds })
        .andWhere('NOT lead_import_ids @> ARRAY[:leadImportId]::uuid[]', {
          leadImportId,
        })
        .execute()
    }

    if (operation === 'remove') {
      await this.entityRepository
        .createQueryBuilder()
        .update()
        .set({
          leadImportIds: () => `array_remove(lead_import_ids, :leadImportId)`,
        })
        .where('id IN (:...companiesIds)', { companiesIds })
        .andWhere('lead_import_ids @> ARRAY[:leadImportId]::uuid[]', {
          leadImportId,
        })
        .execute()
    }
  }

  async countNbAvailableForEnrichment(
    companiesIds: string[],
    enrichmentMaxDate: Date
  ): Promise<number> {
    if (!companiesIds || companiesIds.length === 0) {
      return 0
    }
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .where('c.id IN(:...companiesIds)', {
        companiesIds: companiesIds,
      })
      .andWhere(
        '(c.enrichmentAdvancedDate IS NULL OR c.enrichmentAdvancedDate < :enrichmentMaxDate)',
        { enrichmentMaxDate: enrichmentMaxDate }
      )

    return queryBuilder.getCount()
  }

  async updateBulkEnrichmentStatus(
    ids: string[],
    status: EnrichmentStatus
  ): Promise<void> {
    if (0 >= ids.length) {
      return
    }

    await this.entityRepository
      .createQueryBuilder()
      .update()
      .set({
        enrichmentStatus: status,
      })
      .where('id IN (:...ids)', { ids: ids })
      .execute()
  }

  async incrementNbLeads(id: string, value: number): Promise<void> {
    if (0 === value) {
      return
    }

    await this.entityRepository
      .createQueryBuilder()
      .update()
      .set({
        nbLeads: () => `nbLeads + ${value}`,
        reindexRequired: true,
      })
      .where('id = :id', { id: id })
      .execute()
  }

  async updateBulkReindexRequired(
    ids: string[],
    reindexRequired: boolean
  ): Promise<void> {
    if (ids.length === 0) {
      return
    }

    await this.entityRepository
      .createQueryBuilder()
      .update()
      .set({ reindexRequired })
      .where('id IN (:...ids)', { ids })
      .execute()
  }

  async findIdsAndReindexRequired(
    limit = 100,
    withDeleted = true
  ): Promise<string[]> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .select('c.id')
      .andWhere('c.reindexRequired = :reindexRequired', {
        reindexRequired: true,
      })
      .limit(limit)
      .orderBy('c.updatedAt', 'ASC')

    if (true === withDeleted) {
      queryBuilder.withDeleted()
    }

    const results = await queryBuilder.getRawMany()
    return results.map(result => result.c_id)
  }

  async countReindexRequired(withDeleted = false): Promise<number> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .select('COUNT(id)', 'count')
      .where('c.reindex_required = :reindexRequired', {
        reindexRequired: true,
      })

    if (withDeleted) {
      queryBuilder.withDeleted()
    }

    return queryBuilder.getRawOne().then(result => result.count)
  }

  async setAvailableInWorkspace(
    companiesIds: string[],
    source?: LeadSource
  ): Promise<string[] | null> {
    if (companiesIds.length === 0) {
      return []
    }

    const result = await this.entityRepository
      .createQueryBuilder()
      .update()
      .where('available_in_workspace=false')
      .andWhere('id IN (:...companiesIds)', {
        companiesIds,
      })
      .set({ availableInWorkspace: true, ...(source ? { source } : {}) })
      .returning('id')
      .execute()
    return result?.raw?.map(r => r.id) || null
  }

  async updateExternalUrlTemplate(
    organizationId: string,
    newExternalUrlTemplate: string
  ): Promise<void> {
    if (!organizationId) {
      return
    }

    //raw query to avoid updating updatedAt field
    await this.entityRepository.query(
      `UPDATE companies
       SET external_url_template = $1
       WHERE organization_id = $2
         AND source = $3
         AND external_url_template IS NULL`,
      [newExternalUrlTemplate, organizationId, 'CRM']
    )
  }

  async updateMany(
    companies: Array<CompanyModel>
  ): Promise<Array<CompanyModel>> {
    if (companies.length === 0) {
      return []
    }
    const updateCompanies = await this.entityRepository.save(companies)
    return this.mapper.mapArray(updateCompanies, CompanyEntity, CompanyModel)
  }

  async deleteByIds(
    companyIds: string[],
    onlyDeleteIfNotAvailableInWorkspace?: boolean
  ): Promise<void> {
    if (companyIds.length === 0) {
      return
    }

    const queryBuilder = this.entityRepository
      .createQueryBuilder()
      .delete()
      .from('companies')
      .where('id IN (:...companyIds)', { companyIds })

    if (onlyDeleteIfNotAvailableInWorkspace) {
      queryBuilder.andWhere('available_in_workspace = :available', {
        available: false,
      })
    }

    await queryBuilder.execute()
  }

  async addBulkLeadImportId(
    companiesIds: string[],
    leadImportId: string
  ): Promise<void> {
    if (companiesIds.length === 0 || !leadImportId) {
      return
    }
    await this.entityRepository
      .createQueryBuilder()
      .update()
      .set({
        leadImportIds: () => `array_append(lead_import_ids, :leadImportId)`,
      })
      .where('id IN (:...companiesIds)', {
        companiesIds,
      })
      .andWhere('NOT lead_import_ids @> ARRAY[:leadImportId]::uuid[]', {
        leadImportId,
      })
      .execute()
  }

  async countNbLeadsInLeadImport(
    organizationId: string,
    leadImportId: string
  ): Promise<number> {
    if (!organizationId || !leadImportId) {
      return 0
    }
    const companyCountResult = await this.entityRepository
      .createQueryBuilder('c')
      .select('COUNT(c.id)', 'contactCount')
      .where('c.organization_id = :organizationId', { organizationId })
      .andWhere(':leadImportId = ANY(c.leadImportIds)', {
        leadImportId,
      })
      .getRawOne()
    return parseInt(companyCountResult.contactCount, 10)
  }

  async findByLeadImportAndOrganization(
    organizationId: string,
    leadImportId: string
  ): Promise<CompanyModel[]> {
    const queryBuilder = this.entityRepository
      .createQueryBuilder('c')
      .where('c.organization_id = :organizationId', { organizationId })
      .andWhere(':leadImportId = ANY(c.leadImportIds)', {
        leadImportId,
      })

    const companies = await queryBuilder.getMany()

    return this.mapper.mapArray(companies, CompanyEntity, CompanyModel)
  }
}
