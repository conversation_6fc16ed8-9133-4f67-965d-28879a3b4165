import { Mapper } from '@automapper/core'
import { InjectMapper } from '@automapper/nestjs'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import {
  EnrichmentResultRepositoryInterface,
  EnrichmentResultWithEmailsCheckResult,
} from '../../domain/model/enrichment-result-repository.interface'
import { EnrichmentResultModel } from '../../domain/model/enrichment-result.model'
import { EnrichmentResultEntity } from '../entity/enrichment-result.entity'
import { EnrichmentResultMetrics } from '../../domain/interface/enrichment/enrichment-result-metrics.interface'

@Injectable()
export class EnrichmentResultRepository
  implements EnrichmentResultRepositoryInterface
{
  constructor(
    @InjectRepository(EnrichmentResultEntity)
    private readonly entityRepository: Repository<EnrichmentResultEntity>,
    @InjectMapper() private readonly mapper: Mapper
  ) {}

  async save(
    enrichmentResultModel: EnrichmentResultModel
  ): Promise<EnrichmentResultModel> {
    const enrichmentResultEntity = this.mapper.map(
      enrichmentResultModel,
      EnrichmentResultModel,
      EnrichmentResultEntity
    )
    await this.entityRepository.save(enrichmentResultEntity)
    return this.mapper.map(
      enrichmentResultEntity,
      EnrichmentResultEntity,
      EnrichmentResultModel
    )
  }

  async findById(
    id: string,
    withDeleted = false
  ): Promise<EnrichmentResultModel | null> {
    if (!id) {
      return null
    }
    const enrichmentResultEntity = await this.entityRepository.findOne({
      where: { id },
      relations: {
        createdBy: true,
      },
      withDeleted: withDeleted,
    })
    return enrichmentResultEntity
      ? this.mapper.map(
          enrichmentResultEntity,
          EnrichmentResultEntity,
          EnrichmentResultModel
        )
      : null
  }

  async findByEnrichmentId(
    enrichmentId: string
  ): Promise<EnrichmentResultWithEmailsCheckResult[]> {
    if (!enrichmentId) {
      return []
    }

    const rawAndEntities = await this.entityRepository
      .createQueryBuilder('enrichments_results')
      .leftJoin(
        'contacts',
        'contacts',
        'contacts.id = enrichments_results.contact_id::uuid'
      )
      .addSelect('contacts.emails_check_result')
      .where('enrichments_results.enrichment_id = :enrichmentId', {
        enrichmentId,
      })
      .getRawAndEntities()

    const emailsCheckResultDictionary = Object.fromEntries(
      rawAndEntities.raw.map(raw => [
        raw.enrichments_results_id,
        raw.emails_check_result,
      ])
    )

    return rawAndEntities.entities.map(enrichmentResultEntity => {
      const enrichmentResultModel = this.mapper.map(
        enrichmentResultEntity,
        EnrichmentResultEntity,
        EnrichmentResultModel
      )
      return Object.assign(enrichmentResultModel, {
        emailsCheckResult:
          emailsCheckResultDictionary[enrichmentResultModel.id],
      })
    })
  }

  async countByNewDataFoundForUserAndDate(
    organizationId: string,
    userId: string,
    startDate?: Date
  ): Promise<number> {
    if (!organizationId || !userId) {
      return null
    }

    const queryBuilder = this.entityRepository
      .createQueryBuilder()
      .select()
      .where('created_by_id = :userId', { userId })
      .andWhere('(has_new_email IS TRUE OR has_new_phone IS TRUE)')
      .andWhere('organization_id = :organizationId', { organizationId })

    if (startDate) {
      queryBuilder.andWhere('created_at >= :startDate', { startDate })
    }

    return await queryBuilder.getCount()
  }

  async getEnrichmentsMetrics(
    enrichmentId: string
  ): Promise<EnrichmentResultMetrics> {
    //get sum of creditUsed, sum of nbEnrichmentsProcessed, sum of nbEnrichmentsEstimated
    if (!enrichmentId) {
      return
    }

    const enrichmentMetrics = await this.entityRepository
      .createQueryBuilder()
      .select('SUM(credit_used)', 'creditUsed')
      .addSelect('SUM(success::integer)', 'nbSuccess')
      .addSelect('SUM(partial_success::integer)', 'nbPartialSuccess')
      .addSelect('SUM(partial_email_success::integer)', 'nbPartialEmailSuccess')
      .addSelect('SUM(partial_phone_success::integer)', 'nbPartialPhoneSuccess')
      .addSelect('SUM(failed::integer)', 'nbFailed')
      .where('enrichment_id = :enrichmentId', {
        enrichmentId,
      })
      .getRawOne<{
        creditUsed: string
        nbSuccess: string
        nbPartialSuccess: string
        nbPartialEmailSuccess: string
        nbPartialPhoneSuccess: string
        nbFailed: string
      }>()

    if (enrichmentMetrics) {
      return {
        creditUsed: parseInt(enrichmentMetrics.creditUsed) || 0,
        nbSuccess: parseInt(enrichmentMetrics.nbSuccess) || 0,
        nbPartialSuccess: parseInt(enrichmentMetrics.nbPartialSuccess) || 0,
        nbPartialEmailSuccess:
          parseInt(enrichmentMetrics.nbPartialEmailSuccess) || 0,
        nbPartialPhoneSuccess:
          parseInt(enrichmentMetrics.nbPartialPhoneSuccess) || 0,
        nbFailed: parseInt(enrichmentMetrics.nbFailed) || 0,
      }
    }
  }
}
