import { createMap, Mapper, MappingProfile } from '@automapper/core'
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs'
import { Injectable } from '@nestjs/common'
import { WorkflowEntity } from '../../../integration/captain-data/infrastructure/entity/workflow.entity'
import { WorkflowModel } from '../../../integration/captain-data/domain/model/workflow.model'
import { UserEntity } from '../../../shared/infrastructure/entity/user.entity'
import { UserModel } from '../../../shared/domain/model/user.model'

@Injectable()
export class CaptainDataProfile extends AutomapperProfile {
  constructor(@InjectMapper() readonly mapper: Mapper) {
    super(mapper)
  }

  get profile(): MappingProfile {
    return mapper => {
      createMap(mapper, WorkflowEntity, WorkflowModel)
      createMap(mapper, UserEntity, UserModel)
    }
  }
}
