import { BullModule } from '@nestjs/bull'
import { Module } from '@nestjs/common'
import { CqrsModule } from '@nestjs/cqrs'
import { ElasticsearchService } from '@nestjs/elasticsearch'
import { EventEmitterModule } from '@nestjs/event-emitter'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ViewEntity } from './infrastructure/entity/view.entity'
import { CreditTransactionService } from '../credit/application/service/credit-transaction.service'
import { CreditModule } from '../credit/credit.module'
import { FeaturesModule } from '../feature/features.module'
import { WorkflowEntity } from '../integration/captain-data/infrastructure/entity/workflow.entity'
import { IntegrationCaptainDataModule } from '../integration/captain-data/integration-captain-data.module'
import { ORGANIZATION_REPOSITORY_INTERFACE } from '../organization/domain/organization-repository.interface'
import { OrganizationEntity } from '../organization/infrastructure/entity/organization.entity'
import { OrganizationRepository } from '../organization/infrastructure/repository/organization.repository'
import { AsyncActionEstimateTimeToFinishService } from '../shared/application/service/async-action-estimate-time-to-finish.service'
import { AsyncActionService } from '../shared/application/service/async-action.service'
import { ASYNC_ACTION_ESTIMATE_TIME_TO_FINISH_SERVICE_INTERFACE } from '../shared/domain/interface/async-action-estimate-time-to-finish-service.interface'
import { UserEntity } from '../shared/infrastructure/entity/user.entity'
import { USER_REPOSITORY_INTERFACE } from '../user/domain/interface/user-repository.interface'
import { UserRepository } from '../user/infrastructure/repository/user.repository'
import { ActivitiesContactHandler } from './application/command/activity/activities-contact/activities-contact.handler'
import { CreateActivityHandler } from './application/command/activity/create-activity/create-activity.handler'
import { UpdateActivityHandler } from './application/command/activity/update-activity/update-activity.handler'
import { ArchiveCompanyHandler } from './application/command/company/archive-company/archive-company.handler'
import { CompanyAssignUsersHandler } from './application/command/company/assign-users/company-assign-users.handler'
import { CreateCompanyBulkHandler } from './application/command/company/create-company-bulk/create-company-bulk.handler'
import { CreateCompanyHandler } from './application/command/company/create-company/create-company.handler'
import { DeleteCompaniesByIdsHandler } from './application/command/company/delete-companies-by-ids/delete-companies-by-ids.handler'

import { UnarchiveCompanyHandler } from './application/command/company/unarchive-company/unarchive-company.handler'
import { CompanyUnassignUsersHandler } from './application/command/company/unassign-users/company-unassign-users.handler'
import { UpdateCompaniesBulkAvailableInWorkspaceHandler } from './application/command/company/update-bulk-companies-available-in-workspace/update-companies-bulk-available-in-workspace.handler'
import { UpdateBulkCompaniesExternalTemplateUrlHandler } from './application/command/company/update-bulk-companies-external-url-template/update-bulk-companies-external-url-template.handler'
import { UpdateCompaniesBulkLeadImportIdsHandler } from './application/command/company/update-bulk-companies-lead-import-ids/update-companies-bulk-lead-import-ids.handler'
import { UpdateCompanyHandler } from './application/command/company/update-company/update-company.handler'
import { ArchiveContactHandler } from './application/command/contact/archive-contact/archive-contact.handler'
import { AssignContactsHandler } from './application/command/contact/assign-contacts/assign-contacts.handler'
import { CreateContactBulkHandler } from './application/command/contact/create-contact-bulk/create-contact-bulk.handler'
import { CreateContactHandler } from './application/command/contact/create-contact/create-contact.handler'
import { DeleteContactsByIdsHandler } from './application/command/contact/delete-contacts-by-ids/delete-contacts-by-ids.handler'
import { DeleteContactsNotAvailableInWorkspaceHandler } from './application/command/contact/delete-contacts-by-lead-import-id/delete-contacts-by-lead-import-id.handler'
import { GenerateStrategyHandler } from './application/command/contact/generate-strategy/generate-strategy.handler'
import { IndexContactsCommandHandler } from './application/command/contact/index-contacts/index-contacts.command'
import { UnarchiveContactHandler } from './application/command/contact/unarchive-contact/unarchive-contact.handler'
import { UnassignContactsHandler } from './application/command/contact/unassign-contacts/unassign-contacts.handler'
import { UpdateContactsBulkAvailableInWorkspaceHandler } from './application/command/contact/update-bulk-contacts-available-in-workspace/update-contacts-bulk-available-in-workspace.handler'
import { UpdateContactsBulkEnrichmentStatusHandler } from './application/command/contact/update-bulk-contacts-enrichment-status/update-contacts-bulk-enrichment-status.handler'
import { UpdateBulkContactsExternalTemplateUrlHandler } from './application/command/contact/update-bulk-contacts-external-url-template/update-bulk-contacts-external-url-template.handler'
import { UpdateContactsBulkLeadImportIdsHandler } from './application/command/contact/update-bulk-contacts-lead-import-ids/update-contacts-bulk-lead-import-ids.handler'
import { UpdateContactBulkHandler } from './application/command/contact/update-contact-bulk/update-contact-bulk.handler'
import { UpdateContactFeedbackHandler } from './application/command/contact/update-contact-feedback/update-contact-feedback.handler'
import { UpdateContactStatusByEmailHandler } from './application/command/contact/update-contact-status/update-contact-status-by-email.handler'
import { UpdateContactStrategyHandler } from './application/command/contact/update-contact-strategy/update-contact-strategy.handler'
import { UpdateContactHandler } from './application/command/contact/update-contact/update-contact.handler'
import { CreateCustomFieldHandler } from './application/command/custom-field/create-custom-field/create-custom-field.handler'
import { DeleteCustomFieldHandler } from './application/command/custom-field/delete-custom-field/delete-custom-field.handler'
import { UpdateCustomFieldHandler } from './application/command/custom-field/update-custom-field/update-custom-field.handler'
import { AddJobsToEnrichmentQueueHandler } from './application/command/enrichment/add-jobs-to-enrichment-queue/add-jobs-to-enrichment-queue.handler'
import { CreateApiEnrichmentHandler } from './application/command/enrichment/create-api-enrichment/create-api-enrichment.handler'
import { CreateEnrichmentHandler } from './application/command/enrichment/create-enrichment/create-enrichment.handler'
import { GetEnrichmentProvidersHandler } from './application/command/enrichment/get-enrichment-providers/get-enrichment-providers.handler'
import { HandleEnrichmentCompletedHandler } from './application/command/enrichment/handle-enrichment-completed/handle-enrichment-completed.handler'
import { PersistEnrichmentHandler } from './application/command/enrichment/persist-enrichment/persist-enrichment.handler'
import { ProcessEnrichmentJobHandler } from './application/command/enrichment/process-enrichment-job/process-enrichment-job.handler'
import { ReplayEnrichmentHandler } from './application/command/enrichment/replay-enrichment/replay-enrichment.handler'
import { UpdateEnrichmentHandler } from './application/command/enrichment/update-enrichment/update-enrichment.handler'
import { CreateLeadExportHandler } from './application/command/lead-export/create-lead-export/create-lead-export.handler'
import { CreateLinkedinActionCommandHandler } from './application/command/lead-import/create-linkedin-action.handler'
import { ImportFromZeliqSearchHandler } from './application/command/lead-import/import-leads-from-zeliq-search-command'
import { ReindexCompaniesZeliqHandler } from './application/command/reindex/reindex-companies-zeliq/reindex-companies-zeliq.handler'
import { ReindexCompaniesHandler } from './application/command/reindex/reindex-companies/reindex-companies.handler'
import { ReindexContactsZeliqHandler } from './application/command/reindex/reindex-contacts-zeliq/reindex-contacts-zeliq.handler'
import { ReindexContactsHandler } from './application/command/reindex/reindex-contacts/reindex-contacts.handler'
import { CreateSearchRequestHandler } from './application/command/search/create-search-request/create-search-request.handler'
import { DeleteSearchRequestHandler } from './application/command/search/delete-search-request/delete-search-request.handler'
import { UpdateSearchRequestHandler } from './application/command/search/update-search-request/update-search-request.handler'
import { UpdateCrmStatusHandler } from './application/command/update-crm-status/update-crm-status.handler'
import { CreateViewHandler } from './application/command/view/create-view/create-view.handler'
import { DeleteViewHandler } from './application/command/view/delete-view/delete-view.handler'
import { UpdateViewHandler } from './application/command/view/update-view/update-view.handler'
import { ImportCSVConsumer } from './application/consumer/csv-importation.consumer'
import { EnrichmentAutoQueueConsumer } from './application/consumer/enrichment-auto-queue.consumer'
import { EnrichmentCompletionConsumer } from './application/consumer/enrichment-completion.consumer'
import { EnrichmentNotifConsumer } from './application/consumer/enrichment-notif.consumer'
import { EnrichmentPhoneQueueConsumer } from './application/consumer/enrichment-phone-queue.consumer'
import { EnrichmentQueueConsumer } from './application/consumer/enrichment-queue.consumer'
import { EnrichmentWebhookCallbackConsumer } from './application/consumer/enrichment-webhook-callback.consumer'
import { GeneratorStrategyConsumer } from './application/consumer/generator-strategy.consumer'
import { LeadExportConsumer } from './application/consumer/lead-export.consumer'
import { LeadIndexBulkConsumer } from './application/consumer/lead-index-bulk.consumer'
import { LeadIndexConsumer } from './application/consumer/lead-index.consumer'
import { ProcessCompaniesReindexRequiredCron } from './application/cron/process-companies-reindex-required.cron'
import { ProcessContactsReindexRequiredCron } from './application/cron/process-contacts-reindex-required.cron'
import { ActivityWebsocketListener } from './application/listener/activity/activity/activity-websocket.listener'

import { CallCreatedListener } from './application/listener/activity/call/call-created.listener'
import { CallUpdatedListener } from './application/listener/activity/call/call-updated.listener'
import { ContactAttributUpdatedListener } from './application/listener/activity/contact/contact-attribut-updated.listener'
import { ContactStatusUpdatedListener } from './application/listener/activity/contact/contact-status-updated.listener'
import { GeneratedStrategyWebsocketListener } from './application/listener/activity/contact/generated-strategy-websocket.listener'
import { MessageSentListener } from './application/listener/activity/mail/message-sent.listener'
import { NoteCreatedListener } from './application/listener/activity/note/note-created.listener'
import { NoteUpdatedListener } from './application/listener/activity/note/note-updated.listener'
import { ContactArchivedListener } from './application/listener/contact-archived.listener'
import { ContactCreatedListener } from './application/listener/contact-created.listener'
import { ContactEmailsDebounceRequestedListener } from './application/listener/contact-emails-debounce-requested.listener'
import { CsvImportationListener } from './application/listener/csv-importation/csv-importation.listener'
import { EngagementScoringListener } from './application/listener/engagement-scoring.listener'
import { EnrichmentStatusUpdatedListener } from './application/listener/enrichment/enrichment-status-updated.listener'
import { LeadEnrichmentListener } from './application/listener/enrichment/lead-enrichment.listener'
import { LeadExportCreatedListener } from './application/listener/lead-export/lead-export-created.listener'
import { LeadExportStatusUpdatedListener } from './application/listener/lead-export/lead-export-status-updated.listener'
import { LinkedinCompaniesSearchReceivedListener } from './application/listener/linkedin-actions/linkedin-companies-search-received.listener'
import { LinkedinPeopleSearchReceivedListener } from './application/listener/linkedin-actions/linkedin-people-search-received.listener'
import { LinkedInPostCommentersExtractionReceivedListener } from './application/listener/linkedin-actions/linkedin-post-commenters-extraction-received.event'
import { LinkedInPostLikersAndCommentersDeduplicationListener } from './application/listener/linkedin-actions/linkedin-post-likers-and-commenters-deduplication.listener'
import { LinkedInPostLikersExtractionReceivedListener } from './application/listener/linkedin-actions/linkedin-post-likers-extraction-received.listener'
import { LinkedInPostLikersAndCommentersImportListener } from './application/listener/linkedin-post-likers-and-commenters-import.listener'
import { LinkedinSearchImportListener } from './application/listener/linkedin-search-import-listener'
import { ContactStatsCallUpdatedListener } from './application/listener/stats/contact-stats-call-updated.listener'
import { TaskCreatedListener } from './application/listener/task-created.listener'
import { TaskDeletedListener } from './application/listener/task-deleted.listener'
import { TaskUpdatedListener } from './application/listener/task-updated.listener'
import { ZeliqSearchImportListener } from './application/listener/zeliq-search-import.listener'
import { ActivityProfile } from './application/profile/activity.profile'
import { CompanyUserProfile } from './application/profile/company-user.profile'
import { CompanyZeliqProfile } from './application/profile/company-zeliq.profile'
import { CompanyProfile } from './application/profile/company.profile'
import { ContactStatusHistoryProfile } from './application/profile/contact-status-history.profile'
import { ContactZeliqOptOutProfile } from './application/profile/contact-zeliq-opt-out.profile'
import { ContactZeliqProfile } from './application/profile/contact-zeliq.profile'
import { ContactProfile } from './application/profile/contact.profile'
import { CSVImportProfile } from './application/profile/csv-import.profile'
import { CustomFieldProfile } from './application/profile/custom-field.profile'
import { EnrichmentQueryProfile } from './application/profile/enrichment-query.profile'
import { EnrichmentResultProfile } from './application/profile/enrichment-result.profile'
import { EnrichmentProfile } from './application/profile/enrichment.profile'
import { ExportCsvProfile } from './application/profile/export-csv.profile'
import { JobTitleProfile } from './application/profile/job-title.profile'
import { LeadExportProfile } from './application/profile/lead-export.profile'
import { SearchNewPaginationProfile } from './application/profile/search-new-pagination.profile'
import { SearchPaginationProfile } from './application/profile/search-pagination.profile'
import { SearchRequestProfile } from './application/profile/search-request.profile'
import { ViewProfile } from './application/profile/view.profile'
import { CountOrganizationCompaniesHandler } from './application/query/company/count-organization-companies/count-organization-companies.handler'
import { FindActivitesByCompanyHandler } from './application/query/company/find-activities-by-company/find-activities-by-company.handler'
import { FindCompanyIdsByLeadImportIdHandler } from './application/query/company/find-company-ids-by-lead-import-id/find-company-ids-by-lead-import-id.handler'
import { CountContactsByAssignHandler } from './application/query/contact/count-contacts-by-assign/count-contacts-by-assign.handler'
import { CountLeadsAddedHandler } from './application/query/contact/count-leads-added/count-leads-added.handler'
import { CountLeadsEnrichedInEnrichmentHubHandler } from './application/query/contact/count-leads-enriched-in-enrichment-hub/count-leads-enriched-in-enrichment-hub.handler'
import { CountLeadsInLeadImportHandler } from './application/query/contact/count-leads-in-lead-import/count-leads-in-lead-import.handler'
import { CountOrganizationContactHandler } from './application/query/contact/count-organization-contact/count-organization-contact.handler'
import { CountUserAssignedContactHandler } from './application/query/contact/count-user-assigned-contact/count-user-assigned-contact.handler'
import { CountWeeklySuccessContactsByAssignHandler } from './application/query/contact/count-weekly-success-contacts-by-assign/count-weekly-success-contacts-by-assign.handler'
import { FindByCompanyAndAssignHandler } from './application/query/contact/find-by-company-and-assign/find-by-company-and-assign.handler'
import { FindCompaniesIdsByContactIdsHandler } from './application/query/contact/find-companies-ids-by-contact-ids/find-companies-ids-by-contact-ids.handler'
import { FindContactByExternalIdHandler } from './application/query/contact/find-contact-by-external-id/find-contact-by-external-id.handler'
import { FindContactIdsByLeadImportIdHandler } from './application/query/contact/find-contact-ids-by-lead-import-id/find-contact-ids-by-lead-import-id.handler'
import { FindContactsByLeadImportIdHandler } from './application/query/contact/find-contacts-by-lead-import-id/find-contacts-by-lead-import-id.handler'
import { FindOptOutStatusHandler } from './application/query/contact/find-opt-out-status/find-opt-out-status.handler'
import { FindAllCustomFieldHandler } from './application/query/custom-field/find-all-custom-field/find-all-custom-field.handler'
import { FindCustomFieldHandler } from './application/query/custom-field/find-custom-field/find-custom-field.handler'
import { CountEnrichmentsByOrganizationHandler } from './application/query/enrichment/count-enrichments-by-organization/count-enrichments-by-organization.handler'
import { EstimateEnrichmentHubTimeOfCompletionHandler } from './application/query/enrichment/estimate-enrichment-hub-time-of-completion/estimate-enrichment-hub-time-of-completion.handler'
import { EstimateEnrichmentTimeOfCompletionHandler } from './application/query/enrichment/estimate-enrichment-time-of-completion/estimate-enrichment-time-of-completion.handler'
import { FindEnrichmentHandler } from './application/query/enrichment/find-enrichment/find-enrichment.handler'
import { FindEnrichmentsByEnrichmentHubIdHandler } from './application/query/enrichment/find-enrichments-by-enrichment-hub-id/find-enrichments-by-enrichment-hub-id.handler'
import { FindEnrichmentsByOrganizationHandler } from './application/query/enrichment/find-enrichments-by-organization/find-enrichments-by-organization.handler'
import { FindEnrichmentsResultsByEnrichmentIdHandler } from './application/query/enrichment/find-enrichments-results-by-enrichment-id/find-enrichments-results-by-enrichment-id.handler'
import { GetEnrichmentAsyncActionLabelHandler } from './application/query/enrichment/get-enrichment-async-action-label/get-enrichment-async-action-label.handler'
import { GetEnrichmentsMetricsHandler } from './application/query/enrichment/get-enrichments-metrics/get-enrichments-metrics.handler'
import { GetMetricsByEnrichmentHubHandler } from './application/query/enrichment/get-metrics-by-enrichment-hub/get-metrics-by-enrichment-hub.handler'
import { GetMetricsForOneEnrichmentHandler } from './application/query/enrichment/get-metrics-for-one-enrichment/get-metrics-for-one-enrichment.handler'
import { GetNbLeadsToEnrichInQueueUntilDateHandler } from './application/query/enrichment/get-nb-leads-to-enrich-in-queue/get-nb-leads-to-enrich-in-queue-until-date.handler'
import { HasNewEnrichmentDataAvailableQueryHandler } from './application/query/enrichment/has-new-enrichment-data-available/has-new-enrichment-data-available.query'
import { NbAvailableEnrichmentsHandler } from './application/query/enrichment/nb-available-enrichments/nb-available-enrichments.handler'
import { FindActivityByResourceIdHandler } from './application/query/find-activity/find-activity-by-resource-id.handler'
import { FindByOrganizationAndIdHandler } from './application/query/find-by-organization-and-id/find-by-organization-and-id.handler'
import { FindByOrganizationAndUserHandler } from './application/query/find-by-organization-and-user/find-by-organization-and-user.handler'
import { FindByUserAndEmailHandler } from './application/query/find-by-user-and-email/find-by-user-and-email.handler'
import { FindCompanyByNameHandler } from './application/query/find-company-by-name/find-company-by-name.handler'
import { FindCompanyHandler } from './application/query/find-company/find-company.handler'
import { FindContactByOrganizationAndEmailHandler } from './application/query/find-contact-by-organization-and-email/find-contact-by-organization-and-email.handler'
import { FindContactByPhoneHandler } from './application/query/find-contact-by-phone/find-contact-by-phone.handler'
import { FindContactStatusHistoryHandler } from './application/query/find-contact-status-history/find-contact-status-history.handler'
import { FindContactHandler } from './application/query/find-contact/find-contact.handler'
import { FindContactsByIdHandler } from './application/query/find-contacts-by-id/find-contacts-by-id.handler'
import { FindMailActivityByPayloadHandler } from './application/query/find-mail-activity-by-payload/find-mail-activity-by-payload.handler'
import { FindSequenceActivityByPayloadHandler } from './application/query/find-sequence-activity-by-payload/find-sequence-activity-by-payload.handler'
import { GetWeeklyRecapLeadsMetricsHandler } from './application/query/get-weekly-recap-leads-metrics/get-weekly-recap-leads-metrics.handler'
import { FindAllJobTitleHandler } from './application/query/job-title/find-all-job-title/find-all-job-title.handler'
import { EstimateLeadsExportTimeOfCompletionHandler } from './application/query/leads-export/estimate-leads-export-time-of-completion/estimate-leads-export-time-of-completion.handler'
import { FindLeadsExportByIdHandler } from './application/query/leads-export/find-leads-export-by-id/find-leads-export-by-id.handler'
import { GetCompanyExportableFieldsHandler } from './application/query/leads-export/get-company-exportable-fields/get-company-exportable-fields.handler'
import { GetContactExportableFieldsHandler } from './application/query/leads-export/get-contact-exportable-fields/get-contact-exportable-fields.handler'
import { GetLeadExportAsyncActionLabelHandler } from './application/query/leads-export/get-lead-export-async-action-label/get-lead-export-async-action-label.handler'
import { ListFieldHandler } from './application/query/list-field/list-field.handler'
import { GetSdrLeadsMetricsHandler } from './application/query/metric/sdr-leads/get-sdr-leads-metrics.handler'
import { SearchCompanyByDomainHandler } from './application/query/search-company-by-domain/search-company-by-domain.handler'
import { SearchIndexCompanyHandler } from './application/query/search-company/search-index-company.handler'
import { SearchIndexContactHandler } from './application/query/search-contact/search-index-contact.handler'
import { SearchDistinctValueCompanyHandler } from './application/query/search-distinct-value-company/search-distinct-value-company.handler'
import { SearchDistinctValueContactHandler } from './application/query/search-distinct-value-contact/search-distinct-value-contact.handler'
import { SearchNewCompanyHandler } from './application/query/search-new-company/search-new-company.handler'
import { SearchNewContactHandler } from './application/query/search-new-contact/search-new-contact.handler'
import { SearchNewFilterAutocompleteHandler } from './application/query/search-new-filter-autocomplete/search-new-filter-autocomplete.handler'
import { FindSearchRequestHandler } from './application/query/search/find-search-request/find-search-request.handler'
import { FindUserOrganizationSearchRequestsHandler } from './application/query/search/find-user-organization-search-requests/find-user-organization-search-requests.handler'
import { GetSearchPaginationQueryObjectsHandler } from './application/query/search/get-search-pagination-search-query-objects/get-search-pagination-query-objects.handler'
import { FindDefaultViewHandler } from './application/query/view/find-default-view/find-default-view.handler'
import { FindUserOrganizationViewsHandler } from './application/query/view/find-user-organization-views/find-user-organization-views.handler'
import { FindViewByNameHandler } from './application/query/view/find-view-by-name/find-view-by-name.handler'
import { FindViewHandler } from './application/query/view/find-view/find-view.handler'
import { CompanyDomainService } from './application/service/company-domain/company-domain.service'
import { CompanyMatchingService } from './application/service/company-matching/company-matching.service'
import { CompanyService } from './application/service/company.service'
import { ContactService } from './application/service/contact/contact.service'
import { CustomFieldService } from './application/service/custom-field.service'
import { DuplicateContactService } from './application/service/duplicate-contact.service'
import { EngagementScoringCron } from './application/service/engagement-scoring.cron'
import { EnrichmentNotifContactService } from './application/service/enrichment-notif/enrichment-notif-contact.service'
import { EnrichmentNotifCronService } from './application/service/enrichment-notif/enrichment-notif-cron.service'
import { EnrichmentResponseService } from './application/service/enrichment-response.service'
import { EnrichCompanyOptimizedService } from './application/service/enrichment/enrich-company-optimized.service'
import { EnrichContactOptimizedService } from './application/service/enrichment/enrich-contact-optimized.service'
import { EnrichTransactionService } from './application/service/enrichment/enrich-transaction.service'
import { EnrichService } from './application/service/enrichment/enrich.service'
import { EnrichmentProviderService } from './application/service/enrichment/enrichment-provider.service'
import { EnrichmentQueueService } from './application/service/enrichment/enrichment-queue.service'
import { IndexCompanyZeliqService } from './application/service/enrichment/index-company-zeliq.service'
import { IndexContactZeliqService } from './application/service/enrichment/index-contact-zeliq.service'
import { ExportCsvService } from './application/service/export-csv/export-csv.service'
import { IndexCompanyService } from './application/service/index-company.service'
import { IndexContactService } from './application/service/index-contact.service'
import { IndexQueueConsumerService } from './application/service/index-queue-consumer.service'
import { IndexQueueService } from './application/service/index-queue.service'
import { OptOutMonitoringCron } from './application/service/opt-out/opt-out-monitoring.cron'
import { PlanCompanyService } from './application/service/plan/plan-company.service'
import { PlanContactService } from './application/service/plan/plan-contact.service'
import { SearchNewLeadService } from './application/service/search-new-lead.service'
import { ViewService } from './application/service/view.service'
import { DATA_PROVIDER_ZELIQ_DATA_SERVICE } from './domain/data-provider-zeliq-data-service.interface'
import { LEAD_EXPORT_REPOSITORY_INTERFACE } from './domain/interface/lead-export-repository.interface'
import { LINKEDIN_ACTION_SCHEDULER } from './domain/interface/linkedin-action-scheduler.interface'
import { ACTIVITY_REPOSITORY_INTERFACE } from './domain/model/activity-repository.interface'
import { COMPANY_REPOSITORY_INTERFACE } from './domain/model/company-repository.interface'
import { COMPANY_USER_REPOSITORY_INTERFACE } from './domain/model/company-user-repository.interface'
import { COMPANY_ZELIQ_REPOSITORY_INTERFACE } from './domain/model/company-zeliq-repository.interface'
import { CONTACT_REPOSITORY_INTERFACE } from './domain/model/contact-repository.interface'
import { CONTACT_STATUS_HISTORY_REPOSITORY_INTERFACE } from './domain/model/contact-status-history.interface'
import { CONTACT_ZELIQ_OPT_OUT_REPOSITORY_INTERFACE } from './domain/model/contact-zeliq-opt-out-repository.interface'
import { CONTACT_ZELIQ_REPOSITORY_INTERFACE } from './domain/model/contact-zeliq-repository.interface'
import { CUSTOM_FIELD_REPOSITORY_INTERFACE } from './domain/model/custom-field-repository.interface'
import { ENRICHMENT_METRIC_REPOSITORY_INTERFACE } from './domain/model/enrichment-metric-repository.interface'
import { ENRICHMENT_QUERY_REPOSITORY_INTERFACE } from './domain/model/enrichment-query-repository.interface'
import { ENRICHMENT_REPOSITORY_INTERFACE } from './domain/model/enrichment-repository.interface'
import { ENRICHMENT_RESULT_REPOSITORY_INTERFACE } from './domain/model/enrichment-result-repository.interface'
import { JOB_TITLE_REPOSITORY_INTERFACE } from './domain/model/job-title-repository.interface'
import { LEAD_METRIC_REPOSITORY_INTERFACE } from './domain/model/lead-metric-repository.interface'
import { SEARCH_REQUEST_REPOSITORY_INTERFACE } from './domain/model/search-request-repository.interface'
import { VIEW_REPOSITORY_INTERFACE } from './domain/model/view-repository.interface'
import { UrlIdExtractor } from './domain/service/url-id-extractor'
import { ActivityEntity } from './infrastructure/entity/activity.entity'
import { CompanyUserEntity } from './infrastructure/entity/company-user.entity'
import { CompanyZeliqEntity } from './infrastructure/entity/company-zeliq.entity'
import { CompanyEntity } from './infrastructure/entity/company.entity'
import { ContactStatusHistoryEntity } from './infrastructure/entity/contact-status-history.entity'
import { ContactZeliqOptOutEntity } from './infrastructure/entity/contact-zeliq-opt-out.entity'
import { ContactZeliqEntity } from './infrastructure/entity/contact-zeliq.entity'
import { ContactEntity } from './infrastructure/entity/contact.entity'
import { CustomFieldEntity } from './infrastructure/entity/custom-field.entity'
import { EnrichmentMetricEntity } from './infrastructure/entity/enrichment-metric.entity'
import { EnrichmentQueryEntity } from './infrastructure/entity/enrichment-query.entity'
import { EnrichmentResultEntity } from './infrastructure/entity/enrichment-result.entity'
import { EnrichmentEntity } from './infrastructure/entity/enrichment.entity'
import { JobTitleEntity } from './infrastructure/entity/job-title.entity'
import { LeadExportEntity } from './infrastructure/entity/lead-export.entity'
import { LeadMetricEntity } from './infrastructure/entity/lead-metric.entity'
import { SearchRequestEntity } from './infrastructure/entity/search-request.entity'
import {
  CONTACT_EMAILS_CHECK_QUEUE,
  ENRICHMENT_AUTO_QUEUE,
  ENRICHMENT_COMPLETION_QUEUE,
  ENRICHMENT_NOTIF_QUEUE,
  ENRICHMENT_PHONE_QUEUE,
  ENRICHMENT_QUEUE,
  ENRICHMENT_WEBHOOK_CALLBACK_QUEUE,
  GENERATE_STRATEGY_QUEUE,
  IMPORT_CSV_QUEUE,
  LEAD_EXPORT_QUEUE,
  LEAD_INDEX_BULK_QUEUE,
  LEAD_INDEX_QUEUE,
} from './infrastructure/global'
import { CaptainDataProfile } from './infrastructure/mappings/captain-data.profile'
import { ActivityRepository } from './infrastructure/repository/activity.repository'
import { CaptainDataLinkedinActionScheduler } from './infrastructure/repository/captain-data-linkedin-action-scheduler'
import { CompanyUserRepository } from './infrastructure/repository/company-user-repository'
import { CompanyZeliqRepository } from './infrastructure/repository/company-zeliq.repository'
import { CompanyRepository } from './infrastructure/repository/company.repository'
import { ContactStatusHistoryRepository } from './infrastructure/repository/contact-status-history.repository'
import { ContactZeliqOptOutRepository } from './infrastructure/repository/contact-zeliq-opt-out.repository'
import { ContactZeliqRepository } from './infrastructure/repository/contact-zeliq.repository'
import { ContactRepository } from './infrastructure/repository/contact.repository'
import { CustomFieldRepository } from './infrastructure/repository/custom-field.repository'
import { EnrichmentMetricRepository } from './infrastructure/repository/enrichment-metric.repository'
import { EnrichmentQueryRepository } from './infrastructure/repository/enrichment-query.repository'
import { EnrichmentResultRepository } from './infrastructure/repository/enrichment-result.repository'
import { EnrichmentRepository } from './infrastructure/repository/enrichment.repository'
import { JobTitleRepository } from './infrastructure/repository/job-title.repository'
import { LeadExportRepository } from './infrastructure/repository/lead-export.repository'
import { LeadMetricRepository } from './infrastructure/repository/lead-metrics.repository'
import { SearchRequestRepository } from './infrastructure/repository/search-request.repository'
import { ViewRepository } from './infrastructure/repository/view.repository'
import DataProviderAnymailfinderService from './infrastructure/service/data-provider/anymailfinder-provider/data-provider-anymailfinder.service'
import { DataProviderFindymailService } from './infrastructure/service/data-provider/data-provider-findymail.service'
import { DataProviderForagerService } from './infrastructure/service/data-provider/data-provider-forager.service'
import { DataProviderProspeoService } from './infrastructure/service/data-provider/data-provider-prospeo.service'
import { DataProviderRocketreachService } from './infrastructure/service/data-provider/data-provider-rocketreach.service'
import { DataProviderZeliqAsyncService } from './infrastructure/service/data-provider/data-provider-zeliq-async.service'
import { DataProviderZeliqDataService } from './infrastructure/service/data-provider/data-provider-zeliq-data.service'
import { DataProviderZeliqDataServiceMock } from './infrastructure/service/data-provider/data-provider-zeliq-data.service.mock'
import { DataProviderZeliqOpsService } from './infrastructure/service/data-provider/data-provider-zeliq-ops.service'
import { DataProviderZeliqWaterfallCompanyService } from './infrastructure/service/data-provider/data-provider-zeliq-waterfall-company.service'
import { DataProviderZeliqService } from './infrastructure/service/data-provider/data-provider-zeliq.service'
import { DataProviderPdlService } from './infrastructure/service/data-provider/pdl-provider/data-provider-pdl.service'
import { EnrichmentNotifSearchService } from './infrastructure/service/enrichment-notif/enrichment-notif-search.service'
import EnrichmentDataProviderAnymailfinderService from './infrastructure/service/enrichment/enrichment-data-provider-anymailfinder.service'
import { EnrichmentDataProviderDatagmaService } from './infrastructure/service/enrichment/enrichment-data-provider-datagma.service'
import { EnrichmentDataProviderFindymailService } from './infrastructure/service/enrichment/enrichment-data-provider-findymail.service'
import { EnrichmentDataProviderForagerService } from './infrastructure/service/enrichment/enrichment-data-provider-forager.service'
import { EnrichmentDataProviderPDLService } from './infrastructure/service/enrichment/enrichment-data-provider-pdl.service'
import { EnrichmentDataProviderProspeoService } from './infrastructure/service/enrichment/enrichment-data-provider-prospeo.service'
import { EnrichmentDataProviderRocketreachService } from './infrastructure/service/enrichment/enrichment-data-provider-rocketreach.service'
import { EnrichmentDataProviderZeliqAsyncService } from './infrastructure/service/enrichment/enrichment-data-provider-zeliq-async.service'
import { EnrichmentDataProviderZeliqDataService } from './infrastructure/service/enrichment/enrichment-data-provider-zeliq-data.service'
import { EnrichmentDataProviderZeliqOpsService } from './infrastructure/service/enrichment/enrichment-data-provider-zeliq-ops.service'
import { EnrichmentDataProviderZeliqWaterfallCompanyService } from './infrastructure/service/enrichment/enrichment-data-provider-zeliq-waterfall-company'
import { EnrichmentDataProviderZeliqService } from './infrastructure/service/enrichment/enrichment-data-provider-zeliq.service'
import { FileService } from './infrastructure/service/file-service/file.service'
import { SearchDuplicateContactService } from './infrastructure/service/search-duplicate-contact.service'
import { SearchDataProviderZeliqService } from './infrastructure/service/search-new-contact/search-data-provider-zeliq.service'
import { FindCompaniesByLeadImportIdHandler } from './application/query/company/find-companies-by-lead-import-id/find-companies-by-lead-import-id.handler'

// Import all handlers, services, entities, repositories, etc.
// from the lead module

// Import all handlers, services, entities, repositories, etc.
// from the lead module

const profiles = [
  ContactProfile,
  CompanyProfile,
  CSVImportProfile,
  ExportCsvProfile,
  CompanyUserProfile,
  ViewProfile,
  ActivityProfile,
  CustomFieldProfile,
  SearchPaginationProfile,
  EnrichmentProfile,
  ContactZeliqProfile,
  CompanyZeliqProfile,
  EnrichmentQueryProfile,
  ContactStatusHistoryProfile,
  SearchNewPaginationProfile,
  JobTitleProfile,
  SearchRequestProfile,
  EnrichmentResultProfile,
  LeadExportProfile,
  ContactZeliqOptOutProfile,
  CaptainDataProfile,
]

const commandHandlers = [
  CreateContactHandler,
  UpdateContactHandler,
  CreateCompanyHandler,
  AssignContactsHandler,
  UnassignContactsHandler,
  CompanyAssignUsersHandler,
  CompanyUnassignUsersHandler,
  CreateViewHandler,
  UpdateViewHandler,
  DeleteViewHandler,
  ArchiveCompanyHandler,
  UnarchiveCompanyHandler,
  ArchiveContactHandler,
  UnarchiveContactHandler,
  UpdateCompanyHandler,
  ActivitiesContactHandler,
  CreateCustomFieldHandler,
  DeleteCustomFieldHandler,
  UpdateCustomFieldHandler,
  CreateContactBulkHandler,
  CreateCompanyBulkHandler,
  CreateActivityHandler,
  UpdateActivityHandler,
  FindByCompanyAndAssignHandler,
  CreateEnrichmentHandler,
  GenerateStrategyHandler,
  SearchNewFilterAutocompleteHandler,
  UpdateContactStatusByEmailHandler,
  CreateSearchRequestHandler,
  DeleteSearchRequestHandler,
  UpdateSearchRequestHandler,
  UpdateContactBulkHandler,
  UpdateContactFeedbackHandler,
  PersistEnrichmentHandler,
  UpdateContactsBulkEnrichmentStatusHandler,
  HandleEnrichmentCompletedHandler,
  ProcessEnrichmentJobHandler,
  AddJobsToEnrichmentQueueHandler,
  UpdateEnrichmentHandler,
  UpdateContactsBulkAvailableInWorkspaceHandler,
  UpdateCompaniesBulkAvailableInWorkspaceHandler,
  FindCompaniesIdsByContactIdsHandler,
  GetMetricsByEnrichmentHubHandler,
  UpdateContactStrategyHandler,
  UpdateBulkContactsExternalTemplateUrlHandler,
  UpdateBulkCompaniesExternalTemplateUrlHandler,
  ReindexContactsHandler,
  ReindexContactsZeliqHandler,
  ReindexCompaniesHandler,
  ReindexCompaniesZeliqHandler,
  DeleteCompaniesByIdsHandler,
  ReplayEnrichmentHandler,
  DeleteContactsByIdsHandler,
  DeleteContactsNotAvailableInWorkspaceHandler,
  UpdateCrmStatusHandler,
  UpdateContactsBulkLeadImportIdsHandler,
  GetSearchPaginationQueryObjectsHandler,
  CreateLeadExportHandler,
  CreateApiEnrichmentHandler,
  EstimateLeadsExportTimeOfCompletionHandler,
  ImportFromZeliqSearchHandler,
  IndexContactsCommandHandler,
]

const queryHandlers = [
  FindActivityByResourceIdHandler,
  FindByOrganizationAndUserHandler,
  FindByOrganizationAndIdHandler,
  FindCompanyHandler,
  FindContactHandler,
  FindContactByPhoneHandler,
  ListFieldHandler,
  SearchIndexCompanyHandler,
  SearchIndexContactHandler,
  FindViewHandler,
  FindViewByNameHandler,
  FindUserOrganizationViewsHandler,
  FindDefaultViewHandler,
  FindCustomFieldHandler,
  FindAllCustomFieldHandler,
  GetSdrLeadsMetricsHandler,
  SearchDistinctValueContactHandler,
  SearchDistinctValueCompanyHandler,
  FindContactByOrganizationAndEmailHandler,
  SearchNewContactHandler,
  SearchNewCompanyHandler,
  FindCompanyByNameHandler,
  FindAllJobTitleHandler,
  FindContactByExternalIdHandler,
  FindContactByOrganizationAndEmailHandler,
  FindSearchRequestHandler,
  FindUserOrganizationSearchRequestsHandler,
  FindByUserAndEmailHandler,
  FindActivitesByCompanyHandler,
  FindSequenceActivityByPayloadHandler,
  FindContactsByIdHandler,
  FindContactIdsByLeadImportIdHandler,
  CountLeadsAddedHandler,
  CountWeeklySuccessContactsByAssignHandler,
  CountContactsByAssignHandler,
  CountOrganizationContactHandler,
  CountUserAssignedContactHandler,
  SearchCompanyByDomainHandler,
  FindCompanyIdsByLeadImportIdHandler,
  UpdateCompaniesBulkLeadImportIdsHandler,
  FindContactsByLeadImportIdHandler,
  FindCompaniesByLeadImportIdHandler,

  // Enrichment
  EstimateEnrichmentTimeOfCompletionHandler,
  FindEnrichmentHandler,
  FindEnrichmentsByEnrichmentHubIdHandler,
  FindEnrichmentsByOrganizationHandler,
  GetNbLeadsToEnrichInQueueUntilDateHandler,
  GetEnrichmentsMetricsHandler,
  NbAvailableEnrichmentsHandler,
  EstimateEnrichmentHubTimeOfCompletionHandler,
  CountEnrichmentsByOrganizationHandler,
  FindEnrichmentsResultsByEnrichmentIdHandler,
  GetMetricsForOneEnrichmentHandler,
  GetEnrichmentProvidersHandler,
  CountLeadsEnrichedInEnrichmentHubHandler,
  HasNewEnrichmentDataAvailableQueryHandler,
  // Lead Export
  GetLeadExportAsyncActionLabelHandler,

  // Enrichment
  GetEnrichmentAsyncActionLabelHandler,

  // CSV Import
  FindMailActivityByPayloadHandler,

  CountOrganizationCompaniesHandler,
  CountOrganizationContactHandler,
  GetWeeklyRecapLeadsMetricsHandler,
  FindContactStatusHistoryHandler,

  // Leads Export
  FindLeadsExportByIdHandler,
  GetContactExportableFieldsHandler,
  GetCompanyExportableFieldsHandler,

  // Leads Import
  CountLeadsInLeadImportHandler,
  CreateLinkedinActionCommandHandler,

  // Opt-out
  FindOptOutStatusHandler,
]

const services = [
  IndexQueueService,
  IndexCompanyService,
  IndexContactService,
  ViewService,
  CustomFieldService,
  ContactService,
  DuplicateContactService,
  SearchDuplicateContactService,
  EnrichService,
  IndexContactZeliqService,
  IndexCompanyZeliqService,
  EnrichmentDataProviderDatagmaService,
  EnrichmentDataProviderZeliqService,
  CreditTransactionService,
  SearchNewLeadService,
  EnrichmentDataProviderPDLService,
  DataProviderPdlService,
  EnrichmentDataProviderAnymailfinderService,
  DataProviderAnymailfinderService,
  PlanContactService,
  PlanCompanyService,
  SearchDataProviderZeliqService,
  EnrichmentDataProviderProspeoService,
  DataProviderProspeoService,
  EnrichmentDataProviderFindymailService,
  DataProviderFindymailService,
  EnrichmentDataProviderForagerService,
  DataProviderForagerService,
  EnrichmentDataProviderRocketreachService,
  DataProviderRocketreachService,
  ExportCsvService,
  EnrichmentDataProviderZeliqDataService,
  {
    provide: DATA_PROVIDER_ZELIQ_DATA_SERVICE,
    useClass:
      process.env['IS_TEST'] === 'true'
        ? DataProviderZeliqDataServiceMock
        : DataProviderZeliqDataService,
  },
  DataProviderZeliqService,
  DataProviderZeliqWaterfallCompanyService,
  {
    provide: ASYNC_ACTION_ESTIMATE_TIME_TO_FINISH_SERVICE_INTERFACE,
    useClass: AsyncActionEstimateTimeToFinishService,
  },
  AsyncActionService,
  IndexQueueConsumerService,
  EnrichmentNotifSearchService,
  EnrichmentNotifContactService,
  EnrichmentDataProviderZeliqOpsService,
  EnrichmentDataProviderZeliqWaterfallCompanyService,
  DataProviderZeliqOpsService,
  EngagementScoringCron,
  CompanyService,
  EnrichCompanyOptimizedService,
  EnrichContactOptimizedService,
  EnrichTransactionService,
  EnrichmentProviderService,
  EnrichmentQueueService,
  CompanyDomainService,
  CompanyMatchingService,
  EnrichmentDataProviderZeliqAsyncService,
  DataProviderZeliqAsyncService,
  UrlIdExtractor,
  OptOutMonitoringCron,
  EnrichmentResponseService,
  FileService,
]

let consumers = []
if (process.env.IS_CONSUMER_INSTANCE === 'true') {
  consumers = [
    LeadIndexConsumer,
    GeneratorStrategyConsumer,
    EnrichmentAutoQueueConsumer,
    EnrichmentQueueConsumer,
    EnrichmentPhoneQueueConsumer,
    LeadIndexBulkConsumer,
    EnrichmentNotifConsumer,
    LeadExportConsumer,
    EnrichmentCompletionConsumer,
    ImportCSVConsumer,
    EnrichmentWebhookCallbackConsumer,
  ]
}

const listeners = [
  ContactStatsCallUpdatedListener,
  ActivityWebsocketListener,
  CsvImportationListener,
  CallCreatedListener,
  CallUpdatedListener,
  TaskCreatedListener,
  ContactAttributUpdatedListener,
  ContactStatusUpdatedListener,
  MessageSentListener,
  NoteCreatedListener,
  NoteUpdatedListener,
  GeneratedStrategyWebsocketListener,
  LeadEnrichmentListener,
  ContactCreatedListener,
  ContactArchivedListener,
  ContactEmailsDebounceRequestedListener,
  EngagementScoringListener,
  TaskUpdatedListener,
  TaskDeletedListener,
  ZeliqSearchImportListener,
  EnrichmentStatusUpdatedListener,
  LeadExportCreatedListener,
  LeadExportStatusUpdatedListener,
  LinkedinSearchImportListener,
  LinkedinPeopleSearchReceivedListener,
  LinkedinCompaniesSearchReceivedListener,
  LinkedInPostLikersExtractionReceivedListener,
  LinkedInPostCommentersExtractionReceivedListener,
  LinkedInPostLikersAndCommentersImportListener,
  LinkedInPostLikersAndCommentersDeduplicationListener,
]

const cron = [
  ProcessCompaniesReindexRequiredCron,
  ProcessContactsReindexRequiredCron,
  EnrichmentNotifCronService,
]

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ContactEntity,
      CompanyEntity,
      CompanyUserEntity,
      UserEntity,
      ViewEntity,
      ActivityEntity,
      CustomFieldEntity,
      LeadMetricEntity,
      EnrichmentEntity,
      ContactZeliqEntity,
      CompanyZeliqEntity,
      EnrichmentQueryEntity,
      EnrichmentMetricEntity,
      ContactStatusHistoryEntity,
      JobTitleEntity,
      SearchRequestEntity,
      OrganizationEntity,
      EnrichmentResultEntity,
      LeadExportEntity,
      ContactZeliqOptOutEntity,
      WorkflowEntity,
    ]),
    CqrsModule,
    FeaturesModule,
    CreditModule,
    IntegrationCaptainDataModule,
    BullModule.registerQueue(
      {
        name: LEAD_INDEX_QUEUE,
      },
      {
        name: LEAD_INDEX_BULK_QUEUE,
      },
      {
        name: LEAD_EXPORT_QUEUE,
      },
      {
        name: ENRICHMENT_AUTO_QUEUE,
      },
      {
        name: IMPORT_CSV_QUEUE,
      },
      {
        name: ENRICHMENT_QUEUE,
        settings: {
          stalledInterval: 30000 * 3,
        },
        limiter: {
          max: 15,
          duration: 1000,
        },
      },
      {
        name: ENRICHMENT_PHONE_QUEUE,
        settings: {
          stalledInterval: 30000 * 3,
        },
        limiter: {
          max: 15,
          duration: 1000,
        },
      },
      {
        name: GENERATE_STRATEGY_QUEUE,
      },
      {
        name: CONTACT_EMAILS_CHECK_QUEUE,
      },
      {
        name: ENRICHMENT_NOTIF_QUEUE,
      },
      {
        name: ENRICHMENT_COMPLETION_QUEUE,
        settings: {
          stalledInterval: 30000 * 3,
        },
        limiter: {
          max: 15,
          duration: 1000,
        },
      },
      {
        name: ENRICHMENT_WEBHOOK_CALLBACK_QUEUE,
      }
    ),

    EventEmitterModule.forRoot(),
  ],
  providers: [
    {
      provide: CONTACT_REPOSITORY_INTERFACE,
      useClass: ContactRepository,
    },
    {
      provide: COMPANY_REPOSITORY_INTERFACE,
      useClass: CompanyRepository,
    },

    {
      provide: COMPANY_USER_REPOSITORY_INTERFACE,
      useClass: CompanyUserRepository,
    },
    {
      provide: USER_REPOSITORY_INTERFACE,
      useClass: UserRepository,
    },
    {
      provide: VIEW_REPOSITORY_INTERFACE,
      useClass: ViewRepository,
    },
    {
      provide: ACTIVITY_REPOSITORY_INTERFACE,
      useClass: ActivityRepository,
    },
    {
      provide: CUSTOM_FIELD_REPOSITORY_INTERFACE,
      useClass: CustomFieldRepository,
    },
    {
      provide: LEAD_METRIC_REPOSITORY_INTERFACE,
      useClass: LeadMetricRepository,
    },
    {
      provide: ENRICHMENT_REPOSITORY_INTERFACE,
      useClass: EnrichmentRepository,
    },
    {
      provide: CONTACT_ZELIQ_REPOSITORY_INTERFACE,
      useClass: ContactZeliqRepository,
    },
    {
      provide: COMPANY_ZELIQ_REPOSITORY_INTERFACE,
      useClass: CompanyZeliqRepository,
    },
    {
      provide: ENRICHMENT_QUERY_REPOSITORY_INTERFACE,
      useClass: EnrichmentQueryRepository,
    },
    {
      provide: ENRICHMENT_METRIC_REPOSITORY_INTERFACE,
      useClass: EnrichmentMetricRepository,
    },
    {
      provide: CONTACT_STATUS_HISTORY_REPOSITORY_INTERFACE,
      useClass: ContactStatusHistoryRepository,
    },
    {
      provide: LEAD_EXPORT_REPOSITORY_INTERFACE,
      useClass: LeadExportRepository,
    },
    {
      provide: JOB_TITLE_REPOSITORY_INTERFACE,
      useClass: JobTitleRepository,
    },
    {
      provide: SEARCH_REQUEST_REPOSITORY_INTERFACE,
      useClass: SearchRequestRepository,
    },
    {
      provide: ORGANIZATION_REPOSITORY_INTERFACE,
      useClass: OrganizationRepository,
    },
    {
      provide: ENRICHMENT_RESULT_REPOSITORY_INTERFACE,
      useClass: EnrichmentResultRepository,
    },
    {
      provide: CONTACT_ZELIQ_OPT_OUT_REPOSITORY_INTERFACE,
      useClass: ContactZeliqOptOutRepository,
    },
    {
      provide: 'ELASTICSEARCH_SEARCH_NODE',
      useFactory: () => {
        try {
          return new ElasticsearchService({
            node: process.env.ELASTICSEARCH_SEARCH_URL,
            auth: {
              username: process.env.ELASTICSEARCH_SEARCH_USERNAME,
              password: process.env.ELASTICSEARCH_SEARCH_PASSWORD,
            },
          })
        } catch {
          return null
        }
      },
    },
    {
      provide: LINKEDIN_ACTION_SCHEDULER,
      useClass: CaptainDataLinkedinActionScheduler,
    },
    ...profiles,
    ...commandHandlers,
    ...queryHandlers,
    ...cron,
    ...consumers,
    ...services,
    ...listeners,
  ],
  exports: [
    COMPANY_REPOSITORY_INTERFACE,
    CONTACT_REPOSITORY_INTERFACE,
    CUSTOM_FIELD_REPOSITORY_INTERFACE,
    ExportCsvService,
    EnrichmentNotifCronService,
    ContactService,
    FileService,
  ],
})
export class LeadModule {}
