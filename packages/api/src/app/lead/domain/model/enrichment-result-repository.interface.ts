import { EnrichmentResultModel } from './enrichment-result.model'
import { EnrichmentResultMetrics } from '../interface/enrichment/enrichment-result-metrics.interface'
import { ContactModel } from './contact.model'

export const ENRICHMENT_RESULT_REPOSITORY_INTERFACE =
  'ENRICHMENT_RESULT_REPOSITORY_INTERFACE'

export interface EnrichmentResultWithEmailsCheckResult
  extends EnrichmentResultModel,
    Pick<ContactModel, 'emailsCheckResult'> {}

export interface EnrichmentResultRepositoryInterface {
  save(enrichmentModel: EnrichmentResultModel): Promise<EnrichmentResultModel>
  findById(
    id: string,
    withDeleted?: boolean
  ): Promise<EnrichmentResultModel | null>
  findByEnrichmentId(
    enrichmentId: string
  ): Promise<EnrichmentResultWithEmailsCheckResult[]>
  countByNewDataFoundForUserAndDate(
    organizationId: string,
    userId: string,
    startDate?: Date
  ): Promise<number>
  getEnrichmentsMetrics(enrichmentId: string): Promise<EnrichmentResultMetrics>
}
