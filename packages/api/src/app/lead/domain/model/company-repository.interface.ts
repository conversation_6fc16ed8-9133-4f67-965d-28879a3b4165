import { SelectQ<PERSON><PERSON><PERSON><PERSON>er, UpdateResult } from 'typeorm'
import { PaginationQueryObject } from '../../../shared/domain/object/pagination-query.object'
import { CompanyEntity } from '../../infrastructure/entity/company.entity'
import { CompanyModel } from './company.model'
import { CustomFieldModel } from './custom-field.model'
import { EnrichmentStatus, LeadSource } from '@getheroes/shared'
import { FindManyOptions } from 'typeorm/find-options/FindManyOptions'
import { LeadImportIdOperation } from '../interface/contact.interface'

export const COMPANY_REPOSITORY_INTERFACE = 'COMPANY_REPOSITORY_INTERFACE'

export interface CompanyRepositoryInterface {
  archive(companyModel: CompanyModel): Promise<CompanyModel>

  count(options?: FindManyOptions<CompanyEntity>): Promise<number>

  countNbAvailableForEnrichment(
    companiesIds: string[],
    enrichmentMaxDate: Date
  ): Promise<number>

  create(companyModel: CompanyModel): Promise<CompanyModel>

  createMany(companiesModels: CompanyModel[]): Promise<CompanyModel[]>

  deleteCustomField(customFieldModel: CustomFieldModel): Promise<UpdateResult>

  find(
    organizationId: string,
    params: {
      names?: string[]
      domains?: string[]
      externalIds?: string[]
    }
  ): Promise<CompanyModel[]>

  findByExternalIds(
    organizationId: string,
    externalIds?: string[],
    withDeleted?: boolean
  ): Promise<CompanyModel[]>

  findByExternalIdsOrNamesOrDomains(
    organizationId: string,
    externalIds?: string[],
    names?: string[],
    domains?: string[],
    withDeleted?: boolean
  ): Promise<CompanyModel[]>

  findById(id: string, withDeleted?: boolean): Promise<CompanyModel | null>

  findByIdAndOrganizationId(
    organizationId: string,
    id: string,
    withDeleted?: boolean
  ): Promise<CompanyModel | null>

  findByIds(ids: string[], withDeleted?: boolean): Promise<CompanyModel[]>

  findByIdsForHubspotSync(
    ids: string[],
    withDeleted?: boolean
  ): Promise<CompanyModel[]>

  findByOrganizationAndNameOrDomain(
    organizationId: string,
    name?: string | null,
    domain?: string | null
  ): Promise<CompanyModel[]>

  findIdsByExternalIds(
    organizationId: string,
    externalIds?: string[],
    withDeleted?: boolean
  ): Promise<{ [key: string]: string }>

  findIdsByLeadImportId(
    organizationId: string,
    leadImportId: string
  ): Promise<string[]>

  findIdsByExternalIdsOrNames(
    organizationId: string,
    externalIds?: string[],
    names?: string[],
    withDeleted?: boolean
  ): Promise<{ [key: string]: string }>

  findIdsAndReindexRequired(
    limit?: number,
    withDeleted?: boolean
  ): Promise<string[]>

  incrementNbLeads(id: string, value: number): Promise<void>

  queryFindByOrganization(
    organizationId: string,
    userId: string,
    filter: PaginationQueryObject
  ): SelectQueryBuilder<CompanyEntity>

  setAvailableInWorkspace(
    contactIds: string[],
    source?: LeadSource
  ): Promise<string[] | null>

  unarchive(companyModel: CompanyModel): Promise<CompanyModel>

  update(companyModel: CompanyModel): Promise<CompanyModel>

  updateBulkEnrichmentStatus(
    ids: string[],
    status: EnrichmentStatus
  ): Promise<void>

  addBulkLeadImportId(
    companiesIds: string[],
    leadImportId: string
  ): Promise<void>

  updateBulkReindexRequired(
    ids: string[],
    reindexRequired: boolean
  ): Promise<void>
  countReindexRequired(withDeleted?: boolean): Promise<number>

  updateExternalUrlTemplate(
    organizationId: string,
    newExternalUrlTemplate: string
  ): Promise<void>

  updateLeadImportIds(
    companiesIds: string[],
    leadImportId: string,
    operation: LeadImportIdOperation
  ): Promise<void>

  updateMany(companies: Array<CompanyModel>): Promise<Array<CompanyModel>>
  deleteByIds(
    companyIds: string[],
    onlyDeleteIfNotAvailableInWorkspace?: boolean
  ): Promise<void>

  countNbLeadsInLeadImport(
    organizationId: string,
    leadImportId: string
  ): Promise<number>

  findByLeadImportAndOrganization(
    organizationId: string,
    leadImportId: string
  ): Promise<CompanyModel[]>
}
