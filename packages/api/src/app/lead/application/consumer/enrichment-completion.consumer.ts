import { Process, Processor } from '@nestjs/bull'
import { Job } from 'bull'
import { CommandBus } from '@nestjs/cqrs'
import { Injectable } from '@nestjs/common'
import { ENRICHMENT_COMPLETION_QUEUE } from '../../infrastructure/global'
import { HandleEnrichmentCompletedCommand } from '../command/enrichment/handle-enrichment-completed/handle-enrichment-completed.command'
import {
  EnrichmentLeadData,
  EnrichmentSourceEnum,
} from '../../domain/enrichment-queue-data.interface'
import { LogFeature, LoggerService } from '../../../shared/logger.service'
import { EnrichService } from '../service/enrichment/enrich.service'
import { CacheService } from '../../../shared/infrastructure/service/cache.service'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { EnrichmentEvents } from '../../../shared/domain/event/event.enum'
import { EnrichmentBatchCompletedSuccessfullyEvent } from '../../../shared/domain/event/enrichment/enrichment-batch-completed-successfully.event'
import { EnrichmentBatchFailedEvent } from '../../../shared/domain/event/enrichment/enrichment-batch-failed.event'

@Injectable()
@Processor(ENRICHMENT_COMPLETION_QUEUE)
export class EnrichmentCompletionConsumer {
  constructor(
    private commandBus: CommandBus,
    private enrichService: EnrichService,
    private cacheManager: CacheService,
    private eventEmitter: EventEmitter2
  ) {}

  private logger = new LoggerService({
    context: EnrichmentCompletionConsumer.name,
    feature: LogFeature.ENRICHMENT,
  })

  @Process({
    concurrency: 5,
  })
  async handleCompletion(job: Job<EnrichmentLeadData>) {
    try {
      const nbEnrichmentProcessed = await this.cacheManager.incr(
        this.enrichService.getCacheNbProcessedKey(job.data.enrichmentId)
      )

      if (nbEnrichmentProcessed >= job.data.enrichmentNbEstimated) {
        await this.cacheManager
          .delete(
            this.enrichService.getCacheNbProcessedKey(job.data.enrichmentId)
          )
          .catch(() => null)

        // Execute existing completion logic
        await this.commandBus.execute(
          new HandleEnrichmentCompletedCommand({
            enrichmentId: job.data.enrichmentId,
            enrichmentType: job.data.enrichmentType,
          })
        )

        if (
          job.data.source === EnrichmentSourceEnum.ENRICHMENT_HUB &&
          job.data.isSuccess
        ) {
          if (job.data.isSuccess) {
            this.eventEmitter.emit(
              EnrichmentEvents.ENRICHMENT_BATCH_COMPLETED_SUCCESSFULLY,
              new EnrichmentBatchCompletedSuccessfullyEvent({
                enrichmentHubId: job.data.enrichmentHubContext.enrichmentHubId,
                batchSize: job.data.enrichmentHubContext.batchSize,
                enrichmentId: job.data.enrichmentId,
              })
            )
          } else if (job.data.isLastAttempt) {
            this.eventEmitter.emit(
              EnrichmentEvents.ENRICHMENT_BATCH_FAILED,
              new EnrichmentBatchFailedEvent({
                enrichmentHubId: job.data.enrichmentHubContext.enrichmentHubId,
                enrichmentId: job.data.enrichmentId,
              })
            )
          }
        }
      }
    } catch (error) {
      this.logger.error({
        error,
        data: { jobData: job.data },
        message: 'Failed to complete enrichment',
      })
      throw error
    }
  }
}
