import { OnQueueCompleted, Process, Processor, InjectQueue } from '@nestjs/bull'
import {
  ENRICHMENT_PHONE_QUEUE,
  ENRICHMENT_COMPLETION_QUEUE,
} from '../../infrastructure/global'
import { Job, Queue } from 'bull'
import { EnrichmentLeadData } from '../../domain/enrichment-queue-data.interface'
import { CommandBus } from '@nestjs/cqrs'
import { OnQueueFailed } from '@nestjs/bull/dist/decorators/queue-hooks.decorators'
import { ProcessEnrichmentJobCommand } from '../command/enrichment/process-enrichment-job/process-enrichment-job.command'
import { QueueService } from '../../../shared/infrastructure/service/queue.service'

@Processor(ENRICHMENT_PHONE_QUEUE)
export class EnrichmentPhoneQueueConsumer extends QueueService {
  constructor(
    private commandBus: CommandBus,
    @InjectQueue(ENRICHMENT_COMPLETION_QUEUE)
    private enrichmentCompletionQueue: Queue<EnrichmentLeadData>
  ) {
    super(enrichmentCompletionQueue)
  }

  @OnQueueFailed()
  async onFailed(job: Job<EnrichmentLeadData>, err) {
    const isLastAttempt = job.attemptsMade === job.opts.attempts
    await this.enrichmentCompletionQueue.add({
      ...job.data,
      isSuccess: false,
      isLastAttempt,
    })
  }

  @OnQueueCompleted()
  async onCompleted(job: Job<EnrichmentLeadData>, err) {
    await this.enrichmentCompletionQueue.add({
      ...job.data,
      isSuccess: true,
    })
  }

  @Process({
    concurrency: 5,
  })
  async index(job: Job<EnrichmentLeadData>) {
    await this.commandBus.execute(
      new ProcessEnrichmentJobCommand({
        leadId: job.data.id,
        fieldFamily: job.data.type,
        type: job.data.enrichmentType,
        createdById: job.data.enrichmentCreatedBy,
        id: job.data.enrichmentId,
      })
    )
  }
}
