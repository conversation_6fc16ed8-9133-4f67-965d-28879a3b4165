import { ForbiddenException, Injectable } from '@nestjs/common'
import { QueryBus } from '@nestjs/cqrs'
import jp from 'jsonpath'
import Papa from 'papaparse'

import { instanceToPlain } from 'class-transformer'
import { cloneDeep } from 'lodash'
import { ExportPaginationQueryDto } from '../../../../../ui/api/lead/dto/export/export-pagination-query.dto'
import { ExportQueryParamsDto } from '../../../../../ui/api/lead/dto/export/export-query-params.dto'
import { flattenCustomFieldsHelper } from '../../../../shared/domain/helper/custom-field.helper'
import { PaginationResultObject } from '../../../../shared/domain/object/pagination-result.object'
import { SearchQueryObject } from '../../../../shared/domain/object/search-query.object'
import { EXPOSE_GROUP } from '../../../../shared/globals/expose-group.enum'
import { FieldFamily } from '../../../domain/field-family.enum'
import { CompanyModel } from '../../../domain/model/company.model'
import { ContactModel } from '../../../domain/model/contact.model'
import { FieldModel } from '../../../domain/model/field.model'
import { ExportFieldFilterItemObject } from '../../../domain/object/export-field-filter-item.object'
import { ExportPaginationQueryObject } from '../../../domain/object/export-pagination-query.object'
import { ListFieldQuery } from '../../query/list-field/list-field.query'
import { SearchIndexCompanyQuery } from '../../query/search-company/search-index-company.query'
import { SearchIndexContactQuery } from '../../query/search-contact/search-index-contact.query'

import { Mapper } from '@automapper/core'
import { InjectMapper } from '@automapper/nestjs'
import {
  EnrichmentHubStatusEnum,
  EnrichmentHubTypeEnum,
  FeatureLimit,
  LeadImportSourceEnum,
  LocaleEnum,
  OrganizationServiceSubscriptionPlan,
  UserRole,
} from '@getheroes/shared'
import { ConfigService } from '@nestjs/config'
import { ExportQueryBodyDto } from '../../../../../ui/api/lead/dto/export/export-query-body.dto'
import { SearchQueryDto } from '../../../../../ui/api/shared/infrastructure/dto/search-query.dto'
import { GetEnrichmentHubByIdQuery } from '../../../../enrichment-hub/application/query/get-enrichment-hub-by-id/get-enrichment-hub-by-id.query'
import { EnrichmentHubInterface } from '../../../../enrichment-hub/domain/enrichment-hub.interface'
import { GetFeatureUsageAndLimitQuery } from '../../../../feature/application/query/get-feature-usage-and-limit/get-feature-usage-and-limit.query'
import { FeatureService } from '../../../../feature/application/service/feature.service'
import { GetOneLeadImportByIdQuery } from '../../../../lead-import/application/query/get-one-lead-import-by-id/get-one-lead-import-by-id.query'
import { TryCatchLogger } from '../../../../shared/domain/decorator/try-catch-logger.decorator'
import { FieldKind } from '../../../../shared/domain/field-kind.enum'
import { UserModel } from '../../../../shared/domain/model/user.model'
import { SearchOperation } from '../../../../shared/domain/search.enum'
import { SortType } from '../../../../shared/domain/sort-type.enum'
import { PermissionSpecification } from '../../../../shared/domain/specification/model-permission.specitification'
import { CheckEmailResultItem } from '../../../../shared/domain/type/data-mail-checker.type'
import { LogFeature, LoggerService } from '../../../../shared/logger.service'
import { findPhoneNumberForCountry } from '../../../../shared/utils/find-phone-for-country'
import { swapPropertiesInObject } from '../../../../shared/utils/swap-object-properties.utils'
import { ContactPermission } from '../../../domain/contact-permission.enum'
import { ExportCsvServiceInterface } from '../../../domain/interface/export-csv-service.interface'
import { ContactCustomExportFields } from '../../../domain/interface/lead-export.interface'
import { LeadExportModel } from '../../../domain/model/lead-export.model'
import { FileService } from '../../../infrastructure/service/file-service/file.service'
import { exportCsvTranslation } from './export-fields-translations'

@Injectable()
export class ExportCsvService implements ExportCsvServiceInterface {
  constructor(
    private readonly featureService: FeatureService,
    private readonly configService: ConfigService,
    @InjectMapper() private readonly mapper: Mapper,
    private readonly queryBus: QueryBus,
    private readonly flatfileService: FileService
  ) {}

  private readonly logger = new LoggerService({
    feature: LogFeature.LEAD_EXPORT,
    context: ExportCsvService.name,
  })

  @TryCatchLogger({
    message: 'Failed to custom parse contact',
    feature: LogFeature.LEAD_EXPORT,
  })
  customContactParsing(
    parsedContact: ContactModel,
    field: FieldModel,
    enrichmentHub: EnrichmentHubInterface | null,
    locale: LocaleEnum = LocaleEnum.AMERICAN
  ): Record<string, string> {
    // Email formating
    const hasEmailAndCheckResult =
      !!parsedContact.emails?.[0] && !!parsedContact.emailsCheckResult?.length

    const mainEmail =
      parsedContact.emails?.find((email: string) =>
        email.includes(parsedContact.company.domain)
      ) || parsedContact.emails?.[0]

    const shouldHaveTriedToEnrichEmail =
      enrichmentHub?.enrichmentTypes.includes(EnrichmentHubTypeEnum.EMAIL)

    // TODO: improve this logic
    // For performance reasons we check the latest contact.enrichment
    // We should check the enrichmentResult for that enrichmentHub and that contact to be more accurate
    const hasRecentEnrichment =
      !!parsedContact.enrichmentAdvancedDate &&
      !!enrichmentHub?.createdAt &&
      parsedContact.enrichmentAdvancedDate.getTime() -
        enrichmentHub?.createdAt.getTime() >
        0

    const isEmailNotFoundPlaceholder =
      (enrichmentHub?.status === EnrichmentHubStatusEnum.DONE ||
        (hasRecentEnrichment && shouldHaveTriedToEnrichEmail)) &&
      !mainEmail

    const isNotEnoughCreditsForEmailPlaceholder =
      shouldHaveTriedToEnrichEmail && !hasRecentEnrichment && !mainEmail

    const noEmailPlaceholder = isEmailNotFoundPlaceholder
      ? exportCsvTranslation.noDataFound[locale]
      : isNotEnoughCreditsForEmailPlaceholder
        ? exportCsvTranslation.notEnoughCredit[locale]
        : ''

    // Phone formating
    const optimalPhone = findPhoneNumberForCountry(
      parsedContact.phones,
      parsedContact.country
    )

    const shouldHaveTriedToEnrichPhone =
      enrichmentHub?.enrichmentTypes.includes(EnrichmentHubTypeEnum.PHONE)

    const isPhoneNotFoundPlaceholder =
      (enrichmentHub?.status === EnrichmentHubStatusEnum.DONE ||
        (hasRecentEnrichment && shouldHaveTriedToEnrichPhone)) &&
      !optimalPhone

    const isNotEnoughCreditsForPhonePlaceholder =
      shouldHaveTriedToEnrichPhone && !hasRecentEnrichment && !optimalPhone

    const noPhonePlaceholder = isPhoneNotFoundPlaceholder
      ? exportCsvTranslation.noDataFound[locale]
      : isNotEnoughCreditsForPhonePlaceholder
        ? exportCsvTranslation.notEnoughCredit[locale]
        : ''

    switch (field.id) {
      case ContactCustomExportFields.FULL_NAME:
        return {
          [field.name]: `${parsedContact.firstName} ${parsedContact.lastName}`,
        }
      case ContactCustomExportFields.EMAILS:
        return {
          [exportCsvTranslation.emailOne[locale]]:
            mainEmail || noEmailPlaceholder,
          [exportCsvTranslation.emailOther[locale]]: parsedContact.emails
            ?.filter((email: string) => email !== mainEmail)
            ?.join(', '),
        }
      case ContactCustomExportFields.EMAIL_CHECK_RESULT:
        return {
          [exportCsvTranslation.deliverabilityEmailOne[locale]]:
            hasEmailAndCheckResult
              ? parsedContact.emailsCheckResult.find(
                  (emailsCheckResult: CheckEmailResultItem) =>
                    emailsCheckResult.mail === mainEmail
                )?.result
              : '',
          [exportCsvTranslation.deliverabilityEmailOther[locale]]:
            hasEmailAndCheckResult
              ? parsedContact.emailsCheckResult
                  .filter(
                    (emailsCheckResult: CheckEmailResultItem) =>
                      emailsCheckResult.mail !== mainEmail
                  )
                  ?.map(
                    (emailsCheckResult: CheckEmailResultItem) =>
                      `${emailsCheckResult.mail} <${emailsCheckResult.result}>`
                  )
                  ?.join(' ; ')
              : '',
        }
      case ContactCustomExportFields.PHONES:
        return {
          [exportCsvTranslation.phoneOne[locale]]:
            optimalPhone || noPhonePlaceholder,
          [exportCsvTranslation.phoneOther[locale]]: parsedContact.phones
            ?.filter(phone => phone !== optimalPhone)
            ?.join(', '),
        }
      default:
        // Handle custom fields
        if (
          parsedContact.customFields &&
          field.id in parsedContact.customFields
        ) {
          return { [field.name]: parsedContact.customFields[field.id] }
        }
        return {}
    }
  }

  async getContactExportObject(
    leadExportModel: LeadExportModel,
    exportCustomFields = true
  ): Promise<Record<string, string>[]> {
    const { organizationId, pagination, searchQueryObject, exportFields } =
      leadExportModel

    const contacts = await this.fetchAllData<ContactModel>(
      FieldFamily.CONTACT,
      organizationId,
      pagination,
      searchQueryObject
    )
    if (!contacts.length) {
      return []
    }

    let fields = await this.exportFieldsParsing(
      FieldFamily.CONTACT,
      organizationId,
      exportFields
    )

    // Get custom fields from contacts
    const customFieldNames = new Set<string>()
    contacts.forEach(contact => {
      if (contact.customFields) {
        Object.keys(contact.customFields).forEach(key =>
          customFieldNames.add(key)
        )
      }
    })

    // Add custom fields to the field list
    if (customFieldNames.size > 0 && exportCustomFields) {
      const customFields = Array.from(customFieldNames).map(
        fieldId =>
          ({
            id: fieldId,
            name: fieldId, // Use fieldId as a name or transform it to a more human-readable format
            jsonPath: `$.${fieldId}`,
            kind: FieldKind.CUSTOM,
          }) as FieldModel
      )
      fields.push(...customFields)
    }

    // Force some columns to always be first and in a specific order, keep the rest in the same order as exportFields
    const fieldOrder = [
      ContactCustomExportFields.FULL_NAME,
      'firstName',
      'lastName',
      'title',
      'company.name',
      ContactCustomExportFields.EMAILS,
      ContactCustomExportFields.EMAIL_CHECK_RESULT,
    ]
    const sortedFields = fields.filter(field => fieldOrder.includes(field.id))
    const unsortedFields = fields.filter(
      field => !fieldOrder.includes(field.id)
    )
    fields = [
      ...fieldOrder
        .map(id => sortedFields.find(field => field.id === id))
        .filter(Boolean),
      ...unsortedFields,
    ]

    const enrichmentHub = leadExportModel.enrichmentHubId
      ? ((await this.queryBus.execute(
          new GetEnrichmentHubByIdQuery(leadExportModel.enrichmentHubId)
        )) as unknown as EnrichmentHubInterface)
      : null

    // Note: The parseData method will handle the flattened custom fields because we're
    // already using flattenCustomFieldsHelper in the serializeData method
    return await this.parseData(
      contacts,
      fields,
      (parsedContact: any, field: FieldModel) =>
        this.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          leadExportModel.createdBy?.locale
        )
    )
  }

  async exportContacts(leadExportModel: LeadExportModel): Promise<string> {
    const data = await this.getContactExportObject(leadExportModel)

    this.logger.log({
      message: 'Contacts ready to export',
      data: { leadExportModel },
    })

    return this.objectToCsv(data)
  }

  private getCsvImportContext = async (leadExportModel: LeadExportModel) => {
    const leadImport = await this.queryBus.execute(
      new GetOneLeadImportByIdQuery(
        leadExportModel.leadImportId,
        leadExportModel.organizationId
      )
    )
    if (leadImport?.source !== LeadImportSourceEnum.CSV_FILE) return null

    const csvProps = leadImport.getCsvImportProps()
    const spaceId =
      'space' in csvProps && 'sheet' in csvProps
        ? csvProps.space.id
        : csvProps.spaceId

    const files = await this.flatfileService.listFiles(spaceId)
    if (!files.length) throw new Error('No files found in space')

    const csvData = await this.flatfileService.downloadFileInSpace(files[0].id)
    const columnMapping = await this.flatfileService.getColumnsMapping(spaceId)
    const csvImportParsed = Papa.parse(csvData, { header: true })
    const originalHeaders = csvImportParsed.meta.fields as string[]

    return { csvImportParsed, originalHeaders, columnMapping }
  }

  async exportOriginalColumns(
    leadExportModel: LeadExportModel
  ): Promise<string> {
    if (!leadExportModel.leadImportId)
      return await this.exportContacts(leadExportModel)

    const context = await this.getCsvImportContext(leadExportModel)
    if (!context) return await this.exportContacts(leadExportModel)

    const { csvImportParsed, originalHeaders, columnMapping } = context

    const locale = leadExportModel.createdBy?.locale || LocaleEnum.AMERICAN

    // Will be used as lookup key for matching contacts in DB and original file
    const fullNameColumnLabel = exportCsvTranslation.fullName[locale]
    const companyNameColumnLabel = `company.name`

    // Define ZELIQ fields and their output headers (order matters)
    const zeliqFields = [
      {
        id: 'fullName',
        label: fullNameColumnLabel,
      },
      {
        id: 'phones',
        label: exportCsvTranslation.phoneOne[locale],
      },
      {
        id: 'emails',
        label: exportCsvTranslation.emailOne[locale],
      },
      {
        id: 'emailCheckResult',
        label: exportCsvTranslation.emailCheckResult[locale],
      },
      {
        id: 'linkedinUrl',
        label: exportCsvTranslation.linkedinUrl[locale],
      },
      { id: 'title', label: exportCsvTranslation.title[locale] },
      { id: 'city', label: exportCsvTranslation.city[locale] },
      {
        id: 'country',
        label: exportCsvTranslation.country[locale],
      },
      {
        id: 'company.domain',
        label: exportCsvTranslation.companyDomain[locale],
      },
      {
        id: 'company.name',
        label: companyNameColumnLabel,
      },
    ]
    leadExportModel.set({
      exportFields: zeliqFields.map(field => ({
        id: field.id,
        name: field.label,
      })),
    })

    // Get the data from the database for the ZELIQ rows
    const zeliqData = await this.getContactExportObject(leadExportModel, false)

    // To optimize lookup when matching contacts in original file with contacts in zeliqData (DB)
    const zeliqDataDict = zeliqData.reduce(
      (acc, row) => {
        const lookupKey =
          `${row[fullNameColumnLabel]}-${row[companyNameColumnLabel]}`
            .toLowerCase()
            .replace(/\s+/g, '_')
        acc[lookupKey] = row
        return acc
      },
      {} as Record<string, Record<string, string>>
    )

    // For each row, copy original fields, then compute ZELIQ fields
    const allRows = [...csvImportParsed.data] as Record<string, string>[]
    const resultRows = []
    allRows.forEach(row => {
      const lookupKey =
        `${row[columnMapping['firstName']]}_${row[columnMapping['lastName']]}-${row[columnMapping['companyName']]}`
          .toLowerCase()
          .replace(/\s+/g, '_')

      const contactFromZeliq = zeliqDataDict[lookupKey]
      if (!contactFromZeliq) {
        // Don't export the original rows if for the contacts not imported in the DB
        return
      }

      const result: Record<string, string> = {}

      // Copy original columns
      for (const header of originalHeaders) {
        result[header] = row[header] ?? ''
      }

      // Copy ZELIQ columns
      for (const [key, value] of Object.entries(contactFromZeliq)) {
        result[`${key} (ZELIQ)`] = value ?? ''
      }

      resultRows.push(result)
    })

    // Compose final headers: original + ZELIQ
    const zeliqHeaders = Object.keys(zeliqData[0])
      .filter(key => ![companyNameColumnLabel].includes(key))
      .map(key => `${key} (ZELIQ)`)

    return Papa.unparse(resultRows, {
      columns: [...originalHeaders, ...zeliqHeaders],
    })
  }

  async exportCompanies(leadExportModel: LeadExportModel): Promise<string> {
    const { organizationId, pagination, searchQueryObject, exportFields } =
      leadExportModel

    const companies = await this.fetchAllData<CompanyModel>(
      FieldFamily.COMPANY,
      organizationId,
      pagination,
      searchQueryObject
    )
    if (!companies.length) {
      return ''
    }

    const fields = await this.exportFieldsParsing(
      FieldFamily.COMPANY,
      organizationId,
      exportFields
    )

    const data = await this.parseData(companies, fields)

    return this.objectToCsv(data)
  }

  async exportFieldsParsing(
    exportType: FieldFamily,
    organizationId: string,
    exportFields: ExportFieldFilterItemObject[]
  ): Promise<FieldModel[]> {
    const fieldsModels: FieldModel[] = await this.queryBus.execute(
      new ListFieldQuery(organizationId, exportType)
    )
    if (exportType === FieldFamily.CONTACT) {
      // all company fields are exportable for contacts exports
      const companyFieldsModels = await this.queryBus.execute(
        new ListFieldQuery(organizationId, FieldFamily.COMPANY)
      )
      fieldsModels.push(
        ...companyFieldsModels.map(fieldModel => ({
          ...fieldModel,
          id: `company.${fieldModel.id}`,
          jsonPath: fieldModel.jsonPath.replace('$.', `$.company.`),
        }))
      )
    }

    const contactCustomExportFieldsList: string[] = Object.keys(
      ContactCustomExportFields
    ).map(
      key =>
        ContactCustomExportFields[key as keyof typeof ContactCustomExportFields]
    )

    const customExportFieldsIds: string[] =
      exportType === FieldFamily.CONTACT ? contactCustomExportFieldsList : []

    return exportFields
      .map(field => {
        if (customExportFieldsIds.includes(field.id)) {
          return {
            ...field,
            kind: FieldKind.COMPUTED,
          } as FieldModel
        }

        const fieldModel = fieldsModels.find(f => f.id === field.id)

        if (!fieldModel) {
          // Check if it's a subfield
          const subFieldId = field.id.split('.')[0]
          const subFieldPath = field.id.split('.').slice(1).join('.')
          const subField = fieldsModels.find(f => f.id === subFieldId)

          if (subField && subField.type === 'object') {
            return {
              ...subField,
              id: field.id,
              name: field.name,
              jsonPath: `$.${subFieldId}.${subFieldPath}`,
            } as FieldModel
          }

          this.logger.warn({
            message: `Lead export: Field ${field.name} (${field.id}) not found, will not be exported`,
          })
          return null
        }

        return {
          ...fieldModel,
          name: field.name,
        } as FieldModel
      })
      .filter(Boolean)
  }

  async parseData<T>(
    data: T[],
    fields: FieldModel[],
    parseComputedFields?: (
      parsedLead: any,
      field: FieldModel
    ) => Record<string, string>
  ): Promise<Record<string, string>[]> {
    const dataSerialized = this.serializeData(data)

    return dataSerialized.map(item => {
      const itemParsed = {}

      fields.forEach(field => {
        // Handle a special case for original columns
        if ((field as any).isOriginalColumn) {
          // Try to find the matching original column value
          // This would typically come from stored original data or from
          // the field names that match the original column names
          const origKey = field.name

          // First, try to get from the original data if available
          const origValue = jp.query(
            item,
            `$.original.${origKey.replace(/\s+/g, '_')}`
          )[0]

          if (origValue !== undefined) {
            itemParsed[field.name] = origValue
            return
          }

          // If not found in original, try to get from main data directly matching the column name
          const matchingKeys = Object.keys(item).filter(
            key => key.toLowerCase() === origKey.toLowerCase()
          )

          if (matchingKeys.length > 0) {
            itemParsed[field.name] = item[matchingKeys[0]]
            return
          }

          // If still not found, leave it empty
          itemParsed[field.name] = ''
          return
        }

        if (field.kind === FieldKind.COMPUTED) {
          if (parseComputedFields) {
            Object.assign(itemParsed, parseComputedFields(item, field))
          }
          return
        }

        const value = jp.query(item, field.jsonPath)[0]

        // if the base value is not defined, try to get it in enrichmentResult
        let enrichmentResultValue = ''
        if (!value || field.jsonPath) {
          enrichmentResultValue =
            jp.query(
              item,
              field.jsonPath.replace('$.', '$.enrichmentResult.')
            )?.[0] || ''
        }
        itemParsed[field.name] = value || enrichmentResultValue
      })

      const hasEmailAndDeliverabilityFields =
        !!fields.find(field => field.id === ContactCustomExportFields.EMAILS) &&
        !!fields.find(
          field => field.id === ContactCustomExportFields.EMAIL_CHECK_RESULT
        )
      if (hasEmailAndDeliverabilityFields) {
        return swapPropertiesInObject(
          itemParsed,
          'other emails',
          'email 1 deliverability'
        )
      }

      return itemParsed
    })
  }

  async objectToCsv(data: Record<string, string>[]): Promise<string> {
    if (!data.length) {
      data.push({})
    }
    const columns = Object.keys(data[0]).map(label => label)

    return Papa.unparse(data, {
      quotes: true,
      quoteChar: '"',
      delimiter: ',',
      header: true,
      newline: '\r\n',
      skipEmptyLines: false,
      columns,
    })
  }

  // Remaining methods unchanged...
  async fetchAllData<T>(
    exportType: FieldFamily,
    organizationId: string,
    pagination: ExportPaginationQueryObject,
    searchQueryObject: SearchQueryObject
  ): Promise<T[]> {
    return this.fetchDataRecursive<T>(
      1,
      [],
      exportType,
      organizationId,
      pagination,
      searchQueryObject
    )
  }

  async fetchData<T>(
    query: SearchIndexCompanyQuery | SearchIndexContactQuery
  ): Promise<PaginationResultObject<T>> {
    return await this.queryBus.execute(query)
  }

  private async fetchDataRecursive<T>(
    page: number,
    data: T[],
    exportType: FieldFamily,
    organizationId: string,
    pagination: ExportPaginationQueryObject,
    searchQueryObject: SearchQueryObject
  ): Promise<T[]> {
    const queryPayload = this.getQueryPayload(
      page,
      exportType,
      organizationId,
      pagination,
      searchQueryObject
    )

    const result = await this.fetchData<T>(queryPayload)

    data = [...data, ...result.items]

    if (data.length >= pagination.exportTotalLimit) {
      return data
    }

    if (result.meta.hasNextPage) {
      return this.fetchDataRecursive(
        page + 1,
        data,
        exportType,
        organizationId,
        pagination,
        searchQueryObject
      )
    }

    return data
  }

  private getQueryPayload(
    page: number,
    exportType: FieldFamily,
    organizationId: string,
    pagination: ExportPaginationQueryObject,
    searchQueryObject: SearchQueryObject
  ): SearchIndexCompanyQuery | SearchIndexContactQuery {
    // Deep clone to avoid "filters query corruption" by the search command
    const clonedPagination = cloneDeep(pagination)
    clonedPagination.page = page

    const search = cloneDeep(searchQueryObject)
    search.setPaginationData(clonedPagination)
    search.offset = (clonedPagination.page - 1) * clonedPagination.limitPerPage

    const query =
      exportType === FieldFamily.COMPANY
        ? SearchIndexCompanyQuery
        : SearchIndexContactQuery

    return new query(organizationId, clonedPagination, search)
  }

  private serializeData<T>(data: T[]): Record<string, unknown>[] {
    const flattenData = data.map(item => flattenCustomFieldsHelper(item))

    return instanceToPlain(flattenData, {
      groups: [EXPOSE_GROUP.PUBLIC],
    }) as Record<string, unknown>[]
  }

  public async getExportLimit(
    organizationId: string,
    isEnrichmentHub: boolean
  ): Promise<number> {
    if (isEnrichmentHub) {
      return this.configService.get<number>('export.enrichmentHubTotalLimit')
    } else {
      const { featureLimit } = await this.queryBus.execute(
        new GetFeatureUsageAndLimitQuery(
          organizationId,
          FeatureLimit.LEADS_EXPORT_TOTAL_LIMIT
        )
      )
      return featureLimit
    }
  }

  public async getPaginationObjectWithLimits(
    organizationId: string,
    isEnrichmentHubExport: boolean,
    exportParamsDto: ExportQueryParamsDto
  ): Promise<ExportPaginationQueryObject> {
    let limitPerPage = (await this.featureService.getFeatureLimit(
      organizationId,
      FeatureLimit.LEADS_EXPORT_LIMIT_PER_PAGE
    )) as number
    let totalLimit = (await this.featureService.getFeatureLimit(
      organizationId,
      FeatureLimit.LEADS_EXPORT_TOTAL_LIMIT
    )) as number

    if (isEnrichmentHubExport) {
      limitPerPage = this.configService.get<number>(
        'export.enrichmentHubLimitPerPage'
      )
      totalLimit = await this.getExportLimit(
        organizationId,
        isEnrichmentHubExport
      )
    }

    const paginationQueryObject = this.mapper.map(
      exportParamsDto,
      ExportPaginationQueryDto,
      ExportPaginationQueryObject
    )
    paginationQueryObject.limitPerPage = limitPerPage
    paginationQueryObject.exportTotalLimit = totalLimit

    return paginationQueryObject
  }

  public async restrictAccessToLeads(
    organizationId: string,
    user: UserModel,
    exportParamsDto: ExportQueryParamsDto
  ): Promise<ExportQueryParamsDto> {
    if (
      new PermissionSpecification(new ContactModel()).can(
        ContactPermission.VIEW,
        user
      ) === false
    ) {
      throw new ForbiddenException()
    }

    const { plan } = await this.featureService.getPlan(organizationId)

    if (
      [
        OrganizationServiceSubscriptionPlan.STARTER,
        OrganizationServiceSubscriptionPlan.FREE,
      ].includes(plan)
    ) {
      // Force assignedTo to current user
      exportParamsDto.assignedTo = user.getId()
    }

    // block user to export contacts not assigned to them
    if (
      user.getCurrentOrganizationRole() === UserRole.USER &&
      user.getId() !== exportParamsDto.assignedTo
    ) {
      // Force assignedTo to current user
      exportParamsDto.assignedTo = user.getId()
    }

    return exportParamsDto
  }

  public async buildContactSearchQueryObject(
    exportDto: ExportQueryBodyDto,
    exportParamsDto: ExportQueryParamsDto,
    paginationQueryObject: ExportPaginationQueryObject
  ): Promise<SearchQueryObject> {
    const searchQueryObject = this.mapper.map(
      exportDto,
      SearchQueryDto,
      SearchQueryObject
    )
    searchQueryObject.limit = paginationQueryObject.limitPerPage

    searchQueryObject.fields = [
      'id',
      'firstName',
      'lastName',
      'phones',
      'emails',
      'title',
      'company.name',
      'company.domain',
    ]
    searchQueryObject.source = ['id']

    //add default archived filter
    searchQueryObject.filters = searchQueryObject.filters || []

    const isEnrichmentHubExport = !!exportDto.enrichmentHubImportId
    if (isEnrichmentHubExport) {
      const enrichmentHub = await this.queryBus.execute(
        new GetEnrichmentHubByIdQuery(exportDto.enrichmentHubImportId)
      )
      searchQueryObject.filters.push({
        field: 'leadImportIds',
        operator: SearchOperation.EQUALS,
        value: enrichmentHub.leadImportId,
      })
    } else {
      // Leads imported from the Enrichment Hub are disabled and hidden by default
      searchQueryObject.filters.push({
        field: 'availableInWorkspace',
        operator: SearchOperation.EQUALS_OR_EMPTY,
        value: 'true',
      })
    }

    if (
      !searchQueryObject.filters.some(filter => filter.field === 'archived')
    ) {
      searchQueryObject.filters.push({
        field: 'archived',
        operator: SearchOperation.EQUALS_OR_EMPTY,
        value: 'false',
      })
    }

    // If selection is present, add it to the search query
    if (exportDto.selection && exportDto.selection.length > 0) {
      if (exportDto.selection.length > paginationQueryObject.exportTotalLimit) {
        throw new ForbiddenException(
          `You cannot export more than ${paginationQueryObject.exportTotalLimit} contacts`
        )
      }

      searchQueryObject.filters.push({
        field: 'id',
        operator: SearchOperation.ANY_OF_VALUES,
        values: exportDto.selection,
      })
    }

    //add default company filter if filters does not contain company.*
    if (
      !searchQueryObject.filters.some(filter =>
        filter.field.includes('company.')
      )
    ) {
      searchQueryObject.filters.push({
        field: 'company.id',
        operator: SearchOperation.NOT_EMPTY,
      })
    }

    //add assign filter if userId params is present
    if (exportParamsDto.assignedTo) {
      searchQueryObject.filters.push({
        field: 'assignUser.id',
        operator: SearchOperation.EQUALS,
        value: exportParamsDto.assignedTo,
      })
    }

    // add company.name sort after orderBy from query to have a consistent result
    if (!paginationQueryObject.orderBy) {
      searchQueryObject.sort.push({
        field: 'createdAt',
        order: SortType.DESC,
      })
      searchQueryObject.sort.push({
        field: 'lastName',
        order: SortType.DESC,
      })
    }

    if (
      paginationQueryObject.orderBy &&
      paginationQueryObject.orderBy !== 'company.name'
    ) {
      searchQueryObject.sort.push({
        field: 'company.name',
        order: SortType.ASC,
      })
    }

    searchQueryObject.setPaginationData(paginationQueryObject)

    return searchQueryObject
  }

  public async buildCompanySearchQueryObject(
    exportDto: ExportQueryBodyDto,
    exportParamsDto: ExportQueryParamsDto,
    paginationQueryObject: ExportPaginationQueryObject
  ): Promise<SearchQueryObject> {
    const searchQueryObject = this.mapper.map(
      exportDto,
      SearchQueryDto,
      SearchQueryObject
    )
    searchQueryObject.limit = paginationQueryObject.limitPerPage

    searchQueryObject.fields = ['id', 'name', 'domain']
    searchQueryObject.source = ['id']

    //add default archived filter
    searchQueryObject.filters = searchQueryObject.filters || []

    if (exportDto.enrichmentHubImportId) {
      const enrichmentHub = await this.queryBus.execute(
        new GetEnrichmentHubByIdQuery(exportDto.enrichmentHubImportId)
      )
      searchQueryObject.filters.push({
        field: 'leadImportId',
        operator: SearchOperation.EQUALS,
        value: enrichmentHub.leadImportId,
      })
    } else {
      // Leads imported from the Enrichment Hub are disabled and hidden by default
      searchQueryObject.filters.push({
        field: 'availableInWorkspace',
        operator: SearchOperation.EQUALS_OR_EMPTY,
        value: 'true',
      })
    }

    if (
      !searchQueryObject.filters.some(filter => filter.field === 'archived')
    ) {
      searchQueryObject.filters.push({
        field: 'archived',
        operator: SearchOperation.EQUALS_OR_EMPTY,
        value: 'false',
      })
    }

    // If selection is present, add it to the search query
    if (exportDto.selection && exportDto.selection.length > 0) {
      if (exportDto.selection.length > paginationQueryObject.exportTotalLimit) {
        throw new ForbiddenException(
          `You cannot export more than ${paginationQueryObject.exportTotalLimit} companies`
        )
      }

      searchQueryObject.filters.push({
        field: 'id',
        operator: SearchOperation.ANY_OF_VALUES,
        values: exportDto.selection,
      })
    }

    //add assign filter if userId params is present
    if (exportParamsDto.assignedTo) {
      searchQueryObject.filters.push({
        field: 'assignUsers.user.id',
        operator: SearchOperation.EQUALS,
        value: exportParamsDto.assignedTo,
      })
    }

    searchQueryObject.setPaginationData(paginationQueryObject)

    return searchQueryObject
  }
}

function computeZeliqValue(field: string, row: Record<string, string>): string {
  switch (field) {
    case 'fullName':
      // Try common patterns for first/last name columns
      return (
        (row['First Name'] || row['firstName'] || '') +
        (row['First Name'] || row['firstName'] ? ' ' : '') +
        (row['Last Name'] || row['lastName'] || '')
      ).trim()
    case 'emails':
      // Try common email columns, join if multiple
      return [
        row['Email'] || row['email'] || '',
        row['Secondary Email'] || row['email2'] || '',
        row['Tertiary Email'] || row['email3'] || '',
      ]
        .filter(Boolean)
        .join(', ')
    case 'phones':
      // Try common phone columns, join if multiple
      return [
        row['Work Direct Phone'] || row['phone'] || '',
        row['Home Phone'] || row['phone2'] || '',
        row['Mobile Phone'] || row['phone3'] || '',
        row['Corporate Phone'] || '',
        row['Other Phone'] || '',
      ]
        .filter(Boolean)
        .join(', ')
    case 'emailCheckResult':
      // Example: use Email Status or similar
      return row['Email Status'] || row['emailStatus'] || ''
    case 'linkedinUrl':
      return (
        row['Person Linkedin Url'] ||
        row['Linkedin Url'] ||
        row['linkedinUrl'] ||
        ''
      )
    case 'company.domain':
      return row['Website'] || row['companyDomain'] || ''
    case 'city':
      return row['City'] || row['city'] || ''
    case 'country':
      return row['Country'] || row['country'] || ''
    case 'title':
      return row['Title'] || row['title'] || ''
    default:
      return ''
  }
}
