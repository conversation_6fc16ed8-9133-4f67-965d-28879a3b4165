import {
  describe,
  beforeEach,
  afterEach,
  it,
  expect,
  jest,
} from '@jest/globals'
import { ContactModel } from '../../../domain/model/contact.model'
import { FieldModel } from '../../../domain/model/field.model'
import {
  EnrichmentHubStatusEnum,
  EnrichmentHubTypeEnum,
  LocaleEnum,
} from '@getheroes/shared'
import { Test, TestingModule } from '@nestjs/testing'
import { ExportCsvService } from './export-csv.service'
import { exportCsvTranslation } from './export-fields-translations'
import { ContactCustomExportFields } from '../../../domain/interface/lead-export.interface'
import { CheckEmailResultItem } from '../../../../shared/domain/type/data-mail-checker.type'
import { EnrichmentHubInterface } from '../../../../enrichment-hub/domain/enrichment-hub.interface'
import { FeatureService } from '../../../../feature/application/service/feature.service'
import { FileService } from '../../../infrastructure/service/file-service/file.service'
import { ConfigService } from '@nestjs/config'
import { getMapperToken } from '@automapper/nestjs'
import { QueryBus } from '@nestjs/cqrs'
import { FieldFamily } from '../../../domain/field-family.enum'
import { ExportFieldFilterItemObject } from '../../../domain/object/export-field-filter-item.object'
import { FieldKind } from '../../../../shared/domain/field-kind.enum'
import { FieldType } from '../../../../shared/domain/field-type.enum'

describe('ExportCsvService', () => {
  let module: TestingModule
  let parsedContact: ContactModel
  let field: FieldModel
  let enrichmentHub: EnrichmentHubInterface | null
  let service: ExportCsvService
  let mockFeatureService: jest.Mocked<FeatureService>
  let mockConfigService: jest.Mocked<ConfigService>
  let mockFileService: jest.Mocked<FileService>
  let mockQueryBus: jest.Mocked<QueryBus>
  let mockMapper: any

  beforeEach(async () => {
    mockFeatureService = {
      getFeatureLimit: jest.fn(),
      getPlan: jest.fn(),
    } as any

    mockConfigService = {
      get: jest.fn(),
    } as any

    mockFileService = {
      listFiles: jest.fn(),
      downloadFileInSpace: jest.fn(),
      getColumnsMapping: jest.fn(),
    } as any

    mockQueryBus = {
      execute: jest.fn(),
    } as any

    mockMapper = {
      map: jest.fn(),
    }

    module = await Test.createTestingModule({
      providers: [
        ExportCsvService,
        {
          provide: FeatureService,
          useValue: mockFeatureService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: FileService,
          useValue: mockFileService,
        },
        {
          provide: getMapperToken(),
          useValue: mockMapper,
        },
        {
          provide: QueryBus,
          useValue: mockQueryBus,
        },
      ],
    }).compile()

    service = module.get<ExportCsvService>(ExportCsvService)

    parsedContact = new ContactModel()
    parsedContact.firstName = 'John'
    parsedContact.lastName = 'Doe'
    parsedContact.company = { domain: 'example.com' } as any
    parsedContact.enrichmentAdvancedDate = null
    parsedContact.emails = []
    parsedContact.emailsCheckResult = []
    parsedContact.phones = []
    parsedContact.country = 'US'

    field = new FieldModel({
      id: '',
      name: '',
      kind: null,
      type: null,
      format: '',
      sortable: false,
      filterable: false,
      editable: false,
    })

    // Default enrichmentHub
    enrichmentHub = {
      id: '1234',
      createdBy: {} as any,
      csvImport: {} as any,
      enrichments: [],
      isLeadsEnabledInWorkspace: false,
      enrichmentTypes: [],
      status: EnrichmentHubStatusEnum.DRAFT,
      nbLeads: 0,
      organizationId: 'orgId',
      createdAt: new Date('2021-01-01'),
      updatedAt: new Date('2021-01-02'),
      nbLeadsEnriched: 0,
      totalCreditSpent: 0,
      enrichmentMetrics: {} as any,
    }
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('customContactParsing', () => {
    it('should return empty object for unknown field id', () => {
      field.id = 'UNKNOWN_FIELD'
      const result = service.customContactParsing(
        parsedContact,
        field,
        enrichmentHub
      )
      expect(result).toEqual({})
    })

    describe('FULL_NAME', () => {
      it('should return full name when field id is FULL_NAME', () => {
        field.id = ContactCustomExportFields.FULL_NAME
        field.name = 'fullNameField'

        parsedContact.firstName = 'Jane'
        parsedContact.lastName = 'Smith'

        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub
        )
        expect(result).toEqual({ fullNameField: 'Jane Smith' })
      })
    })

    describe('EMAILS', () => {
      beforeEach(() => {
        field.id = ContactCustomExportFields.EMAILS
      })

      it('should return main email and other emails', () => {
        parsedContact.emails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ]
        // The company domain is "example.com", so the main email is the first that contains it
        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )
        expect(result).toEqual({
          [exportCsvTranslation.emailOne[LocaleEnum.AMERICAN]]:
            '<EMAIL>',
          [exportCsvTranslation.emailOther[LocaleEnum.AMERICAN]]:
            '<EMAIL>, <EMAIL>',
        })
      })

      it('should default the main email to the first if no domain match', () => {
        parsedContact.emails = ['<EMAIL>', '<EMAIL>']
        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )
        expect(result[exportCsvTranslation.emailOne[LocaleEnum.AMERICAN]]).toBe(
          '<EMAIL>'
        )
        expect(
          result[exportCsvTranslation.emailOther[LocaleEnum.AMERICAN]]
        ).toBe('<EMAIL>')
      })

      it('should show "No data found" placeholder if email not found after enrichment is done', () => {
        // Mark enrichmentHub as "DONE" and no emails on contact
        enrichmentHub.status = EnrichmentHubStatusEnum.DONE
        parsedContact.emails = []

        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )
        expect(result).toEqual({
          [exportCsvTranslation.emailOne[LocaleEnum.AMERICAN]]:
            exportCsvTranslation.noDataFound[LocaleEnum.AMERICAN],
          [exportCsvTranslation.emailOther[LocaleEnum.AMERICAN]]: '',
        })
      })

      it('should show "Not enough credits" placeholder if we want to enrich but no enrichment done yet', () => {
        enrichmentHub.enrichmentTypes = [EnrichmentHubTypeEnum.EMAIL]
        // Contact has no advanced enrichment date, meaning no "recent" enrichment
        parsedContact.emails = []
        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )
        expect(result).toEqual({
          [exportCsvTranslation.emailOne[LocaleEnum.AMERICAN]]:
            exportCsvTranslation.notEnoughCredit[LocaleEnum.AMERICAN],
          [exportCsvTranslation.emailOther[LocaleEnum.AMERICAN]]: '',
        })
      })

      it('should not show any placeholder if enrichmentHub is null', () => {
        enrichmentHub = null
        parsedContact.emails = []
        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )
        // Expect empty strings rather than placeholders
        expect(result).toEqual({
          [exportCsvTranslation.emailOne[LocaleEnum.AMERICAN]]: '',
          [exportCsvTranslation.emailOther[LocaleEnum.AMERICAN]]: '',
        })
      })
    })

    describe('EMAIL_CHECK_RESULT', () => {
      beforeEach(() => {
        field.id = ContactCustomExportFields.EMAIL_CHECK_RESULT
      })

      it('should return the right deliverability for main and other emails', () => {
        parsedContact.emails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ]
        parsedContact.emailsCheckResult = [
          { mail: '<EMAIL>', result: 'valid' } as CheckEmailResultItem,
          {
            mail: '<EMAIL>',
            result: 'invalid',
          } as CheckEmailResultItem,
          {
            mail: '<EMAIL>',
            result: 'unknown',
          } as CheckEmailResultItem,
        ]

        // The "main" email is the one matching company domain "example.com": '<EMAIL>'
        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )

        expect(result).toEqual({
          [exportCsvTranslation.deliverabilityEmailOne[LocaleEnum.AMERICAN]]:
            'valid',
          [exportCsvTranslation.deliverabilityEmailOther[LocaleEnum.AMERICAN]]:
            '<EMAIL> <invalid> ; <EMAIL> <unknown>',
        })
      })

      it('should return empty if no emails or no check results', () => {
        parsedContact.emails = []
        parsedContact.emailsCheckResult = []
        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )
        expect(result).toEqual({
          [exportCsvTranslation.deliverabilityEmailOne[LocaleEnum.AMERICAN]]:
            '',
          [exportCsvTranslation.deliverabilityEmailOther[LocaleEnum.AMERICAN]]:
            '',
        })
      })

      it('should return empty check result when enrichmentHub is null and no emails', () => {
        enrichmentHub = null
        parsedContact.emails = []
        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )
        expect(result).toEqual({
          [exportCsvTranslation.deliverabilityEmailOne[LocaleEnum.AMERICAN]]:
            '',
          [exportCsvTranslation.deliverabilityEmailOther[LocaleEnum.AMERICAN]]:
            '',
        })
      })
    })

    describe('PHONES', () => {
      beforeEach(() => {
        field.id = ContactCustomExportFields.PHONES
      })

      it('should return optimal phone and other phones', () => {
        parsedContact.phones = [
          '******-555-0147',
          '******-555-0189',
          '******-555-0122',
        ]
        // Suppose `findPhoneNumberForCountry` picks the first in the list or any logic.
        // If you have actual logic to pick the "optimal" phone, mock or replicate it as needed.
        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )
        expect(result[exportCsvTranslation.phoneOne[LocaleEnum.AMERICAN]]).toBe(
          '******-555-0147' // or whichever your logic picks
        )
        expect(
          result[exportCsvTranslation.phoneOther[LocaleEnum.AMERICAN]]
        ).toBe('******-555-0189, ******-555-0122')
      })

      it('should show "No data found" if phone is not found after enrichment is done', () => {
        enrichmentHub.status = EnrichmentHubStatusEnum.DONE
        parsedContact.phones = []
        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )
        expect(result).toEqual({
          [exportCsvTranslation.phoneOne[LocaleEnum.AMERICAN]]:
            exportCsvTranslation.noDataFound[LocaleEnum.AMERICAN],
          [exportCsvTranslation.phoneOther[LocaleEnum.AMERICAN]]: '',
        })
      })

      it('should show "Not enough credits" placeholder if no recent phone enrichment', () => {
        enrichmentHub.enrichmentTypes = [EnrichmentHubTypeEnum.PHONE]
        parsedContact.phones = []
        parsedContact.enrichmentAdvancedDate = null
        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )
        expect(result).toEqual({
          [exportCsvTranslation.phoneOne[LocaleEnum.AMERICAN]]:
            exportCsvTranslation.notEnoughCredit[LocaleEnum.AMERICAN],
          [exportCsvTranslation.phoneOther[LocaleEnum.AMERICAN]]: '',
        })
      })

      it('should not show any placeholder if enrichmentHub is null (for phones)', () => {
        enrichmentHub = null
        parsedContact.phones = []
        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )
        // Expect empty strings rather than placeholders
        expect(result).toEqual({
          [exportCsvTranslation.phoneOne[LocaleEnum.AMERICAN]]: '',
          [exportCsvTranslation.phoneOther[LocaleEnum.AMERICAN]]: '',
        })
      })
    })

    describe('CUSTOM_FIELDS', () => {
      it('should return custom field value when field id matches custom field', () => {
        field.id = 'customField1'
        field.name = 'Custom Field 1'
        parsedContact.customFields = {
          customField1: 'Custom Value 1',
          customField2: 'Custom Value 2',
        }

        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub
        )

        expect(result).toEqual({
          'Custom Field 1': 'Custom Value 1',
        })
      })

      it('should return empty object when custom field does not exist', () => {
        field.id = 'nonExistentCustomField'
        field.name = 'Non Existent Field'
        parsedContact.customFields = {
          customField1: 'Custom Value 1',
        }

        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub
        )

        expect(result).toEqual({})
      })

      it('should return empty object when contact has no custom fields', () => {
        field.id = 'customField1'
        field.name = 'Custom Field 1'
        parsedContact.customFields = undefined

        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub
        )

        expect(result).toEqual({})
      })
    })
  })

  describe('objectToCsv', () => {
    it('should convert object array to CSV string', async () => {
      const data = [
        { name: 'John Doe', email: '<EMAIL>' },
        { name: 'Jane Smith', email: '<EMAIL>' },
      ]

      const result = await service.objectToCsv(data)

      expect(result).toContain('"name","email"')
      expect(result).toContain('"John Doe","<EMAIL>"')
      expect(result).toContain('"Jane Smith","<EMAIL>"')
    })

    it('should handle empty data array', async () => {
      const data = []

      // The service will throw an error when trying to process empty data
      // because Papa.parse requires columns to be defined
      await expect(service.objectToCsv(data)).rejects.toThrow(
        'Option columns is empty'
      )
    })

    it('should handle data with special characters', async () => {
      const data = [
        { name: 'John "Johnny" Doe', email: '<EMAIL>' },
        { name: 'Jane, Smith', email: '<EMAIL>' },
      ]

      const result = await service.objectToCsv(data)

      expect(result).toContain('"John ""Johnny"" Doe"')
      expect(result).toContain('"Jane, Smith"')
    })
  })

  describe('getExportLimit', () => {
    it('should return enrichment hub limit when isEnrichmentHub is true', async () => {
      const organizationId = 'org-123'
      const expectedLimit = 5000
      mockConfigService.get.mockReturnValue(expectedLimit)

      const result = await service.getExportLimit(organizationId, true)

      expect(result).toBe(expectedLimit)
      expect(mockConfigService.get).toHaveBeenCalledWith(
        'export.enrichmentHubTotalLimit'
      )
    })

    it('should return feature limit when isEnrichmentHub is false', async () => {
      const organizationId = 'org-123'
      const expectedLimit = 1000
      mockQueryBus.execute.mockResolvedValue({ featureLimit: expectedLimit })

      const result = await service.getExportLimit(organizationId, false)

      expect(result).toBe(expectedLimit)
      expect(mockQueryBus.execute).toHaveBeenCalled()
    })
  })

  describe('parseData', () => {
    it('should parse data with standard fields', async () => {
      const data = [
        { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
        { firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' },
      ]

      const fields = [
        new FieldModel({
          id: 'firstName',
          name: 'First Name',
          jsonPath: '$.firstName',
          kind: FieldKind.STANDARD,
          type: FieldType.STRING,
          format: '',
          sortable: true,
          filterable: true,
          editable: true,
        }),
        new FieldModel({
          id: 'lastName',
          name: 'Last Name',
          jsonPath: '$.lastName',
          kind: FieldKind.STANDARD,
          type: FieldType.STRING,
          format: '',
          sortable: true,
          filterable: true,
          editable: true,
        }),
      ]

      const result = await service.parseData(data, fields)

      expect(result).toHaveLength(2)
      expect(result[0]).toEqual({
        'First Name': 'John',
        'Last Name': 'Doe',
      })
      expect(result[1]).toEqual({
        'First Name': 'Jane',
        'Last Name': 'Smith',
      })
    })

    it('should handle computed fields with parseComputedFields function', async () => {
      const data = [{ firstName: 'John', lastName: 'Doe' }]

      const fields = [
        new FieldModel({
          id: ContactCustomExportFields.FULL_NAME,
          name: 'Full Name',
          kind: FieldKind.COMPUTED,
          type: null,
          format: '',
          sortable: false,
          filterable: false,
          editable: false,
        }),
      ]

      const parseComputedFields = jest.fn().mockReturnValue({
        'Full Name': 'John Doe',
      })

      const result = await service.parseData(
        data,
        fields,
        parseComputedFields as any
      )

      expect(result).toHaveLength(1)
      expect(result[0]).toEqual({
        'Full Name': 'John Doe',
      })
      expect(parseComputedFields).toHaveBeenCalledWith(data[0], fields[0])
    })

    it('should handle empty data', async () => {
      const data = []
      const fields = [
        new FieldModel({
          id: 'firstName',
          name: 'First Name',
          jsonPath: '$.firstName',
          kind: FieldKind.STANDARD,
          type: FieldType.STRING,
          format: '',
          sortable: true,
          filterable: true,
          editable: true,
        }),
      ]

      const result = await service.parseData(data, fields)

      expect(result).toHaveLength(0)
    })
  })

  describe('exportFieldsParsing', () => {
    beforeEach(() => {
      // Mock the QueryBus to return field models
      mockQueryBus.execute.mockResolvedValue([
        new FieldModel({
          id: 'firstName',
          name: 'First Name',
          jsonPath: '$.firstName',
          kind: FieldKind.STANDARD,
          type: FieldType.STRING,
          format: '',
          sortable: true,
          filterable: true,
          editable: true,
        }),
        new FieldModel({
          id: 'lastName',
          name: 'Last Name',
          jsonPath: '$.lastName',
          kind: FieldKind.STANDARD,
          type: FieldType.STRING,
          format: '',
          sortable: true,
          filterable: true,
          editable: true,
        }),
      ])
    })

    it('should parse export fields for contacts', async () => {
      const exportFields: ExportFieldFilterItemObject[] = [
        { id: 'firstName', name: 'First Name' },
        { id: ContactCustomExportFields.FULL_NAME, name: 'Full Name' },
      ]

      const result = await service.exportFieldsParsing(
        FieldFamily.CONTACT,
        'org-123',
        exportFields
      )

      expect(result).toHaveLength(2)
      expect(result[0].id).toBe('firstName')
      expect(result[0].name).toBe('First Name')
      expect(result[1].id).toBe(ContactCustomExportFields.FULL_NAME)
      expect(result[1].kind).toBe(FieldKind.COMPUTED)
    })

    it('should handle unknown fields gracefully', async () => {
      const exportFields: ExportFieldFilterItemObject[] = [
        { id: 'firstName', name: 'First Name' },
        { id: 'unknownField', name: 'Unknown Field' },
      ]

      const result = await service.exportFieldsParsing(
        FieldFamily.CONTACT,
        'org-123',
        exportFields
      )

      expect(result).toHaveLength(1) // Only firstName should be included
      expect(result[0].id).toBe('firstName')
    })
  })

  describe('Edge Cases and Error Scenarios', () => {
    describe('customContactParsing with null/undefined values', () => {
      it('should handle null firstName and lastName for FULL_NAME', () => {
        field.id = ContactCustomExportFields.FULL_NAME
        field.name = 'fullNameField'
        parsedContact.firstName = null
        parsedContact.lastName = undefined

        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub
        )

        expect(result).toEqual({ fullNameField: 'null undefined' })
      })

      it('should handle null company domain for email parsing', () => {
        field.id = ContactCustomExportFields.EMAILS
        parsedContact.emails = ['<EMAIL>']
        parsedContact.company = { domain: null } as any

        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )

        expect(result[exportCsvTranslation.emailOne[LocaleEnum.AMERICAN]]).toBe(
          '<EMAIL>'
        )
      })

      it('should handle empty emails array', () => {
        field.id = ContactCustomExportFields.EMAILS
        parsedContact.emails = []

        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )

        expect(result[exportCsvTranslation.emailOne[LocaleEnum.AMERICAN]]).toBe(
          ''
        )
        expect(
          result[exportCsvTranslation.emailOther[LocaleEnum.AMERICAN]]
        ).toBe('')
      })

      it('should handle null phones array', () => {
        field.id = ContactCustomExportFields.PHONES
        parsedContact.phones = null

        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )

        expect(result[exportCsvTranslation.phoneOne[LocaleEnum.AMERICAN]]).toBe(
          ''
        )
        expect(
          result[exportCsvTranslation.phoneOther[LocaleEnum.AMERICAN]]
        ).toBeUndefined()
      })
    })

    describe('enrichment scenarios', () => {
      it('should handle enrichmentHub with null createdAt', () => {
        field.id = ContactCustomExportFields.EMAILS
        parsedContact.emails = []
        enrichmentHub.createdAt = null
        enrichmentHub.enrichmentTypes = [EnrichmentHubTypeEnum.EMAIL]
        parsedContact.enrichmentAdvancedDate = new Date()

        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.AMERICAN
        )

        expect(result[exportCsvTranslation.emailOne[LocaleEnum.AMERICAN]]).toBe(
          exportCsvTranslation.notEnoughCredit[LocaleEnum.AMERICAN]
        )
      })

      it('should handle different locale (French)', () => {
        field.id = ContactCustomExportFields.EMAILS
        parsedContact.emails = []
        enrichmentHub.status = EnrichmentHubStatusEnum.DONE

        const result = service.customContactParsing(
          parsedContact,
          field,
          enrichmentHub,
          LocaleEnum.FRENCH
        )

        expect(result[exportCsvTranslation.emailOne[LocaleEnum.FRENCH]]).toBe(
          exportCsvTranslation.noDataFound[LocaleEnum.FRENCH]
        )
      })
    })
  })
})
