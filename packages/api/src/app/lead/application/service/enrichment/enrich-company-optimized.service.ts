import {
  BaseEvent,
  EnrichmentEvent,
  EnrichmentNameEvent,
  EnrichmentProvider,
  EnrichmentStatus,
  EnrichmentType,
} from '@getheroes/shared'
import { Inject, Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { instanceTo<PERSON>lain, plainToInstance } from 'class-transformer'
import { domainFromUrl } from '../../../../shared/domain/helper/domain.helper'
import { UserModel } from '../../../../shared/domain/model/user.model'
import { EXPOSE_GROUP } from '../../../../shared/globals/expose-group.enum'
import { CompanyEnrichmentResult } from '../../../domain/interface/enrichment/company-enrichment-result.interface'
import { EnrichmentDataProviderService } from '../../../domain/interface/enrichment/enrichment-data-provider-service.interface'
import {
  COMPANY_REPOSITORY_INTERFACE,
  CompanyRepositoryInterface,
} from '../../../domain/model/company-repository.interface'
import {
  COMPANY_ZELIQ_REPOSITORY_INTERFACE,
  CompanyZeliqRepositoryInterface,
} from '../../../domain/model/company-zeliq-repository.interface'
import { CompanyZeliqModel } from '../../../domain/model/company-zeliq.model'
import { CompanyModel } from '../../../domain/model/company.model'
import { ContactZeliqModel } from '../../../domain/model/contact-zeliq.model'
import { ContactModel } from '../../../domain/model/contact.model'
import {
  ENRICHMENT_QUERY_REPOSITORY_INTERFACE,
  EnrichmentQueryRepositoryInterface,
} from '../../../domain/model/enrichment-query-repository.interface'
import { EnrichmentQueryModel } from '../../../domain/model/enrichment-query.model'
import {
  ENRICHMENT_RESULT_REPOSITORY_INTERFACE,
  EnrichmentResultRepositoryInterface,
} from '../../../domain/model/enrichment-result-repository.interface'
import { EnrichmentModel } from '../../../domain/model/enrichment.model'
import { DataProviderResultObject } from '../../../domain/object/enrichment/data-provider-result.object'
import { isLinkedInCompanyProfileUrl } from '../../../domain/validator/is-linkedin-company-profile-url.validator'
import { EnrichmentDataProviderZeliqDataService } from '../../../infrastructure/service/enrichment/enrichment-data-provider-zeliq-data.service'
import { EnrichmentDataProviderZeliqWaterfallCompanyService } from '../../../infrastructure/service/enrichment/enrichment-data-provider-zeliq-waterfall-company'
import { EnrichmentDataProviderZeliqService } from '../../../infrastructure/service/enrichment/enrichment-data-provider-zeliq.service'
import { ENRICHMENT_LOGGER } from '../../../utils/logger-flag'
import { IndexQueueService } from '../index-queue.service'

type DataProviderService = {
  order?: number
  service: EnrichmentDataProviderService
}

type DataProviderServices = DataProviderService[]
const MAX_ENRICHMENT_AUTO_PER_SECOND = 10

@Injectable()
export class EnrichCompanyOptimizedService {
  dataProviderServices: DataProviderServices

  private readonly loggerContext = `${ENRICHMENT_LOGGER} | ${EnrichCompanyOptimizedService.name}`
  private readonly logger = new Logger(this.loggerContext)

  constructor(
    @Inject(COMPANY_REPOSITORY_INTERFACE)
    private readonly companyRepository: CompanyRepositoryInterface,
    @Inject(COMPANY_ZELIQ_REPOSITORY_INTERFACE)
    private readonly companyZeliqRepository: CompanyZeliqRepositoryInterface,
    @Inject(ENRICHMENT_QUERY_REPOSITORY_INTERFACE)
    private readonly enrichmentQueryRepository: EnrichmentQueryRepositoryInterface,
    private readonly configService: ConfigService,
    private readonly dataProviderZeliqService: EnrichmentDataProviderZeliqService,
    private readonly dataProviderZeliqDataService: EnrichmentDataProviderZeliqDataService,
    private readonly dataProviderZeliqWaterfallCompanyService: EnrichmentDataProviderZeliqWaterfallCompanyService,
    private readonly indexQueueService: IndexQueueService,
    @Inject(ENRICHMENT_RESULT_REPOSITORY_INTERFACE)
    private readonly enrichmentResultRepository: EnrichmentResultRepositoryInterface
  ) {
    this.dataProviderServices = [
      {
        order: 1,
        service: this.dataProviderZeliqService,
      },
      {
        order: 2,
        service: this.dataProviderZeliqDataService,
      },
      {
        order: 3,
        service: this.dataProviderZeliqWaterfallCompanyService,
      },
    ].sort((a, b) => a.order - b.order)
  }

  fieldsAreMissing(companyZeliqModel: CompanyZeliqModel): boolean {
    return (
      companyZeliqModel &&
      (!companyZeliqModel.name ||
        !companyZeliqModel.linkedinUrl ||
        !companyZeliqModel.country ||
        !companyZeliqModel.city ||
        !companyZeliqModel.nbEmployees ||
        !companyZeliqModel.industry)
    )
  }

  enrichmentDateIsTooOld(date: Date | null): boolean {
    const delayBetweenEnrichment = this.configService.get<number>(
      'enrichment.delayBetweenEnrichment'
    )

    const maxDate = new Date()
    maxDate.setDate(maxDate.getDate() - delayBetweenEnrichment)
    return !date || date.getTime() < maxDate.getTime()
  }

  dataRequireUpdate(date): boolean {
    const delayBetweenEnrichment = this.configService.get<number>(
      'enrichment.delayToRefreshData'
    )

    const maxDate = new Date()
    maxDate.setDate(maxDate.getDate() - delayBetweenEnrichment)

    return !date || date.getTime() < maxDate.getTime()
  }

  needToEnrich(companyModel: CompanyModel): boolean {
    return this.enrichmentDateIsTooOld(companyModel.enrichmentDate)
  }

  zeliqHasEnrichedData(
    contactModel: ContactModel,
    contactZeliqModel: ContactZeliqModel
  ): boolean {
    if (!contactZeliqModel) {
      return false
    }
    contactModel.emails = contactModel.emails || []
    contactZeliqModel.emails = contactZeliqModel.emails || []
    contactModel.phones = contactModel.phones || []
    contactZeliqModel.phones = contactZeliqModel.phones || []

    const zeliqHasNewMail = contactZeliqModel.emails.some(
      email => !contactModel.emails.includes(email)
    )

    const zeliqHasNewPhone = contactZeliqModel.phones.some(
      phone => !contactModel.phones.includes(phone)
    )

    return zeliqHasNewMail || zeliqHasNewPhone
  }

  async enrichCompanyId(
    companyId: string,
    enrichmentType: EnrichmentType,
    enrichmentId: string,
    enrichmentCreatedById: string
  ): Promise<CompanyEnrichmentResult | null> {
    const company = await this.companyRepository.findById(companyId)
    if (!company || !this.needToEnrich(company)) {
      return null
    }

    let companyZeliqModel
    const organizationId = company.organizationId
    let creditConsumed = 0
    let enrichedCompanyData = company
    const enrichmentDate = new Date()

    //inject previously enriched data if exists
    if (company.enrichmentResult) {
      const companyEnrichmentResult =
        company.enrichmentResult as Partial<CompanyModel>
      company.linkedinUrl =
        companyEnrichmentResult.linkedinUrl || company.linkedinUrl
    }

    //normalize linkedin url
    if (company.linkedinUrl) {
      company.linkedinUrl = decodeURI(company.linkedinUrl)
    }

    //remove linkedinUrl from waterfall input if not a linkedin profile
    if (!isLinkedInCompanyProfileUrl(company.linkedinUrl)) {
      enrichedCompanyData.linkedinUrl = null
    }

    this.logger.log(
      {
        data: {
          companyId,
          enrichmentType,
          enrichmentId,
          enrichmentCreatedById,
          dataProviderWaterfall: this.dataProviderServices,
        },
      },
      `${this.loggerContext} -> enrichCompanyId`
    )

    for (const dataProviderService of this.dataProviderServices) {
      const currentDataProviderName = dataProviderService.service.providerName

      //build enrichmentQuery data
      const enrichmentQueryModel = new EnrichmentQueryModel()
      enrichmentQueryModel.organizationId = organizationId
      enrichmentQueryModel.enrichmentId = enrichmentId
      enrichmentQueryModel.enrichmentType = enrichmentType
      enrichmentQueryModel.provider = dataProviderService.service.providerName
      enrichmentQueryModel.companyId = companyId
      enrichmentQueryModel.createdBy = plainToInstance(
        UserModel,
        {
          id: enrichmentCreatedById,
        },
        { ignoreDecorators: true }
      )
      const startOfQueryDateTime = new Date()

      const result: DataProviderResultObject = await dataProviderService.service
        .enrichCompany(enrichedCompanyData, enrichmentType)
        .catch(error => {
          this.logger.error(
            { err: error },
            `DataProviderService ${dataProviderService.service.providerName} error`
          )
          const endOfQueryDateTime = new Date()
          enrichmentQueryModel.duration =
            endOfQueryDateTime.getTime() - startOfQueryDateTime.getTime()
          enrichmentQueryModel.error = error.message
          //save enrichmentQuery data and continue to next provider
          this.enrichmentQueryRepository
            .save(enrichmentQueryModel)
            .catch(() => null)
          return null
        })

      if (!result) continue

      //save enrichmentQuery data
      const endOfQueryDateTime = new Date()
      enrichmentQueryModel.duration =
        endOfQueryDateTime.getTime() - startOfQueryDateTime.getTime()
      enrichmentQueryModel.query = result.query
      enrichmentQueryModel.queryResult = result.providerRawResult
      enrichmentQueryModel.creditUsed = result.providerCreditConsumed || 0
      this.enrichmentQueryRepository
        .save(enrichmentQueryModel)
        .catch(() => null)

      //if no result, continue to next provider
      if (!result?.providerRawResult || !result?.companyZeliqModel) {
        //company not found
        continue
      }

      if (result.providerCreditConsumed) {
        creditConsumed += result.providerCreditConsumed
      }

      companyZeliqModel ??= result.companyZeliqModel ?? new CompanyZeliqModel()

      //store enrichment data per provider
      companyZeliqModel.enrichmentResult =
        companyZeliqModel.enrichmentResult ?? {}

      const providerResultFiltered = instanceToPlain<CompanyZeliqModel>(
        result.companyZeliqModel,
        {
          groups: [EXPOSE_GROUP.ENRICHMENT_GENERAL],
          exposeUnsetFields: false,
        }
      )

      companyZeliqModel.enrichmentResult = {
        ...companyZeliqModel.enrichmentResult,
        [dataProviderService.service.providerName]: {
          ...providerResultFiltered,
          enrichmentDate: enrichmentDate,
        },
      }

      this.mergeDataproviderResultsIntoCompanyZeliqModels(
        companyZeliqModel,
        result.companyZeliqModel
      )

      //enrich for next provider
      if (companyZeliqModel) {
        enrichedCompanyData = plainToInstance(
          CompanyModel,
          {
            ...company,
            ...this.removeEmptyKeys(companyZeliqModel),
          },
          { ignoreDecorators: true }
        )
      }

      const zeliqDataUpdateRequired =
        currentDataProviderName === EnrichmentProvider.ZELIQ &&
        this.dataRequireUpdate(companyZeliqModel.enrichmentDate)

      //if auto enrichment and contact already enriched once, do not enrich again
      if (
        enrichmentType === EnrichmentType.GENERAL &&
        !zeliqDataUpdateRequired
      ) {
        break
      }

      if (
        !this.fieldsAreMissing(companyZeliqModel) &&
        !zeliqDataUpdateRequired
      ) {
        break
      }
    }

    //push enriched result
    const companyResult: CompanyEnrichmentResult = {
      companyId: companyId,
      creditConsumed: 0,
    }

    if (companyZeliqModel) {
      companyZeliqModel.enrichmentDate = enrichmentDate

      //if a company with same name exists, use it.
      const existingCompanyZeliqModel =
        await this.companyZeliqRepository.findByName(companyZeliqModel.name)

      if (existingCompanyZeliqModel) {
        this.mergeDataproviderResultsIntoCompanyZeliqModels(
          existingCompanyZeliqModel,
          companyZeliqModel
        )
      }

      const finalCompanyZeliqModel =
        existingCompanyZeliqModel || companyZeliqModel
      finalCompanyZeliqModel.reindexRequired = true

      //save company
      const companyZeliqModelSaved = await this.companyZeliqRepository
        .save(finalCompanyZeliqModel)
        .catch(e => {
          console.log('error saving companyZeliqModel', e.message)
          return null
        })

      if (companyZeliqModelSaved) {
        /*if (this.zeliqHasEnrichedData(company, contactZeliqModelSaved)) {
          contactResult.creditConsumed = creditConsumed
        }*/
        companyResult.enrichData = companyZeliqModelSaved
      }
    }

    await this.updateCompaniesEnrichmentData(
      plainToInstance(
        EnrichmentModel,
        {
          id: enrichmentId,
          organizationId: organizationId,
          type: enrichmentType,
          status: EnrichmentStatus.COMPLETED,
          updatedAt: new Date(),
          createdBy: {
            id: enrichmentCreatedById,
          },
        },
        { ignoreDecorators: true }
      ),
      companyResult
    )
  }

  async updateCompaniesEnrichmentData(
    enrichmentModel: EnrichmentModel,
    companyEnrichmentResult: CompanyEnrichmentResult
  ) {
    let companyModelUpdated: CompanyModel | null = null
    const companiesIdsUpdated: string[] = []

    try {
      const companyModel = await this.companyRepository.findById(
        companyEnrichmentResult.companyId
      )
      if (!companyModel) {
        throw new Error('company not found')
      }

      companyModel.enrichmentId = enrichmentModel.id
      companyModel.enrichmentStatus = enrichmentModel.status
      companyModel.enrichmentType = enrichmentModel.type
      companyModel.enrichmentDate = enrichmentModel.updatedAt

      if (EnrichmentType.EMAIL === enrichmentModel.type) {
        companyModel.enrichmentEmailDate = new Date()
      }

      //filter enrichment data
      const enrichmentData = plainToInstance(
        CompanyZeliqModel,
        companyEnrichmentResult.enrichData,
        {
          groups: [EXPOSE_GROUP.ENRICHMENT_GENERAL],
        }
      )
      companyModel.enrichmentResult = {
        ...companyModel.enrichmentResult,
        ...enrichmentData,
      }

      //add LinkedIn in root level
      if (
        !isLinkedInCompanyProfileUrl(companyModel.linkedinUrl) &&
        enrichmentData?.linkedinUrl
      ) {
        companyModel.linkedinUrl = enrichmentData?.linkedinUrl
      }

      //add website  and domain at the root level
      companyModel.foundedYear ??=
        companyEnrichmentResult?.enrichData?.foundedYear ?? null
      companyModel.website = companyModel.website || enrichmentData?.website
      companyModel.domain =
        companyModel.domain || domainFromUrl(companyModel.website)
      companyModel.enrichmentStatus = EnrichmentStatus.COMPLETED

      companyModelUpdated = await this.companyRepository.update(companyModel)
      companiesIdsUpdated.push(companyModelUpdated.id)
      console.log('enrichment company.id', companyModelUpdated.id, 'done')

      //reindex companies
      const enrichmentEvent = new EnrichmentEvent()
      enrichmentEvent.createdById = enrichmentModel.createdBy.id
      enrichmentEvent.enrichmentId = enrichmentModel.id
      enrichmentEvent.status = EnrichmentStatus.COMPLETED
      enrichmentEvent.type = enrichmentModel.type
      enrichmentEvent.lastEvent = false
      enrichmentEvent.organizationId = enrichmentModel.organizationId

      const processFinishedEvent = new BaseEvent(
        EnrichmentNameEvent.LEAD_ENRICHMENT_DONE,
        enrichmentEvent
      )

      this.indexQueueService
        .updateCompany(companyModelUpdated.id, processFinishedEvent)
        .catch(error => {
          this.logger.error(
            `An error occured while enqueuing a job to update company ${companyModelUpdated.id}`,
            error?.stack
          )
        })
    } catch (error) {
      this.logger.error(
        `An error occured while updating company enrichment data for company ${companyEnrichmentResult.companyId}`,
        error?.stack
      )
    }
  }

  removeEmptyKeys(obj) {
    return Object.fromEntries(
      Object.entries(obj).filter(([_, v]) => v !== null && v !== undefined)
    )
  }

  mergeDataproviderResultsIntoCompanyZeliqModels(
    companyZeliqModel: CompanyZeliqModel,
    companyZeliqFromResults: CompanyZeliqModel
  ) {
    if (!companyZeliqFromResults || !companyZeliqModel) return

    for (const key in companyZeliqFromResults) {
      if (['id', 'createdAt', 'updatedAt'].includes(key)) continue

      companyZeliqModel[key] =
        companyZeliqFromResults[key] ?? companyZeliqModel[key] ?? null
    }

    companyZeliqModel.domain =
      companyZeliqModel.domain || domainFromUrl(companyZeliqModel.website)
  }
}
