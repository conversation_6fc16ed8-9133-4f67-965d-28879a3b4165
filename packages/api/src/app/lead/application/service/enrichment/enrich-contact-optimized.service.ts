import {
  BaseEvent,
  EnrichmentEvent,
  EnrichmentNameEvent,
  EnrichmentProvider,
  EnrichmentProviderStatus,
  EnrichmentServiceType,
  EnrichmentStatus,
  EnrichmentType,
  EnrichmentWebsocketName,
} from '@getheroes/shared'
import { Inject, Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { plainToInstance } from 'class-transformer'
import { LeadEvents } from '../../../../shared/domain/event/event.enum'
import { ContactAttributUpdatedEvent } from '../../../../shared/domain/event/lead/contact-attribut-updated.event'
import { generateUUID } from '../../../../shared/domain/helper/uuid.helper'
import { UserModel } from '../../../../shared/domain/model/user.model'
import { CheckEmailsResponse } from '../../../../shared/domain/type/data-mail-checker.type'
import { EXPOSE_GROUP } from '../../../../shared/globals/expose-group.enum'
import { DataAsyncEnrichService } from '../../../../shared/infrastructure/service/data/data-async-enrich.service'
import { DataMailCheckerService } from '../../../../shared/infrastructure/service/data/data-mail-checker.service'
import { WebSocketService } from '../../../../shared/infrastructure/service/websocket.service'
import { LogFeature, LoggerService } from '../../../../shared/logger.service'
import { EnrichmentCost } from '../../../domain/constant/enrichment.constant'
import { ContactEnrichmentResult } from '../../../domain/interface/enrichment/contact-enrichment-result.interface'
import { EnrichmentDataProviderService } from '../../../domain/interface/enrichment/enrichment-data-provider-service.interface'
import { CompanyZeliqModel } from '../../../domain/model/company-zeliq.model'
import {
  CONTACT_REPOSITORY_INTERFACE,
  ContactRepositoryInterface,
} from '../../../domain/model/contact-repository.interface'
import {
  CONTACT_ZELIQ_REPOSITORY_INTERFACE,
  ContactZeliqRepositoryInterface,
} from '../../../domain/model/contact-zeliq-repository.interface'
import { ContactZeliqModel } from '../../../domain/model/contact-zeliq.model'
import { ContactModel } from '../../../domain/model/contact.model'
import {
  ENRICHMENT_QUERY_REPOSITORY_INTERFACE,
  EnrichmentQueryRepositoryInterface,
} from '../../../domain/model/enrichment-query-repository.interface'
import { EnrichmentQueryModel } from '../../../domain/model/enrichment-query.model'
import {
  ENRICHMENT_RESULT_REPOSITORY_INTERFACE,
  EnrichmentResultRepositoryInterface,
} from '../../../domain/model/enrichment-result-repository.interface'
import { EnrichmentResultModel } from '../../../domain/model/enrichment-result.model'
import { EnrichmentModel } from '../../../domain/model/enrichment.model'
import { DataProviderResultObject } from '../../../domain/object/enrichment/data-provider-result.object'
import { DataProviderWaterfallResult } from '../../../domain/type/enrichment/enrichment-data-provider.type'
import { isValidDomain } from '../../../domain/validator/is-domain.validator'
import { isLinkedInProfileUrl } from '../../../domain/validator/is-linkedin-profile-url.validator'
import { EnrichmentDataProviderZeliqService } from '../../../infrastructure/service/enrichment/enrichment-data-provider-zeliq.service'
import { IndexQueueService } from '../index-queue.service'
import { EnrichCompanyOptimizedService } from './enrich-company-optimized.service'
import { EnrichTransactionService } from './enrich-transaction.service'
import { EnrichmentProviderService } from './enrichment-provider.service'

type EnrichmentContext = {
  contactZeliqModel?: ContactZeliqModel
  contactModel?: ContactModel
  currentWaterfallResult?: DataProviderWaterfallResult<ContactZeliqModel>
  enrichmentType?: EnrichmentType
}
type DataProviderWaterfallElement = {
  service: EnrichmentDataProviderService
  serviceOpts?: any
  isExecutable?: (enrichmentContext: EnrichmentContext) => boolean
}

type DataProviderWaterfall = DataProviderWaterfallElement[]

interface EnrichContactParams {
  contactId: string
  enrichmentType: EnrichmentType
  enrichmentId: string
  enrichmentCreatedById: string
  isEnrichmentHubSource?: boolean
  isFromApiRequest?: boolean
}

@Injectable()
export class EnrichContactOptimizedService {
  // In-memory cache to store the state of each provider for a given contact
  private providerStatusesArray: {
    [contactId: string]: {
      name: EnrichmentProvider
      status: EnrichmentProviderStatus
      type: EnrichmentServiceType
    }[]
  } = {}
  private readonly logger = new LoggerService({
    context: EnrichContactOptimizedService.name,
    feature: LogFeature.ENRICHMENT,
  })

  constructor(
    @Inject(CONTACT_REPOSITORY_INTERFACE)
    private contactRepository: ContactRepositoryInterface,
    @Inject(CONTACT_ZELIQ_REPOSITORY_INTERFACE)
    private contactZeliqRepository: ContactZeliqRepositoryInterface,
    @Inject(ENRICHMENT_QUERY_REPOSITORY_INTERFACE)
    private enrichmentQueryRepository: EnrichmentQueryRepositoryInterface,
    @Inject(ENRICHMENT_RESULT_REPOSITORY_INTERFACE)
    private enrichmentResultRepository: EnrichmentResultRepositoryInterface,
    private readonly configService: ConfigService,
    private readonly dataMailCheckService: DataMailCheckerService,
    private readonly dataAsyncEnrichService: DataAsyncEnrichService,
    private indexQueueService: IndexQueueService,
    private readonly websocketService: WebSocketService,
    private enrichCompanyOptimizedService: EnrichCompanyOptimizedService,
    private enrichTransactionService: EnrichTransactionService,
    private dataProviderZeliqService: EnrichmentDataProviderZeliqService,
    private readonly enrichmentProviderService: EnrichmentProviderService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  fieldsAreMissing(
    contactZeliqModel: ContactZeliqModel,
    type: EnrichmentType
  ): boolean {
    if (!contactZeliqModel) {
      return true
    }

    const isMailMissing =
      !contactZeliqModel.emails || contactZeliqModel.emails.length === 0
    const isPhoneMissing =
      !contactZeliqModel.phones || contactZeliqModel.phones.length === 0

    if (type === EnrichmentType.PHONE) {
      return isPhoneMissing
    }

    if (type === EnrichmentType.ADVANCED) {
      return isMailMissing || isPhoneMissing
    }

    return isMailMissing
  }

  enrichmentDateIsTooOldOrMissing(date: Date | null): boolean {
    const delayBetweenEnrichment = this.configService.get<number>(
      'enrichment.delayBetweenEnrichment'
    )

    const maxDate = new Date()
    maxDate.setDate(maxDate.getDate() - delayBetweenEnrichment)

    return !date || date.getTime() < maxDate.getTime()
  }

  dataRequireUpdate(date: Date): boolean {
    const delayBetweenEnrichment = this.configService.get<number>(
      'enrichment.delayToRefreshData'
    )

    const maxDate = new Date()
    maxDate.setDate(maxDate.getDate() - delayBetweenEnrichment)

    return !date || date.getTime() < maxDate.getTime()
  }

  needToEnrich(contactModel: ContactModel): boolean {
    return this.enrichmentDateIsTooOldOrMissing(
      contactModel.enrichmentAdvancedDate
    )
  }

  zeliqHasEnrichedData(
    contactModel: ContactModel,
    contactZeliqModel: ContactZeliqModel
  ): boolean {
    if (!contactZeliqModel) {
      return false
    }
    contactModel.phones = contactModel.phones || []
    contactZeliqModel.phones = contactZeliqModel.phones || []

    return contactZeliqModel.phones.some(
      phone => !contactModel.phones.includes(phone)
    )
  }

  /**
   * Check if emails need to be rechecked
   * @param contactZeliqModel
   * @returns boolean
   */
  needToRecheckEmails(contactZeliqModel: ContactZeliqModel): boolean {
    const delayToRecheckEmails = this.configService.get<number>(
      'enrichment.delayToRecheckEmails'
    )
    const lastCheckDate = contactZeliqModel.emailsCheckDate
    if (!lastCheckDate) {
      return true
    }
    const currentDate = new Date()
    const diffTime = Math.abs(currentDate.getTime() - lastCheckDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    return diffDays >= delayToRecheckEmails
  }

  /**
   * Check emails validity and update contactZeliqModel
   * Update contactZeliqModel.emailsCheckDate and contactZeliqModel.emailsCheckResult with check result
   * Remove invalid emails from contactZeliqModel.emails
   */
  async checkAndUpdateEmails(
    contactZeliqModel: ContactZeliqModel,
    companyZeliqModel?: CompanyZeliqModel
  ): Promise<void> {
    const needToRecheckEmails = this.needToRecheckEmails(contactZeliqModel)
    contactZeliqModel.emailsCheckResult =
      contactZeliqModel.emailsCheckResult || []

    if (needToRecheckEmails) {
      contactZeliqModel.emailsCheckResult = []
      contactZeliqModel.emailsCheckDate = null
    }

    const contactZeliqModelEmails = contactZeliqModel.emails || []

    const emailsToCheck = contactZeliqModelEmails.filter(
      email =>
        !contactZeliqModel.emailsCheckResult.find(
          emailCheckResult => emailCheckResult.mail === email
        )
    )

    if (emailsToCheck.length > 0) {
      const contactPayload = {
        ...contactZeliqModel,
        emails: emailsToCheck,
      }

      if (companyZeliqModel) {
        contactPayload.company = companyZeliqModel
      }

      const checkEmailsResponse: CheckEmailsResponse =
        await this.dataMailCheckService
          .checkEmailsAPIV2([contactPayload])
          .catch(() => null)

      if (checkEmailsResponse && checkEmailsResponse.length > 0) {
        contactZeliqModel.emailsCheckDate = new Date()
        const checkResults = checkEmailsResponse[0].result || []

        for (let i = 0; i < checkResults.length; i++) {
          const checkResult = checkResults[i]
          if (
            !contactZeliqModel.emailsCheckResult.some(
              emailCheckResult => emailCheckResult.mail === checkResult.mail
            )
          ) {
            contactZeliqModel.emailsCheckResult.push(checkResult)
          }
        }
      }
    }

    contactZeliqModel.emails = contactZeliqModel.emailsCheckResult
      .filter(emailCheckResult => emailCheckResult.is_valid)
      .map(emailCheckResult => emailCheckResult.mail)

    contactZeliqModel.emails = [...new Set(contactZeliqModel.emails)]
  }

  removeEmptyKeys(obj) {
    if (!obj) {
      return {}
    }
    return Object.fromEntries(
      Object.entries(obj).filter(([_, v]) => v !== null && v !== undefined)
    )
  }

  mergeDataproviderResultsIntoContactZeliqModels(
    contactZeliqModel: ContactZeliqModel,
    contactZeliqFromResults: ContactZeliqModel
  ) {
    if (!contactZeliqFromResults || !contactZeliqModel) return

    for (const key in contactZeliqFromResults) {
      if (['id', 'createdAt', 'updatedAt'].includes(key)) continue

      if (key === 'phones' || key === 'emails') {
        contactZeliqModel[key] = contactZeliqModel[key] || []
        contactZeliqFromResults[key] = contactZeliqFromResults[key] || []
        contactZeliqModel[key] = [
          ...new Set([
            ...contactZeliqModel[key],
            ...contactZeliqFromResults[key],
          ]),
        ]
      } else if (key === 'company') {
        const mergedData = {
          ...contactZeliqModel[key],
          ...contactZeliqFromResults[key],
        }
        contactZeliqModel[key] = plainToInstance(
          CompanyZeliqModel,
          mergedData,
          { ignoreDecorators: true, exposeUnsetFields: false }
        )
      } else {
        contactZeliqModel[key] =
          (contactZeliqFromResults[key] ?? contactZeliqModel[key]) || null
      }
    }
  }

  async enrichContactId(params: EnrichContactParams) {
    const {
      contactId,
      enrichmentType,
      enrichmentId,
      enrichmentCreatedById,
      isEnrichmentHubSource,
      isFromApiRequest,
    } = params
    let contact = await this.contactRepository.findById(contactId)

    //enrich company if not already enriched
    if (
      enrichmentType !== EnrichmentType.GENERAL &&
      contact &&
      contact.company &&
      contact.company.enrichmentStatus !== EnrichmentStatus.COMPLETED
    ) {
      //TODO optimize, use already created job
      await this.enrichCompanyOptimizedService
        .enrichCompanyId(
          contact.company.id,
          EnrichmentType.GENERAL,
          null,
          contact.createdBy?.id
        )
        .catch(error => {
          return null
        })
      contact = await this.contactRepository.findById(contactId)
    }

    const contactResult: ContactEnrichmentResult = {
      contactId: contactId,
      creditConsumed: 0,
    }

    if (!contact || !this.needToEnrich(contact)) {
      return null
    }

    const organizationId = contact.organizationId
    let contactZeliqModel: ContactZeliqModel | null = null
    let enrichedContactData = contact

    //inject previously enriched data if exists
    if (contact.enrichmentResult) {
      const contactEnrichmentResult =
        contact.enrichmentResult as Partial<ContactModel>

      contact.linkedinUrl =
        contactEnrichmentResult.linkedinUrl || contact.linkedinUrl

      contact.emails = contactEnrichmentResult.emails || contact.emails
      contact.phones = contactEnrichmentResult.phones || contact.phones
    }

    // LinkedIn URL normalization and validation
    if (contact.linkedinUrl) {
      contact.linkedinUrl = decodeURI(contact.linkedinUrl)
      if (!isLinkedInProfileUrl(contact.linkedinUrl)) {
        enrichedContactData.linkedinUrl = null
        enrichedContactData.linkedinId = null
      }
    }

    // Company domain validation
    if (contact.company?.domain && !isValidDomain(contact.company.domain)) {
      enrichedContactData.company.domain = null
    }

    // Get the list of data providers based on enrichment type
    const dataProviderWaterfall = this.enrichmentProviderService
      .getProvidersForEnrichmentType(enrichmentType)
      .filter(provider => {
        const isZeliqProvider = [
          EnrichmentProvider.ZELIQ,
          EnrichmentProvider.ZELIQ_OPS,
          EnrichmentProvider.ZELIQ_DATA,
          EnrichmentProvider.ZELIQ_ASYNC,
        ].includes(provider.service.providerName as EnrichmentProvider)

        // Skip Zeliq providers if contact is optOut
        return !(contact?.optOut && isZeliqProvider)
      })

    if (!dataProviderWaterfall.length) {
      return null
    }

    this.logger.log({
      data: {
        contactId,
        enrichmentType,
        enrichmentId,
        enrichmentCreatedById,
        isEnrichmentHubSource,
        isFromApiRequest,
        dataProviderWaterfall: dataProviderWaterfall.map(
          provider => provider.service.providerName
        ),
      },
      message: 'enrichContactId',
    })

    const dataProviderWaterfallResult: DataProviderWaterfallResult<ContactZeliqModel> =
      {}

    //loop through dataProviderWaterfall
    for (const dataProviderWaterfallElement of dataProviderWaterfall) {
      const currentDataProviderName =
        dataProviderWaterfallElement.service.providerName
      const currentDataProviderType =
        dataProviderWaterfallElement.serviceOpts?.type

      //check if provider is executable
      if (
        dataProviderWaterfallElement.isExecutable &&
        !dataProviderWaterfallElement.isExecutable({
          contactZeliqModel: contactZeliqModel,
          contactModel: contact,
          currentWaterfallResult: dataProviderWaterfallResult,
          enrichmentType: enrichmentType,
        })
      ) {
        await this.emitProviderResult(
          organizationId,
          enrichmentCreatedById,
          contactId,
          contact.externalId,
          enrichmentId,
          enrichmentType,
          currentDataProviderName,
          currentDataProviderType,
          EnrichmentProviderStatus.NOT_FOUND,
          { reason: 'notExecutable' }
        )
        continue
      }

      await this.emitProviderResult(
        organizationId,
        enrichmentCreatedById,
        contactId,
        contact.externalId,
        enrichmentId,
        enrichmentType,
        currentDataProviderName,
        currentDataProviderType,
        EnrichmentProviderStatus.SEARCHING
      )

      //build enrichmentQuery data
      const enrichmentQueryModel = new EnrichmentQueryModel()
      enrichmentQueryModel.organizationId = organizationId
      enrichmentQueryModel.enrichmentId = enrichmentId
      enrichmentQueryModel.enrichmentType = enrichmentType
      enrichmentQueryModel.provider = currentDataProviderName
      enrichmentQueryModel.contactId = contactId
      enrichmentQueryModel.createdBy = plainToInstance(
        UserModel,
        {
          id: enrichmentCreatedById,
        },
        { ignoreDecorators: true }
      )

      const startOfQueryDateTime = new Date()

      const result: DataProviderResultObject =
        await dataProviderWaterfallElement.service
          .enrichContact(
            enrichedContactData,
            enrichmentType,
            dataProviderWaterfallElement.serviceOpts
          )
          .catch(error => {
            this.logger.error({
              error,
              data: { currentDataProviderName },
              message: `${currentDataProviderName} Error while enriching contact: ${error.message}`,
            })
            const endOfQueryDateTime = new Date()
            enrichmentQueryModel.duration =
              endOfQueryDateTime.getTime() - startOfQueryDateTime.getTime()
            enrichmentQueryModel.error = error.message
            //save enrichmentQuery data and continue to next provider
            this.enrichmentQueryRepository
              .save(enrichmentQueryModel)
              .catch(() => null)
            return null
          })

      this.logger.log({
        data: {
          provider: currentDataProviderName,
          query: {
            firstName:
              result?.query?.firstName || enrichedContactData.firstName,
            lastName: result?.query?.lastName || enrichedContactData.lastName,
            companyName:
              result?.query?.companyName || result?.query?.companyDomain,
            linkedinUrl: result?.query?.linkedinUrl || null,
          },
          result: {
            emails: result?.contactZeliqModel?.emails || [],
            phones: result?.contactZeliqModel?.phones || [],
          },
        },
        message: `Enrichment result for provider ${currentDataProviderName}`,
      })

      if (result) {
        //save enrichmentQuery data
        const endOfQueryDateTime = new Date()
        enrichmentQueryModel.duration =
          endOfQueryDateTime.getTime() - startOfQueryDateTime.getTime()
        enrichmentQueryModel.query = result.query
        enrichmentQueryModel.queryResult = result.providerRawResult
        enrichmentQueryModel.creditUsed = result.providerCreditConsumed || 0
        this.enrichmentQueryRepository
          .save(enrichmentQueryModel)
          .catch(() => null)

        //store result per provider or continue to next provider if not found
        if (!result?.providerRawResult || !result?.contactZeliqModel) {
          dataProviderWaterfallResult[currentDataProviderName] = null
          //not found
          //TODO refactor
          if (this.fieldsAreMissing(contactZeliqModel, enrichmentType)) {
            await this.emitProviderResult(
              organizationId,
              enrichmentCreatedById,
              contactId,
              contact.externalId,
              enrichmentId,
              enrichmentType,
              currentDataProviderName,
              currentDataProviderType,
              EnrichmentProviderStatus.NOT_FOUND,
              {
                contactZeliqModelEmails: contactZeliqModel?.emails,
                contactZeliqModelPhones: contactZeliqModel?.phones,
                emailsCheckResult: contactZeliqModel?.emailsCheckResult,
                enrichedContactData,
                zeliqHasEnrichedData: this.zeliqHasEnrichedData(
                  contact,
                  contactZeliqModel
                ),
                fieldsAreMissing: this.fieldsAreMissing(
                  contactZeliqModel,
                  enrichmentType
                ),
                dataRequireUpdate: this.dataRequireUpdate(
                  contactZeliqModel?.enrichmentDate
                ),
                ...(currentDataProviderName === EnrichmentProvider.ZELIQ && {
                  forceContinue: true,
                }),
              }
            )
            continue
          } else {
            // Emit WebSocket event for the current provider's result
            await this.emitProviderResult(
              organizationId,
              enrichmentCreatedById,
              contactId,
              contact.externalId,
              enrichmentId,
              enrichmentType,
              currentDataProviderName,
              currentDataProviderType,
              EnrichmentProviderStatus.FOUND,
              {
                contactZeliqModelEmails: contactZeliqModel?.emails,
                contactZeliqModelPhones: contactZeliqModel?.phones,
                emailsCheckResult: contactZeliqModel?.emailsCheckResult,
                enrichedContactData,
                zeliqHasEnrichedData: this.zeliqHasEnrichedData(
                  contact,
                  contactZeliqModel
                ),
                fieldsAreMissing: this.fieldsAreMissing(
                  contactZeliqModel,
                  enrichmentType
                ),
                dataRequireUpdate: this.dataRequireUpdate(
                  contactZeliqModel?.enrichmentDate
                ),
                ...(currentDataProviderName === EnrichmentProvider.ZELIQ && {
                  forceContinue: true,
                }),
              }
            )

            break
          }
        } else {
          dataProviderWaterfallResult[currentDataProviderName] =
            result.contactZeliqModel
        }

        if (!contactZeliqModel) {
          contactZeliqModel =
            result.contactZeliqModel || new ContactZeliqModel()
        }

        //update contactZeliq enrichment data per provider
        contactZeliqModel.enrichmentResult =
          contactZeliqModel.enrichmentResult || {}

        const contactZeliqModelProviderEnrichmentResult: Partial<ContactZeliqModel> =
          contactZeliqModel.enrichmentResult[currentDataProviderName] || {}

        if (result.contactZeliqModel?.emails?.length > 0) {
          contactZeliqModelProviderEnrichmentResult.emails =
            result.contactZeliqModel?.emails
        }
        if (result.contactZeliqModel?.phones?.length > 0) {
          contactZeliqModelProviderEnrichmentResult.phones =
            result.contactZeliqModel?.phones
        }

        if (result.providerResultId) {
          contactZeliqModelProviderEnrichmentResult.id = result.providerResultId
        }

        contactZeliqModelProviderEnrichmentResult.enrichmentDate = new Date()

        contactZeliqModel.enrichmentResult = {
          ...contactZeliqModel.enrichmentResult,
          [currentDataProviderName]: contactZeliqModelProviderEnrichmentResult,
        }

        //update contactZeliq properties with enrichment result
        this.mergeDataproviderResultsIntoContactZeliqModels(
          contactZeliqModel,
          result.contactZeliqModel
        )

        //check emails validity and remove invalid emails
        if (
          contactZeliqModel &&
          (enrichmentType === EnrichmentType.EMAIL ||
            enrichmentType === EnrichmentType.ADVANCED)
        ) {
          const companyData = plainToInstance(
            CompanyZeliqModel,
            {
              ...enrichedContactData.company,
              ...this.removeEmptyKeys(result.companyZeliqModel),
            },
            { ignoreDecorators: true }
          )

          await this.emitProviderResult(
            organizationId,
            enrichmentCreatedById,
            contactId,
            contact.externalId,
            enrichmentId,
            enrichmentType,
            currentDataProviderName,
            currentDataProviderType,
            EnrichmentProviderStatus.VERIFICATION,
            {
              emails: contactZeliqModel.emails,
            }
          )

          //check emails validity
          await this.checkAndUpdateEmails(contactZeliqModel, companyData).catch(
            () => null
          )

          this.logger.log({
            data: {
              contactZeliqModelEmails: contactZeliqModel?.emails,
              emailsCheckResult: contactZeliqModel?.emailsCheckResult,
            },
            message: 'Email check result after enrichment',
          })
        }

        //update enrichedContactData for next provider
        if (contactZeliqModel) {
          enrichedContactData = plainToInstance(
            ContactModel,
            {
              ...contact,
              ...this.removeEmptyKeys(contactZeliqModel),
              company: {
                ...contact.company,
                ...this.removeEmptyKeys(enrichedContactData.company),
                ...this.removeEmptyKeys(result.companyZeliqModel),
              },
            },
            { ignoreDecorators: true }
          )

          // Emit WebSocket event for the current provider's result
          await this.emitProviderResult(
            organizationId,
            enrichmentCreatedById,
            contactId,
            contact.externalId,
            enrichmentId,
            enrichmentType,
            currentDataProviderName,
            currentDataProviderType,
            this.fieldsAreMissing(contactZeliqModel, enrichmentType)
              ? EnrichmentProviderStatus.NOT_FOUND
              : EnrichmentProviderStatus.FOUND,
            {
              contactZeliqModelEmails: contactZeliqModel.emails,
              contactZeliqModelPhones: contactZeliqModel.phones,
              emailsCheckResult: contactZeliqModel.emailsCheckResult,
              enrichedContactData,
              zeliqHasEnrichedData: this.zeliqHasEnrichedData(
                contact,
                contactZeliqModel
              ),
              fieldsAreMissing: this.fieldsAreMissing(
                contactZeliqModel,
                enrichmentType
              ),
              dataRequireUpdate: this.dataRequireUpdate(
                contactZeliqModel?.enrichmentDate
              ),
              ...(currentDataProviderName === EnrichmentProvider.ZELIQ && {
                forceContinue: true,
              }),
            }
          )
        }

        //force next provider while ops provider is not called
        if (currentDataProviderName === EnrichmentProvider.ZELIQ) {
          continue
        }

        const zeliqDataUpdateRequired =
          currentDataProviderName === EnrichmentProvider.ZELIQ_OPS &&
          this.dataRequireUpdate(contactZeliqModel?.enrichmentDate)

        //if auto enrichment and contact already enriched once, do not enrich again
        if (
          enrichmentType === EnrichmentType.GENERAL &&
          !zeliqDataUpdateRequired
        ) {
          break
        }

        //check whether to continue to next provider or not
        if (
          !this.fieldsAreMissing(contactZeliqModel, enrichmentType) &&
          !zeliqDataUpdateRequired
        ) {
          break
        }
      } else {
        // Emit WebSocket event indicating that no result was found by this provider
        await this.emitProviderResult(
          organizationId,
          enrichmentCreatedById,
          contactId,
          contact.externalId,
          enrichmentId,
          enrichmentType,
          currentDataProviderName,
          currentDataProviderType,
          EnrichmentProviderStatus.NOT_FOUND
        )
      }
    }

    //at the end of waterfall, save and index updated contactZeliqModel
    if (contactZeliqModel) {
      //If we have not found any data the first time, we try to get it using all the data we have at the end of the waterfall
      //TODO remove this when ops provider + storage endpoint is ready
      if (!contactZeliqModel.id) {
        let lastCheckContactZeliqModel: ContactZeliqModel | null = null
        const lastCheckContactZeliqResult = await this.dataProviderZeliqService
          .enrichContact(contactZeliqModel as ContactModel, enrichmentType)
          .catch(() => null)

        if (lastCheckContactZeliqResult?.contactZeliqModel) {
          lastCheckContactZeliqModel =
            lastCheckContactZeliqResult.contactZeliqModel

          //merge data from lastCheckContactZeliqModel into contactZeliqModel to keep the most complete data
          this.mergeDataproviderResultsIntoContactZeliqModels(
            lastCheckContactZeliqModel,
            contactZeliqModel
          )
        }

        contactZeliqModel = lastCheckContactZeliqModel || contactZeliqModel
      }

      contactZeliqModel.id = contactZeliqModel.id || generateUUID()
      contactZeliqModel.enrichmentDate = new Date()
      contactZeliqModel.reindexRequired = true

      //save contact in Zeliq DB
      const contactZeliqModelSaved = await this.contactZeliqRepository
        .save(contactZeliqModel)
        .catch(() => {
          return null
        })

      if (this.zeliqHasEnrichedData(contact, contactZeliqModel)) {
        contactResult.creditConsumed = 1
      }

      contactResult.enrichData = contactZeliqModelSaved || contactZeliqModel
    }

    this.logger.log({
      data: {
        contactId: contactId,
        enrichmentType,
        enrichmentId,
        enrichedContactData: {
          id: enrichedContactData.id,
          emails: enrichedContactData.emails,
          phones: enrichedContactData.phones,
          linkedinUrl: enrichedContactData.linkedinUrl,
          enrichmentDate: enrichedContactData.enrichmentDate,
        },
        dataProviderWaterfallResult: Object.keys(
          dataProviderWaterfallResult
        ).reduce((acc, provider) => {
          acc[provider] = {
            emails: dataProviderWaterfallResult[provider]?.emails || [],
            phones: dataProviderWaterfallResult[provider]?.phones || [],
            enrichmentDate:
              dataProviderWaterfallResult[provider]?.enrichmentDate || null,
          }
          return acc
        }, {}),
        contactResult: {
          contactId: contactResult.contactId,
          creditConsumed: contactResult.creditConsumed,
          enrichData: {
            id: contactResult.enrichData?.id,
            emails: contactResult.enrichData?.emails,
            phones: contactResult.enrichData?.phones,
            linkedinUrl: contactResult.enrichData?.linkedinUrl,
            enrichmentDate: contactResult.enrichData?.enrichmentDate,
          },
        },
      },
      message: 'Final enrichment result',
    })

    // Remove the provider status array for this contactId
    delete this.providerStatusesArray[contactId]

    await this.updateContactsEnrichmentData(
      plainToInstance(
        EnrichmentModel,
        {
          id: enrichmentId,
          organizationId: organizationId,
          type: enrichmentType,
          status: EnrichmentStatus.COMPLETED,
          updatedAt: new Date(),
          createdBy: {
            id: enrichmentCreatedById,
          },
        },
        { ignoreDecorators: true }
      ),
      contactResult,
      isEnrichmentHubSource,
      isFromApiRequest
    )

    if (
      (enrichmentType === EnrichmentType.ADVANCED ||
        enrichmentType === EnrichmentType.EMAIL) &&
      !enrichedContactData.emails?.length &&
      !enrichedContactData.optOut
    ) {
      if (process.env.NODE_ENV === 'production') {
        await this.dataAsyncEnrichService.asyncEnrich(enrichedContactData)
      }
    }
  }

  async updateContactsEnrichmentData(
    enrichmentModel: Partial<EnrichmentModel>,
    contactEnrichmentResult: ContactEnrichmentResult,
    isEnrichmentHubSource: boolean,
    isFromApiRequest: boolean
  ) {
    let contactModelUpdated: ContactModel
    let creditUsed = 0
    let hasNewPhoneOrEmail = false
    let hasNewEmail = false
    let hasNewPhone = false
    try {
      const contactModel = await this.contactRepository.findById(
        contactEnrichmentResult.contactId
      )

      if (!contactModel) {
        throw new Error('contact not found')
      }

      // We keep a copy of the original contact model before any modification
      const originalContactModel = plainToInstance(ContactModel, contactModel, {
        ignoreDecorators: true,
        exposeUnsetFields: true,
      })

      contactModel.enrichmentId = enrichmentModel.id
      contactModel.enrichmentDate = enrichmentModel.updatedAt
      contactModel.enrichmentStatus = EnrichmentStatus.COMPLETED

      //on expose necessary fields
      const exposedGroups = [EXPOSE_GROUP.ENRICHMENT_GENERAL]

      if (enrichmentModel.type === EnrichmentType.ADVANCED) {
        exposedGroups.push(EXPOSE_GROUP.ENRICHMENT_ADVANCED)
        contactModel.enrichmentAdvancedDate = enrichmentModel.updatedAt
        contactModel.enrichmentAdvancedStatus = EnrichmentStatus.COMPLETED

        // Reset enrichmentNewDataAvailable and enrichmentNewDataAvailableDetails
        contactModel.enrichmentNewDataAvailable = false
        contactModel.enrichmentNewDataAvailableDetails = {}
      } else if (enrichmentModel.type === EnrichmentType.PHONE) {
        exposedGroups.push(EXPOSE_GROUP.ENRICHMENT_PHONE)
        contactModel.enrichmentPhoneDate = enrichmentModel.updatedAt
        contactModel.enrichmentPhoneStatus = EnrichmentStatus.COMPLETED

        // Remove phone from enrichmentNewDataAvailableDetails
        delete contactModel.enrichmentNewDataAvailableDetails?.phone

        // Reset enrichmentNewDataAvailable if no email
        if (!contactModel.enrichmentNewDataAvailableDetails?.email) {
          contactModel.enrichmentNewDataAvailable = false
          contactModel.enrichmentNewDataAvailableDetails = {}
        }
      } else if (enrichmentModel.type === EnrichmentType.EMAIL) {
        exposedGroups.push(EXPOSE_GROUP.ENRICHMENT_EMAIL)
        contactModel.enrichmentEmailDate = new Date()
        contactModel.enrichmentEmailStatus = EnrichmentStatus.COMPLETED

        // Remove email from enrichmentNewDataAvailableDetails
        delete contactModel.enrichmentNewDataAvailableDetails?.email

        // Reset enrichmentNewDataAvailable if no phone
        if (!contactModel.enrichmentNewDataAvailableDetails?.phone) {
          contactModel.enrichmentNewDataAvailable = false
          contactModel.enrichmentNewDataAvailableDetails = {}
        }
      }

      //filter enrichment data
      const enrichmentData = plainToInstance(
        ContactZeliqModel,
        contactEnrichmentResult.enrichData || {},
        {
          groups: exposedGroups,
        }
      )
      contactModel.enrichmentResult = {
        ...contactModel.enrichmentResult,
        ...enrichmentData,
        company: {
          ...(contactModel.enrichmentResult as Partial<ContactZeliqModel>)
            ?.company,
          ...enrichmentData.company,
        },
      }

      //add LinkedIn in root level
      if (
        !isLinkedInProfileUrl(contactModel.linkedinUrl) &&
        enrichmentData.linkedinUrl
      ) {
        contactModel.linkedinUrl = enrichmentData.linkedinUrl
      }

      //add emails in root level
      const emailsCheckResult =
        contactEnrichmentResult?.enrichData?.emailsCheckResult || []

      if (
        enrichmentModel.type === EnrichmentType.EMAIL ||
        enrichmentModel.type === EnrichmentType.ADVANCED
      ) {
        contactModel.emails = contactModel.emails || []
        contactModel.emailsCheckResult = contactModel.emailsCheckResult || []
        enrichmentData.emails = enrichmentData.emails || []

        hasNewEmail = false
        //push check result and re-order emails
        for (let j = 0; j < enrichmentData.emails.length; j++) {
          const email = enrichmentData.emails[j]
          //add email in root level
          if (!contactModel.emails.includes(email)) {
            hasNewEmail = true
            hasNewPhoneOrEmail = true
            contactModel.emails.push(email)
          }

          //update email check result
          const emailCheckResult = emailsCheckResult.find(
            emailCheckResult => emailCheckResult.mail === email
          )

          contactModel.emailsCheckResult = contactModel.emailsCheckResult || []
          const contactEmailCheckResult = contactModel.emailsCheckResult.find(
            v => v && v.mail === emailCheckResult?.mail
          )

          if (emailCheckResult) {
            if (!contactEmailCheckResult) {
              contactModel.emailsCheckResult.push(emailCheckResult)
            } else {
              Object.assign(contactEmailCheckResult, {
                ...emailCheckResult,
              })
            }
          }
        }

        if (
          hasNewEmail &&
          (enrichmentModel.type === EnrichmentType.EMAIL ||
            enrichmentModel.type === EnrichmentType.ADVANCED)
        ) {
          const costPerEmail = await this.enrichTransactionService
            .getEmailEnrichmentCost(enrichmentModel.organizationId)
            .catch(error => {
              this.logger.error({
                error,
                data: { enrichmentModel },
                message: 'Error while getting email enrichment cost',
              })
              return 0
            })
          creditUsed +=
            costPerEmail +
            (isFromApiRequest
              ? EnrichmentCost.API_EXTRA_COST[EnrichmentType.EMAIL]
              : 0)
        }
        contactModel.reorderEmailsByEmailsCheckResult()
      }

      //add phones in root level
      if (
        enrichmentModel.type === EnrichmentType.ADVANCED ||
        enrichmentModel.type === EnrichmentType.PHONE
      ) {
        contactModel.phones = contactModel.phones || []
        enrichmentData.phones = enrichmentData.phones || []

        hasNewPhone = false
        for (let j = 0; j < enrichmentData.phones.length; j++) {
          const phone = enrichmentData.phones[j]
          if (!contactModel.phones.includes(phone)) {
            hasNewPhone = true
            hasNewPhoneOrEmail = true
            contactModel.phones.push(phone)
          }
        }

        if (hasNewPhone) {
          creditUsed +=
            EnrichmentCost.PHONE +
            (isFromApiRequest
              ? EnrichmentCost.API_EXTRA_COST[EnrichmentType.PHONE]
              : 0)
        }
      }

      //update contact
      contactModelUpdated = await this.contactRepository.update(contactModel)

      // After the contact update if new phone or email is found
      if (hasNewPhoneOrEmail) {
        const contactAttributUpdatedEvent = new ContactAttributUpdatedEvent()
        contactAttributUpdatedEvent.contactmodel = originalContactModel
        contactAttributUpdatedEvent.newValues = {
          ...(hasNewEmail && { emails: contactModelUpdated.emails }),
          ...(hasNewPhone && { phones: contactModelUpdated.phones }),
        }

        this.eventEmitter.emit(
          LeadEvents.CONTACT_ATTRIBUT_UPDATED,
          contactAttributUpdatedEvent
        )
      }

      const enrichmentEvent = new EnrichmentEvent()
      enrichmentEvent.createdById = enrichmentModel.createdBy.id
      enrichmentEvent.enrichmentId = enrichmentModel.id
      enrichmentEvent.status = EnrichmentStatus.COMPLETED
      enrichmentEvent.type = enrichmentModel.type
      enrichmentEvent.listContactIds = [contactModelUpdated.id]
      enrichmentEvent.contactIdList = [contactModelUpdated.id]
      enrichmentEvent.externalContactIds = [contactModelUpdated.externalId]

      const contactsArray = await this.contactRepository.findByIds(
        enrichmentEvent.contactIdList,
        true,
        {
          company: true,
          assignUser: false,
          createdBy: false,
          archivedBy: false,
          updatedBy: false,
        }
      )
      enrichmentEvent.contactsList = Object.fromEntries(
        contactsArray.map(contact => [
          contact.id,
          {
            externalId: contactModelUpdated.externalId,
            linkedinUrl: contactModelUpdated.linkedinUrl,
            linkedinId: contactModelUpdated.linkedinId,
            emails: contactModelUpdated.emails,
            emailsCheckResult: contactModelUpdated.emailsCheckResult,
            phones: contactModelUpdated.phones,
            company: {
              id: contactModelUpdated.company?.id,
              linkedinUrl: contactModelUpdated.company?.linkedinUrl,
              domain: contactModelUpdated.company?.domain,
              website: contactModelUpdated.company?.website,
            },
            enrichmentId: contactModelUpdated.enrichmentId,
            enrichmentType: contactModelUpdated.enrichmentType,
            enrichmentStatus: contactModelUpdated.enrichmentStatus,
            enrichmentDate: contactModelUpdated.enrichmentDate,
            enrichmentEmailStatus: contactModelUpdated.enrichmentEmailStatus,
            enrichmentEmailDate: contactModelUpdated.enrichmentEmailDate,
            enrichmentPhoneStatus: contactModelUpdated.enrichmentPhoneStatus,
            enrichmentPhoneDate: contactModelUpdated.enrichmentPhoneDate,
            enrichmentAdvancedStatus:
              contactModelUpdated.enrichmentAdvancedStatus,
            enrichmentAdvancedDate: contactModelUpdated.enrichmentAdvancedDate,
            enrichmentResult: contactModelUpdated.enrichmentResult, // Temporary
            enrichmentNewDataAvailable:
              contactModelUpdated.enrichmentNewDataAvailable,
            enrichmentNewDataAvailableDetails:
              contactModelUpdated.enrichmentNewDataAvailableDetails,
          },
        ])
      )

      enrichmentEvent.lastEvent = false
      enrichmentEvent.organizationId = enrichmentModel.organizationId
      enrichmentEvent.isEnrichmentHubSource = isEnrichmentHubSource

      //reindex contacts
      const processFinishedEvent = new BaseEvent(
        EnrichmentNameEvent.LEAD_ENRICHMENT_DONE,
        enrichmentEvent
      )

      this.indexQueueService
        .updateContact(contactModelUpdated.id, processFinishedEvent)
        .catch(() => {
          //TODO log strategy
        })

      const enrichedData = contactEnrichmentResult.enrichData || {}

      //store enrichment result
      const enrichmentResultModel = new EnrichmentResultModel()
      enrichmentResultModel.enrichmentId = enrichmentModel.id
      enrichmentResultModel.organizationId = enrichmentModel.organizationId
      enrichmentResultModel.contactId = contactModelUpdated.id
      enrichmentResultModel.creditUsed = creditUsed
      enrichmentResultModel.enrichmentType = enrichmentModel.type
      enrichmentResultModel.phoneFound = enrichedData.phones?.length > 0
      enrichmentResultModel.emailFound = enrichedData.emails?.length > 0
      enrichmentResultModel.queryResult =
        contactEnrichmentResult?.enrichData?.enrichmentResult
      enrichmentResultModel.createdBy = enrichmentModel.createdBy
      enrichmentResultModel.setResultsFlag()
      enrichmentResultModel.hasNewEmail = hasNewEmail
      enrichmentResultModel.hasNewPhone = hasNewPhone

      await this.enrichmentResultRepository
        .save(enrichmentResultModel)
        .catch(error => {
          this.logger.error({
            error,
            data: {
              enrichmentId: enrichmentResultModel.enrichmentId,
              contactId: enrichmentResultModel.contactId,
              organizationId: enrichmentResultModel.organizationId,
            },
            message: 'Error saving enrichment result model',
          })
        })
    } catch (error) {
      this.logger.error({
        error,
        data: {
          contactId: contactEnrichmentResult.contactId,
          enrichmentId: enrichmentModel.id,
          organizationId: enrichmentModel.organizationId,
          type: enrichmentModel.type,
        },
        message: 'Error updating contact enrichment data',
      })
    }
  }

  private async emitProviderResult(
    organizationId: string,
    enrichmentCreatedById: string,
    contactId: string,
    externalId: string,
    enrichmentId: string,
    enrichmentType: EnrichmentType,
    providerName: string,
    providerType: EnrichmentServiceType,
    providerStatus: EnrichmentProviderStatus,
    debug?: any
  ): Promise<void> {
    // Skip events for GENERAL enrichments
    if (enrichmentType === EnrichmentType.GENERAL) return

    // Initialize array if it doesn't exist
    if (!this.providerStatusesArray[contactId]) {
      this.providerStatusesArray[contactId] = []
    }

    const currentProviderStatuses = this.providerStatusesArray[contactId]
    const previousStatus =
      currentProviderStatuses[currentProviderStatuses.length - 1] || null

    // Store the current provider status for this contactId
    currentProviderStatuses.push({
      name: providerName as EnrichmentProvider,
      status: providerStatus,
      type: providerType,
    })

    // Check if the provider is one of the Zeliq types
    const isZeliqProvider = [
      EnrichmentProvider.ZELIQ,
      EnrichmentProvider.ZELIQ_OPS,
      EnrichmentProvider.ZELIQ_DATA,
      EnrichmentProvider.ZELIQ_ASYNC,
    ].includes(providerName as EnrichmentProvider)

    // Skip "verification" status for Zeliq providers
    if (
      isZeliqProvider &&
      providerStatus === EnrichmentProviderStatus.VERIFICATION
    ) {
      return
    }

    // Handle Zeliq-specific logic: Only emit the first "SEARCHING" and the last "FOUND" or "NOT_FOUND"
    if (isZeliqProvider) {
      // Emit the first "SEARCHING" event
      if (
        providerStatus === EnrichmentProviderStatus.SEARCHING &&
        !previousStatus
      ) {
        await this.emitWebSocketEvent({
          organizationId,
          enrichmentCreatedById,
          contactId,
          externalId,
          enrichmentId,
          enrichmentType,
          providerName,
          providerStatus,
          providerType,
          debug,
        })
        return
      }
    }

    // If the current provider is not Zeliq, check if the previous one was a Zeliq provider with a final status
    if (
      !isZeliqProvider &&
      previousStatus &&
      [
        EnrichmentProvider.ZELIQ,
        EnrichmentProvider.ZELIQ_OPS,
        EnrichmentProvider.ZELIQ_DATA,
        EnrichmentProvider.ZELIQ_ASYNC,
      ].includes(previousStatus.name)
    ) {
      // Emit the final event for the previous Zeliq provider
      await this.emitWebSocketEvent({
        organizationId,
        enrichmentCreatedById,
        contactId,
        externalId,
        enrichmentId,
        enrichmentType,
        providerName: EnrichmentProvider.ZELIQ,
        providerStatus: previousStatus.status,
        providerType: previousStatus.type,
        debug,
      })
    }

    // Emit the event for non-Zeliq providers directly
    if (!isZeliqProvider) {
      await this.emitWebSocketEvent({
        organizationId,
        enrichmentCreatedById,
        contactId,
        externalId,
        enrichmentId,
        enrichmentType,
        providerName,
        providerStatus,
        providerType,
        debug,
      })
      return
    }
  }

  private async emitWebSocketEvent(eventPayload: {
    organizationId: string
    enrichmentCreatedById: string
    contactId: string
    externalId: string
    enrichmentId: string
    enrichmentType: EnrichmentType
    providerName: string
    providerStatus: EnrichmentProviderStatus
    providerType: EnrichmentServiceType
    debug?: any
  }): Promise<void> {
    // Construct the base event object for the WebSocket message
    const baseEvent = new BaseEvent(
      EnrichmentWebsocketName.ENRICHMENT_PROVIDER_RESULT,
      eventPayload
    )

    // Send the event via WebSocket to the specified recipient
    await this.websocketService.send(
      eventPayload.enrichmentCreatedById,
      JSON.stringify(baseEvent)
    )
  }
}
