import { Injectable } from '@nestjs/common'
import { ContactModel } from '../../domain/model/contact.model'
import { SearchQueryObject } from '../../../shared/domain/object/search-query.object'
import { SearchResult } from '../../../shared/domain/search.interface'
import { SearchNewFiltersAutoCompleteField } from '../../domain/search-new.enum'
import { SearchNewFilterDefinitionItemPayload } from '../../domain/object/search-new-filter-definition-item-payload.object'
import { SearchDataProviderZeliqService } from '../../infrastructure/service/search-new-contact/search-data-provider-zeliq.service'
import { LeadCategory } from '@getheroes/shared'
import { CompanyModel } from '../../domain/model/company.model'

@Injectable()
export class SearchNewLeadService {
  constructor(
    private searchProviderZeliqService: SearchDataProviderZeliqService
  ) {}

  async search<T extends ContactModel | CompanyModel>(
    searchQueryObject: SearchQueryObject,
    resource?: LeadCategory
  ): Promise<SearchResult<T>> {
    return await this.searchProviderZeliqService.search(
      searchQueryObject,
      resource
    )
  }

  async autocomplete(
    field: SearchNewFiltersAutoCompleteField,
    searchText?: string
  ): Promise<SearchNewFilterDefinitionItemPayload[]> {
    if (field === SearchNewFiltersAutoCompleteField.LOCATION) {
      const [countries, regions, cities] = await Promise.all([
        this.searchProviderZeliqService.autocomplete(
          field,
          searchText,
          'country'
        ),
        this.searchProviderZeliqService.autocomplete(
          field,
          searchText,
          'region'
        ),
        this.searchProviderZeliqService.autocomplete(field, searchText, 'city'),
      ])

      return [
        ...countries.map(item => ({
          id: item.id,
          label: item.label,
          type: 'country',
        })),
        ...regions.map(item => ({
          id: item.id,
          label: item.label,
          type: 'region',
        })),
        ...cities.map(item => ({
          id: item.id,
          label: item.label,
          type: 'city',
        })),
      ]
    }
    return await this.searchProviderZeliqService.autocomplete(field, searchText)
  }
}
