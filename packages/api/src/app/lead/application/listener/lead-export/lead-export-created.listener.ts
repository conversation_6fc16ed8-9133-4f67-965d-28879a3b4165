import { Inject, Injectable } from '@nestjs/common'
import { QueryBus } from '@nestjs/cqrs'
import { OnEvent } from '@nestjs/event-emitter'
import {
  AsyncActionCategoryEnum,
  AsyncActionStatusEnum,
} from '@getheroes/shared'
import { LeadExportEvents } from '../../../../shared/domain/event/event.enum'
import { LeadExportCreatedEvent } from '../../../../shared/domain/event/lead-export/lead-export-created.event'
import { CreateAsyncActionDto } from '../../../../shared/domain/dto/create-async-action.dto'
import { LogFeature, LoggerService } from '../../../../shared/logger.service'
import { TryCatchLogger } from '../../../../shared/domain/decorator/try-catch-logger.decorator'
import { FindLeadsExportByIdQuery } from '../../query/leads-export/find-leads-export-by-id/find-leads-export-by-id.query'
import {
  ASYNC_ACTION_SERVICE_INTERFACE,
  AsyncActionServiceInterface,
} from '../../../../shared/domain/interface/async-action-service.interface'

@Injectable()
export class LeadExportCreatedListener {
  private readonly logger = new LoggerService({
    feature: LogFeature.LEAD_EXPORT,
  })

  constructor(
    private readonly queryBus: QueryBus,
    @Inject(ASYNC_ACTION_SERVICE_INTERFACE)
    private readonly asyncActionService: AsyncActionServiceInterface
  ) {}

  @TryCatchLogger({
    feature: LogFeature.LEAD_EXPORT,
    message: 'Failed to create async action for lead export',
    stopErrorPropagation: true,
  })
  @OnEvent(LeadExportEvents.LEAD_EXPORT_CREATED)
  async createAsyncAction({
    leadExportId,
    organizationId,
  }: LeadExportCreatedEvent) {
    const leadExport = await this.queryBus.execute(
      new FindLeadsExportByIdQuery(leadExportId)
    )

    const createAsyncActionDto = new CreateAsyncActionDto({
      status: AsyncActionStatusEnum.PRELOADING,
      organizationId,
      createdById: leadExport.createdBy.id,
      category: AsyncActionCategoryEnum.LEAD_EXPORT,
      internalRelationId: leadExportId,
    })

    await this.asyncActionService.create(createAsyncActionDto)
  }
}
