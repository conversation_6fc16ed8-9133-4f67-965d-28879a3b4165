import { Injectable, Logger } from '@nestjs/common'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter'
import {
  LeadImportEvents,
  LinkedinEvents,
} from '../../../../shared/domain/event/event.enum'
import { LeadImportSourceProcessingFinishedEvent } from '../../../../shared/domain/event/lead-import/lead-import-source-processing-finished.event'
import { LinkedInPostLikersAndCommentersDeduplicationEnabledEvent } from '../../../../shared/domain/event/lead-import/linkedin-post-likers-and-commenters.-deduplication-enabled.event'
import { ContactModel } from '../../../domain/model/contact.model'
import { DeleteContactsByIdsCommand } from '../../command/contact/delete-contacts-by-ids/delete-contacts-by-ids.command'
import { FindContactsByLeadImportIdQuery } from '../../query/contact/find-contacts-by-lead-import-id/find-contacts-by-lead-import-id.query'
import { LeadImportErrorEvent } from '../../../../shared/domain/event/lead-import/lead-import-error.event'

@Injectable()
export class LinkedInPostLikersAndCommentersDeduplicationListener {
  private logger = new Logger(
    LinkedInPostLikersAndCommentersDeduplicationEnabledEvent.name
  )

  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
    private readonly eventEmitter: EventEmitter2
  ) {}

  @OnEvent(LinkedinEvents.POST_LIKERS_AND_COMMENTERS_DEDUPLICATION_ENABLED)
  async handlePostLikersAndCommentersDeduplicationEnabledEvent(
    event: LinkedInPostLikersAndCommentersDeduplicationEnabledEvent
  ) {
    try {
      const contactIds = await this.getContactsToDeleteFrom(event)

      await this.commandBus.execute(
        new DeleteContactsByIdsCommand({
          contactIds,
          organizationId: event.organizationId,
        })
      )

      this.eventEmitter.emit(
        LeadImportEvents.LEAD_IMPORT_SOURCE_PROCESSING_FINISHED,
        new LeadImportSourceProcessingFinishedEvent({
          organizationId: event.organizationId,
          leadImportId: event.leadImportId,
        })
      )
    } catch (error) {
      this.logger.error(
        'Linkedin post likers/commenters extraction: An error occured during deduplication step',
        error
      )
      this.eventEmitter.emit(
        LeadImportEvents.LEAD_IMPORT_ERROR,
        new LeadImportErrorEvent({
          leadImportId: event.leadImportId,
          organizationId: event.organizationId,
        })
      )
    }
  }

  private async getContactsToDeleteFrom(
    event: LinkedInPostLikersAndCommentersDeduplicationEnabledEvent
  ) {
    const contacts = await this.queryBus.execute<
      FindContactsByLeadImportIdQuery,
      ContactModel[]
    >(
      new FindContactsByLeadImportIdQuery(
        event.organizationId,
        event.leadImportId
      )
    )

    this.logger.log(
      `Linkedin post likers/commenters extraction: ${contacts.length} contacts processed for deduplication on Linkedin id for lead import ${event.leadImportId}`
    )

    const contactIdsForDeletion: string[] = []
    const trackedLinkedinIds = new Set<string>()

    for (const contact of contacts) {
      if (trackedLinkedinIds.has(contact.linkedinId)) {
        contactIdsForDeletion.push(contact.id)
        continue
      }

      trackedLinkedinIds.add(contact.linkedinId)
    }

    this.logger.log(
      `Linkedin post likers/commenters extraction: ${contactIdsForDeletion.length}/${contacts.length} contacts will be deleted for lead import ${event.leadImportId}`
    )

    return contactIdsForDeletion
  }
}
