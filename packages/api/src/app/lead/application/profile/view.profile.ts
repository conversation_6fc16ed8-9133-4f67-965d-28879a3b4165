import { createMap, Mapper, MappingProfile } from '@automapper/core'
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs'
import { Injectable } from '@nestjs/common'
import { ViewEntity } from '../../infrastructure/entity/view.entity'

import { ViewModel } from '../../domain/model/view.model'
import { CreateViewDto } from '../../../../ui/api/lead/dto/view/create-view.dto'
import { SearchFilterItemDto } from '../../../../ui/api/shared/infrastructure/dto/search-filter-item.dto'
import { SearchQueryFilterItemObject } from '../../../shared/domain/object/search-query-filter-item.object'

@Injectable()
export class ViewProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper)
  }

  get profile(): MappingProfile {
    return mapper => {
      createMap(mapper, <PERSON><PERSON><PERSON><PERSON>wDto, ViewModel)
      createMap(mapper, SearchFilterItemDto, SearchQueryFilterItemObject)
      createMap(mapper, ViewModel, ViewEntity)
      createMap(mapper, ViewEntity, ViewModel)
    }
  }
}
