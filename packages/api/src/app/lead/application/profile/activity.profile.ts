import { createMap, Mapper, MappingProfile } from '@automapper/core'
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs'
import { Injectable } from '@nestjs/common'
import { ActivityModel } from '../../domain/model/activity.model'
import { ActivityEntity } from '../../infrastructure/entity/activity.entity'

@Injectable()
export class ActivityProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper)
  }

  get profile(): MappingProfile {
    return mapper => {
      createMap(mapper, ActivityModel, ActivityEntity)
      createMap(mapper, ActivityEntity, ActivityModel)
    }
  }
}
