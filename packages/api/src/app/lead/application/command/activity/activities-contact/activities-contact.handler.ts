import { Inject } from '@nestjs/common'
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs'
import { ActivitiesContactCommand } from './activities-contact.command'
import {
  ACTIVITY_REPOSITORY_INTERFACE,
  ActivityRepositoryInterface,
} from '../../../../domain/model/activity-repository.interface'
import { ActivityModel } from '../../../../domain/model/activity.model'

@CommandHandler(ActivitiesContactCommand)
export class ActivitiesContactHandler
  implements ICommandHandler<ActivitiesContactCommand>
{
  constructor(
    @Inject(ACTIVITY_REPOSITORY_INTERFACE)
    private readonly activityRepository: ActivityRepositoryInterface
  ) {}

  async execute(command: ActivitiesContactCommand): Promise<ActivityModel[]> {
    return await this.activityRepository.findByContactIdExcludingTypes(
      command.organizationId,
      command.contactId,
      command.excludedTypes
    )
  }
}
