import { LeadExportModel } from '../../../../domain/model/lead-export.model'

export type CreateLeadExportCommandProps = Pick<
  LeadExportModel,
  | 'exportFields'
  | 'pagination'
  | 'searchQueryObject'
  | 'leadType'
  | 'organizationId'
  | 'createdById'
  | 'createdBy'
  | 'exportType'
>

export type CreateLeadExportCommandResponse = Promise<LeadExportModel>

export class CreateLeadExportCommand {
  constructor(readonly props: CreateLeadExportCommandProps) {}
}

export class ExportLimitReachedError extends Error {
  constructor(maxLimit: number, limitType: 'simultaneous' | 'monthly') {
    super(
      limitType === 'simultaneous'
        ? `You reached the limit of ${maxLimit} simultaneous exports. Please wait for those exports to finish before launching a new one.`
        : `You reached the limit of ${maxLimit} monthly exports. Your limit will be reset at the end of the month.`
    )
    this.name = 'SimultaneousExportLimitReachedError'
  }
}

export class EmptyLeadsFilterResultError extends Error {
  constructor() {
    super('No leads match your current filters / search terms')
    this.name = 'EmptyLeadsFilterResultError'
  }
}
