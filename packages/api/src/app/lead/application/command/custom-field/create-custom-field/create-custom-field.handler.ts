import { ConflictException, Inject } from '@nestjs/common'
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler, QueryBus } from '@nestjs/cqrs'
import { CreateCustomFieldCommand } from './create-custom-field.command'
import {
  CUSTOM_FIELD_REPOSITORY_INTERFACE,
  CustomFieldRepositoryInterface,
} from '../../../../domain/model/custom-field-repository.interface'
import { CustomFieldModel } from '../../../../domain/model/custom-field.model'
import { ListFieldQuery } from '../../../query/list-field/list-field.query'
import { FieldModel } from '../../../../domain/model/field.model'
import { NotDefaultFieldSpecification } from '../../../../domain/specification/not-default-field.specification'
import { FieldKind } from '../../../../../shared/domain/field-kind.enum'

@CommandHandler(CreateCustomFieldCommand)
export class CreateCustom<PERSON>ieldHandler
  implements ICommandHandler<CreateCustomFieldCommand>
{
  constructor(
    @Inject(CUSTOM_FIELD_REPOSITORY_INTERFACE)
    private customFieldRepository: CustomFieldRepositoryInterface,
    private queryBus: QueryBus
  ) {}

  async execute(command: CreateCustomFieldCommand): Promise<CustomFieldModel> {
    const defaultFields: FieldModel[] = await this.queryBus.execute(
      new ListFieldQuery(
        command.customFieldModel.organizationId,
        command.customFieldModel.fieldFamily,
        FieldKind.STANDARD
      )
    )

    if (
      new NotDefaultFieldSpecification().isSatisfiedBy(
        command.customFieldModel,
        defaultFields
      ) === false
    ) {
      throw new ConflictException(
        `Field ${command.customFieldModel.fieldId} cannot be created, it is a default field`
      )
    }

    const customFieldModel = await this.customFieldRepository
      .create(command.customFieldModel)
      .catch(error => {
        throw new ConflictException(error.message)
      })
    return customFieldModel
  }
}
