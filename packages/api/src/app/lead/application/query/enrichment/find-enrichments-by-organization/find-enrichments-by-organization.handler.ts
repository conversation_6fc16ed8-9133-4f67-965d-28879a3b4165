import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'

import {
  ENRICHMENT_REPOSITORY_INTERFACE,
  EnrichmentRepositoryInterface,
} from '../../../../domain/model/enrichment-repository.interface'
import { FindEnrichmentsByOrganizationQuery } from './find-enrichments-by-organization.query'
import { PaginationService } from '../../../../../shared/infrastructure/service/pagination.service'
import { EnrichmentEntity } from '../../../../infrastructure/entity/enrichment.entity'
import { EnrichmentModel } from '../../../../domain/model/enrichment.model'
import { PaginationResultObject } from '../../../../../shared/domain/object/pagination-result.object'

@QueryHandler(FindEnrichmentsByOrganizationQuery)
export class FindEnrichmentsByOrganizationHandler
  implements IQueryHandler<FindEnrichmentsByOrganizationQuery>
{
  constructor(
    @Inject(ENRICHMENT_REPOSITORY_INTERFACE)
    private enrichmentRepository: EnrichmentRepositoryInterface,
    private paginationService: PaginationService
  ) {}

  execute(
    query: FindEnrichmentsByOrganizationQuery
  ): Promise<PaginationResultObject<EnrichmentModel>> {
    const queryBuilder =
      this.enrichmentRepository.queryFindByOrganizationAndTypes(
        query.organizationId,
        query.pagination,
        query.types,
        query.filters
      )

    return this.paginationService.paginate<EnrichmentEntity, EnrichmentModel>(
      EnrichmentEntity,
      EnrichmentModel,
      queryBuilder,
      query.pagination
    )
  }
}
