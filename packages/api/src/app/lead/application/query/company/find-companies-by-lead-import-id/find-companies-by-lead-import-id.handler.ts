import { Inject } from '@nestjs/common'
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs'
import {
  COMPANY_REPOSITORY_INTERFACE,
  CompanyRepositoryInterface,
} from '../../../../domain/model/company-repository.interface'
import { CompanyModel } from '../../../../domain/model/company.model'
import { FindCompaniesByLeadImportIdQuery } from './find-companies-by-lead-import-id.query'

@QueryHandler(FindCompaniesByLeadImportIdQuery)
export class FindCompaniesByLeadImportIdHandler
  implements IQueryHandler<FindCompaniesByLeadImportIdQuery>
{
  constructor(
    @Inject(COMPANY_REPOSITORY_INTERFACE)
    private readonly companyRepository: CompanyRepositoryInterface
  ) {}

  async execute(
    query: FindCompaniesByLeadImportIdQuery
  ): Promise<CompanyModel[]> {
    const companies =
      await this.companyRepository.findByLeadImportAndOrganization(
        query.organizationId,
        query.leadImportId
      )

    return companies
  }
}
