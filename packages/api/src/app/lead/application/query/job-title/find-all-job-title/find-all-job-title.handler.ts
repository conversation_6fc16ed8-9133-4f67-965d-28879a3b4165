import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'
import { FindAllJobTitleQuery } from './find-all-job-title.query'
import {
  JOB_TITLE_REPOSITORY_INTERFACE,
  JobTitleRepositoryInterface,
} from '../../../../domain/model/job-title-repository.interface'
import { JobTitleModel } from '../../../../domain/model/job-title.model'

@QueryHandler(FindAllJobTitleQuery)
export class FindAllJobTitleHandler
  implements IQueryHandler<FindAllJobTitleQuery>
{
  constructor(
    @Inject(JOB_TITLE_REPOSITORY_INTERFACE)
    private jobTitleRepository: JobTitleRepositoryInterface
  ) {}

  execute(): Promise<JobTitleModel[]> {
    return this.jobTitleRepository.findAll()
  }
}
