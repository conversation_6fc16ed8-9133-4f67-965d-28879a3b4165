import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'

import {
  CONTACT_REPOSITORY_INTERFACE,
  ContactRepositoryInterface,
} from '../../../../domain/model/contact-repository.interface'
import { CountUserAssignedContactQuery } from './count-user-assigned-contact.query'
import {
  COMPANY_USER_REPOSITORY_INTERFACE,
  CompanyUserRepositoryInterface,
} from '../../../../domain/model/company-user-repository.interface'

@QueryHandler(CountUserAssignedContactQuery)
export class CountUserAssignedContactHandler
  implements IQueryHandler<CountUserAssignedContactQuery>
{
  constructor(
    @Inject(CONTACT_REPOSITORY_INTERFACE)
    private contactRepository: ContactRepositoryInterface,
    @Inject(COMPANY_USER_REPOSITORY_INTERFACE)
    private companyUserRepository: CompanyUserRepositoryInterface
  ) {}

  async execute(query: CountUserAssignedContactQuery): Promise<{
    contacts: number
    companies: number
  }> {
    const { organizationId, userId } = query

    const [contactCount, companyCount] = await Promise.all([
      this.contactRepository.countByAssignUserId(organizationId, userId),
      this.companyUserRepository.countByAssignUserId(organizationId, userId),
    ])

    return {
      contacts: contactCount,
      companies: companyCount,
    }
  }
}
