import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs'
import { Inject } from '@nestjs/common'

import { CountOrganizationContactQuery } from './count-organization-contact.query'

import {
  CONTACT_REPOSITORY_INTERFACE,
  ContactRepositoryInterface,
} from '../../../../domain/model/contact-repository.interface'
import {
  COMPANY_REPOSITORY_INTERFACE,
  CompanyRepositoryInterface,
} from '../../../../domain/model/company-repository.interface'

@QueryHandler(CountOrganizationContactQuery)
export class CountOrganizationContactHandler
  implements IQueryHandler<CountOrganizationContactQuery>
{
  constructor(
    @Inject(CONTACT_REPOSITORY_INTERFACE)
    private contactRepository: ContactRepositoryInterface,
    @Inject(COMPANY_REPOSITORY_INTERFACE)
    private companyRepository: CompanyRepositoryInterface
  ) {}

  async execute(query: CountOrganizationContactQuery): Promise<any> {
    const { organizationId } = query

    const [contactCount, companyCount] = await Promise.all([
      this.contactRepository.count({
        where: { organizationId, availableInWorkspace: true },
      }),
      this.companyRepository.count({
        where: { organizationId, availableInWorkspace: true },
      }),
    ])

    return {
      contacts: contactCount,
      companies: companyCount,
    }
  }
}
