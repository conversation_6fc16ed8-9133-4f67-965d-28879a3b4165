# Enrichment Hub Module

[Datadog logs for monitoring and debugging](https://app.datadoghq.eu/logs?query=env%3Aprod%20service%3A%28dashboard-api-service%20OR%20dashboard-consumer-service%20OR%20dashboard-cron-service%29%20-%22%2Fapi%2F%3A%22%20%40data.feature%3A%22Enrichment%20hub%22&agg_m=count&agg_m_source=base&agg_q=status%2Cservice&agg_q_source=base%2Cbase&agg_t=count&clustering_pattern_field_path=message&cols=%40data.feature%2Ccontext%2C%40data.organizationId&fromUser=true&messageDisplay=inline&refresh_mode=sliding&sort_m=%2C&sort_m_source=%2C&sort_t=%2C&storage=hot&stream_sort=time%2Cdesc&top_n=10%2C10&top_o=top%2Ctop&viz=stream&x_missing=true%2Ctrue&from_ts=1742350999557&to_ts=1742379799557&live=true)

## Overview

The Enrichment Hub module enables users to enrich lead data with additional information such as email addresses and phone numbers.
This module uses the enrichment module, handling high volume of leads to enrich, processing them in batches, managing credit usage, and tracking enrichment metrics.

## Key Features
- Enrich high volume of leads with email addresses and phone numbers
- Batch processing for efficient resource utilization
- Credit allocation and management
- Enrichment metrics tracking
- Available in workspace switch for lead visibility in the UI
- Support for re-enrichment of leads with new data

## Architecture

The Enrichment Hub module follows a clean architecture pattern with:

- **Domain Layer**: Contains the core business logic, models, and interfaces
- **Application Layer**: Implements use cases through commands, queries, and event handlers
- **Infrastructure Layer**: Provides implementations for repositories and external services
- **UI Layer**: Exposes the functionality through REST API endpoints

### Key Components

#### Models

- `EnrichmentHubModel`: Core domain model representing an enrichment hub
- `EnrichmentModel`: Model representing an enrichment batch

#### Repositories

- `EnrichmentHubRepository`: Manages persistence of enrichment hub entities

#### Services

- `EnrichmentHubService`: Provides business logic for enrichment hubs
- `EnrichmentHubCreditsService`: Manages credit allocation and consumption
- `EnrichmentHubErrorRecoveryService`: Detects and recovers stuck enrichment hubs

#### Command Handlers

- `CreateEnrichmentHubHandler`: Creates new enrichment hubs
- `InitiateEnrichmentHubProcessingHandler`: Starts the enrichment process
- `EnrichNextHubBatchHandler`: Processes the next batch of leads
- `FinaliseOneEnrichmentBatchHandler`: Finalizes a batch of enriched leads
- `RelaunchEnrichmentHubProcessingHandler`: Restarts enrichment for new data or failed enrichments
- `RelaunchForStuckEnrichmentHandler`: Handles the recovery of stuck enrichment hubs

#### Query Handlers

- `GetAllEnrichmentHubByOrganizationHandler`: Lists all enrichment hubs for an organization
- `GetEnrichmentHubByIdHandler`: Retrieves a specific enrichment hub

#### Event Listeners

- `ReadyForNextEnrichmentBatchListener`: Triggers the next batch of enrichments
- `EnrichmentHubSuccessfulListener`: Handles successful enrichment completion
- `LeadImportDoneListener`: Processes lead imports for enrichment
- `EnrichmentBatchFinishedListener`: Handles completion of an enrichment batch

#### Cron Jobs

- `EnrichmentHubErrorRecoveryCron`: Runs hourly to detect and recover stuck enrichment hubs

#### Queue Consumers

- `EnrichmentHubInitiateProcessingConsumer`: Processes enrichment hub jobs from the queue

## Enrichment Types

The module supports two main enrichment types:

1. **Email** (`EnrichmentHubTypeEnum.EMAIL`): Enriches leads with email addresses
2. **Phone** (`EnrichmentHubTypeEnum.PHONE`): Enriches leads with phone numbers

These can be used individually or together.

## Enrichment Hub Status

The enrichment hub status is tracked through `EnrichmentHubStatusEnum`:

1. **DRAFT**: Initial state when the hub is created
2. **IN_PROCESS**: Enrichment is in progress
3. **DONE**: Enrichment completed successfully
4. **PARTIALLY_DONE**: Enrichment completed partially (e.g., ran out of credits)
5. **FAILED**: Enrichment failed

## Enrichment Reasons

The reason for enrichment is tracked through `HubEnrichmentReasonEnum`:

1. **IDLE**: No active enrichment process
2. **ENRICH_ALL_FIRST_LAUNCH**: Initial enrichment of all leads
3. **RELAUNCH_FOR_NOT_ENRICHED**: Re-enrichment of leads that failed to enrich
4. **RELAUNCH_FOR_NEW_DATA_AVAILABLE**: Re-enrichment of leads with new data available

## Workflow

### Enrichment Hub Creation Flow

1. User initiates an enrichment hub through the API
2. System creates an `EnrichmentHub` entity with status `DRAFT`
3. User selects enrichment types (EMAIL, PHONE, or both)
4. User imports leads through CSV or selects existing leads
5. System creates a `LeadImport` entity linked to the `EnrichmentHub`
6. When leads are imported and enrichment types are set, the enrichment process is initiated

### Enrichment Process Flow

1. `InitiateEnrichmentHubProcessingHandler` starts the enrichment process:
   - Updates status to `IN_PROCESS`
   - Estimates credit cost for all leads
   - Allocates credits from the organization's balance
   - Emits `READY_FOR_NEXT_ENRICHMENT_BATCH` event

2. `EnrichNextHubBatchHandler` processes batches of leads:
   - Determines batch size based on available credits
   - Retrieves leads to enrich
   - Creates an enrichment model
   - Adds jobs to the enrichment queue
   - Updates lead enrichment status

3. When a batch is completed, `FinaliseOneEnrichmentBatchHandler`:
   - Updates enrichment metrics
   - Reallocates unused credits to the hub
   - Checks if more leads need to be enriched
   - If more leads exist and credits are available, emits `READY_FOR_NEXT_ENRICHMENT_BATCH` event
   - If all leads are enriched or credits are exhausted, finalizes the enrichment hub

4. When the enrichment process is complete, `EnrichmentHubService.finalizeEnrichmentHub`:
   - Refunds unused credits to the organization
   - Updates status to `DONE` or `PARTIALLY_DONE`
   - Sends notifications to the user

### Re-enrichment Flow

1. User initiates re-enrichment for:
   - Leads that failed to enrich (`RELAUNCH_FOR_NOT_ENRICHED`)
   - Leads with new data available (`RELAUNCH_FOR_NEW_DATA_AVAILABLE`)
2. System allocates credits and starts the enrichment process
3. Only the specified leads are processed in batches
4. When complete, the enrichment hub is finalized

### Automatic Error Recovery Flow

1. The `EnrichmentHubErrorRecoveryCron` runs hourly to detect stuck enrichment hubs
2. For each hub in `IN_PROCESS` status, the system:
   - Calculates the expected completion time based on the number of leads (nbLeads × 1 minute)
   - Checks if the current time exceeds the expected completion time
   - Identifies the latest enrichment for the hub
   - If the enrichment is stuck in `IN_PROGRESS` status, initiates recovery
3. The recovery process is handled by the `relaunchForStuckEnrichments` method which:
   - Updates the stuck enrichment status to `FAILED`
   - Finalizes the enrichment hub as `PARTIALLY_DONE`
   - Refunds the allocated credits to the organization
   - Automatically relaunches the enrichment hub to process remaining leads
   - Skips sending a confirmation email to the user
4. This ensures that enrichment hubs don't get permanently stuck and can recover automatically

## Credit System

The Enrichment Hub module integrates with the Credit module to manage credit allocation and consumption:

### Credit Costs

- Email enrichment: 1 credit per lead (free for ADVANCED and ENTERPRISE plans)
- Phone enrichment: 10 credits per lead

### Credit Allocation

1. When an enrichment process starts, credits are allocated from the organization's balance
2. Credits are set aside in the `allocatedCreditsForEnrichments` field
3. This prevents the UI from showing fluctuating credit balances during the process and avoids a problem of double-spending

### Credit Consumption

1. As each batch is processed, credits are consumed based on the actual enrichment performed
2. Unused credits from a batch are reallocated to the hub for future batches
3. The total credits spent are tracked in the `totalCreditSpent` field

### Credit Refund

1. When the enrichment process is complete, unused credits are refunded to the organization
2. This ensures that organizations only pay for successful enrichments

## Data Structure and Relationships

The Enrichment Hub module uses several database tables to manage the enrichment process and maintain relationships with other entities in the system.

### Database Schema

#### Main Tables

1. **enrichment_hub**: Stores the main enrichment hub information
   - Primary key: `id` (UUID)
   - Foreign keys:
     - `organization_id` → `organizations.id`
     - `lead_import_id` → `lead_imports.id`
     - `csv_import_id` → `csv_imports.id`
     - `user_id` → `users.id`
   - Key fields:
     - `enrichment_types`: Array of enrichment types (EMAIL, PHONE)
     - `status`: Current status of the enrichment hub
     - `current_enrichment_reason`: Reason for the current enrichment
     - `allocated_credits_for_enrichments`: Credits allocated for the enrichment process
     - `total_credit_spent`: Total credits spent on enrichments
     - `nb_leads`: Total number of leads in the hub
     - `nb_leads_enriched`: Number of leads successfully enriched
     - `is_leads_enabled_in_workspace`: Whether leads are visible in the workspace
     - `has_new_data_available`: Whether new data is available for re-enrichment
     - `enrichment_metrics`: JSON data with enrichment metrics

2. **enrichments**: Stores individual enrichment jobs
   - Foreign keys:
     - `enrichment_hub_id` → `enrichment_hub.id`
   - Key fields:
     - `type`: Type of enrichment (EMAIL, PHONE, ADVANCED)
     - `credit_estimated`: Estimated credit cost
     - `credit_used`: Actual credits used
     - `nb_enrichments_estimated`: Estimated number of enrichments
     - `nb_enrichments_done`: Number of enrichments completed

#### Related Tables

1. **lead_imports**: Stores lead import information
   - One-to-one relationship with enrichment_hub
   - Used to track the source of leads for enrichment

2. **csv_imports**: Stores CSV import information
   - One-to-one relationship with enrichment_hub
   - Used when leads are imported directly from CSV

3. **contacts**: Stores contact leads
   - Many-to-many relationship with enrichment_hub through enrichments
   - Enrichment results are stored in contact records

### Entity Relationships

```
+----------------+       +----------------+       +----------------+
|                |       |                |       |                |
| organizations  |<------| enrichment_hub |------>|  lead_imports  |
|                |       |                |       |                |
+----------------+       +----------------+       +----------------+
        ^                       ^                        ^
        |                       |                        |
        |                       |                        |
        v                       v                        v
+----------------+       +----------------+       +----------------+
|                |       |                |       |                |
|     users      |       |  enrichments   |       |  csv_imports   |
|                |       |                |       |                |
+----------------+       +----------------+       +----------------+
                                 ^
                                 |
                                 v
                         +----------------+
                         |                |
                         |    contacts    |
                         |                |
                         +----------------+
```

### Key Relationships

1. **EnrichmentHub to Organization**: Each enrichment hub belongs to one organization
2. **EnrichmentHub to User**: Each enrichment hub is created by one user
3. **EnrichmentHub to LeadImport**: Each enrichment hub may be associated with one lead import
4. **EnrichmentHub to CSVImport**: Each enrichment hub may be associated with one CSV import
5. **EnrichmentHub to Enrichments**: One enrichment hub has many enrichment jobs
6. **Enrichments to Contacts**: Each enrichment job processes multiple contacts

### Data Flow

1. When an enrichment hub is created, a record is inserted into the `enrichment_hub` table
2. Leads are imported through a lead import or CSV import
3. When the enrichment process starts, enrichment jobs are created in the `enrichments` table
4. As leads are enriched, the `contacts` table is updated with enrichment results
5. Metrics are updated in the `enrichment_hub` table
6. When the process is complete, the status is updated to DONE or PARTIALLY_DONE

## API Endpoints

The module exposes several REST API endpoints:

- `POST /:organizationId/enrichment-hub`: Create a new enrichment hub
- `GET /:organizationId/enrichment-hub`: Get all enrichment hubs for an organization
- `GET /:organizationId/enrichment-hub/:id`: Get a specific enrichment hub
- `PATCH /:organizationId/enrichment-hub`: Update an enrichment hub
- `DELETE /:organizationId/enrichment-hub/:id`: Delete an enrichment hub
- `POST /:organizationId/enrichment-hub/:id/relaunch`: Relaunch enrichment for failed or new data

## Integration with Other Modules

The Enrichment Hub module integrates with several other modules:

1. **Lead Import Module**: For importing leads to enrich
2. **Credit Module**: For managing credit allocation and consumption
3. **Lead Module**: For updating lead data with enrichment results
4. **Enrichment Module**: For performing the actual enrichment operations
5. **Lead Export Module**: For exporting the leads to a CSV file

## Error Handling

The module includes comprehensive error handling:

- `TryCatchLogger` decorator for error logging
- Validation of enrichment parameters
- Credit allocation checks to prevent over-consumption
- Batch processing to manage large datasets efficiently
- Status tracking for failed enrichments

## Best Practices for Development

1. **Adding a New Enrichment Type**:
   - Add the type to `EnrichmentHubTypeEnum`
   - Update the credit cost calculation in `EnrichmentHubCreditsService`
   - Implement the enrichment logic in the appropriate handler

2. **Modifying the Batch Processing**:
   - Update the batch size configuration in `enrichment-hub.config.ts`
   - Ensure credit allocation is properly handled
   - Test with various organization credit balances

3. **Error Handling**:
   - Use the `TryCatchLogger` decorator for error logging
   - Update the enrichment hub status appropriately
   - Ensure credits are properly refunded on failure

4. **Testing**:
   - Write unit tests for command and query handlers
   - Test the credit allocation and consumption logic
   - Verify that metrics are correctly updated

## Troubleshooting

Common issues and their solutions:

[Use the datadog logs for troubleshooting](https://app.datadoghq.eu/logs?query=env%3Aprod%20service%3A%28dashboard-api-service%20OR%20dashboard-consumer-service%20OR%20dashboard-cron-service%29%20-%22%2Fapi%2F%3A%22%20%40data.feature%3A%22Enrichment%20hub%22&agg_m=count&agg_m_source=base&agg_q=status%2Cservice&agg_q_source=base%2Cbase&agg_t=count&clustering_pattern_field_path=message&cols=%40data.feature%2Ccontext%2C%40data.organizationId&fromUser=true&messageDisplay=inline&refresh_mode=sliding&sort_m=%2C&sort_m_source=%2C&sort_t=%2C&storage=hot&stream_sort=time%2Cdesc&top_n=10%2C10&top_o=top%2Ctop&viz=stream&x_missing=true%2Ctrue&from_ts=1742350999557&to_ts=1742379799557&live=true)

1. **Enrichment hub stuck in IN_PROCESS status**:
  - A related enrichment is probably stuck in that same status in the enrichments table
  - The system has an automatic error recovery mechanism (`EnrichmentHubErrorRecoveryCron`) that runs hourly to detect and recover stuck hubs
  - The cron job identifies hubs that have been in IN_PROCESS status for longer than expected (based on the number of leads) and recovers them by:
    - Marking the stuck enrichment as FAILED
    - Refunding allocated credits
    - Setting the hub status to PARTIALLY_DONE
    - Automatically relaunching the hub to process remaining leads

2. **Credits not being properly allocated or refunded**:
  - Verify that the credit transaction was created
  - Credits can get stuck if the enrichment hub is stuck in IN_PROCESS status
  - Ensure that the credit cost calculation is correct

3. **Leads not being enriched**:
  - Ask the Marvel heroes for help ❤️
