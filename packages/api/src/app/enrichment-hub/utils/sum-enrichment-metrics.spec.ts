import { describe, expect, it } from '@jest/globals'
import { sumEnrichmentMetrics, sumProviders } from './sum-enrichment-metrics'
import { EnrichmentHubMetrics, EnrichmentProvider } from '@getheroes/shared'

describe('sumProviders', () => {
  it('should return an empty object when both providers are undefined', () => {
    const result = sumProviders(undefined, undefined)
    expect(result).toEqual({})
  })

  it('should return the first provider counts when second is undefined', () => {
    const providers1 = {
      [EnrichmentProvider.DATAGMA]: 5,
      [EnrichmentProvider.ZELIQ]: 10,
    }
    const result = sumProviders(providers1, undefined)
    expect(result).toEqual(providers1)
  })

  it('should return the second provider counts when first is undefined', () => {
    const providers2 = {
      [EnrichmentProvider.PEOPLE_DATA_LABS]: 7,
    }
    const result = sumProviders(undefined, providers2)
    expect(result).toEqual(providers2)
  })

  it('should sum counts for overlapping providers', () => {
    const providers1 = {
      [EnrichmentProvider.DATAGMA]: 5,
      [EnrichmentProvider.ZELIQ]: 10,
    }
    const providers2 = {
      [EnrichmentProvider.DATAGMA]: 3,
      [EnrichmentProvider.PEOPLE_DATA_LABS]: 7,
    }
    const result = sumProviders(providers1, providers2)
    expect(result).toEqual({
      [EnrichmentProvider.DATAGMA]: 8,
      [EnrichmentProvider.ZELIQ]: 10,
      [EnrichmentProvider.PEOPLE_DATA_LABS]: 7,
    })
  })

  it('should keep counts when providers are non-overlapping', () => {
    const providers1 = {
      [EnrichmentProvider.DATAGMA]: 5,
    }
    const providers2 = {
      [EnrichmentProvider.ZELIQ]: 10,
    }
    const result = sumProviders(providers1, providers2)
    expect(result).toEqual({
      [EnrichmentProvider.DATAGMA]: 5,
      [EnrichmentProvider.ZELIQ]: 10,
    })
  })
})

describe('sumEnrichmentMetrics', () => {
  it('should return zeroed metrics when both inputs are null', () => {
    const result = sumEnrichmentMetrics(null, null)
    expect(result).toEqual({
      phonesMetrics: {
        nbLeadsWithPhone: 0,
      },
      emailsMetrics: {
        nbLeadsWithEmail: 0,
        nbAcceptAll: 0,
        nbSafeToSend: 0,
        nbRiskyToSend: 0,
        nbFoundWithProviders: {},
      },
      nbLeadsWithEmailOrPhone: 0,
      nbLeadsEnrichedWithNoDataFound: 0,
    })
  })

  it('should return the first metrics when second is null', () => {
    const metrics1: EnrichmentHubMetrics = {
      phonesMetrics: {
        nbLeadsWithPhone: 5,
      },
      emailsMetrics: {
        nbLeadsWithEmail: 10,
        nbAcceptAll: 2,
        nbSafeToSend: 3,
        nbRiskyToSend: 1,
        nbFoundWithProviders: {
          [EnrichmentProvider.DATAGMA]: 4,
        },
      },
      nbLeadsWithEmailOrPhone: 0,
      nbLeadsEnrichedWithNoDataFound: 0,
    }
    const result = sumEnrichmentMetrics(metrics1, null)
    expect(result).toEqual(metrics1)
  })

  it('should return the second metrics when first is null', () => {
    const metrics2: EnrichmentHubMetrics = {
      phonesMetrics: {
        nbLeadsWithPhone: 7,
      },
      emailsMetrics: {
        nbLeadsWithEmail: 12,
        nbAcceptAll: 3,
        nbSafeToSend: 4,
        nbRiskyToSend: 2,
        nbFoundWithProviders: {
          [EnrichmentProvider.ZELIQ]: 5,
        },
      },
      nbLeadsWithEmailOrPhone: 0,
      nbLeadsEnrichedWithNoDataFound: 0,
    }
    const result = sumEnrichmentMetrics(null, metrics2)
    expect(result).toEqual(metrics2)
  })

  it('should sum the metrics correctly', () => {
    const metrics1: EnrichmentHubMetrics = {
      phonesMetrics: {
        nbLeadsWithPhone: 5,
      },
      emailsMetrics: {
        nbLeadsWithEmail: 10,
        nbAcceptAll: 2,
        nbSafeToSend: 3,
        nbRiskyToSend: 1,
        nbFoundWithProviders: {
          [EnrichmentProvider.DATAGMA]: 4,
        },
      },
      nbLeadsWithEmailOrPhone: 0,
      nbLeadsEnrichedWithNoDataFound: 0,
    }

    const metrics2: EnrichmentHubMetrics = {
      phonesMetrics: {
        nbLeadsWithPhone: 7,
      },
      emailsMetrics: {
        nbLeadsWithEmail: 12,
        nbAcceptAll: 3,
        nbSafeToSend: 4,
        nbRiskyToSend: 2,
        nbFoundWithProviders: {
          [EnrichmentProvider.DATAGMA]: 6,
          [EnrichmentProvider.ZELIQ]: 5,
        },
      },
      nbLeadsWithEmailOrPhone: 0,
      nbLeadsEnrichedWithNoDataFound: 0,
    }

    const result = sumEnrichmentMetrics(metrics1, metrics2)
    expect(result).toEqual({
      phonesMetrics: {
        nbLeadsWithPhone: 12,
      },
      emailsMetrics: {
        nbLeadsWithEmail: 22,
        nbAcceptAll: 5,
        nbSafeToSend: 7,
        nbRiskyToSend: 3,
        nbFoundWithProviders: {
          [EnrichmentProvider.DATAGMA]: 10,
          [EnrichmentProvider.ZELIQ]: 5,
        },
      },
      nbLeadsWithEmailOrPhone: 0,
      nbLeadsEnrichedWithNoDataFound: 0,
    })
  })

  it('should handle empty nbFoundWithProviders', () => {
    const metrics1: EnrichmentHubMetrics = {
      phonesMetrics: {
        nbLeadsWithPhone: 5,
      },
      emailsMetrics: {
        nbLeadsWithEmail: 10,
        nbAcceptAll: 2,
        nbSafeToSend: 3,
        nbRiskyToSend: 1,
        nbFoundWithProviders: {},
      },
      nbLeadsWithEmailOrPhone: 0,
      nbLeadsEnrichedWithNoDataFound: 0,
    }

    const metrics2: EnrichmentHubMetrics = {
      phonesMetrics: {
        nbLeadsWithPhone: 7,
      },
      emailsMetrics: {
        nbLeadsWithEmail: 12,
        nbAcceptAll: 3,
        nbSafeToSend: 4,
        nbRiskyToSend: 2,
        nbFoundWithProviders: {
          [EnrichmentProvider.ZELIQ]: 5,
        },
      },
      nbLeadsWithEmailOrPhone: 0,
      nbLeadsEnrichedWithNoDataFound: 0,
    }

    const result = sumEnrichmentMetrics(metrics1, metrics2)
    expect(result).toEqual({
      phonesMetrics: {
        nbLeadsWithPhone: 12,
      },
      emailsMetrics: {
        nbLeadsWithEmail: 22,
        nbAcceptAll: 5,
        nbSafeToSend: 7,
        nbRiskyToSend: 3,
        nbFoundWithProviders: {
          [EnrichmentProvider.ZELIQ]: 5,
        },
      },
      nbLeadsWithEmailOrPhone: 0,
      nbLeadsEnrichedWithNoDataFound: 0,
    })
  })
})
