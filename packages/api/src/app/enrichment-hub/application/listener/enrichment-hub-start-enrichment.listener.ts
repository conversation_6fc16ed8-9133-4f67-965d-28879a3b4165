import { Inject, Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import {
  AsyncActionCategoryEnum,
  AsyncActionStatusEnum,
} from '@getheroes/shared'
import { EnrichmentHubEvents } from '../../../shared/domain/event/event.enum'

import { CreateAsyncActionDto } from '../../../shared/domain/dto/create-async-action.dto'
import { LogFeature } from '../../../shared/logger.service'
import { TryCatchLogger } from '../../../shared/domain/decorator/try-catch-logger.decorator'
import {
  ASYNC_ACTION_SERVICE_INTERFACE,
  AsyncActionServiceInterface,
} from '../../../shared/domain/interface/async-action-service.interface'
import { EnrichmentHubStartEnrichmentEvent } from '../../../shared/domain/event/enrichment-hub/enrichment-hub-start-enrichment.event'
import {
  ASYNC_ACTION_ESTIMATE_TIME_TO_FINISH_SERVICE_INTERFACE,
  AsyncActionEstimateTimeToFinishServiceInterface,
} from '../../../shared/domain/interface/async-action-estimate-time-to-finish-service.interface'

@Injectable()
export class EnrichmentHubStartEnrichmentListener {
  constructor(
    @Inject(ASYNC_ACTION_SERVICE_INTERFACE)
    private readonly asyncActionService: AsyncActionServiceInterface,
    @Inject(ASYNC_ACTION_ESTIMATE_TIME_TO_FINISH_SERVICE_INTERFACE)
    private readonly estimateTimeToFinishService: AsyncActionEstimateTimeToFinishServiceInterface
  ) {}

  @TryCatchLogger({
    feature: LogFeature.ENRICHMENT_HUB,
    message: 'Failed to update async action for enrichment hub',
  })
  @OnEvent(EnrichmentHubEvents.START_ENRICHMENT_PROCESS)
  async updateAsyncAction({
    enrichmentHubId,
    organizationId,
    createdById,
  }: EnrichmentHubStartEnrichmentEvent) {
    const asyncActionModel = await this.asyncActionService.create(
      new CreateAsyncActionDto({
        status: AsyncActionStatusEnum.IN_PROGRESS,
        organizationId,
        createdById,
        category: AsyncActionCategoryEnum.ENRICHMENT_HUB,
        internalRelationId: enrichmentHubId,
      })
    )

    const estimatedTimeToFinish =
      await this.estimateTimeToFinishService.estimateTimeOfCompletion(
        asyncActionModel
      )

    await this.asyncActionService.updateEstimatedTimeOfCompletion(
      asyncActionModel.id,
      estimatedTimeToFinish
    )
  }
}
