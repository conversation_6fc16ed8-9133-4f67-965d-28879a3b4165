import { EnrichmentHubModel } from '../../../domain/model/enrichment-hub.model'
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsUUID,
  ValidateNested,
  IsOptional,
  Equals,
  IsNumber,
} from 'class-validator'
import { Type } from 'class-transformer'
import { EnrichmentInterface } from '../../../../lead/domain/enrichment.interface'
import {
  EnrichmentHubStatusEnum,
  EnrichmentHubTypeEnum,
} from '@getheroes/shared'

type RequiredProps = Pick<EnrichmentHubModel, 'id'>
type OptionalProps = Partial<
  Pick<
    EnrichmentHubModel,
    'status' | 'enrichments' | 'enrichmentTypes' | 'nbLeads'
  >
> & { isLeadsEnabledInWorkspace?: boolean }

type UpdateEnrichmentHubCommandProps = RequiredProps & OptionalProps

export class UpdateEnrichmentHubCommand {
  @IsUUID()
  id: string

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  enrichments?: EnrichmentInterface[]

  @IsOptional()
  @IsBoolean()
  @Equals(true, { message: 'You cannot undo enabling to workspace' })
  isLeadsEnabledInWorkspace?: boolean

  @IsOptional()
  @IsEnum(EnrichmentHubStatusEnum)
  status?: EnrichmentHubStatusEnum

  @IsOptional()
  @IsEnum(EnrichmentHubTypeEnum, { each: true })
  enrichmentTypes?: EnrichmentHubTypeEnum[]

  @IsNumber({ allowNaN: false, allowInfinity: false })
  @IsOptional()
  nbLeads: number

  constructor(readonly props: UpdateEnrichmentHubCommandProps) {
    this.id = props.id
    this.enrichments = props.enrichments
    this.isLeadsEnabledInWorkspace = props.isLeadsEnabledInWorkspace
    this.status = props.status
    this.enrichmentTypes = props.enrichmentTypes
    this.nbLeads = props.nbLeads
  }
}
