import { Injectable } from '@nestjs/common'
import { EnrichmentHubRepositoryInterface } from '../../../domain/enrichment-hub-repository.interface'
import { EnrichmentHubModel } from '../../../domain/model/enrichment-hub.model'
import { v4 as uuidv4 } from 'uuid'
import { MockRepositoryInterface } from '../../../../shared/globals/mock-repository.interface'
import { CreateEnrichmentHubDto } from '../../../domain/dto/create-enrichment-hub.dto'
import { FindManyOptions, FindOptionsWhere } from 'typeorm'
import { EnrichmentHubEntity } from '../../entity/enrichment-hub.entity'
import { EnrichmentHubStatusEnum } from '@getheroes/shared'
import { sub } from 'date-fns'

@Injectable()
export class MockEnrichmentHubRepository
  implements
    EnrichmentHubRepositoryInterface,
    MockRepositoryInterface<EnrichmentHubModel>
{
  private data: EnrichmentHubModel[] = []

  constructor() {}

  getData(): EnrichmentHubModel[] {
    return [...this.data]
  }

  clearData(): void {
    this.data = []
  }

  setData(data: EnrichmentHubModel[]): void {
    this.data = [...data]
  }

  /**
   * Finds an EnrichmentHubModel by its ID.
   * @param id The ID of the enrichment hub import.
   * @param relations Not used in the mock.
   * @returns The found EnrichmentHubModel or null.
   */
  async findById(
    id: string,
    relations?: any
  ): Promise<EnrichmentHubModel | null> {
    if (!id) {
      return null
    }
    const enrichmentHub = this.data.find(item => item.id === id)
    if (!enrichmentHub) {
      return null
    }
    return enrichmentHub
  }

  async findByLeadImportId(
    leadImportId: string,
    relations?: any
  ): Promise<EnrichmentHubModel | null> {
    if (!leadImportId) {
      return null
    }
    const enrichmentHub = this.data.find(
      item => item.leadImportId === leadImportId
    )
    if (!enrichmentHub) {
      return null
    }
    return enrichmentHub
  }

  /**
   * Finds all EnrichmentHubImportModels by organization ID.
   * @param organizationId The organization ID to filter by.
   * @param relations Not used in the mock.
   * @returns An array of EnrichmentHubImportModels.
   */
  async findByOrganizationId(
    organizationId: string,
    relations?: any
  ): Promise<EnrichmentHubModel[]> {
    if (!organizationId) {
      return []
    }

    // Calculate date one week ago to filter out old FAILED items
    const oneWeekAgo = sub(new Date(), { weeks: 1 })

    // Filter out FAILED status items older than one week
    const enrichmentHubs = this.data.filter(item => {
      if (item.organizationId !== organizationId) {
        return false
      }

      // Filter out FAILED status items older than one week
      if (item.status === EnrichmentHubStatusEnum.FAILED) {
        const createdAt = new Date(item.createdAt)
        if (createdAt < oneWeekAgo) {
          return false
        }
      }

      return true
    })

    enrichmentHubs.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0
      const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0
      return dateB - dateA
    })
    return enrichmentHubs
  }

  /**
   * Finds all enrichment hubs with optional filtering.
   * @param options Optional filtering and pagination options.
   * @returns An array of filtered EnrichmentHubModels.
   */
  async findAll(
    options: FindManyOptions<EnrichmentHubEntity>
  ): Promise<EnrichmentHubModel[]> {
    let filteredData = [...this.data]

    if (options.where) {
      const whereCondition =
        options.where as FindOptionsWhere<EnrichmentHubEntity>

      if ('status' in whereCondition) {
        filteredData = filteredData.filter(
          item => item.status === whereCondition.status
        )
      }
    }

    return filteredData
  }

  /**
   * Creates a new EnrichmentHubModel.
   * @param enrichmentHubImport The model to create.
   * @returns The created EnrichmentHubModel with a generated ID.
   */
  async create(
    enrichmentHubImport: CreateEnrichmentHubDto
  ): Promise<EnrichmentHubModel> {
    const createdEnrichmentHub = new EnrichmentHubModel({
      ...enrichmentHubImport,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date(),
    })
    this.data.push(createdEnrichmentHub)
    return createdEnrichmentHub
  }

  /**
   * Updates an existing EnrichmentHubModel.
   * @param enrichmentHubImport Partial model containing updates.
   * @returns The updated EnrichmentHubModel.
   * @throws Error if the entity is not found.
   */
  async update(
    enrichmentHubImport: Partial<EnrichmentHubModel>
  ): Promise<EnrichmentHubModel> {
    if (!enrichmentHubImport.id) {
      throw new Error('ID is required for update')
    }
    const index = this.data.findIndex(
      item => item.id === enrichmentHubImport.id
    )
    if (index === -1) {
      throw new Error('Entity not found')
    }
    const existing = this.data[index]
    const updatedEnrichmentHub: EnrichmentHubModel = Object.assign(existing, {
      ...enrichmentHubImport,
      updatedAt: new Date(),
    })
    this.data[index] = updatedEnrichmentHub
    return updatedEnrichmentHub
  }

  /**
   * Deletes an EnrichmentHubModel by ID and organization ID.
   * @param id The ID of the enrichment hub import to delete.
   */
  async delete(id: string): Promise<void> {
    if (!id) {
      return
    }
    this.data = this.data.filter(item => !(item.id === id))
  }

  /**
   * Finds enrichment IDs associated with a specific enrichment hub import ID.
   * @param enrichmentHubId The enrichment hub import ID.
   * @returns An array of enrichment IDs.
   */
  async findEnrichmentIdsForOne(enrichmentHubId: string): Promise<string[]> {
    if (!enrichmentHubId) {
      return []
    }
    const enrichmentHub = this.data.find(item => item.id === enrichmentHubId)
    if (!enrichmentHub || !enrichmentHub.enrichments) {
      return []
    }
    return enrichmentHub.enrichments.map(enrichment => enrichment.id)
  }
}
