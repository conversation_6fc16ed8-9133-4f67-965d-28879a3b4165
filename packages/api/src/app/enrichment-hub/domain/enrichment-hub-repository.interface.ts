import { EnrichmentHubModel } from './model/enrichment-hub.model'
import { FindManyOptions, FindOptionsRelations } from 'typeorm'
import { CreateEnrichmentHubDto } from './dto/create-enrichment-hub.dto'
import { EnrichmentHubEntity } from '../infrastructure/entity/enrichment-hub.entity'

export const ENRICHMENT_HUB_REPOSITORY_INTERFACE =
  'ENRICHMENT_HUB_REPOSITORY_INTERFACE'

export interface EnrichmentHubRepositoryInterface {
  findById(
    id: string,
    relations?: FindOptionsRelations<EnrichmentHubEntity>
  ): Promise<EnrichmentHubModel>
  findByLeadImportId(
    id: string,
    relations?: FindOptionsRelations<EnrichmentHubEntity>
  ): Promise<EnrichmentHubModel | null>
  findByOrganizationId(
    organizationId: string,
    relations?: FindOptionsRelations<EnrichmentHubEntity>
  ): Promise<EnrichmentHubModel[]>
  findAll(
    options: FindManyOptions<EnrichmentHubEntity>
  ): Promise<EnrichmentHubModel[]>
  findEnrichmentIdsForOne(enrichmentHubId: string): Promise<string[]>
  create(dto: CreateEnrichmentHubDto): Promise<EnrichmentHubModel>
  update(
    enrichmentHubModel: Partial<EnrichmentHubModel> & { id: string }
  ): Promise<EnrichmentHubModel>
  delete(id: string): Promise<void>
}
