import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common'
import { QueryBus } from '@nestjs/cqrs'
import { FindOrganizationMemberApiKeyQuery } from '../../../../../app/organization/application/query/find-organization-member-api-key/find-organization-member-api-key.query'
import { FindOrganizationQuery } from '../../../../../app/organization/application/query/find-organization/find-organization.query'
import { ClsService } from 'nestjs-cls'
import { CacheService } from '../../../../../app/shared/infrastructure/service/cache.service'
import { OrganizationInterface } from '../../../../../app/shared/domain/organization.interface'
import {
  OrganizationMemberApiKey,
  OrganizationServiceSubscriptionPlan,
  RateLimits,
} from '@getheroes/shared'

@Injectable()
export class OrganizationMemberApiKeyGuard implements CanActivate {
  private static readonly HARD_RATE_LIMIT_PER_MINUTE = 1000
  private static readonly RATE_LIMIT_EXCLUDED_ROUTES = ['/api/credits/balance']

  constructor(
    private readonly queryBus: QueryBus,
    private readonly cls: ClsService,
    private cacheManager: CacheService
  ) {}

  private readonly logger = new Logger('OrganizationMemberApiKeyGuard')

  private getPlanLimits(plan: OrganizationServiceSubscriptionPlan): RateLimits {
    switch (plan) {
      case OrganizationServiceSubscriptionPlan.FREE:
        return { perMinute: 50, perHour: 200, perDay: 600 }
      case OrganizationServiceSubscriptionPlan.STARTER:
        return { perMinute: 200, perHour: 400, perDay: 2000 }
      case OrganizationServiceSubscriptionPlan.ESSENTIAL:
      case OrganizationServiceSubscriptionPlan.ADVANCED:
      case OrganizationServiceSubscriptionPlan.ENTERPRISE:
        return { perMinute: 600, perHour: 2000, perDay: 6000 }
      default:
        return { perMinute: 50, perHour: 200, perDay: 600 }
    }
  }

  private async incrementOrInitializeCounter(
    key: string,
    ttl: number
  ): Promise<number> {
    //exists
    const count = await this.cacheManager.exists(key)
    if (count > 0) {
      return this.cacheManager.incr(key)
    }
    await this.cacheManager.set(key, 1, ttl)
    return 1
  }

  private async checkRateLimit(
    apiKey: OrganizationMemberApiKey,
    organizationPlan: OrganizationServiceSubscriptionPlan
  ): Promise<void> {
    const organizationPlanLimits = this.getPlanLimits(organizationPlan)
    const limits: RateLimits = apiKey.ignoreRateLimit
      ? {
          perMinute: OrganizationMemberApiKeyGuard.HARD_RATE_LIMIT_PER_MINUTE,
          perHour:
            OrganizationMemberApiKeyGuard.HARD_RATE_LIMIT_PER_MINUTE * 60,
          perDay:
            OrganizationMemberApiKeyGuard.HARD_RATE_LIMIT_PER_MINUTE * 60 * 24,
        }
      : {
          perMinute:
            apiKey.rateLimitPerMinute || organizationPlanLimits.perMinute,
          perHour: apiKey.rateLimitPerHour || organizationPlanLimits.perHour,
          perDay: apiKey.rateLimitPerDay || organizationPlanLimits.perDay,
        }

    const minuteKey = `api:ratelimit:${apiKey.key}:1m`
    const hourKey = `api:ratelimit:${apiKey.key}:1h`
    const dayKey = `api:ratelimit:${apiKey.key}:1d`

    const [minuteCount, hourCount, dayCount] = await Promise.all([
      this.incrementOrInitializeCounter(minuteKey, 60), // 1 minute
      this.incrementOrInitializeCounter(hourKey, 3600), // 1 hour
      this.incrementOrInitializeCounter(dayKey, 86400), // 1 day
    ])

    if (
      minuteCount > limits.perMinute ||
      hourCount > limits.perHour ||
      dayCount > limits.perDay
    ) {
      throw new HttpException(
        {
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          error: 'Too Many Requests',
          message: `Rate limit exceeded. Please try again later.`,
          limits: {
            perMinute: limits.perMinute,
            perHour: limits.perHour,
            perDay: limits.perDay,
          },
        },
        HttpStatus.TOO_MANY_REQUESTS
      )
    }
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest()
    const route = req.route.path
    const apiKey = req.headers['x-api-key']

    if (!apiKey) {
      throw new UnauthorizedException('API key is required')
    }

    const organizationMemberApiKey = await this.queryBus.execute(
      new FindOrganizationMemberApiKeyQuery(apiKey)
    )

    if (!organizationMemberApiKey) {
      this.logger.warn('Invalid API key attempted access')
      throw new UnauthorizedException('Invalid API key')
    }

    const organizationId =
      organizationMemberApiKey.organizationMember.organizationId

    // Query the organization to get the plan
    const organization: OrganizationInterface = await this.queryBus.execute(
      new FindOrganizationQuery(organizationId)
    )

    if (
      !OrganizationMemberApiKeyGuard.RATE_LIMIT_EXCLUDED_ROUTES.includes(route)
    ) {
      await this.checkRateLimit(organizationMemberApiKey, organization.plan)
    }

    this.cls.set('organizationId', organizationId)
    this.cls.set('creditBalance', organization.creditBalance)
    this.cls.set(
      'organizationMemberId',
      organizationMemberApiKey.organizationMember.userId
    )
    this.cls.set('organizationPlan', organization.plan)
    this.cls.set('userId', organizationMemberApiKey.organizationMember.userId)

    return true
  }
}
