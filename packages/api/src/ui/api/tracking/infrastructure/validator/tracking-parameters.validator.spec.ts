import { TrackingParametersValidator } from './tracking-parameters-validator.service'

describe('UTM Validation', () => {
  let trackingParametersValidator: TrackingParametersValidator

  beforeEach(() => {
    trackingParametersValidator = new TrackingParametersValidator()
  })

  it('should validate one UTM data', () => {
    const validUtm = {
      FT_utm_source: {
        link: 'linkedin',
        date: new Date('2024-03-18T12:00:00Z'),
      },
    }

    expect(trackingParametersValidator.validate(validUtm)).toBe(true)
  })

  it('should validate correct UTM data', () => {
    const validUtm = {
      FT_utm_source: {
        link: 'linkedin',
        date: new Date('2024-03-18T12:00:00Z'),
      },
      FT_utm_medium: {
        link: 'social',
        date: new Date('2024-03-18T12:00:00Z'),
      },
      FT_utm_campaign: {
        link: 'growth',
        date: new Date('2024-03-18T12:00:00Z'),
      },
      LT_utm_source: {
        link: 'linkedin',
        date: new Date('2024-03-19T12:00:00Z'),
      },
      LT_utm_medium: {
        link: 'social',
        date: new Date('2024-03-19T12:00:00Z'),
      },
      LT_utm_campaign: {
        link: 'growth',
        date: new Date('2024-03-19T12:00:00Z'),
      },
    }

    expect(trackingParametersValidator.validate(validUtm)).toBe(true)
  })

  it('should reject UTM data missing FT_utm_source', () => {
    const invalidUtm = {
      // Missing FT_utm_source
      FT_utm_medium: {
        link: 'social',
        date: new Date('2024-03-18T12:00:00Z'),
      },
      FT_utm_campaign: {
        link: 'growth',
        date: new Date('2024-03-18T12:00:00Z'),
      },
      LT_utm_source: {
        link: 'linkedin',
        date: new Date('2024-03-19T12:00:00Z'),
      },
      LT_utm_medium: {
        link: 'social',
        date: new Date('2024-03-19T12:00:00Z'),
      },
      LT_utm_campaign: {
        link: 'growth',
        date: new Date('2024-03-19T12:00:00Z'),
      },
    }

    expect(trackingParametersValidator.validate(invalidUtm)).toBe(false)
  })

  it('should reject UTM data with LT_ field but no corresponding FT_ field', () => {
    const invalidUtm = {
      FT_utm_source: {
        link: 'linkedin',
        date: new Date('2024-03-18T12:00:00Z'),
      },
      // Missing FT_utm_medium but has LT_utm_medium
      FT_utm_campaign: {
        link: 'growth',
        date: new Date('2024-03-18T12:00:00Z'),
      },
      LT_utm_source: {
        link: 'linkedin',
        date: new Date('2024-03-19T12:00:00Z'),
      },
      LT_utm_medium: {
        link: 'social',
        date: new Date('2024-03-19T12:00:00Z'),
      },
      LT_utm_campaign: {
        link: 'growth',
        date: new Date('2024-03-19T12:00:00Z'),
      },
    }

    expect(trackingParametersValidator.validate(invalidUtm)).toBe(false)
  })
})
