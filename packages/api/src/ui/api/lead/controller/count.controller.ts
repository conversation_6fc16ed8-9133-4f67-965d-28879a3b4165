import {
  ClassSerializerInterceptor,
  Controller,
  ForbiddenException,
  Get,
  HttpStatus,
  Param,
  SerializeOptions,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { QueryBus } from '@nestjs/cqrs'
import { AuthGuard } from '@nestjs/passport'
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger'
import { UserModel } from '../../../../app/shared/domain/model/user.model'
import { PermissionSpecification } from '../../../../app/shared/domain/specification/model-permission.specitification'
import { User } from '../../shared/infrastructure/auth/decorator/user.decorator'
import { OrganizationGuard } from '../../shared/infrastructure/guard/organization.guard'
import { EXPOSE_GROUP } from '../../../../app/shared/globals/expose-group.enum'
import { ContactModel } from '../../../../app/lead/domain/model/contact.model'
import { ContactPermission } from '../../../../app/lead/domain/contact-permission.enum'
import { OrganizationPipe } from '../../organization/pipe/organization.pipe'
import { CountOrganizationContactQuery } from '../../../../app/lead/application/query/contact/count-organization-contact/count-organization-contact.query'
import { CountUserAssignedContactQuery } from '../../../../app/lead/application/query/contact/count-user-assigned-contact/count-user-assigned-contact.query'

@Controller()
@ApiTags('Leads')
@UseInterceptors(ClassSerializerInterceptor)
@SerializeOptions({
  groups: [EXPOSE_GROUP.PUBLIC],
})
export class CountContactsController {
  constructor(private readonly queryBus: QueryBus) {}

  @Get('/:organizationId/users/leads/count')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Endpoint use to count contacts & companies in the sidebar',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    schema: {
      type: 'object',
      properties: {
        all: {
          type: 'object',
          properties: {
            contacts: { type: 'number' },
            companies: { type: 'number' },
          },
        },
        my: {
          type: 'object',
          properties: {
            contacts: { type: 'number' },
            companies: { type: 'number' },
          },
        },
      },
    },
  })
  async invoke(
    @Param('organizationId', OrganizationPipe) organizationModel,
    @User() user: UserModel
  ): Promise<any> {
    if (
      new PermissionSpecification(new ContactModel()).can(
        ContactPermission.VIEW,
        user
      ) === false
    ) {
      throw new ForbiddenException()
    }

    const organizationId = organizationModel.id
    const userId = user.id

    const [allContacts, myContacts] = await Promise.all([
      this.queryBus.execute(new CountOrganizationContactQuery(organizationId)),
      this.queryBus.execute(
        new CountUserAssignedContactQuery(organizationId, userId)
      ),
    ])

    return {
      all: allContacts,
      my: myContacts,
    }
  }
}
