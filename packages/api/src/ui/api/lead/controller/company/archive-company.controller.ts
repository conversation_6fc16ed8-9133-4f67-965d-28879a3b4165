import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  ForbiddenException,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  SerializeOptions,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { CommandBus } from '@nestjs/cqrs'
import { AuthGuard } from '@nestjs/passport'
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger'
import { UserModel } from '../../../../../app/shared/domain/model/user.model'
import { User } from '../../../shared/infrastructure/auth/decorator/user.decorator'
import { OrganizationGuard } from '../../../shared/infrastructure/guard/organization.guard'
import { CompanyModel } from '../../../../../app/lead/domain/model/company.model'
import { PermissionSpecification } from '../../../../../app/shared/domain/specification/model-permission.specitification'
import { CompanyPermission } from '../../../../../app/lead/domain/company-permission.enum'
import { ArchiveCompanyDto } from '../../dto/company/archive-company.dto'
import { ArchiveCompanyCommand } from '../../../../../app/lead/application/command/company/archive-company/archive-company.command'
import { EXPOSE_GROUP } from '../../../../../app/shared/globals/expose-group.enum'
import { LeadResponseInterceptor } from '../../interceptor/lead-response.interceptor'

@Controller()
@ApiTags('Leads')
@UseInterceptors(ClassSerializerInterceptor)
@SerializeOptions({
  groups: [EXPOSE_GROUP.PUBLIC],
})
export class ArchiveCompanyController {
  constructor(private commandBus: CommandBus) {}

  @Post('/:organizationId/leads/companies/:companyId/archive')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiBearerAuth('access-token')
  @ApiBadRequestResponse({
    schema: {
      type: 'object',
      example: {
        error: 'VALIDATION_FAILED',
        message: 'a message to describe error',
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Endpoint used to archive a company',
  })
  @UseInterceptors(LeadResponseInterceptor)
  async archiveCompany(
    @Param('organizationId') organizationId: string,
    @Param('companyId') companyId: string,
    @User() user: UserModel
  ): Promise<CompanyModel> {
    const companyModel = new CompanyModel()
    if (
      new PermissionSpecification(companyModel).can(
        CompanyPermission.ARCHIVE,
        user
      ) === false
    ) {
      throw new ForbiddenException()
    }

    const command = new ArchiveCompanyCommand(organizationId, companyId, user)
    return this.commandBus.execute(command)
  }

  @HttpCode(HttpStatus.NO_CONTENT)
  @Post('/:organizationId/leads/companies/archive')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiBearerAuth('access-token')
  @ApiBadRequestResponse({
    schema: {
      type: 'object',
      example: {
        error: 'VALIDATION_FAILED',
        message: 'a message to describe error',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
  })
  @ApiOperation({
    summary: 'Endpoint used to archive multiple companies',
  })
  async archiveCompanies(
    @Param('organizationId') organizationId: string,
    @Body() archiveDto: ArchiveCompanyDto,
    @User() user: UserModel
  ) {
    const companyModel = new CompanyModel()
    if (
      new PermissionSpecification(companyModel).can(
        CompanyPermission.ARCHIVE,
        user
      ) === false
    ) {
      throw new ForbiddenException()
    }

    for (let i = 0; i < archiveDto.companies.length; i++) {
      const id = archiveDto.companies[i]
      const command = new ArchiveCompanyCommand(organizationId, id, user)
      await this.commandBus.execute(command).catch(error => {
        console.log(error.message)
      })
    }
  }
}
