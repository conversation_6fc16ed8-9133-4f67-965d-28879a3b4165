import { Modu<PERSON> } from '@nestjs/common'
import { CreateCompanyController } from './controller/company/create-company.controller'
import { CreateContactController } from './controller/contact/create-contact.controller'
import { AssignController } from './controller/user/assign.controller'
import { UnassignController } from './controller/user/unassign.controller'
import { CqrsModule } from '@nestjs/cqrs'
import { FlatfileHookController } from './controller/flatfile/hook.controller'
import { AuthController } from './controller/flatfile/auth.controller'
import { GetContactController } from './controller/contact/get-contact.controller'
import { AssignUserCompanyController } from './controller/company/assign-user-company.controller'
import { UnassignCompanyController } from './controller/company/unassign-company.controller'
import { ListFieldController } from './controller/field/list.controller'
import { SearchContactController } from './controller/contact/search-contact.controller'
import { SearchCompanyController } from './controller/company/search-company.controller'
import { UpdateContactController } from './controller/contact/update-contact.controller'
import { CreateViewController } from './controller/view/create-view.controller'
import { UpdateViewController } from './controller/view/update-view.controller'
import { DeleteViewController } from './controller/view/delete-view.controller'
import { GetListViewsController } from './controller/view/get-list-view.controller'
import { GetViewController } from './controller/view/get-view.controller'
import { ArchiveCompanyController } from './controller/company/archive-company.controller'
import { UnArchiveCompanyController } from './controller/company/unarchive-company.controller'
import { UnArchiveContactController } from './controller/contact/unarchive-contact.controller'
import { ArchiveContactController } from './controller/contact/archive-contact.controller'
import { GetCompanyController } from './controller/company/get-company.controller'
import { UpdateCompanyController } from './controller/company/update-company.controller'
import { GetListContactActivityController } from './controller/activity/get-list-contact-activity.controller'
import { GetListCompanyActivityController } from './controller/activity/get-list-company-activity.controller'
import { CreateCustomFieldController } from './controller/custom-field/create-custom-field.controller'
import { DeleteCustomFieldController } from './controller/custom-field/delete-custom-field.controller'
import { UpdateCustomFieldController } from './controller/custom-field/update-custom-fields.controller'
import { GetSdrLeadsMetricsController } from './controller/metric/get-sdr-leads-metrics.controller'
import { UpdateAssignUserCompanyController } from './controller/company/update-assign-user-company.controller'
import { SearchDistinctValueContactController } from './controller/contact/search-distinct-value.controller'
import { SearchDistinctValueCompanyController } from './controller/company/search-distinct-value.controller'
import { ContactProfile } from './profile/contact.profile'
import { CreateEnrichmentController } from './controller/enrichment/create-enrichment.controller'
import { NbEnrichmentController } from './controller/enrichment/nb-available-enrichment.controller'
import { GetEnrichmentController } from './controller/enrichment/get-enrichment.controller'
import { GetListEnrichmentController } from './controller/enrichment/get-list-enrichment.controller'
import { GenerateStrategyController } from './controller/contact/generate-strategy.controller'
import { GetEnrichmentsMetricsController } from './controller/enrichment/get-enrichments-metrics.controller'
import { SearchNewLeadController } from './controller/search-new/search-new-lead.controller'
import { SearchNewFilterDefinitionController } from './controller/search-new/search-new-filter-definition.controller'
import { SearchNewFilterAutocompleteController } from './controller/search-new/search-new-filter-autocomplete.controller'
import { CreateContactBulkController } from './controller/contact/create-contact-bulk.controller'
import { CompanyProfile } from './profile/company.profile'
import { CreateCompanyBulkController } from './controller/company/create-company-bulk.controller'
import { CreateSearchRequestController } from './controller/search-new/create-search-request.controller'
import { DeleteSearchRequestController } from './controller/search-new/delete-search-request.controller'
import { UpdateSearchRequestController } from './controller/search-new/update-search-request.controller'
import { GetListSearchRequestController } from './controller/search-new/get-list-search-request.controller'
import { UpdateContactBulkController } from './controller/contact/update-contact-bulk.controller'
import { UpdateContactFeedbackController } from './controller/contact/update-contact-feedback.controller'
import { UpdateEngagementScoringPriorityController } from './controller/contact/update-priority.controller'
import { FeatureService } from '../../../app/feature/application/service/feature.service'
import { CountContactsController } from './controller/count.controller'
import { CountContactsByEngagementScoringController } from './controller/contact/count-by-engagement-scoring-contact.controller'
import { GetEnrichmentProvidersController } from './controller/enrichment/get-enrichment-providers.controller'
import { CountWeeklySuccessController } from '../organization/controller/member/count-weekly-success.controller'
import { ExportCsvService } from '../../../app/lead/application/service/export-csv/export-csv.service'
import { ExportContactAsyncController } from './controller/contact/export-contact-async.controller'
import { ApiContactEnrichPhoneController } from './controller/enrichment/api-contact-enrich-phone.controller'
import { ApiContactEnrichEmailController } from './controller/enrichment/api-contact-enrich-email.controller'
import { GetContactExportableFieldsController } from './controller/contact/get-contact-exportable-fields.controller'
import { GetCompanyExportableFieldsController } from './controller/company/get-company-exportable-fields.controller'
import { ExportCompanyAsyncController } from './controller/company/export-company-async.controller'
import { EnrichmentResponseService } from '../../../app/lead/application/service/enrichment-response.service'
import { LeadImportModule } from '../../../app/lead-import/lead-import.module'
import { FileService } from '../../../app/lead/infrastructure/service/file-service/file.service'

const profiles = [ContactProfile, CompanyProfile]

@Module({
  imports: [CqrsModule, LeadImportModule],
  controllers: [
    CreateContactController,
    ExportContactAsyncController,
    ExportCompanyAsyncController,
    GetContactController,
    UpdateContactController,
    CreateCompanyController,
    GetCompanyController,
    AssignController,
    UnassignController,
    FlatfileHookController,
    AuthController,
    AssignUserCompanyController,
    UpdateAssignUserCompanyController,
    UnassignCompanyController,
    ListFieldController,
    SearchContactController,
    SearchCompanyController,
    CreateViewController,
    UpdateViewController,
    DeleteViewController,
    GetListViewsController,
    GetViewController,
    ArchiveCompanyController,
    UnArchiveCompanyController,
    ArchiveContactController,
    UnArchiveContactController,
    UpdateCompanyController,
    GetListContactActivityController,
    GetListCompanyActivityController,
    CreateCustomFieldController,
    DeleteCustomFieldController,
    UpdateCustomFieldController,
    GetSdrLeadsMetricsController,
    SearchDistinctValueContactController,
    SearchDistinctValueCompanyController,
    CreateEnrichmentController,
    NbEnrichmentController,
    GetEnrichmentController,
    GetListEnrichmentController,
    GenerateStrategyController,
    GetEnrichmentsMetricsController,
    GetEnrichmentProvidersController,
    SearchNewLeadController,
    SearchNewFilterDefinitionController,
    SearchNewFilterAutocompleteController,
    CreateContactBulkController,
    CreateCompanyBulkController,
    CreateSearchRequestController,
    DeleteSearchRequestController,
    UpdateSearchRequestController,
    GetListSearchRequestController,
    UpdateContactBulkController,
    UpdateContactFeedbackController,
    UpdateEngagementScoringPriorityController,
    CountContactsController,
    CountWeeklySuccessController,
    CountContactsByEngagementScoringController,
    ApiContactEnrichPhoneController,
    ApiContactEnrichEmailController,
    GetContactExportableFieldsController,
    GetCompanyExportableFieldsController,
  ],
  providers: [
    ...profiles,
    FeatureService,
    ExportCsvService,
    EnrichmentResponseService,
    FileService,
  ],
})
export class LeadModule {}
