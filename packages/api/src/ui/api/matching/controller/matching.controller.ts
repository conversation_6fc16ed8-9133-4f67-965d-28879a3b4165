import { GetCompanyMatchingGroupsByLeadImportQuery } from '../../../../app/matching/application/query/get-company-matching-groups-by-lead-import/get-company-matching-groups-by-lead-import.query'
import { GetContactMatchingGroupsByLeadImportQuery } from '../../../../app/matching/application/query/get-contact-matching-groups-by-lead-import/get-contact-matching-groups-by-lead-import.query'
import { LeadImportMatchingContactsGroupReadModel } from '../../../../app/matching/domain/read-model/lead-import-matching-contacts-group.read-model'
import CompanyMatchingInputModel from '@matching/domain/model/company-matching-input.model'
import {
  BadRequestException,
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  HttpStatus,
  InternalServerErrorException,
  Param,
  Post,
  SerializeOptions,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { QueryBus } from '@nestjs/cqrs'
import { AuthGuard } from '@nestjs/passport'
import { ApiBearerAuth, ApiBody, ApiResponse, ApiTags } from '@nestjs/swagger'
import CompanyMatchingResultDto from '../../../../app/matching/application/query/get-company-matching-groups/dto/company-matching-result.dto'
import { GetCompanyMatchingGroupsQuery } from '../../../../app/matching/application/query/get-company-matching-groups/get-company-matching-groups.query'
import { ContactMatchingGroupResponseDto } from '../../../../app/matching/application/query/get-contact-matching-groups/contact-matching-group-response.dto'
import {
  ContactMatchingInput,
  GetContactMatchingGroupsQuery,
} from '../../../../app/matching/application/query/get-contact-matching-groups/get-contact-matching-groups.query'
import { DuplicatedMatchingIdError } from '../../../../app/matching/domain/errors/duplicated-matching-id.error'
import { MatchingError } from '../../../../app/matching/domain/errors/index'
import { EXPOSE_GROUP } from '../../../../app/shared/globals/expose-group.enum'
import { OrganizationGuard } from '../../shared/infrastructure/guard/organization.guard'
import CompanyMatchingInputDto from '../dto/company-matching-input.dto'
import ContactMatchingChromeExtensionInputDto from '../dto/contact-matching-chrome-extension-input.dto'
import { ContactMatchingInputSourceDto } from '../dto/contact-matching-input-source.dto'
import { ArrayMultiValidationsPipe } from '../validation/array-multi-validations-pipe'
import { LeadImportMatchingCompaniesGroupReadModel } from '../../../../app/matching/domain/read-model/lead-import-matching-companies-group.read-model'

@ApiTags('Matching service')
@Controller('/:organizationId/matching')
@UseGuards(AuthGuard('jwt'), OrganizationGuard)
@ApiBearerAuth('access-token')
@UseInterceptors(ClassSerializerInterceptor)
@SerializeOptions({
  groups: [EXPOSE_GROUP.PUBLIC],
})
export default class MatchingController {
  public constructor(private readonly queryBus: QueryBus) {}

  @Post('/contacts')
  @ApiResponse({
    status: HttpStatus.OK,
    type: Array<ContactMatchingGroupResponseDto>,
  })
  @ApiBody({ type: Array<ContactMatchingInputSourceDto> })
  async matchContacts(
    @Body(new ArrayMultiValidationsPipe(ContactMatchingInputSourceDto))
    matchingInputDtos: ContactMatchingInputSourceDto[],
    @Param('organizationId') organizationId: string
  ) {
    try {
      const matchingGroups = await this.queryBus.execute(
        new GetContactMatchingGroupsQuery(
          matchingInputDtos.map(input =>
            ContactMatchingInput.from({ ...input, organizationId })
          )
        )
      )

      return matchingGroups
    } catch (error) {
      if (error instanceof MatchingError) {
        throw new BadRequestException(error)
      }

      throw new InternalServerErrorException(error)
    }
  }

  @Post('/chrome-extension/contacts')
  @ApiResponse({
    status: HttpStatus.OK,
    type: Array<ContactMatchingGroupResponseDto>,
  })
  @ApiBody({ type: Array<ContactMatchingChromeExtensionInputDto> })
  async matchContactsForChromeExtension(
    @Body(new ArrayMultiValidationsPipe(ContactMatchingChromeExtensionInputDto))
    matchingInputDtos: ContactMatchingChromeExtensionInputDto[],
    @Param('organizationId') organizationId: string
  ) {
    try {
      const matchingGroups = await this.queryBus.execute(
        new GetContactMatchingGroupsQuery(
          matchingInputDtos.map(({ source, avatarUrl }) =>
            ContactMatchingInput.from({ ...source, avatarUrl, organizationId })
          )
        )
      )

      return matchingGroups
    } catch (error) {
      if (error instanceof MatchingError) {
        throw new BadRequestException(error)
      }

      throw new InternalServerErrorException(error)
    }
  }

  @Post('/companies')
  @ApiResponse({
    status: HttpStatus.OK,
    type: Array<CompanyMatchingResultDto>,
  })
  @ApiBody({ type: Array<CompanyMatchingInputDto> })
  async matchCompanies(
    @Body(new ArrayMultiValidationsPipe(CompanyMatchingInputDto))
    companyMatchingInputDto: CompanyMatchingInputDto[],
    @Param('organizationId') organizationId: string
  ) {
    try {
      const inputs = companyMatchingInputDto.map(rawCompanyInput => {
        return CompanyMatchingInputModel.builder()
          .withName(rawCompanyInput.name)
          .withDomain(rawCompanyInput.domain)
          .withLinkedinUrl(rawCompanyInput.linkedinUrl)
          .withMatchingId(rawCompanyInput.matchingId)
          .build()
      })
      const result = await this.queryBus.execute(
        new GetCompanyMatchingGroupsQuery(inputs, organizationId)
      )

      return result
    } catch (e) {
      if (e instanceof DuplicatedMatchingIdError) {
        throw new BadRequestException(e)
      }
      throw e
    }
  }

  @Get('/lead-import/contact/:leadImportId')
  @ApiResponse({
    status: HttpStatus.OK,
    type: Array<LeadImportMatchingContactsGroupReadModel>,
  })
  async getContactMatchesForLeadImport(
    @Param('organizationId') organizationId: string,
    @Param('leadImportId') leadImportId: string
  ) {
    const matchingGroups = await this.queryBus.execute(
      new GetContactMatchingGroupsByLeadImportQuery(
        organizationId,
        leadImportId
      )
    )

    return matchingGroups
  }

  @Get('/lead-import/company/:leadImportId')
  @ApiResponse({
    status: HttpStatus.OK,
    type: Array<LeadImportMatchingCompaniesGroupReadModel>,
  })
  async getCompanyMatchesForLeadImport(
    @Param('organizationId') organizationId: string,
    @Param('leadImportId') leadImportId: string
  ) {
    const matchingGroups = await this.queryBus.execute(
      new GetCompanyMatchingGroupsByLeadImportQuery(
        organizationId,
        leadImportId
      )
    )

    return matchingGroups
  }
}
