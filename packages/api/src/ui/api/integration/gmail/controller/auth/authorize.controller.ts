import {
  Body,
  Controller,
  HttpStatus,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common'
import { CommandBus } from '@nestjs/cqrs'
import { AuthGuard } from '@nestjs/passport'
import { User } from '../../../../shared/infrastructure/auth/decorator/user.decorator'
import { GmailGenerateAuthorizeCommand } from '../../../../../../app/integration/gmail/application/command/generate-authorize/generate-authorize.command'
import { GmailAuthorizationModel } from '../../../../../../app/integration/gmail/domain/model/gmail-authorization.model'
import { AuthorizeDto } from '../../../common/dto/authorize.dto'
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger'
import { OrganizationGuard } from '../../../../shared/infrastructure/guard/organization.guard'

@Controller()
@ApiTags('Integration/Gmail')
export class GmailAuthorizeController {
  constructor(readonly commandBus: CommandBus) {}

  @Post(':organizationId/integrations/gmail/authorize')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiUnauthorizedResponse()
  @ApiForbiddenResponse()
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Endpoint to fetch an authorize url for Gmail integration',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    schema: {
      type: 'object',
      properties: {
        authorizationUrl: { type: 'string' },
      },
    },
  })
  async invoke(
    @Param('organizationId') organizationId: string,
    @User() user,
    @Body() authorizeDto: AuthorizeDto
  ): Promise<Record<string, string>> {
    //Create gmail authorization model
    const gmailAuthorizationModel = new GmailAuthorizationModel()
    gmailAuthorizationModel.user = user
    gmailAuthorizationModel.organizationId = organizationId
    gmailAuthorizationModel.redirectUrl = authorizeDto.redirectUrl

    const authorizationUrl = await this.commandBus.execute(
      new GmailGenerateAuthorizeCommand(gmailAuthorizationModel)
    )

    return {
      authorizationUrl,
    }
  }
}
