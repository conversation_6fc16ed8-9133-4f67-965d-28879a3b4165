import {
  BadRequestException,
  ClassSerializerInterceptor,
  Controller,
  Get,
  HttpStatus,
  Inject,
  NotFoundException,
  Query,
  SerializeOptions,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { AuthGuard } from '@nestjs/passport'
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger'
import { EnrichmentHubStatusEnum } from '@getheroes/shared'
import { EnrichmentHubModel } from '../../../../app/enrichment-hub/domain/model/enrichment-hub.model'
import { EXPOSE_GROUP } from '../../../../app/shared/globals/expose-group.enum'
import { GetEnrichmentHubByIdQuery } from '../../../../app/enrichment-hub/application/query/get-enrichment-hub-by-id/get-enrichment-hub-by-id.query'
import { OrganizationGuard } from '../../shared/infrastructure/guard/organization.guard'
import { ResetEnrichmentHubMetricsCommand } from '../../../../app/enrichment-hub/application/command/reset-enrichment-hub-metrics/reset-enrichment-hub-metrics.command'

import { CountLeadsEnrichedInEnrichmentHubQuery } from '../../../../app/lead/application/query/contact/count-leads-enriched-in-enrichment-hub/count-leads-enriched-in-enrichment-hub.query'
import { isUUID } from 'class-validator'
import {
  LogFeature,
  LoggerService,
} from '../../../../app/shared/logger.service'
import { FindCsvImportBySpaceIdQuery } from '../../../../app/lead-import/application/query/find-csv-import-by-space-id/find-csv-import-by-space-id.query'
import {
  ENRICHMENT_HUB_CREDITS_SERVICE_INTERFACE,
  EnrichmentHubCreditsServiceInterface,
} from '../../../../app/enrichment-hub/domain/interface/enrichment-hub-credit-service.interface'
import { EnrichmentHubDto } from '../dto/enrichment-hub.dto'
import { EnrichmentHubMapper } from '../utils/enrichment-hub.mapper'
import { GetOneLeadImportByIdQuery } from '../../../../app/lead-import/application/query/get-one-lead-import-by-id/get-one-lead-import-by-id.query'

@Controller()
@ApiTags('EnrichmentHub')
@UseInterceptors(ClassSerializerInterceptor)
@SerializeOptions({
  groups: [EXPOSE_GROUP.ME],
})
export class GetOneEnrichmentHubController {
  constructor(
    private readonly queryBus: QueryBus,
    @Inject(ENRICHMENT_HUB_CREDITS_SERVICE_INTERFACE)
    private enrichmentHubCreditService: EnrichmentHubCreditsServiceInterface,
    private readonly commandBus: CommandBus,
    private readonly enrichmentHubMapper: EnrichmentHubMapper
  ) {}

  private logger = new LoggerService({
    context: GetOneEnrichmentHubController.name,
    feature: LogFeature.ENRICHMENT_HUB,
  })

  @Get('/:organizationId/enrichment-hub')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Endpoint to get one enrichment-hub by id or batch-id',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    type: EnrichmentHubDto,
  })
  @ApiUnauthorizedResponse()
  @ApiBadRequestResponse({
    schema: {
      type: 'object',
    },
  })
  async invoke(
    @Query('id') enrichmentHubId?: string,
    @Query('spaceId') spaceId?: string
  ): Promise<EnrichmentHubDto> {
    if (!isUUID(enrichmentHubId) && !isUUID(spaceId)) {
      throw new BadRequestException(
        'Please provide either enrichmentHubId or spaceId'
      )
    }

    if (enrichmentHubId) {
      const enrichmentHubModel: EnrichmentHubModel =
        await this.queryBus.execute(
          new GetEnrichmentHubByIdQuery(enrichmentHubId, {
            createdBy: true,
          })
        )
      if (!enrichmentHubModel) {
        throw new NotFoundException()
      }

      if (!enrichmentHubModel.leadImport && enrichmentHubModel.leadImportId) {
        enrichmentHubModel.leadImport = await this.queryBus.execute(
          new GetOneLeadImportByIdQuery(
            enrichmentHubModel.leadImportId,
            enrichmentHubModel.organizationId
          )
        )
      }

      const enrichmentHubDto =
        await this.enrichmentHubMapper.toEnrichmentHubDTO(enrichmentHubModel)

      if (
        enrichmentHubModel.status === EnrichmentHubStatusEnum.PARTIALLY_DONE
      ) {
        enrichmentHubDto.missingCreditsToCompletePartialEnrichment =
          await this.enrichmentHubCreditService.estimateEnrichmentCost({
            enrichmentHub: enrichmentHubModel,
            nbLeadsToEnrich: enrichmentHubModel.getNbLeadsToEnrich(),
          })
      }

      if (!enrichmentHubModel.enrichmentMetrics) {
        if (enrichmentHubModel.status !== EnrichmentHubStatusEnum.DRAFT) {
          this.logger.log({
            data: {
              enrichmentHubModel,
            },
            message: 'compute metrics for older enrichment hub',
          })
        }
        // Older enrichments don't have metrics, so we need to compute them if needed
        const enrichmentHubMetricsUpdated = await this.commandBus.execute(
          new ResetEnrichmentHubMetricsCommand(enrichmentHubModel.id)
        )
        enrichmentHubDto.enrichmentMetrics =
          enrichmentHubMetricsUpdated.enrichmentMetrics
        return enrichmentHubDto
      } else if (
        enrichmentHubModel.status === EnrichmentHubStatusEnum.IN_PROCESS
      ) {
        // refresh the metrics for in progress enrichments
        const enrichmentHubWithUpdatedMetrics: EnrichmentHubModel =
          await this.commandBus.execute(
            new ResetEnrichmentHubMetricsCommand(enrichmentHubModel.id)
          )
        const { count } = await this.queryBus.execute(
          new CountLeadsEnrichedInEnrichmentHubQuery(
            enrichmentHubModel.organizationId,
            enrichmentHubModel.id
          )
        )
        enrichmentHubDto.nbLeadsEnriched = count
        enrichmentHubDto.enrichmentMetrics =
          enrichmentHubWithUpdatedMetrics.enrichmentMetrics
        return enrichmentHubDto
      } else {
        return enrichmentHubDto
      }
    }

    if (spaceId) {
      const csvImportModel = await this.queryBus.execute(
        new FindCsvImportBySpaceIdQuery(spaceId)
      )
      if (!csvImportModel?.enrichmentHubId) {
        this.logger.warn({
          data: {
            spaceId,
            csvImportModel,
          },
          message: 'No enrichmentHubId found for this spaceId',
        })
        throw new NotFoundException(
          'No CSV Import found with this spaceId or no enrichmentHub attached to it'
        )
      }

      const enrichmentHubModel = await this.queryBus.execute(
        new GetEnrichmentHubByIdQuery(csvImportModel.enrichmentHubId, {
          createdBy: true,
        })
      )

      return await this.enrichmentHubMapper.toEnrichmentHubDTO(
        enrichmentHubModel
      )
    }
  }
}
