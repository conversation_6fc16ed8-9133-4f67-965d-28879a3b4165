import { <PERSON><PERSON><PERSON> } from '@nestjs/common'
import { CqrsModule } from '@nestjs/cqrs'
import { CreateSequenceController } from './controller/sequence/create-sequence.controller'
import { DeleteSequenceController } from './controller/sequence/delete-sequence.controller'
import { GetListSequenceController } from './controller/sequence/get-list-sequence.controller'
import { GetSequenceController } from './controller/sequence/get-sequence.controller'
import { UpdateSequenceController } from './controller/sequence/update-sequence.controller'
import { CreateContactSequenceController } from './controller/contact/create-sequence-contact.controller'
import { GetSequenceContactController } from './controller/contact/get-sequence-contact.controller'
import { CreateSequenceStepController } from './controller/step/create-sequence-step.controller'
import { DeleteSequenceStepController } from './controller/step/delete-sequence-step.controller'
import { UpdateSequenceStepController } from './controller/step/update-sequence-step.controller'
import { GetSequenceStepController } from './controller/step/get-sequence-step.controller'
import { GetListSequenceStepController } from './controller/step/get-list-sequence-step.controller'
import { DeleteContactSequenceController } from './controller/contact/delete-sequence-contact.controller'
import { GetSequencesKpisController } from './controller/sequence/get-sequences-kpis.controller'
import { GetSequenceContactErrorController } from './controller/contact/get-sequence-contact-error.controller'
import { UpdateSequenceContactController } from './controller/contact/update-sequence-contact.controller'
import { FilterSequenceContactsNotInOtherSequenceController } from './controller/contact/filter-sequence-contacts-not-in-other-sequence.controller'
import { GetListSequenceWithStepsController } from './controller/sequence/get-list-sequence-with-steps.controller'
import { GetSequenceContactErrorOneController } from './controller/contact/get-sequence-contact-error-one.controller'
import { GetListSequenceStepActivitiesByContactsController } from './controller/step/get-list-sequence-step-activities-by-contacts.controller'
import { FeaturesModule } from '../../../app/feature/features.module'
import { GetSequenceContactActivityController } from './controller/contact/get-sequence-contact-activity.controller'
import { SkipContactSequenceController } from './controller/contact/skip-sequence-contact.controller'
import { CountSequencesController } from './controller/sequence/count-sequences.controller'
import { SequenceStepService } from '../../../app/sequence/application/service/sequence-step.service'
import { ReviewSequenceContactController } from './controller/contact/review-sequence-contact.controller'
import { CreateSequenceContactFromImportController } from './controller/contact/create-sequence-contact-from-import.controller'
import { UpdateSequenceContactStepController } from './controller/contact/update-sequence-contact-step.controller'
import { DeleteSequenceContactStepController } from './controller/contact/delete-sequence-contact-step.controller'
import { ComputeKpiSequenceController } from './controller/sequence/compute-kpi-sequence.controller'
import { DuplicateSequenceController } from './controller/sequence/duplicate-sequence.controller'

@Module({
  imports: [CqrsModule, FeaturesModule],
  controllers: [
    // Sequence
    CreateSequenceController,
    DeleteSequenceController,
    GetListSequenceController,
    GetSequencesKpisController,
    GetSequenceController,
    UpdateSequenceController,
    GetListSequenceWithStepsController,
    GetListSequenceStepActivitiesByContactsController,
    CountSequencesController,
    ComputeKpiSequenceController,

    // Contact
    CreateContactSequenceController,
    CreateSequenceContactFromImportController,
    ReviewSequenceContactController,
    UpdateSequenceContactController,
    DeleteContactSequenceController,
    GetSequenceContactController,
    GetSequenceContactErrorOneController,
    GetSequenceContactErrorController,
    FilterSequenceContactsNotInOtherSequenceController,
    GetSequenceContactActivityController,
    SkipContactSequenceController,
    UpdateSequenceContactStepController,
    DeleteSequenceContactStepController,

    // Step
    CreateSequenceStepController,
    DeleteSequenceStepController,
    GetListSequenceStepController,
    GetSequenceStepController,
    UpdateSequenceStepController,
    DuplicateSequenceController,
  ],
})
export class SequenceModule {}
