import { ArrayMaxSize, IsArray } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { SequenceWithStepsObject } from '../../../../app/sequence/domain/object/sequence-with-steps.object'

import { ListResponseDto } from '../../../../app/shared/domain/object/list-response.object'
import { AutoMap } from '@automapper/classes'

export class GetListSequenceWithStepsByContactOutputDto extends ListResponseDto(
  SequenceWithStepsObject
) {}

export class ListSequenceStepsByContactDto {
  contactId: string
  steps: SequenceWithStepsObject[]
}

export class FilterContactsDto {
  @AutoMap()
  @IsArray()
  @ApiProperty()
  @ArrayMaxSize(100)
  public ids: string[]
}
