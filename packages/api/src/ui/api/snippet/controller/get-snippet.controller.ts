import {
  <PERSON>,
  ForbiddenException,
  Get,
  HttpStatus,
  Param,
  UseGuards,
} from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'
import {
  ApiBearerAuth,
  ApiNotFoundResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger'
import { OrganizationGuard } from '../../shared/infrastructure/guard/organization.guard'
import { SnippetModel } from '../../../../app/snippet/domain/model/snippet.model'
import { SnippetPipe } from '../pipe/snippet.pipe'
import { PermissionSpecification } from '../../../../app/shared/domain/specification/model-permission.specitification'
import { SnippetPermission } from '../../../../app/snippet/domain/snippet-permission.enum'
import { User } from '../../shared/infrastructure/auth/decorator/user.decorator'
import { UserModel } from '../../../../app/shared/domain/model/user.model'

@Controller('/:organizationId')
@ApiTags('Snippet')
export class GetSnippetController {
  @Get('/snippets/:snippetId')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiUnauthorizedResponse()
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Endpoint to get one snippet by id',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(SnippetModel),
    },
  })
  @ApiNotFoundResponse({
    schema: {
      type: 'object',
      oneOf: [
        {
          example: {
            statusCode: '404',
            message: 'Not Found',
          },
        },
      ],
    },
  })
  async getOne(
    @User() user: UserModel,
    @Param(SnippetPipe) snippetModel
  ): Promise<SnippetModel> {
    if (
      new PermissionSpecification(snippetModel).can(
        SnippetPermission.VIEW,
        user
      ) === false
    ) {
      throw new ForbiddenException()
    }

    return snippetModel
  }
}
