import {
  <PERSON>,
  Controller,
  ForbiddenException,
  Param,
  Patch,
  UseGuards,
} from '@nestjs/common'
import { CommandBus, QueryBus } from '@nestjs/cqrs'
import { InjectMapper } from '@automapper/nestjs'
import { Mapper } from '@automapper/core'
import { AuthGuard } from '@nestjs/passport'
import { ApiResponse, ApiTags, ApiUnauthorizedResponse } from '@nestjs/swagger'
import { SubscriptionModel } from '../../../../app/subscription/domain/subscription.model'
import { SubscriptionInterface } from '../../../../app/shared/domain/model/subscription/subscription.interface'
import { PermissionSpecification } from '../../../../app/shared/domain/specification/model-permission.specitification'
import { SubscriptionPermission } from '../../../../app/subscription/domain/subscription-permission.enum'
import { User } from '../../shared/infrastructure/auth/decorator/user.decorator'
import { UserModel } from '../../../../app/shared/domain/model/user.model'
import { OrganizationGuard } from '../../shared/infrastructure/guard/organization.guard'
import { UpdateSubscriptionQuantityDto } from '../dto/update-subscription-quantity.dto'
import { UpdateSubscriptionQuantityCommand } from '../../../../app/subscription/application/command/update-subscription-quantity/update-subscription-quantity.command'
import { FindSubscriptionByIdQuery } from '../../../../app/subscription/application/query/find-subscription-by-id/find-subscription-by-id.query'

@Controller()
@ApiTags('Subscription')
export class UpdateCreditSubscriptionController {
  constructor(
    @InjectMapper() private readonly mapper: Mapper,
    private queryBus: QueryBus,
    private commandBus: CommandBus
  ) {}

  @Patch('/:organizationId/subscriptions/credit/:subscriptionId')
  @UseGuards(AuthGuard('jwt'), OrganizationGuard)
  @ApiUnauthorizedResponse()
  @ApiResponse({
    status: 200,
    type: [SubscriptionModel],
  })
  async invoke(
    @Param('organizationId') organizationId: string,
    @Param('subscriptionId') subscriptionId: string,
    @User() user: UserModel,
    @Body() updateSubscriptionDto: UpdateSubscriptionQuantityDto
  ): Promise<SubscriptionInterface> {
    try {
      if (
        new PermissionSpecification(new SubscriptionModel()).can(
          SubscriptionPermission.CREATE,
          user
        ) === false
      ) {
        throw new ForbiddenException()
      }

      // Get subscription by id
      const subscription = await this.queryBus.execute<
        FindSubscriptionByIdQuery,
        SubscriptionModel
      >(new FindSubscriptionByIdQuery(organizationId, subscriptionId))

      // Update the subscription in Stripe
      return await this.commandBus.execute(
        new UpdateSubscriptionQuantityCommand(
          subscription,
          updateSubscriptionDto
        )
      )
    } catch (error) {
      throw new ForbiddenException()
    }
  }
}
