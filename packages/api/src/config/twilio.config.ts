import { registerAs } from '@nestjs/config'
import { IsOptional, IsString } from 'class-validator'
import { validateConfigFromClass } from './config.validation'
import dotenv from 'dotenv'

dotenv.config()

export class TwilioConfig {
  @IsString()
  @IsOptional()
  twilioAccountSid: string

  @IsString()
  @IsOptional()
  twilioAuthToken: string

  @IsString()
  @IsOptional()
  verifyServiceSid: string
}

export default registerAs('twilio', () => {
  const config: TwilioConfig = {
    twilioAccountSid: process.env.TWILIO_ACCOUNT_SID,
    twilioAuthToken: process.env.TWILIO_AUTH_TOKEN,
    verifyServiceSid: process.env.TWILIO_VERIFY_SERVICE_SID,
  }

  return validateConfigFromClass(TwilioConfig, config)
})
