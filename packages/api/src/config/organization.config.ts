import { registerAs } from '@nestjs/config'
import { IsString } from 'class-validator'
import { validateConfigFromClass } from './config.validation'

export class OrganizationConfig {
  @IsString()
  privacyPolicyUrl: string

  @IsString()
  termsOfServiceUrl: string
}

export default registerAs('organization', async () => {
  const config = {
    privacyPolicyUrl: 'https://www.zeliq.com/privacy-policy/',
    termsOfServiceUrl: 'https://www.zeliq.com/terms-of-use/',
  }

  return validateConfigFromClass(OrganizationConfig, config)
})
