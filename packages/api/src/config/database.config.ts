import { TypeOrmModuleOptions } from '@nestjs/typeorm'
import { registerAs } from '@nestjs/config'
import { validateConfigFromClass } from './config.validation'
import { IsObject } from 'class-validator'
import { SnakeNamingStrategy } from 'typeorm-naming-strategies'

// Needed when imported outside the NestJS app (e.g. TypeOrm config file generation)
import dotenv from 'dotenv'
import { environment } from '../environments/environment'
import { DataSourceOptions } from 'typeorm'
import { OrganizationEntity } from '../app/organization/infrastructure/entity/organization.entity'
import { OrganizationMemberEntity } from '../app/organization/infrastructure/entity/organization-member.entity'
import { OrganizationMemberApiKeyEntity } from '../app/organization/infrastructure/entity/organization-member-api-key.entity'
import { InvitationMemberEntity } from '../app/organization/infrastructure/entity/invitation-member.entity'
import { UserEntity } from '../app/shared/infrastructure/entity/user.entity'
import { ContactEntity } from '../app/lead/infrastructure/entity/contact.entity'
import { AircallAuthorizationEntity } from '../app/integration/aircall/infrastructure/entity/aircall-authorization.entity'
import { HubspotAuthorizationEntity } from '../app/integration/hubspot/infrastructure/entity/hubspot-authorization.entity'
import { TemplateEntity } from '../app/template/infrastructure/entity/template.entity'
import { SnippetEntity } from '../app/snippet/infrastructure/entity/snippet.entity'
import { CompanyEntity } from '../app/lead/infrastructure/entity/company.entity'
import { CallEntity } from '../app/dialer/infrastructure/entity/call.entity'
import { CSVImportEntity } from '../app/lead-import/infrastructure/entity/csv-import.entity'
import { CompanyUserEntity } from '../app/lead/infrastructure/entity/company-user.entity'
import { TaskEntity } from '../app/journey/infrastructure/entity/task/task.entity'
import { GmailAuthorizationEntity } from '../app/integration/gmail/infrastructure/entity/gmail-authorization.entity'
import { ViewEntity } from '../app/lead/infrastructure/entity/view.entity'
import { NoteEntity } from '../app/journey/infrastructure/entity/note/note.entity'
import { ActivityEntity } from '../app/lead/infrastructure/entity/activity.entity'
import { CustomFieldEntity } from '../app/lead/infrastructure/entity/custom-field.entity'
import { HubspotSyncedContactListEntity } from '../app/integration/hubspot/infrastructure/entity/hubspot-synced-contact-list.entity'
import { LeadMetricEntity } from '../app/lead/infrastructure/entity/lead-metric.entity'
import { NbLeadsByMemberEntity } from '../app/organization/infrastructure/entity/nb-lead-by-member.entity'
import { EnrichmentEntity } from '../app/lead/infrastructure/entity/enrichment.entity'
import { ContactZeliqEntity } from '../app/lead/infrastructure/entity/contact-zeliq.entity'
import { CompanyZeliqEntity } from '../app/lead/infrastructure/entity/company-zeliq.entity'
import { CrystalProfileEntity } from '../app/integration/crystal/infrastructure/entity/crystal-profile.entity'
import { RingoverAuthorizationEntity } from '../app/integration/ringover/infrastructure/entity/ringover-authorization.entity'
import { EnrichmentQueryEntity } from '../app/lead/infrastructure/entity/enrichment-query.entity'
import { EnrichmentMetricEntity } from '../app/lead/infrastructure/entity/enrichment-metric.entity'
import { CreditTransactionEntity } from '../app/credit/infrastructure/entity/credit-transaction.entity'
import { ContactStatusHistoryEntity } from '../app/lead/infrastructure/entity/contact-status-history.entity'
import { JobTitleEntity } from '../app/lead/infrastructure/entity/job-title.entity'
import { SearchRequestEntity } from '../app/lead/infrastructure/entity/search-request.entity'
import { SequenceEntity } from '../app/sequence/infrastructure/entity/sequence.entity'
import { SequenceContactEntity } from '../app/sequence/infrastructure/entity/sequence-contact.entity'
import { SequenceStepEntity } from '../app/sequence/infrastructure/entity/sequence-step.entity'
import { EmailSettingsEntity } from '../app/mailer/infrastructure/entity/email-settings.entity'
import { UnsubscribedEmailEntity } from '../app/mailer/infrastructure/entity/unsubscribed-email.entity'
import { HubspotObjectEntity } from '../app/hubspot-lead/infrastructure/entity/hubspot-object.entity'
import { SubscriptionEntity } from '../app/subscription/infrastructure/entity/subscription.entity'
import { EmailBouncedEntity } from '../app/mailer/infrastructure/entity/email-bounced.entity'
import { AsyncActionEntity } from '../app/shared/infrastructure/entity/async-action.entity'
import { AllowedDomainEntity } from '../app/mailer/infrastructure/entity/allowed-domain.entity'
import { OutlookAuthorizationEntity } from '../app/integration/outlook/infrastructure/entity/outlook-authorization.entity'
import { MessageEntity } from '../app/mailer/infrastructure/entity/message.entity'
import { EnrichmentResultEntity } from '../app/lead/infrastructure/entity/enrichment-result.entity'
import { StepEntity } from '../app/onboarding/infrastructure/entity/step.entity'
import { OnboardingInAppEntity } from '../app/onboarding/infrastructure/entity/onboarding-in-app.entity'
import { PrismaticCustomerEntity } from '../app/integration/prismatic/infrastructure/entity/prismatic-customer.entity'
import { PrismaticInstanceEntity } from '../app/integration/prismatic/infrastructure/entity/prismatic-instance.entity'
import { EnrichmentHubEntity } from '../app/enrichment-hub/infrastructure/entity/enrichment-hub.entity'
import { IntegrationAuthorizationEntity } from '../app/integration/captain-data/infrastructure/entity/integration-authorization.entity'
import { LinkedinUserEntity } from '../app/integration/captain-data/infrastructure/entity/linkedin-user.entity'
import { WorkflowEntity } from '../app/integration/captain-data/infrastructure/entity/workflow.entity'
import { JobEntity } from '../app/integration/captain-data/infrastructure/entity/job.entity'
import { LinkedinUsersConnectionsEntity } from '../app/integration/captain-data/infrastructure/entity/linkedin-users-connections.entity'
import { LinkedinThreadEntity } from '../app/integration/captain-data/infrastructure/entity/linkedin-thread.entity'
import { LinkedinMessageEntity } from '../app/integration/captain-data/infrastructure/entity/linkedin-message.entity'
import { DnsSettingsEntity } from '../app/mailer/infrastructure/entity/dns-settings.entity'
import { OrganizationEventEntity } from '../app/organization/infrastructure/entity/organization-event.entity'
import { UserSettingsEntity } from '../app/user/infrastructure/entity/user-settings.entity'
import { OrganizationMemberNotificationEntity } from '../app/organization/infrastructure/entity/organization-member-notification.entity'
import { BlacklistedDomainEntity } from '../app/admin/infrastructure/entity/blacklisted-domain.entity'
import { SequenceActivityEntity } from '../app/sequence/infrastructure/entity/sequence-activity.entity'
import { StripeWebhookEventEntity } from '../app/subscription/infrastructure/entity/stripe-webhook-event.entity'
import { WhitelistedDomainEntity } from '../app/admin/infrastructure/entity/whitelisted-domain.entity'
import { EmailBouncedToProcessEntity } from '../app/mailer/infrastructure/entity/email-bounced-to-process.entity'
import { LeadImportEntity } from '../app/lead-import/infrastructure/entity/lead-import.entity'
import { WhitelistedEmailEntity } from '../app/admin/infrastructure/entity/whitelisted-email.entity'
import { OrganizationMemberMetricsEntity } from '../app/organization/infrastructure/entity/organization-member-metrics.entity'
import { LeadExportEntity } from '../app/lead/infrastructure/entity/lead-export.entity'
import { ContactZeliqOptOutEntity } from '../app/lead/infrastructure/entity/contact-zeliq-opt-out.entity'
import { SequenceContactStepEntity } from '../app/sequence/infrastructure/entity/sequence-contact-step.entity'
import { EmailJobTrackingEntity } from '../app/mailer/infrastructure/entity/email-job-tracking.entity'

dotenv.config()

const entities = [
  OrganizationEntity,
  OrganizationMemberEntity,
  OrganizationMemberApiKeyEntity,
  InvitationMemberEntity,
  UserEntity,
  NoteEntity,
  ContactEntity,
  CompanyEntity,
  CompanyUserEntity,
  AircallAuthorizationEntity,
  RingoverAuthorizationEntity,
  IntegrationAuthorizationEntity,
  GmailAuthorizationEntity,
  MessageEntity,
  EmailBouncedEntity,
  HubspotAuthorizationEntity,
  HubspotSyncedContactListEntity,
  CallEntity,
  TemplateEntity,
  TaskEntity,
  SnippetEntity,
  CSVImportEntity,
  ViewEntity,
  ActivityEntity,
  CustomFieldEntity,
  LeadMetricEntity,
  NbLeadsByMemberEntity,
  EnrichmentEntity,
  ContactZeliqEntity,
  CompanyZeliqEntity,
  CrystalProfileEntity,
  EnrichmentQueryEntity,
  EnrichmentMetricEntity,
  CreditTransactionEntity,
  ContactStatusHistoryEntity,
  JobTitleEntity,
  AllowedDomainEntity,
  OutlookAuthorizationEntity,
  EmailSettingsEntity,
  DnsSettingsEntity,
  SequenceEntity,
  SequenceContactEntity,
  SequenceStepEntity,
  UnsubscribedEmailEntity,
  SearchRequestEntity,
  HubspotObjectEntity,
  SubscriptionEntity,
  StripeWebhookEventEntity,
  AsyncActionEntity,
  EnrichmentResultEntity,
  StepEntity,
  LeadImportEntity,
  OnboardingInAppEntity,
  PrismaticCustomerEntity,
  PrismaticInstanceEntity,
  EnrichmentHubEntity,
  LinkedinUserEntity,
  WorkflowEntity,
  JobEntity,
  LinkedinUsersConnectionsEntity,
  LinkedinThreadEntity,
  LinkedinMessageEntity,
  OrganizationEventEntity,
  UserSettingsEntity,
  OrganizationMemberNotificationEntity,
  BlacklistedDomainEntity,
  WhitelistedDomainEntity,
  SequenceActivityEntity,
  EmailBouncedToProcessEntity,
  WhitelistedEmailEntity,
  LeadExportEntity,
  OrganizationMemberMetricsEntity,
  ContactZeliqOptOutEntity,
  SequenceContactStepEntity,
  EmailJobTrackingEntity,
]

dotenv.config()

export const databaseConfig: DataSourceOptions = {
  type: 'postgres',
  url: `${process.env.POSTGRES_URL}${process.env.POSTGRES_DATABASE}`,
  entities,
  migrationsTableName: 'migration',
  migrations: ['./src/migrations/*.ts'],
  ssl: environment.production,
  namingStrategy: new SnakeNamingStrategy(),
  extra: {
    ...(environment.production
      ? {
          ssl: {
            rejectUnauthorized: false,
          },
          connectionTimeoutMillis: 10000,
          idleTimeoutMillis: 10000,
        }
      : {}),
  },
}

export class DatabaseConfig {
  @IsObject()
  typeOrmConfig: TypeOrmModuleOptions
}

export default registerAs('database', () => {
  const config = {
    typeOrmConfig: {
      ...databaseConfig,
    },
  }

  return validateConfigFromClass(DatabaseConfig, config)
})
