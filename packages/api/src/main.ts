/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import 'reflect-metadata'
import './datadog'
import {
  BadRequestException,
  INestApplication,
  Logger,
  ValidationError,
  ValidationPipe,
} from '@nestjs/common'
import { ModulesContainer, NestFactory } from '@nestjs/core'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import { json } from 'body-parser'
export * from 'pg'
// eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-var-requires
const QueryStream = require('pg-query-stream')

import { AppModule } from './app.module'
import { InvitationMemberModel } from './app/organization/domain/invitation-member.model'
import { OrganizationModel } from './app/organization/domain/organization.model'
import { environment } from './environments/environment'
import helmet from 'helmet'
import { ConfigService } from '@nestjs/config'
import { GlobalConfig } from './config/global.config'
import { ContactModel } from './app/lead/domain/model/contact.model'
import { TemplateModel } from './app/template/domain/model/template.model'
import { PaginationResultMetaObject } from './app/shared/domain/object/pagination-result-meta.object'
import { FieldModel } from './app/lead/domain/model/field.model'
import { findValuesByKey } from './ui/api/shared/infrastructure/helper/object.helper'
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter'
import { Logger as PinoLogger } from 'nestjs-pino'
import rawBodyMiddleware from './app/subscription/middleware/raw-body.middleware'
import { createBullBoard } from '@bull-board/api'
import { ExpressAdapter } from '@bull-board/express'
import { BullConfig } from './config/bull.config'
import { BullAdapter } from '@bull-board/api/bullAdapter'
import Queue from 'bull'
import { Queue as QueueMQ } from 'bullmq'
import expressBasicAuth from 'express-basic-auth'
import { RedisConfig } from './config/redis.config'

declare const module: any

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'fatal'],
  })

  app.useLogger(app.get(PinoLogger))
  app.use(rawBodyMiddleware()) // This must run be before the body-parser middleware

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      stopAtFirstError: true,
      exceptionFactory: (errors: ValidationError[]) => {
        const constraints = findValuesByKey(errors, 'constraints', true)
        const firstConstraint = constraints[0] ?? {}

        return new BadRequestException(
          Object.values(firstConstraint)[0],
          'VALIDATION_FAILED'
        )
      },
    })
  )
  const globalPrefix = 'api'
  app.setGlobalPrefix(globalPrefix)
  const port = process.env.PORT ?? '3333'

  const configService: ConfigService = app.get(ConfigService)
  const { corsOptions } = configService.get<GlobalConfig>('global')
  app.enableCors(corsOptions)

  // //>>>>>>>>> LATENCY
  // //simulate backend latency
  // app.use(async (req, res, next) => {
  //   //latency 5 s
  //   await new Promise(resolve => setTimeout(resolve, 5000))
  //   next()
  // })
  // //<<<<<<<<< LATENCY
  app.use(helmet())

  app.use('/api/:organizationId/mailer/message', json({ limit: '34mb' }))
  app.use(
    '/api/:organizationId/mailer/message/:messageId/reply',
    json({ limit: '34mb' })
  )
  app.use(
    '/api/:organizationId/sequences/:sequenceId/step/:stepId',
    json({ limit: '34mb' })
  )
  app.use(
    '/api/integrations/outlook/settings/:integrationId',
    json({ limit: '2mb' })
  )
  app.use(
    '/api/:organizationId/integrations/prismatic/leads/sync',
    json({ limit: '5mb' })
  )
  app.use(json({ limit: '100kb' }))

  if (environment.production != true) {
    const config = new DocumentBuilder()
      .setTitle('Zeliq API Documentation')
      .setDescription('The Zeliq API for dashboard description')
      .setVersion('1.0')
      .addBearerAuth(
        {
          // I was also testing it without prefix 'Bearer ' before the JWT
          description: `[just text field] Please enter token in following format: Bearer <JWT>`,
          name: 'Authorization',
          bearerFormat: 'Bearer', // I`ve tested not to use this field, but the result was the same
          scheme: 'Bearer',
          type: 'http',
          in: 'Header',
        },
        'access-token' // This name here is important for matching up with @ApiBearerAuth() in your controller!
      )
      .build()
    const document = SwaggerModule.createDocument(app, config, {
      extraModels: [
        OrganizationModel,
        InvitationMemberModel,
        ContactModel,
        TemplateModel,
        PaginationResultMetaObject,
        FieldModel,
      ],
    })
    SwaggerModule.setup('api', app, document)
  }

  addBullBoard(app, configService)

  await app.listen(port)
  app.enableShutdownHooks()

  Logger.log(
    `🚀 Application is running on: http://localhost:${port}/${globalPrefix}`
  )

  if (module.hot) {
    module.hot.accept()
    module.hot.dispose(() => app.close())
  }
}

const getBullQueue = (queueName, redisConfig) => {
  return new BullAdapter(
    new Queue(queueName, {
      redis: {
        host: redisConfig.host,
        port: redisConfig.port,
      },
    }),
    {
      readOnlyMode: environment.production === true,
      delimiter: '-',
    }
  )
}

const getBullMQQueue = (queueName, redisConfig) => {
  return new BullMQAdapter(
    new QueueMQ(queueName, {
      connection: {
        host: redisConfig.host,
        port: redisConfig.port,
        db: 1,
      },
    }),
    {
      readOnlyMode: environment.production === true,
      delimiter: '-',
    }
  )
}

function addBullBoard(app: INestApplication, configService: ConfigService) {
  if (!configService.get<BullConfig>('bull').bullBoardConfig.isEnabled) {
    return
  }

  const redisConfig = configService.get<RedisConfig>('redis')

  const modulesContainer = app.get(ModulesContainer)
  const queues = []

  // Iterate through all modules to find @Processor decorated classes
  for (const module of modulesContainer.values()) {
    const providers = module.providers

    for (const provider of providers.values()) {
      if (!provider.instance) {
        continue
      }

      const instance: any = provider.instance
      if (instance instanceof QueueMQ) {
        const bullMQ = getBullMQQueue(instance.name, redisConfig)
        queues.push(bullMQ)
      }
      if (instance instanceof Queue) {
        const bull = getBullQueue(instance.name, redisConfig)
        queues.push(bull)
      }
    }
  }

  const serverAdapter = new ExpressAdapter()
  serverAdapter.setBasePath('/bull-board')

  const { addQueue } = createBullBoard({
    queues: [],
    serverAdapter,
  })

  queues.forEach(queue => {
    addQueue(queue)
  })

  Logger.log(`*** BULL-BOARD *** nb queues : ${queues.length}`)
  const middlewares = []

  if (
    configService.get<BullConfig>('bull').bullBoardConfig.login &&
    configService.get<BullConfig>('bull').bullBoardConfig.password
  ) {
    middlewares.push(
      expressBasicAuth({
        users: {
          [configService.get<BullConfig>('bull').bullBoardConfig.login]:
            configService.get<BullConfig>('bull').bullBoardConfig.password,
        },
        challenge: true,
      })
    )
  }

  middlewares.push(serverAdapter.getRouter())

  app.use('/bull-board', middlewares)
}

bootstrap()

//Handle unhandled promise rejections and uncaught exceptions globally
process.on('uncaughtException', (error: Error) => {
  Logger.error(`uncaughtException--> ${error.stack}`)
})
process.on('unhandledRejection', (error: Error) => {
  Logger.error(`unhandledRejection -->${error.stack}`)
})
