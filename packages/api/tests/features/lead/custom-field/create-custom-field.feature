Feature: Test create a custom field

  Background:
    Given The baseUrl is "http://localhost:3333/api"
    Given Headers fields are
    """
        {
        "Content-Type": "application/json"
        }
    """

  Scenario: Create custom-field with user not connected
    When I send a POST request to "/ef93c4a3-4ab4-4c0d-9caf-386b491a4d61/leads/custom-fields" with the data is
    """
      {
        "fieldId": "custom-field-2",
        "fieldLabel": "Custom field",
        "fieldFamily": "company",
        "fieldType": "string"
      }
    """
    Then the response status should be 401

  Scenario: Create custom-field with user not member organization
    Given Cognito user email is "<EMAIL>" with password "Azerty123@"
    Given User needs to be authenticated by Access Token
    When I send a POST request to "/ccd72cb3-7304-4a8d-9371-3c75d3539428/leads/custom-fields" with the data is
    """
      {
        "fieldId": "custom-field-2",
        "fieldLabel": "Custom field",
        "fieldFamily": "company",
        "fieldType": "string"
      }
    """
    Then the response status should be 403

  Scenario: Create custom-field with success
    Given The fixtures files are "tests/features/common/users.fixtures.yml,tests/features/common/organizations.fixtures.yml,tests/features/common/organization-members.fixtures.yml,tests/features/lead/custom-field/custom-field.fixtures.yml"
    Given Cognito user email is "<EMAIL>" with password "Azerty123@"
    Given User needs to be authenticated by Access Token
    When I send a POST request to "/ef93c4a3-4ab4-4c0d-9caf-386b491a4d61/leads/custom-fields" with the data is
    """
      {
        "fieldId": "custom-field-2",
        "fieldLabel": "Custom field",
        "fieldFamily": "company",
        "fieldType": "string"
      }
    """
    Then the response status should be 201

  Scenario: Create custom-field with default (reserved) fieldId
    Given The fixtures files are "tests/features/common/users.fixtures.yml,tests/features/common/organizations.fixtures.yml,tests/features/common/organization-members.fixtures.yml,tests/features/lead/custom-field/custom-field.fixtures.yml"
    Given Cognito user email is "<EMAIL>" with password "Azerty123@"
    Given User needs to be authenticated by Access Token
    When I send a POST request to "/ef93c4a3-4ab4-4c0d-9caf-386b491a4d61/leads/custom-fields" with the data is
    """
      {
        "fieldId": "name",
        "fieldLabel": "Custom field",
        "fieldFamily": "company",
        "fieldType": "string"
      }
    """
    Then the response status should be 409

  Scenario: Create custom-field with duplicate name for a user in the same organization
    Given The fixtures files are "tests/features/common/users.fixtures.yml,tests/features/common/organizations.fixtures.yml,tests/features/common/organization-members.fixtures.yml,tests/features/lead/custom-field/custom-field.fixtures.yml"
    Given Cognito user email is "<EMAIL>" with password "Azerty123@"
    Given User needs to be authenticated by Access Token
    When I send a POST request to "/ef93c4a3-4ab4-4c0d-9caf-386b491a4d61/leads/custom-fields" with the data is
    """
      {
        "fieldId": "custom_field_1",
        "fieldLabel": "Custom field",
        "fieldFamily": "company",
        "fieldType": "string"
      }
    """
    Then the response status should be 409

  Scenario: Create custom-field with wrong fieldFamily
    Given The fixtures files are "tests/features/common/users.fixtures.yml,tests/features/common/organizations.fixtures.yml,tests/features/common/organization-members.fixtures.yml,tests/features/lead/custom-field/custom-field.fixtures.yml"
    Given Cognito user email is "<EMAIL>" with password "Azerty123@"
    Given User needs to be authenticated by Access Token
    When I send a POST request to "/ef93c4a3-4ab4-4c0d-9caf-386b491a4d61/leads/custom-fields" with the data is
    """
      {
        "fieldId": "custom-field-2",
        "fieldLabel": "Custom field",
        "fieldFamily": "company test",
        "fieldType": "string"
      }
    """
    Then the response status should be 400

  Scenario: Create Company with custom-field
    Given The fixtures files are "tests/features/common/users.fixtures.yml,tests/features/common/organizations.fixtures.yml,tests/features/common/organization-members.fixtures.yml,tests/features/lead/companies.fixtures.yml"
    Given Cognito user email is "<EMAIL>" with password "Azerty123@"
    Given User needs to be authenticated by Access Token
    When I send a POST request to "/ef93c4a3-4ab4-4c0d-9caf-386b491a4d61/leads/companies" with the data is
    """
        {
          "name":"company custom_field_1",
          "custom_field_1":"qonto.com"
        }
    """
    Then the response status should be 201
    Then the response should contains "id" field
    Then the response should contains "custom_field_1" field

  Scenario: Create Company with custom-field with wrong type
    Given The fixtures files are "tests/features/common/users.fixtures.yml,tests/features/common/organizations.fixtures.yml,tests/features/common/organization-members.fixtures.yml,tests/features/lead/companies.fixtures.yml"
    Given Cognito user email is "<EMAIL>" with password "Azerty123@"
    Given User needs to be authenticated by Access Token
    When I send a POST request to "/ef93c4a3-4ab4-4c0d-9caf-386b491a4d61/leads/companies" with the data is
    """
        {
          "name":"company wrong type custom_field_1",
          "custom_field_1":1000
        }
    """
    Then the response status should be 400

  Scenario: Create Company with custom-field which dont exist in the organization
    Given The fixtures files are "tests/features/common/users.fixtures.yml,tests/features/common/organizations.fixtures.yml,tests/features/common/organization-members.fixtures.yml,tests/features/lead/companies.fixtures.yml"
    Given Cognito user email is "<EMAIL>" with password "Azerty123@"
    Given User needs to be authenticated by Access Token
    When I send a POST request to "/ef93c4a3-4ab4-4c0d-9caf-386b491a4d61/leads/companies" with the data is
    """
        {
          "name":"company missing custom-field",
          "custom_field_company_2":"test"
        }
    """
    Then the response status should be 400

  Scenario: Create Contact with custom-field with success
    Given The fixtures files are "tests/features/common/users.fixtures.yml,tests/features/common/organizations.fixtures.yml,tests/features/common/organization-members.fixtures.yml,tests/features/lead/companies.fixtures.yml"
    Given Cognito user email is "<EMAIL>" with password "Azerty123@"
    Given User needs to be authenticated by Access Token
    When I send a POST request to "/ef93c4a3-4ab4-4c0d-9caf-386b491a4d61/leads/contacts" with the data is
    """
        {
          "firstName":"John",
          "lastName":"Fixture",
          "companyId": "eddf2dce-73c4-40ac-9521-3c1d1c260c3c",
          "companyName": "Google",
          "title": "Software Engineer",
          "emails": ["<EMAIL>"],
          "phones": ["+33600110054"],
          "custom_field_contact":"test"
        }
    """
    Then the response status should be 201
    Then the response should contains "id" field
    Then the response should contains "custom_field_contact" field

  Scenario: Create Contact with custom-field with wrong type
    Given The fixtures files are "tests/features/common/users.fixtures.yml,tests/features/common/organizations.fixtures.yml,tests/features/common/organization-members.fixtures.yml,tests/features/lead/companies.fixtures.yml"
    Given Cognito user email is "<EMAIL>" with password "Azerty123@"
    Given User needs to be authenticated by Access Token
    When I send a POST request to "/ef93c4a3-4ab4-4c0d-9caf-386b491a4d61/leads/contacts" with the data is
    """
        {
          "firstName":"John",
          "lastName":"Fixture",
          "companyId": "eddf2dce-73c4-40ac-9521-3c1d1c260c3c",
          "companyName": "Google",
          "title": "Software Engineer",
          "emails": ["<EMAIL>"],
          "phones": ["+33600110054"],
          "custom_field_contact":1000
        }
    """
    Then the response status should be 400

  Scenario: Create Contact with custom-field which dont exist in the organization
    Given The fixtures files are "tests/features/common/users.fixtures.yml,tests/features/common/organizations.fixtures.yml,tests/features/common/organization-members.fixtures.yml,tests/features/lead/companies.fixtures.yml"
    Given Cognito user email is "<EMAIL>" with password "Azerty123@"
    Given User needs to be authenticated by Access Token
    When I send a POST request to "/ef93c4a3-4ab4-4c0d-9caf-386b491a4d61/leads/contacts" with the data is
    """
        {
          "firstName":"John",
          "lastName":"Fixture",
          "companyId": "eddf2dce-73c4-40ac-9521-3c1d1c260c3c",
          "companyName": "Google",
          "title": "Software Engineer",
          "emails": ["<EMAIL>"],
          "phones": ["+33600110054"],
          "custom_field_contact2":"test"
        }
    """
    Then the response status should be 400
