import type { Company, Contact, ExternalContact, ProviderStatus } from '@getheroes/shared'
import { EnrichmentType } from '@getheroes/shared'
import { getMostRecentDate, ONE_HOUR_IN_MILLISECONDS } from '../date/date'

type WaterfallPositionUtilsProps = {
  providerList: Array<{
    name: string
    status: ProviderStatus
  }>
}

export const getEnrichmentTypeFromSelectedFields = ({
  email,
  phone,
}: {
  email?: boolean
  phone?: boolean
}): EnrichmentType | undefined => {
  if (email && phone) {
    return EnrichmentType.ADVANCED
  }

  if (email) {
    return EnrichmentType.EMAIL
  }

  if (phone) {
    return EnrichmentType.PHONE
  }

  return undefined
}

export const waterfallPositionUtils = ({
  providerList,
}: WaterfallPositionUtilsProps) => {
  const providersNotIdle = providerList.filter(
    ({ status }) => status !== 'idle'
  )
  const currentProviderPosition = providersNotIdle.length

  const amountOfProviders = providerList.length

  const currentProviderName =
    providersNotIdle[currentProviderPosition - 1]?.name

  return {
    currentProviderPosition,
    amountOfProviders,
    currentProviderName,
  }
}

export const getLastEnrichmentInTheLastDuration = ({
  lead,
  enrichmentType,
  durationMs = ONE_HOUR_IN_MILLISECONDS,
}: {
  lead: Contact | ExternalContact | Company
  enrichmentType?: EnrichmentType
  durationMs?: number
}): boolean => {
  const mostRecentEnrichment = getLeadMostRecentEnrichment(lead, enrichmentType)
  if (!mostRecentEnrichment) {
    return false
  }
  const timeDifference =
    new Date().getTime() - new Date(mostRecentEnrichment).getTime()

  if (timeDifference < 0) {
    return false
  }
  return timeDifference < durationMs
}

export const getLeadMostRecentEnrichment = (
  lead: Contact | ExternalContact | Company,
  enrichmentType?: EnrichmentType
): string | null => {
  const isContact = 'firstName' in lead

  if (!isContact) {
    return getMostRecentDate([lead.enrichmentDate, lead.enrichmentAdvancedDate])
  }

  if (enrichmentType) {
    switch (enrichmentType) {
      case EnrichmentType.GENERAL:
        return null
      case EnrichmentType.ADVANCED:
        return getMostRecentDate([lead.enrichmentAdvancedDate])
      case EnrichmentType.PHONE:
        return getMostRecentDate([
          lead.enrichmentAdvancedDate,
          lead.enrichmentPhoneDate,
        ])
      case EnrichmentType.EMAIL:
        return getMostRecentDate([
          lead.enrichmentEmailDate,
          lead.enrichmentAdvancedDate,
        ])
    }
  } else {
    switch ((lead as Contact).enrichmentType) {
      case EnrichmentType.GENERAL:
        return null
      case EnrichmentType.ADVANCED:
        return getMostRecentDate([lead.enrichmentAdvancedDate])
      case EnrichmentType.PHONE:
        return getMostRecentDate([lead.enrichmentPhoneDate])
      case EnrichmentType.EMAIL:
        return getMostRecentDate([
          lead.enrichmentAdvancedDate,
          lead.enrichmentEmailDate,
        ])
    }

    return null
  }
}
