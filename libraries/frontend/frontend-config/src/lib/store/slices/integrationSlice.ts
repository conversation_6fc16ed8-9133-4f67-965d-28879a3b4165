import { createSlice } from '@reduxjs/toolkit'

import { integrationApi } from '@internals/features/settings/components/integration/api/integrationApi'
import { IntegrationServiceName } from '@internals/features/settings/types/IntegrationType'

export type PhoneIntegration = {
  provider: IntegrationServiceName
  connected: boolean
}

const PhoneProviders = [
  IntegrationServiceName.AIRCALL,
  IntegrationServiceName.RINGOVER,
]

type IntegrationState = {
  voip: PhoneIntegration | undefined
}

const initialState: IntegrationState = {
  voip: undefined,
}

export const integrationSlice = createSlice({
  name: 'integration',
  initialState: initialState,
  reducers: {},
  extraReducers: builder => {
    builder.addMatcher(
      integrationApi.endpoints.getIntegrations.matchFulfilled,
      (state, { payload }) => {
        const phoneIntegration = payload.find(
          ({ name, isConnected }) =>
            PhoneProviders.includes(name as IntegrationServiceName) &&
            isConnected
        )

        if (phoneIntegration) {
          state.voip = {
            connected: phoneIntegration.isConnected,
            provider: phoneIntegration.name as IntegrationServiceName,
          }
        } else {
          state.voip = undefined
        }
      }
    )
  },
})

export const integration = integrationSlice.reducer

export const selectVoipIntegration = (state: {
  integration: IntegrationState
}) => state.integration.voip
