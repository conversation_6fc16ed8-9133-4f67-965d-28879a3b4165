import { createEntityAdapter, createSlice } from '@reduxjs/toolkit'

import type { Contact } from '@getheroes/shared'
import type { RootState } from '@internals/store/store'

const contactInCompanyListAdapter = createEntityAdapter<Contact>()

const initialState = contactInCompanyListAdapter.getInitialState()

export const contactInCompanyListSlice = createSlice({
  name: 'contactInCompanyList',
  initialState,
  reducers: {
    removeAllContactInCompanyList: contactInCompanyListAdapter.removeAll,
    // https://redux-toolkit.js.org/api/createEntityAdapter#crud-functions
    upersetManyContactInCompanyList: contactInCompanyListAdapter.upsertMany,
  },
})

export const {
  removeAllContactInCompanyList,
  upersetManyContactInCompanyList,
} = contactInCompanyListSlice.actions

export const contactInCompanyList = contactInCompanyListSlice.reducer

export const { selectAll: selectAllContactInCompanyList } =
  contactInCompanyListAdapter.getSelectors<RootState>(
    state => state.contactInCompanyList
  )
