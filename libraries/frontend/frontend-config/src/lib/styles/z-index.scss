:root {
  /* Z-index values */
  --base: 0;
  --above: 1;
  /* use this for all values above the base */
  --below: -1;
  /* and this for all values below the base */

  /* Page Layout */
  --zLayoutHeaderOverlay: var(--base);
  --zLayoutSidePanel: calc(var(--above));
  --zLayoutNavigation: calc(var(--above) + var(--base));
  --zLayoutAboveBase: calc(var(--above) + var(--base));
  --zLayoutHeader: calc(var(--above) + var(--zLayoutNavigation));
  --zLayoutFooter: calc(var(--above) + var(--base));
  --zLayoutLoader: calc(var(--above) + var(--zLayoutHeader));

  /* Form field */
  --zFormFieldForm: var(--base);
  --zFormFieldLabel: calc(var(--above) + var(--zFormFieldForm));
  --zFormFieldOpenSelect: calc(var(--above) + var(--zFormFieldLabel));

  /* Select */
  --zSelect: var(--base);
  --zSelectMenu: calc(var(--above) + var(--zDrawer));
  --zSelectValue: calc(var(--above) + var(--zSelectMenu));

  /* DropdownMenu */
  --zDropdown: var(--base);
  --zDropdownMenu: calc(var(--above) + var(--base));
  --zDropdownValue: calc(var(--above) + var(--zDropdownMenu));

  /* Drawer  */
  --zDrawer: calc(var(--zLayoutSidePanel) + var(--above));

  /* Modal */
  --zModal: calc(var(--zDrawer) + var(--above));

  /* Overlay Paper FlatFile */
  --zOverlayFlatFile: calc(var(--zDrawer) + var(--above));

  /* DatePicker Popper */
  --zDatePickerPopper: calc(var(--above) + var(--zModal));
  --zTypeheadMenuPopper: calc(var(--above) + var(--zDrawer));

  /* Table */
  --zIndexTableHoverActions: calc(var(--above) + var(--base));
  --zIndexTableStickyCell: calc(var(--above) + var(--base));
  --zIndexTableHead: calc(var(--above) + var(--zIndexTableStickyCell));

  /* Tooltip */
  --zTooltip: calc(var(--above) + var(--zDrawer));
  --zPopover: calc(var(--above) + var(--zDrawer));
  --zPopoverAboveDrawer: calc(var(--above) + var(--zDrawer));
  /* Floating Action Bar */
  --zFloatingActionBar: calc(var(--below) + var(--zModal));

  /* Toaster */
  --zToaster: calc(var(--above) + var(--zDrawer));

  /* Preview Button LEXICAL */
  --zPreviewButtonLexical: calc(var(--below) + var(--zDrawer));

  /* Step-builder Timeline step */
  --zStepBuilderTimelineStep: calc(var(--above) + var(--base));
}
