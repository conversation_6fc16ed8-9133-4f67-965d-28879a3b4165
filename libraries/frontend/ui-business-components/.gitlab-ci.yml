.ui-business-changes: &ui-business-changes
  - libraries/frontend/ui-business-components/*
  - libraries/frontend/ui-business-components/**/*
#  - '.gitlab-ci.yml'
#  - 'package.json'
#  - 'tsconfig.base.json'
#  - 'yarn.lock'

.ui_business_rules:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
    when: never
  - if: $CI_PIPELINE_SOURCE == "pipeline"
    when: never
  # Do not trigger if we are creating a tag (from master)
  - if: '$CI_COMMIT_TAG != null'
    when: never
  # Do not trigger when merging on develop, release or master
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "master"'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    changes: *ui-business-changes
    when: always
  # - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_TAG != null'
  #   changes: *ui-business-changes
  #   when: always
  - when: never

.chromatic_ui_business_rules:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
    when: never
  - if: $CI_PIPELINE_SOURCE == "pipeline"
    when: never
  # Do not trigger if we are creating a tag (from master)
  - if: '$CI_COMMIT_TAG != null'
    when: never
  # Do not trigger when merging on develop, release or master
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "master"'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    changes: *ui-business-changes
    when: always
    allow_failure: true
  # - if: '$CI_COMMIT_REF_NAME == "develop"'
  #   changes: *ui-business-changes
  #   when: always
  - when: never

.chromatic_ui_business_develop_rules:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: '$CI_COMMIT_REF_NAME == "develop"'
    changes: *ui-business-changes
    when: always
  - when: never

test:ui-business:
  stage: test
  needs: [ 'install' ]
  image: node:22.15.0-alpine3.21
  cache:
    - !reference [ .cache_app_pull ]
    - !reference [ .cache_coverage ]
  rules:
    - !reference [ .ui_business_rules ]
  script:
    - npx nx test ui-business --watch=false --ci # --skip-nx-cache

test:ui-business-lint:
  stage: test
  needs: [ 'install' ]
  image: node:22.15.0-alpine3.21
  cache:
    - !reference [ .cache_app_pull ]
  rules:
    - !reference [ .chromatic_ui_business_rules ]
  script:
    - npx nx lint ui-business # --skip-nx-cache
  interruptible: true

build:ui-business:
  stage: build
  needs:
    - job: install
    - job: test:ui-business-lint
      optional: true
    - job: test:ui-business
      optional: true
  image: node:22.15.0-alpine3.21
  rules:
    - !reference [ .chromatic_ui_business_develop_rules ]
  cache:
    - !reference [.cache_app_pull]
  script:
    - npx nx build ui-business --skip-nx-cache
  resource_group: zeliq-$ENVIRONMENT

deploy:chromatic-ui-business-develop:
  needs: ['build:ui-business']
  stage: deploy
  image: node:22.15.0-alpine3.21
  rules:
    - !reference [ .chromatic_ui_business_develop_rules ]
  cache:
    - !reference [.cache_app_pull]
  script:
    - apk add git
    - yarn chromatic:ui-business --auto-accept-changes
  resource_group: zeliq-$ENVIRONMENT
  interruptible: true
  variables:
    CHROMATIC_PROJECT_TOKEN: $UI_BUSINESS_CHROMATIC_PROJECT_TOKEN

deploy:chromatic-ui-business-feature:
  needs: ['build:ui-business']
  stage: deploy
  image: node:22.15.0-alpine3.21
  rules:
    - !reference [.chromatic_ui_business_develop_rules]
  cache:
    - !reference [.cache_app_pull]
  script:
    - apk add git
    - yarn chromatic:ui-business --only-changed
  resource_group: zeliq-$ENVIRONMENT
  interruptible: true
  variables:
    CHROMATIC_PROJECT_TOKEN: $UI_BUSINESS_CHROMATIC_PROJECT_TOKEN
