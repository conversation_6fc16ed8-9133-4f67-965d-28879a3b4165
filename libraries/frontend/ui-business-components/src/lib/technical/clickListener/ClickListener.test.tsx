import { render, fireEvent } from '@testing-library/react'
import { describe, expect, it } from '@jest/globals'
import { ClickListener } from './ClickListener'

describe('ClickListener', () => {
  it('should not call onClickOutside if onClickInside is not called first', () => {
    const onClickInside = jest.fn()
    const onClickOutside = jest.fn()

    const { getByTestId } = render(
      <>
        <ClickListener
          isTriggerOnClickOutsideOnlyAfterClickInside={true}
          onClickInside={onClickInside}
          onClickOutside={onClickOutside}
        >
          <div data-testid="inside-element">Inside</div>
        </ClickListener>
        <div data-testid="outside-element">Outside</div>
      </>
    )

    fireEvent.mouseDown(getByTestId('outside-element'))

    expect(onClickInside).not.toHaveBeenCalled()
    expect(onClickOutside).not.toHaveBeenCalled()
  })

  it('should call onClickOutside if onClickInside has been called once', () => {
    const onClickInside = jest.fn()
    const onClickOutside = jest.fn()

    const { getByTestId } = render(
      <>
        <ClickListener
          onClickInside={onClickInside}
          onClickOutside={onClickOutside}
        >
          <div data-testid="inside-element">Inside</div>
        </ClickListener>
        <div data-testid="outside-element">Outside</div>
      </>
    )

    fireEvent.mouseDown(getByTestId('inside-element'))
    expect(onClickInside).toHaveBeenCalled()

    fireEvent.mouseDown(getByTestId('outside-element'))
    expect(onClickOutside).toHaveBeenCalled()
  })

  it('should cleans up event listeners on unmount', () => {
    const onClickInside = jest.fn()
    const onClickOutside = jest.fn()

    const { unmount, getByTestId } = render(
      <ClickListener
        onClickInside={onClickInside}
        onClickOutside={onClickOutside}
      >
        <div data-testid="inside-element">Inside</div>
      </ClickListener>
    )

    unmount()

    fireEvent.mouseDown(document.body)

    expect(onClickOutside).not.toHaveBeenCalled()
    expect(onClickInside).not.toHaveBeenCalled()
  })
})
