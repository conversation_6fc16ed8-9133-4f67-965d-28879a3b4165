import type { Meta, StoryObj } from '@storybook/react'

import { MicrosoftButton } from './MicrosoftButton'

export default {
  title: 'components/business/auth/SSO/MicrosoftButton',
  tags: ['Auth', 'SSO-buttons', 'MicrosoftButton'],
  component: MicrosoftButton,
  argTypes: {
    onClick: { action: 'clicked' },
  },
  args: {
    children: 'Microsoft Button',
  },
} as Meta

type Story = StoryObj<typeof MicrosoftButton>

export const Default: Story = {
  args: {
    onClick: () => alert('Clicked!'),
    dataTestId: 'navigation-button-microsoft-default',
  },
}

export const Disabled: Story = {
  args: {
    isDisabled: true,
    dataTestId: 'navigation-button-microsoft-disabled',
  },
}
