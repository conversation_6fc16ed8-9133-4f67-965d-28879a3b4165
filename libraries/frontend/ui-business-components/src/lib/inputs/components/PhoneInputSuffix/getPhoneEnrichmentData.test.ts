// import { describe, expect, it } from 'vitest'
import { getPhoneEnrichmentData } from './getPhoneEnrichmentData'
import type { Contact } from '@getheroes/shared'

describe('getPhoneEnrichmentData', () => {
  it('should return true for isFromEnrichment if the phone is in the enrichmentPhones list', () => {
    const contact = {
      enrichmentResult: {
        phones: [' 123456789 ', '987654321'],
      },
      enrichmentPhoneDate: '2023-10-01',
      enrichmentAdvancedDate: '2023-09-15',
      enrichmentDate: '2023-08-20',
    } as Contact
    const phone = '123456789'

    const result = getPhoneEnrichmentData(contact, phone)

    expect(result.isFromEnrichment).toBe(true)
    expect(result.enrichmentDate).toEqual(new Date('2023-10-01'))
  })

  it('should return false for isFromEnrichment if the phone is not in the enrichmentPhones list', () => {
    const contact = {
      enrichmentResult: {
        phones: ['555555555', '999999999'],
      },
      enrichmentPhoneDate: '2023-10-01',
      enrichmentAdvancedDate: '2023-09-15',
      enrichmentDate: '2023-08-20',
    } as Contact
    const phone = '123456789'

    const result = getPhoneEnrichmentData(contact, phone)

    expect(result.isFromEnrichment).toBe(false)
    expect(result.enrichmentDate).toEqual(new Date('2023-10-01'))
  })

  it('should return null for enrichmentDate if no date is specified', () => {
    const contact = {
      enrichmentResult: {
        phones: ['123456789'],
      },
      enrichmentPhoneDate: '',
      enrichmentAdvancedDate: '',
      enrichmentDate: '',
    } as Contact
    const phone = '123456789'

    const result = getPhoneEnrichmentData(contact, phone)

    expect(result.isFromEnrichment).toBe(true)
    expect(result.enrichmentDate).toBeNull()
  })

  it('should trim phone numbers before comparison', () => {
    const contact = {
      enrichmentResult: {
        phones: [' 123456789 '],
      },
      enrichmentPhoneDate: '2023-10-01',
      enrichmentAdvancedDate: '',
      enrichmentDate: '',
    } as Contact
    const phone = '123456789'

    const result = getPhoneEnrichmentData(contact, phone)

    expect(result.isFromEnrichment).toBe(true)
    expect(result.enrichmentDate).toEqual(new Date('2023-10-01'))
  })

  it('should use the most recent date when multiple dates are available', () => {
    const contact = {
      enrichmentResult: {
        phones: ['123456789'],
      },
      enrichmentPhoneDate: '2023-10-01',
      enrichmentAdvancedDate: '2023-11-01',
      enrichmentDate: '2023-09-01',
    } as Contact
    const phone = '123456789'

    const result = getPhoneEnrichmentData(contact, phone)

    expect(result.isFromEnrichment).toBe(true)
    expect(result.enrichmentDate).toEqual(new Date('2023-11-01'))
  })
})
