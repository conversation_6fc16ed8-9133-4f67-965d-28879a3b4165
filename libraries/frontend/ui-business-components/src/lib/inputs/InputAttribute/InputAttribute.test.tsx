import React from 'react'
import { render, screen } from '@testing-library/react'
import { useForm } from 'react-hook-form'
import { InputAttribute } from './InputAttribute'
import { isPhoneNumberValid } from '@getheroes/frontend/utils'
import { RegexClass } from '@getheroes/shared'

const Wrapper = ({ field }: any) => {
  const { control } = useForm()
  return <InputAttribute field={field} control={control} />
}

describe('InputAttribute', () => {
  it('renders text input correctly', () => {
    const field = {
      type: 'text',
      name: 'testText',
      rules: {
        validate: (value: string) => {
          if (value.length < 10) {
            return true
          }
          return 'Too long'
        },
      },
      inputProps: {
        suffixContent: 'suffix',
      },
    }

    render(<Wrapper field={field} />)
    const input: HTMLInputElement = screen.getByRole('textbox')

    expect(input).toBeInTheDocument()
    expect(input?.type).toBe('text')
  })

  it('renders phone input correctly', () => {
    const field = {
      type: 'phone',
      name: 'testPhone',
      rules: {
        validate: (value: string) => {
          if (isPhoneNumberValid(value)) {
            return true
          }
          return 'Invalid phone number'
        },
      },
      inputProps: {
        suffixContent: 'suffix',
      },
    }

    render(<Wrapper field={field} />)
    const input = screen.getByRole('textbox')
    expect(input).toHaveAttribute('type', 'tel')
  })

  it('renders email input correctly', () => {
    const field = {
      type: 'email',
      name: 'testEmail',
      rules: {
        validate: (value: string) => {
          if (RegexClass.EMAIL_VALIDATION.test(value)) {
            return true
          }
          return 'Invalid email address'
        },
      },
      inputProps: {
        suffixContent: 'suffix',
      },
    }

    render(<Wrapper field={field} />)
    const input: HTMLInputElement = screen.getByRole('textbox')
    expect(input.type).toBe('email')
  })
})
