export type TextColor =
  | 'base-default'
  | 'base-subtle'
  | 'base-inverse'
  | 'base-label'
  | 'base-placeholder'
  | 'base-disabled'
  | 'base-error'
  | 'base-highlight'
  | 'decorative-green'
  | 'decorative-orange'
  | 'decorative-red'
  | 'decorative-blue'
  | 'decorative-blue-strong'
  | 'decorative-brand'
  | 'decorative-brand-strong'
  | 'decorative-pink'
  | 'decorative-pink-strong'
  | 'decorative-beige'
  | 'decorative-yellow'
  | 'decorative-light-pink'
  | 'basic-black'
  | 'basic-white'
  | 'error-accent'

export type ActionColor =
  | 'action-primary-default'
  | 'action-primary-hover'
  | 'action-primary-active'
  | 'action-secondary-default'
  | 'action-secondary-hover'
  | 'action-secondary-active'
  | 'action-tertiary-default'
  | 'action-tertiary-hover'
  | 'action-tertiary-hover-border'
  | 'action-tertiary-active'
  | 'action-tertiary-active-border'
  | 'action-tertiary-outlined-default'
  | 'action-tertiary-outlined-default-border'
  | 'action-tertiary-outlined-hover'
  | 'action-tertiary-outlined-hover-border'
  | 'action-tertiary-outlined-active'
  | 'action-tertiary-outlined-active-border'
  | 'action-destructive-default'
  | 'action-destructive-hover'
  | 'action-destructive-active'
  | 'action-disabled'

export type BorderColor =
  | 'base-default'
  | 'base-subtle'
  | 'base-divider'
  | 'base-strong'
  | 'base-accent-dark'
  | 'error-default'
  | 'error-strong'
  | 'error-accent'
  // Todo check after design update tokens
  | 'decorative-pink-default'
  | 'decorative-pink-strong'
  | 'decorative-brand-default'
  | 'decorative-brand-strong'
  | 'decorative-blue'
  | 'decorative-orange'
  | 'decorative-red'
  | 'decorative-beige'
  | 'decorative-yellow'
  | 'decorative-green'

export type BackgroundColor =
  | 'base-default'
  | 'base-card'
  | 'base-primary'
  | 'base-secondary'
  | 'base-inverse'
  | 'base-info'
  | 'base-success'
  | 'base-warning'
  | 'base-error'
  | 'base-subtle'
  | 'overlay'
  | 'decorative-conversion-ai'
  | 'decorative-conversion-ai-hover'
  | 'decorative-conversion-data'
  | 'decorative-conversion-data-hover'
  | 'decorative-conversion-plg'
  | 'decorative-conversion-plg-hover'
  | 'decorative-pink-default'
  | 'decorative-pink-strong'
  | 'decorative-brand-default'
  | 'decorative-brand-strong'
  | 'decorative-blue-default'
  | 'decorative-blue-strong'
  | 'decorative-orange-default'
  | 'decorative-orange-strong'
  | 'decorative-red-default'
  | 'decorative-red-strong'
  | 'decorative-beige-default'
  | 'decorative-yellow-default'
  | 'decorative-yellow-strong'
  | 'decorative-green-default'
  | 'decorative-green-strong'

export type GlobalColor =
  | 'brand-100'
  | 'brand-200'
  | 'brand-300'
  | 'brand-400'
  | 'brand-500'
  | 'brand-600'
  | 'brand-700'
  | 'brand-800'
  | 'brand-900'
  | 'grey-100'
  | 'grey-200'
  | 'grey-300'
  | 'grey-400'
  | 'grey-500'
  | 'grey-600'
  | 'grey-700'
  | 'grey-800'
  | 'grey-900'
  | 'grey-1000'
  | 'beige-100'
  | 'beige-200'
  | 'beige-300'
  | 'beige-400'
  | 'beige-500'
  | 'beige-600'
  | 'beige-700'
  | 'beige-800'
  | 'beige-900'
  | 'green-100'
  | 'green-300'
  | 'green-500'
  | 'green-600'
  | 'green-700'
  | 'green-900'
  | 'red-100'
  | 'red-200'
  | 'red-300'
  | 'red-500'
  | 'red-700'
  | 'red-900'
  | 'orange-100'
  | 'orange-300'
  | 'orange-500'
  | 'orange-600'
  | 'orange-700'
  | 'orange-900'
  | 'yellow-100'
  | 'yellow-300'
  | 'yellow-500'
  | 'yellow-700'
  | 'yellow-800'
  | 'yellow-900'
  | 'blue-100'
  | 'blue-300'
  | 'blue-500'
  | 'blue-700'
  | 'blue-900'
  | 'pink-100'
  | 'pink-300'
  | 'pink-500'
  | 'pink-700'
  | 'pink-900'
  | 'ai-100'
  | 'ai-200'
  | 'ai-300'
  | 'ai-400'
  | 'ai-500'
  | 'ai-600'
  | 'ai-700'
  | 'data-100'
  | 'data-200'
  | 'data-300'
  | 'data-400'
  | 'data-500'
  | 'data-600'
  | 'data-700'
  | 'conversion-100'
  | 'conversion-200'
  | 'conversion-300'
  | 'conversion-400'
  | 'conversion-500'
  | 'conversion-600'
  | 'conversion-700'
  | 'basic-white'
  | 'basic-black'
