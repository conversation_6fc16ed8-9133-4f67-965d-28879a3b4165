import clsx from 'clsx'
import get from 'lodash/get'
import type { PropsWithChildren } from 'react'
import { useMemo } from 'react'

import type { ActionColor, TextColor } from '../colors/colors.types'
import { colors } from '../colors/colors'
import { splitString } from '../../utils/splitString'

export type TypographyVariant =
  | 'heading'
  | 'body'
  | 'label'
  | 'labelbrand'
  | 'link'
export type TypographySize = 'xxs' | 'xs' | 's' | 'm' | 'l' | 'xl'
export type TypographyWeight = 'regular' | 'medium' | 'semi-bold' | 'bold'
export type Align = 'left' | 'center' | 'right'
export type TypographyWhitespace =
  | 'normal'
  | 'nowrap'
  | 'pre'
  | 'pre-line'
  | 'pre-wrap'
  | 'break-spaces'

interface TypographyProps {
  variant?: TypographyVariant
  size?: TypographySize
  weight?: TypographyWeight
  color?: TextColor | ActionColor
  align?: Align
  linethough?: boolean
  isTruncate?: boolean
  lineClamp?: 'none' | 1 | 2 | 3 | 4 | 5 | 6
  whitespace?: TypographyWhitespace
  underlined?: boolean
  isCapitalized?: boolean
  backgroundColor?: TextColor | ActionColor | 'transparent'
}

const validSizes: Record<TypographyVariant, TypographySize[]> = {
  heading: ['xs', 's', 'm', 'l', 'xl'],
  body: ['xxs', 'xs', 's', 'm'],
  label: ['xs', 's', 'm', 'l'],
  labelbrand: ['m'],
  link: ['m', 'l'],
}

/**
 * Typography component used in the design system for rendering text with consistent styling.
 *
 * @throws {Error} If the size is not valid for the given variant.
 *
 * @example
 * <Typography variant="heading" size="l" weight="bold" color="text-primary">Hello World</Typography>
 *
 * @figma https://www.figma.com/design/DYk8762y6eOKo9U39jjFPx/branch/4wNyRULYd63mXqvPqxiNt4/_Zeliq-Design-System-Product?node-id=365-1781
 */
export const Typography = ({
  children,
  variant = 'body',
  size = 'm',
  weight,
  color = 'base-default',
  align = 'left',
  linethough,
  lineClamp,
  whitespace,
  isTruncate,
  underlined = false,
  isCapitalized = false,
  backgroundColor = 'transparent',
}: PropsWithChildren<TypographyProps>) => {
  /* Errors */

  if (!validSizes[variant].includes(size)) {
    throw new Error(
      `Invalid size '${size}' for variant '${variant}'. Expected one of ${validSizes[variant].join(', ')}.`
    )
  }

  /* Vars */

  const fontFamily = variant === 'heading' ? 'font-pp-telegraf' : 'font-inter'
  const textAlign = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  }[align]

  /* Memos */

  const fontSize = useMemo(() => {
    if (variant === 'heading') {
      // @ts-expect-error - Checked in throw error above
      return {
        xs: 'text-heading-xs leading-5',
        s: 'text-heading-s leading-6',
        m: 'text-heading-m leading-8',
        l: 'text-heading-l leading-9',
        xl: 'text-heading-xl leading-10',
      }[size]
    }

    if (variant === 'label') {
      // @ts-expect-error - Checked in throw error above
      return {
        xs: 'text-label-xs leading-none',
        s: 'text-label-s leading-none',
        m: 'text-label-m leading-none',
        l: 'text-label-l leading-none',
      }[size]
    }

    if (variant === 'labelbrand') {
      return 'text-labelbrand leading-none'
    }

    if (variant === 'link' && (size === 'm' || size === 'l')) {
      return {
        m: 'text-link leading-none',
        l: 'text-body-l leading-none',
      }[size]
    }

    // body
    // @ts-expect-error - Checked in throw error above
    return {
      xxs: 'text-body-xxs leading-3',
      xs: 'text-body-xs leading-4',
      s: 'text-body-s leading-5',
      m: 'text-body-m leading-6',
      l: 'text-body-l leading-7',
    }[size]
  }, [size, variant])

  const fontWeight = useMemo(() => {
    if ((variant === 'heading' || variant === 'labelbrand') && !weight) {
      return 'font-bold'
    }

    if (variant === 'link' && !weight) {
      return 'font-semibold'
    }

    return {
      regular: 'font-normal',
      medium: 'font-medium',
      'semi-bold': 'font-semibold',
      bold: 'font-bold',
    }[weight ?? 'regular']
  }, [variant, weight])

  return (
    <span
      className={clsx(fontFamily, fontSize, fontWeight, textAlign, {
        'line-through': linethough,
        uppercase: variant === 'labelbrand',
        'cursor-pointer': variant === 'link',
        'underline underline-offset-2': underlined,
        'block truncate text-ellipsis': isTruncate,
        [`whitespace-${whitespace}`]: whitespace && !isTruncate,
        'line-clamp-1': lineClamp === 1 && !isTruncate,
        'line-clamp-2': lineClamp === 2 && !isTruncate,
        'line-clamp-3': lineClamp === 3 && !isTruncate,
        'line-clamp-4': lineClamp === 4 && !isTruncate,
        'line-clamp-5': lineClamp === 5 && !isTruncate,
        'line-clamp-6': lineClamp === 6 && !isTruncate,
        capitalize: isCapitalized,
      })}
      style={{
        color: get(colors.text, splitString(color, '-')),
        backgroundColor: get(colors.text, splitString(backgroundColor, '-')),
      }}
    >
      {children}
    </span>
  )
}
