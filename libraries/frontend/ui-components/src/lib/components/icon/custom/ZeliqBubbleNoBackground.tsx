import type { SVGProps } from 'react'

export const <PERSON><PERSON><PERSON>B<PERSON>bleNoBackground = ({
  width = 16,
  height = 16,
  color,
}: SVGProps<SVGElement>) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 16 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.01107 18.2653C12.3071 18.2653 15.7897 14.8253 15.7897 10.5819C15.7897 6.33844 12.3071 2.89844 8.01107 2.89844C3.71504 2.89844 0.232422 6.33844 0.232422 10.5819C0.232422 14.8253 3.71504 18.2653 8.01107 18.2653Z"
        fill={color}
      />
      <path
        d="M14.1584 1.10763C14.5288 1.00839 14.4775 1.15627 13.882 1.89705C13.4612 2.42559 13.2273 2.58663 13.1263 2.4259C13.0614 2.32258 13.9771 1.15623 14.1584 1.10763Z"
        fill={color}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.1784 1.18053C14.1785 1.1805 14.1785 1.18052 14.1783 1.1806C14.1772 1.18106 14.171 1.18366 14.1587 1.19195C14.1455 1.20089 14.1289 1.21386 14.1091 1.23108C14.0695 1.26546 14.0211 1.31337 13.9668 1.37135C13.8584 1.4871 13.7304 1.6389 13.6095 1.79302C13.4887 1.94723 13.3762 2.10216 13.2984 2.22404C13.2593 2.28531 13.2303 2.33615 13.2133 2.37336C13.2078 2.38519 13.2042 2.3945 13.2018 2.40148C13.2104 2.40987 13.2184 2.41135 13.2271 2.4112C13.2475 2.41084 13.2844 2.39939 13.3421 2.35918C13.456 2.27971 13.6139 2.11501 13.824 1.85106L13.8243 1.85077L13.8243 1.85078C13.9729 1.66585 14.0867 1.51912 14.1681 1.4045C14.2509 1.28795 14.2958 1.21078 14.3124 1.16325C14.3134 1.16042 14.3142 1.15786 14.3149 1.15553C14.3078 1.1556 14.2986 1.15615 14.287 1.15757C14.259 1.16098 14.2231 1.16855 14.1784 1.18053ZM13.1982 2.41566C13.1982 2.41571 13.1982 2.41503 13.1985 2.41348C13.1984 2.41484 13.1983 2.41562 13.1982 2.41566ZM13.941 1.94439C14.0901 1.759 14.206 1.60951 14.2901 1.49119C14.3728 1.37477 14.4293 1.28228 14.4537 1.21265C14.4656 1.17858 14.4742 1.13821 14.4635 1.09933C14.4508 1.0529 14.416 1.02557 14.3775 1.01393C14.3436 1.00368 14.3052 1.00458 14.2689 1.00901C14.2311 1.01362 14.1876 1.02314 14.1397 1.03597L14.1397 1.03597C14.1163 1.04224 14.0939 1.05516 14.075 1.0679C14.055 1.08143 14.0333 1.09861 14.0109 1.11813C13.9659 1.15724 13.9136 1.20924 13.8575 1.26904C13.7453 1.38888 13.6144 1.54423 13.4918 1.70069C13.3692 1.85706 13.2536 2.01613 13.1723 2.14352C13.1318 2.20688 13.0985 2.2645 13.0772 2.31104C13.0667 2.33402 13.0577 2.35729 13.0528 2.37887C13.0491 2.39534 13.0424 2.43265 13.0636 2.46633C13.1005 2.52506 13.1568 2.56212 13.2298 2.56083C13.2948 2.55968 13.3616 2.52797 13.4277 2.48195C13.561 2.38895 13.7305 2.20891 13.941 1.94439Z"
        fill={color}
      />
      <path
        d="M12.1293 2.05158C12.2038 1.99262 12.4537 0.450192 12.4128 0.294096C12.3279 -0.0359848 12.1361 0.0139367 12.0627 0.387685C11.8569 1.40118 11.8922 2.23915 12.1293 2.05158Z"
        fill={color}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.3878 0.0686465C12.4296 0.116905 12.4613 0.188216 12.4839 0.276035C12.4888 0.294806 12.4902 0.317351 12.4907 0.338123C12.4911 0.360526 12.4904 0.387118 12.4888 0.416724C12.4856 0.476026 12.4788 0.550931 12.4693 0.635342C12.4503 0.804363 12.4203 1.01489 12.3869 1.22192C12.3534 1.42891 12.3162 1.63345 12.2826 1.79043C12.2658 1.86874 12.2497 1.9364 12.2351 1.987C12.2279 2.01212 12.2205 2.03462 12.2132 2.05269C12.2096 2.06168 12.2053 2.07102 12.2004 2.07962C12.1964 2.0867 12.1881 2.10009 12.1744 2.11098C12.1351 2.14207 12.0834 2.1647 12.0272 2.14792C11.9743 2.13214 11.9417 2.08821 11.9214 2.04697C11.8806 1.96385 11.8602 1.83131 11.8538 1.67379C11.8408 1.35322 11.8844 0.884125 11.988 0.373745C12.0073 0.275403 12.0352 0.193932 12.07 0.132688C12.1036 0.0735905 12.1505 0.021868 12.2129 0.005331C12.2828 -0.0131783 12.3445 0.0185789 12.3878 0.0686465ZM12.2001 0.206674C12.1758 0.249541 12.1522 0.31438 12.1348 0.402801L12.1347 0.40329L12.1347 0.40329C12.0326 0.906312 11.9909 1.36285 12.0033 1.6677C12.0097 1.82291 12.0297 1.92803 12.0557 1.98095C12.0614 1.99249 12.066 1.99881 12.0689 2.00211C12.0701 2.00149 12.0715 2.00068 12.0732 1.99961C12.0736 1.99867 12.074 1.9976 12.0745 1.99641C12.079 1.98533 12.0847 1.9685 12.0913 1.9455C12.1045 1.89985 12.1198 1.83611 12.1363 1.75909C12.1692 1.60541 12.2059 1.40353 12.2391 1.19804C12.2723 0.99257 12.3019 0.78454 12.3206 0.618612C12.3299 0.535548 12.3364 0.46388 12.3394 0.408723C12.3408 0.381099 12.3414 0.35849 12.341 0.34112C12.3409 0.332456 12.3405 0.325683 12.34 0.320612C12.3395 0.315727 12.3391 0.313711 12.3391 0.31371L12.339 0.313458L12.339 0.313458C12.3191 0.236293 12.295 0.190109 12.2747 0.166594C12.2649 0.155289 12.2579 0.151499 12.2551 0.150364C12.2531 0.149581 12.2525 0.149671 12.2512 0.150002C12.2459 0.151404 12.2258 0.16161 12.2001 0.206674ZM12.0705 2.00538C12.0701 2.00606 12.0701 2.0059 12.0706 2.00513C12.0705 2.00523 12.0705 2.00532 12.0705 2.00538ZM12.0641 2.00396C12.0641 2.00393 12.0644 2.00384 12.065 2.0038C12.0644 2.00398 12.0641 2.004 12.0641 2.00396Z"
        fill={color}
      />
      <path
        d="M13.9288 3.16875C13.9208 3.33105 13.9393 3.33557 14.7275 3.30993C15.1501 3.29944 15.4482 3.25105 15.611 3.17145C15.8349 3.06382 15.7873 2.88796 15.638 2.8813C14.3527 2.81708 13.9424 2.88842 13.9288 3.16875Z"
        fill={color}
      />
      <path
        d="M13.8546 3.16377L14.2946 2.8207L13.9767 2.93522L13.8546 3.16377Z"
        fill={color}
      />
      <path
        d="M14.2946 2.8207C14.1578 2.84386 14.0512 2.87962 13.9767 2.93522L14.2946 2.8207Z"
        fill={color}
      />
      <path
        d="M13.9767 2.93522C13.8978 2.99408 13.8591 3.07205 13.8546 3.16377L13.9767 2.93522Z"
        fill={color}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.6422 2.80522C15.0003 2.77315 14.5687 2.77431 14.2946 2.8207L13.8546 3.16377C13.8536 3.18383 13.8528 3.20618 13.8545 3.22728C13.8563 3.24886 13.8611 3.2741 13.8749 3.29841C13.9048 3.35118 13.9597 3.37191 14.0171 3.38214C14.0756 3.39258 14.1577 3.39564 14.2701 3.395C14.3837 3.39435 14.5336 3.38979 14.7302 3.38339C15.1532 3.37288 15.4656 3.3247 15.6443 3.23744C15.7688 3.17753 15.8443 3.08518 15.8398 2.98387C15.8351 2.87794 15.7457 2.80992 15.6422 2.80522ZM14.0662 3.05518C14.0242 3.08653 14.0065 3.12261 14.0041 3.17104C14.0031 3.19157 14.0028 3.20486 14.0037 3.21497C14.004 3.21927 14.0045 3.22196 14.0049 3.22349C14.0086 3.22574 14.0189 3.23046 14.0433 3.23481C14.087 3.24259 14.1567 3.24599 14.2692 3.24534C14.3805 3.2447 14.5283 3.24022 14.7257 3.2338L14.7262 3.23378C15.1481 3.22331 15.4318 3.17475 15.5788 3.10288L15.5792 3.10267C15.6785 3.05495 15.691 3.00561 15.6903 2.99048C15.6898 2.97998 15.681 2.95676 15.6353 2.95472L15.6349 2.9547C14.9916 2.92255 14.5754 2.92496 14.3196 2.96826C14.1915 2.98995 14.1126 3.02058 14.0662 3.05518ZM14.0031 3.22215C14.0031 3.22214 14.0034 3.22233 14.0038 3.22278C14.0033 3.22239 14.0031 3.22217 14.0031 3.22215Z"
        fill={color}
      />
      <path
        d="M7.30686 10.8317C7.39088 10.7483 7.4683 10.6634 7.54009 10.5796C7.662 10.4378 7.76807 10.2987 7.86279 10.1745C7.88003 10.1519 7.89689 10.1298 7.91341 10.1082C8.12606 9.82659 8.28718 9.64632 8.38177 9.67902L8.38229 9.67892C8.42869 9.69465 8.45244 9.76062 8.45262 9.86797C8.45272 9.97474 8.42986 10.1233 8.37483 10.3021C8.31972 10.4806 8.23162 10.691 8.09324 10.911C8.02401 11.0209 7.94196 11.1332 7.84629 11.2429C7.78457 11.3126 7.71994 11.3771 7.64162 11.432C7.55327 11.4915 7.44891 11.5468 7.31168 11.575C7.24429 11.5903 7.16666 11.5943 7.09449 11.5894C7.02151 11.5828 6.94999 11.5688 6.88387 11.5422C6.60775 11.4369 6.43239 11.1957 6.32074 10.9556C6.26215 10.8269 6.22215 10.7022 6.18646 10.5777C6.1184 10.3301 6.08522 10.1958 6.05526 9.95482C6.01917 9.66452 6.15521 9.45526 6.36268 9.44068C6.66798 9.41913 6.70977 9.66062 6.72656 9.77588C6.75946 9.99553 6.8025 10.2124 6.86337 10.4151C6.89421 10.5156 6.92845 10.6143 6.97076 10.7005C6.99142 10.7439 7.01372 10.7842 7.0369 10.8201C7.04343 10.8308 7.05022 10.8398 7.05692 10.8488C7.06195 10.8555 7.06694 10.8621 7.07172 10.8694C7.16655 10.9639 7.22676 10.9138 7.30686 10.8317Z"
        fill="#00402F"
      />
      <path
        d="M3.12524 10.8537C3.1607 10.8886 3.20098 10.8871 3.24001 10.8614C3.26785 10.8416 3.28101 10.8297 3.32599 10.7788C3.40225 10.6952 3.47254 10.6101 3.53774 10.5261C3.64846 10.384 3.74482 10.2446 3.83088 10.1201C3.84654 10.0975 3.86186 10.0753 3.87686 10.0538C4.07007 9.77159 4.21637 9.59093 4.3019 9.62334H4.30237C4.34432 9.63888 4.36564 9.70485 4.36553 9.8121C4.36533 9.91897 4.34426 10.0675 4.29398 10.2465C4.24363 10.4251 4.16334 10.6357 4.03752 10.8561C3.97457 10.966 3.90001 11.0786 3.81313 11.1886C3.60822 11.4439 3.26996 11.636 2.94128 11.4903C2.68302 11.3822 2.51516 11.1565 2.40965 10.9045C2.27509 10.5764 2.22757 10.2282 2.18908 9.87817C2.1405 9.26159 2.60761 9.3413 2.69404 9.65747C2.79264 10.018 2.84398 10.5979 3.12524 10.8537Z"
        fill="#00402F"
      />
      <path
        d="M6.45524 14.3139C9.47345 13.4351 11.3163 11.6388 12.225 10.4981C12.5184 10.0372 12.9983 10.3531 12.7567 10.7785C12.566 11.1141 11.8053 11.9435 11.123 12.5779C10.4407 13.2123 8.29379 14.5853 6.60652 14.883C6.14463 14.9645 6.03946 14.435 6.45524 14.3139Z"
        fill="#00402F"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.23786 9.8746C2.27631 10.2243 2.32335 10.5658 2.45484 10.8866C2.55754 11.1317 2.71799 11.3442 2.95965 11.4454L2.96013 11.4456L2.9606 11.4458C3.25826 11.5777 3.57391 11.4068 3.77321 11.1587C3.85838 11.0508 3.93152 10.9404 3.9933 10.8325C4.11688 10.616 4.19569 10.4092 4.24507 10.2341C4.2945 10.0581 4.31456 9.91423 4.31474 9.81313C4.31479 9.76205 4.30964 9.72478 4.30148 9.70014C4.2947 9.67972 4.2879 9.67355 4.28526 9.67184L4.28332 9.6711C4.28244 9.67077 4.27398 9.66792 4.25083 9.68143C4.22823 9.69463 4.19976 9.71891 4.16531 9.75576C4.09673 9.82911 4.01371 9.942 3.91712 10.0831L3.9169 10.0834C3.90743 10.097 3.89787 10.1108 3.88812 10.1249C3.88248 10.1331 3.87677 10.1413 3.87099 10.1496C3.78502 10.274 3.68793 10.4145 3.57623 10.5578C3.51048 10.6425 3.43943 10.7286 3.36222 10.8132C3.31637 10.8651 3.30028 10.8803 3.26796 10.9032L3.26727 10.9037L3.26658 10.9041C3.24115 10.9209 3.21114 10.9325 3.17843 10.9312C3.1451 10.9299 3.1151 10.9154 3.0901 10.8911C2.93853 10.7528 2.85263 10.5318 2.79211 10.309C2.76152 10.1965 2.73681 10.0809 2.71376 9.97195C2.71277 9.96728 2.71178 9.96261 2.7108 9.95797C2.68875 9.85367 2.66817 9.75638 2.64503 9.67174L2.64502 9.67174C2.60665 9.53135 2.48755 9.45541 2.39719 9.46852C2.35411 9.47477 2.31054 9.50148 2.27877 9.56308C2.24628 9.6261 2.22626 9.7262 2.23786 9.8746ZM2.74127 9.64543C2.69321 9.46964 2.53554 9.34764 2.38287 9.36978C2.30444 9.38116 2.23495 9.43036 2.19009 9.51735C2.14603 9.60282 2.12589 9.72373 2.13845 9.8832L2.13851 9.88397L2.1386 9.88473C2.17709 10.2349 2.22512 10.5893 2.36259 10.9246L2.36259 10.9246L2.36274 10.9249C2.47101 11.1835 2.64612 11.4221 2.92066 11.5372C3.28013 11.6962 3.6407 11.4831 3.85114 11.221L3.85114 11.221L3.85139 11.2207C3.93991 11.1086 4.01583 10.9939 4.07991 10.882L4.07994 10.882C4.20801 10.6576 4.28978 10.4432 4.34109 10.2612L4.34111 10.2611C4.39223 10.0791 4.41431 9.92594 4.41452 9.8133V9.81327C4.41458 9.75711 4.40913 9.70779 4.39618 9.66874C4.38346 9.6304 4.36028 9.59303 4.3188 9.57767L4.31041 9.57457H4.30872C4.2702 9.56408 4.23218 9.57679 4.20053 9.59527C4.16561 9.61565 4.12939 9.64809 4.09242 9.68762C4.01819 9.76702 3.93149 9.88549 3.83491 10.0265C3.8254 10.0402 3.81572 10.0542 3.80594 10.0683C3.80031 10.0765 3.79465 10.0846 3.78898 10.0928L3.78894 10.0929C3.70281 10.2175 3.60722 10.3557 3.4975 10.4965L3.49744 10.4966C3.43287 10.5798 3.36343 10.6639 3.28824 10.7463L3.28797 10.7466L3.28771 10.7469C3.24426 10.7961 3.2338 10.8051 3.21101 10.8212C3.19774 10.8299 3.1883 10.8318 3.18235 10.8316C3.17724 10.8314 3.16965 10.8294 3.15934 10.8193L3.15864 10.8186L3.15792 10.8179C3.02864 10.7003 2.94841 10.5038 2.88839 10.2829C2.85865 10.1734 2.83447 10.0605 2.81137 9.9513C2.81029 9.94621 2.80922 9.94113 2.80815 9.93606C2.78634 9.83291 2.76521 9.73298 2.74127 9.64543ZM6.61112 9.58679C6.57121 9.53006 6.50183 9.48192 6.36529 9.49155L6.36528 9.49155C6.27902 9.49762 6.20704 9.54361 6.15927 9.62154C6.11097 9.70033 6.0869 9.81328 6.10387 9.94978C6.1334 10.1873 6.16587 10.3189 6.23359 10.5654C6.26898 10.6887 6.30814 10.8106 6.36516 10.9359C6.47445 11.1708 6.64231 11.3981 6.90074 11.4966L6.90119 11.4968L6.90165 11.497C6.96172 11.5213 7.02791 11.5344 7.09755 11.5408C7.1658 11.5454 7.23822 11.5414 7.29977 11.5275L7.30026 11.5274L7.30074 11.5273C7.42953 11.5008 7.52776 11.449 7.61248 11.392C7.68608 11.3404 7.74763 11.2791 7.80794 11.2111C7.90176 11.1034 7.98224 10.9933 8.05014 10.8855C8.18594 10.6696 8.2723 10.4633 8.32626 10.2886C8.38028 10.113 8.40193 9.96956 8.40183 9.86916C8.40175 9.81834 8.39598 9.78159 8.38709 9.75747C8.37985 9.73786 8.37238 9.73097 8.3679 9.72842L8.36458 9.72728C8.35958 9.72555 8.34698 9.72422 8.32093 9.73793C8.2953 9.75143 8.26361 9.77597 8.22567 9.8127C8.15003 9.88593 8.05861 9.99863 7.95233 10.1394L7.9521 10.1397C7.93909 10.1566 7.92589 10.174 7.9124 10.1916L7.90156 10.2058L7.90155 10.2059L7.90154 10.2059C7.80692 10.33 7.70003 10.4701 7.57705 10.6132C7.50466 10.6977 7.42642 10.7835 7.34139 10.8679C7.32137 10.8885 7.30086 10.9088 7.28 10.9255C7.25909 10.9423 7.23528 10.9576 7.20814 10.9657C7.17974 10.9742 7.14989 10.974 7.11935 10.963C7.08996 10.9523 7.06241 10.9325 7.0356 10.9058L7.032 10.9022L7.0292 10.898C7.02582 10.8928 7.02233 10.8882 7.01781 10.8821L7.01607 10.8798L7.01599 10.8797L7.01551 10.879C7.00915 10.8705 7.00138 10.8601 6.99379 10.8478C6.96947 10.8101 6.94629 10.7681 6.92494 10.7234C6.88095 10.6337 6.84583 10.532 6.81478 10.4308L6.8147 10.4306C6.75291 10.2248 6.70944 10.0054 6.67633 9.78438L6.6763 9.78418C6.66785 9.72617 6.65423 9.64808 6.61112 9.58679ZM6.77502 9.76973C6.76668 9.71247 6.751 9.61223 6.69272 9.52938C6.63121 9.44195 6.52704 9.38012 6.35828 9.39203M6.77502 9.76973C6.8077 9.98788 6.85029 10.2022 6.91021 10.4017C6.9408 10.5014 6.97412 10.5971 7.01464 10.6797L7.0149 10.6802L7.01489 10.6802C7.03476 10.7219 7.05603 10.7603 7.0779 10.7941L7.07823 10.7946L7.07856 10.7952C7.0837 10.8036 7.08912 10.8108 7.09597 10.82L7.09605 10.8201C7.09661 10.8209 7.09718 10.8216 7.09777 10.8224C7.10124 10.8271 7.10529 10.8325 7.10935 10.8384C7.12833 10.8567 7.1428 10.8653 7.15327 10.8691C7.1632 10.8727 7.17118 10.8726 7.1797 10.8701C7.18948 10.8672 7.20166 10.8605 7.21753 10.8477C7.23336 10.835 7.25032 10.8184 7.27026 10.798L7.27053 10.7977L7.27082 10.7974C7.3537 10.7151 7.43021 10.6312 7.50131 10.5482L7.50137 10.5481C7.62219 10.4076 7.72743 10.2697 7.82222 10.1453L7.82224 10.1453L7.83305 10.1311C7.8465 10.1135 7.85976 10.0961 7.8728 10.0791C7.97913 9.93832 8.07456 9.82013 8.15627 9.74102C8.19702 9.70156 8.23663 9.66956 8.27446 9.64965C8.30767 9.63216 8.34529 9.62106 8.38291 9.62902L8.3851 9.62861L8.39741 9.63279C8.44017 9.64728 8.46603 9.68318 8.48069 9.72293C8.49531 9.76257 8.50151 9.81248 8.50161 9.869V9.86903C8.50172 9.98218 8.47764 10.1358 8.42162 10.3179L8.4216 10.318C8.36534 10.5002 8.2755 10.7146 8.13457 10.9387L8.13455 10.9387C8.06402 11.0506 7.98046 11.165 7.88299 11.2768L7.88274 11.2771C7.81976 11.3482 7.75218 11.4159 7.66936 11.474L7.66898 11.4743L7.66859 11.4745C7.5768 11.5363 7.46655 11.595 7.32132 11.6249C7.24839 11.6414 7.16598 11.6454 7.09024 11.6403L7.08965 11.6403L7.08905 11.6402C7.0131 11.6333 6.93663 11.6187 6.86475 11.5897C6.57127 11.4776 6.38856 11.2228 6.27461 10.9778L6.27444 10.9774C6.21436 10.8455 6.17358 10.718 6.13761 10.5926L6.13746 10.5921L6.13746 10.5921C6.06912 10.3434 6.03525 10.2065 6.00486 9.96209C5.98574 9.80829 6.01164 9.67146 6.07421 9.5694C6.13729 9.46648 6.23707 9.40055 6.35827 9.39203M12.6653 10.35C12.5694 10.2935 12.4035 10.3103 12.2662 10.526L12.2647 10.5283L12.2631 10.5303C11.3501 11.6765 9.49917 13.4804 6.46829 14.3629C6.28269 14.417 6.22413 14.5558 6.24944 14.6642C6.2744 14.7711 6.38719 14.872 6.59696 14.835C7.43272 14.6875 8.38567 14.2728 9.21662 13.8141C10.0475 13.3555 10.751 12.8559 11.0881 12.5425L11.1221 12.579L11.0881 12.5425C11.4283 12.2262 11.788 11.8613 12.0864 11.5337C12.3861 11.2047 12.6202 10.9173 12.7124 10.755C12.8233 10.5598 12.7626 10.4074 12.6653 10.35ZM12.7159 10.2641C12.8715 10.3558 12.9299 10.5741 12.7991 10.8043C12.7007 10.9776 12.4591 11.2726 12.1602 11.6009C11.86 11.9304 11.4983 12.2974 11.1561 12.6155C10.8109 12.9365 10.0998 13.4406 9.26483 13.9015C8.42993 14.3623 7.46581 14.783 6.6143 14.9332C6.36217 14.9777 6.19143 14.8546 6.15228 14.6869C6.11348 14.5207 6.21022 14.3342 6.4404 14.2671L6.45435 14.315L6.4404 14.2671C9.44416 13.3926 11.2786 11.6055 12.1834 10.4702C12.3394 10.227 12.5594 10.1718 12.7159 10.2641Z"
        fill="#00402F"
      />
    </svg>
  )
}
