import React from 'react'
import { fireEvent, render } from '@testing-library/react'

import { Button } from './Button'

describe('Button component', () => {
  it('renders the button with children', () => {
    const { getByText } = render(<Button>Click Me</Button>)

    expect(getByText('Click Me')).toBeInTheDocument()
  })

  it('applies correct classes based on size prop (extra-small)', () => {
    const { rerender, getByTestId } = render(
      <Button size="extra-small" dataTestId="ds-component-button">
        Extra Small Button
      </Button>
    )

    expect(getByTestId('ds-component-button')).toHaveClass(
      'px-2 py-1 h-6 min-w-6'
    )
  })

  it('applies correct classes based on size prop (small)', () => {
    const { rerender, getByTestId } = render(
      <Button size="small" dataTestId="ds-component-button">
        Small Button
      </Button>
    )

    expect(getByTestId('ds-component-button')).toHaveClass(
      'px-2 py-1.5 h-8 min-w-8'
    )
  })

  it('applies correct classes based on size prop (medium)', () => {
    const { rerender, getByTestId } = render(
      <Button size="medium" dataTestId="ds-component-button">
        Medium Button
      </Button>
    )

    expect(getByTestId('ds-component-button')).toHaveClass(
      'px-3 py-2 h-9 min-w-9'
    )
  })

  it('applies correct classes based on size prop (large)', () => {
    const { rerender, getByTestId } = render(
      <Button size="large" dataTestId="ds-component-button">
        Large Button
      </Button>
    )

    expect(getByTestId('ds-component-button')).toHaveClass(
      'px-4 py-3 h-11 min-w-11'
    )
  })

  it('disables the button when the disabled prop is true', () => {
    const onClick = jest.fn()
    const { getByTestId } = render(
      <Button disabled onClick={onClick} dataTestId="ds-component-button">
        Disabled Button
      </Button>
    )

    expect(getByTestId('ds-component-button')).toBeDisabled()
    fireEvent.click(getByTestId('ds-component-button'))
    expect(onClick).not.toHaveBeenCalled()
    expect(getByTestId('ds-component-button')).toHaveClass('bg-action-disabled')
  })

  it('disables the button when the loading prop is true', () => {
    const onClick = jest.fn()
    const { getByTestId } = render(
      <Button loading onClick={onClick} dataTestId="ds-component-button">
        Disabled Button
      </Button>
    )

    expect(getByTestId('ds-component-button')).toBeDisabled()
    fireEvent.click(getByTestId('ds-component-button'))
    expect(onClick).not.toHaveBeenCalled()
  })

  it('calls the onClick handler when clicked', () => {
    const onClick = jest.fn()
    const { getByTestId } = render(
      <Button onClick={onClick} dataTestId="ds-component-button">
        Clickable Button
      </Button>
    )

    fireEvent.click(getByTestId('ds-component-button'))
    expect(onClick).toHaveBeenCalledTimes(1)
  })
})
