import type { ReactNode } from 'react'
import { render, screen } from '@testing-library/react'

import { Step } from './Step'

jest.mock('../../badge/Badge', () => ({
  Badge: ({
    count,
    color,
    iconName,
  }: {
    count: number
    color: string
    iconName: string
  }) => (
    <div data-testid="badge" className={color}>
      {iconName && <span data-testid="badge-icon">{iconName}</span>}
      {count && <span data-testid="badge-count">{count}</span>}
    </div>
  ),
}))

jest.mock('../../../atoms/typography/Typography', () => ({
  Typography: ({ children, color }: { children: ReactNode; color: string }) => (
    <span data-testid="typography" className={color}>
      {children}
    </span>
  ),
}))

jest.mock('../../icon/Icon', () => ({
  Icon: ({
    name,
    color,
    backgroundColor,
  }: {
    name: string
    color: string
    backgroundColor: string
  }) => (
    <div data-testid="icon" className={`${color} ${backgroundColor}`}>
      {name}
    </div>
  ),
}))

describe('Step component', () => {
  it('renders with default props', () => {
    render(<Step label="Test Step" index={1} />)

    expect(screen.getByText('Test Step')).toBeInTheDocument()
    expect(screen.getByTestId('badge')).toHaveClass('white')
    expect(screen.getByTestId('badge-count')).toHaveTextContent('1')
  })

  it('renders with active state', () => {
    render(<Step label="Active Step" type="active" index={2} />)

    expect(screen.getByText('Active Step')).toBeInTheDocument()
    expect(screen.getByTestId('typography')).toHaveClass('base-default')
    expect(screen.getByTestId('badge-count')).toHaveTextContent('2')
  })

  it('renders with complete state', () => {
    render(<Step label="Complete Step" type="complete" index={3} />)

    expect(screen.getByText('Complete Step')).toBeInTheDocument()
    expect(screen.getByTestId('typography')).toHaveClass('decorative-green')
    expect(screen.getByTestId('badge')).toHaveClass('light-green')
    expect(screen.getByTestId('badge-icon')).toHaveTextContent('Check')
  })

  it('renders with warning state', () => {
    render(<Step label="Warning Step" type="warning" index={4} />)

    expect(screen.getByText('Warning Step')).toBeInTheDocument()
    expect(screen.getByTestId('typography')).toHaveClass('decorative-orange')
    expect(screen.getByTestId('badge')).toHaveClass('light-orange')
    expect(screen.getByTestId('badge-icon')).toHaveTextContent(
      'WarningTriangle'
    )
  })

  it('renders with error state', () => {
    render(<Step label="Error Step" type="error" index={5} />)

    expect(screen.getByText('Error Step')).toBeInTheDocument()
    expect(screen.getByTestId('typography')).toHaveClass('base-error')
    expect(screen.getByTestId('badge')).toHaveClass('light-red')
    expect(screen.getByTestId('badge-icon')).toHaveTextContent('Xmark')
  })

  it('renders with disabled state', () => {
    render(<Step label="Disabled Step" isDisabled index={6} />)

    expect(screen.getByText('Disabled Step')).toBeInTheDocument()
    expect(screen.getByTestId('typography')).toHaveClass('base-disabled')
    expect(screen.getByTestId('badge')).toHaveClass('light-grey')
  })

  it('renders with additional badge', () => {
    render(<Step label="Step with Badge" index={7} badge={3} />)

    const badges = screen.getAllByTestId('badge')
    expect(badges).toHaveLength(2)
    expect(badges[1]).toHaveClass('red')
    expect(screen.getAllByTestId('badge-count')[1]).toHaveTextContent('3')
  })
})
