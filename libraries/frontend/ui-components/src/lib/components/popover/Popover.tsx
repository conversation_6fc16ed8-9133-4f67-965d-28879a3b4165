import type { <PERSON><PERSON><PERSON><PERSON>, MouseEvent, ReactNode } from 'react'
import React, {
  cloneElement,
  forwardRef,
  isValidElement,
  useEffect,
} from 'react'
import {
  FloatingFocusManager,
  FloatingPortal,
  useMergeRefs,
  useTransitionStyles,
} from '@floating-ui/react'
import { clsx } from 'clsx'

import { PopoverContext, usePopoverContext } from './PopoverContext'
import type { PopoverOptions } from './hooks/usePopover'
import { usePopover } from './hooks/usePopover'

import './popover.scoped.scss'

export const Popover = ({
  children,
  modal = false,
  container,
  ...restOptions
}: { children: ReactNode } & PopoverOptions) => {
  const popover = usePopover({ modal, container, ...restOptions })
  return (
    <PopoverContext.Provider value={popover}>
      {children}
    </PopoverContext.Provider>
  )
}

export const PopoverTrigger = forwardRef<
  HTMLElement,
  HTMLProps<HTMLElement> & {
    children: ReactNode
    asChild?: boolean
    isFullWidth?: boolean
  }
>(({ children, asChild = false, isFullWidth = false, ...props }, propRef) => {
  const context = usePopoverContext()
  const childrenRef = (children as any).ref
  const ref = useMergeRefs([context.refs.setReference, propRef, childrenRef])

  if (asChild && isValidElement(children)) {
    return cloneElement(
      <div>{children}</div>,
      context.getReferenceProps({
        ref,
        ...props,
        ...children.props,
        className: clsx('ds-popover-component_trigger cursor-pointer', {
          'w-full': isFullWidth,
        }),
        'data-state': context.open ? 'open' : 'closed',
        onClick: event => {
          event.stopPropagation()
          event.preventDefault()
          if (props.onClick) {
            props.onClick(
              event as MouseEvent<HTMLElement, globalThis.MouseEvent>
            )
          }
        },
      })
    )
  }

  return (
    <button
      ref={ref}
      type="button"
      data-state={context.open ? 'open' : 'closed'}
      className={'ds-popover-component_trigger'}
      {...context.getReferenceProps(props)}
    >
      {children}
    </button>
  )
})

export type PopoverContentPadding = 'xs' | 's' | 'm' | 'l' | 'xl'

type PopoverContentProps = HTMLProps<HTMLDivElement> & {
  padding?: PopoverContentPadding
}

export const PopoverContent = forwardRef<HTMLDivElement, PopoverContentProps>(
  ({ style, ...props }, propRef) => {
    const {
      context: floatingContext,
      middlewareData,
      dataTestId,
      padding = props.padding || 'm',
      shouldCloseOnClickInside,
      setOpen,
      container,
      ...context
    } = usePopoverContext()

    const ref = useMergeRefs([context.refs.setFloating, propRef])

    useEffect(() => {
      if (!container || !floatingContext.open) return

      const handleContainerClick = (event: globalThis.MouseEvent) => {
        const containerElement = container as HTMLElement
        const popoverElement = floatingContext.refs.floating?.current

        // Check if click is inside container but outside popover
        if (
          containerElement.contains(event.target as Node) &&
          popoverElement &&
          !popoverElement.contains(event.target as Node)
        ) {
          setOpen(false)
        }
      }

      const containerElement = container as HTMLElement
      containerElement.addEventListener('click', handleContainerClick)
      return () => {
        containerElement.removeEventListener('click', handleContainerClick)
      }
    }, [container, floatingContext.open, setOpen, ref, floatingContext])

    const { styles: transitionStyles, isMounted } = useTransitionStyles(
      floatingContext,
      {
        duration: 100,
        initial: ({ side }) => {
          let styles = {}
          switch (side) {
            case 'top':
              styles = {
                transform: 'translateY(-50px)',
              }
              break
            case 'bottom':
              styles = {
                transform: 'translateY(50px)',
              }
              break
            case 'left':
              styles = {
                transform: 'translateX(-150px)',
              }
              break
            case 'right':
              styles = {
                transform: 'translateX(150px)',
              }
              break
          }
          return styles
        },
        open: ({ side }) => {
          return {
            opacity: 1,
            transform:
              side === 'top' || side === 'bottom'
                ? 'translateY(0)'
                : 'translateX(0)',
          }
        },
        common: ({ side }) => {
          return {
            opacity: 0,
          }
        },
      }
    )

    if (!floatingContext.open) return null

    return (
      <FloatingPortal root={container}>
        <FloatingFocusManager context={floatingContext} modal={context.modal}>
          <div
            ref={ref}
            style={{
              ...style,
              ...context.floatingStyles,
              ...transitionStyles,
              visibility: middlewareData.hide?.referenceHidden
                ? 'hidden'
                : 'visible',
            }}
            aria-labelledby={context.labelId}
            aria-describedby={context.descriptionId}
            {...context.getFloatingProps(props)}
            data-testid={`${dataTestId}-content`}
            onClick={() => {
              if (shouldCloseOnClickInside) {
                setOpen(false)
              }
            }}
            className={clsx(
              'ds-popover-component_content bg-white rounded-md focus:outline-none body-s-medium cursor-pointer z-zPopover shadow-2xl flex flex-col gap-2 border border-ui-base-default',
              {
                'p-1': padding === 'xs',
                'p-2': padding === 's',
                'p-4': padding === 'm',
                'p-6': padding === 'l',
              }
            )}
          >
            {props.children}
          </div>
        </FloatingFocusManager>
      </FloatingPortal>
    )
  }
)

Popover.Trigger = PopoverTrigger
Popover.Content = PopoverContent
