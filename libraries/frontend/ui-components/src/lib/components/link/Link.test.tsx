import { fireEvent, render, screen } from '@testing-library/react'

import { Link } from './Link'

describe('Link component', () => {
  it('renders the correct label text', () => {
    render(
      <Link onClick={() => {}} dataTestId="typography-link">
        Click here
      </Link>
    )

    const linkElement = screen.getByTestId('typography-link')
    expect(linkElement).toHaveTextContent('Click here')
  })

  it('calls onClick when the link is clicked', () => {
    const onClickMock = jest.fn()
    render(
      <Link onClick={onClickMock} dataTestId="typography-link">
        Click here
      </Link>
    )

    const linkElement = screen.getByTestId('typography-link')
    fireEvent.click(linkElement)

    expect(onClickMock).toHaveBeenCalledTimes(1)
  })
})
