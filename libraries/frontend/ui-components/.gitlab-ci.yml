.ui-changes: &ui-changes
  - libraries/frontend/ui-components/*
  - libraries/frontend/ui-components/**/*
#  - '.gitlab-ci.yml'
#  - 'package.json'
#  - 'tsconfig.base.json'
#  - 'yarn.lock'

.ui_rules:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
    when: never
  - if: $CI_PIPELINE_SOURCE == "pipeline"
    when: never
  # Do not trigger if we are creating a tag (from master)
  - if: '$CI_COMMIT_TAG != null'
    when: never
  # Do not trigger when merging on develop, release or master
  - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "master"'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    changes: *ui-changes
    when: always
  # - if: '$CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_TAG != null'
  #   changes: *ui-changes
  #   when: always
  - when: never

.chromatic_ui_rules:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
    when: never
  - if: $CI_PIPELINE_SOURCE == "pipeline"
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    changes: *ui-changes
    when: always
    allow_failure: true
  - if: '$CI_COMMIT_REF_NAME == "develop"'
    changes: *ui-changes
    when: always
  - when: never

.chromatic_ui_develop_rules:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
    when: never
  - if: '$CI_COMMIT_REF_NAME == "develop"'
    changes: *ui-changes
    when: always
  - when: never

test:ui:
  stage: test
  needs: ['install']
  coverage: '/All files[^\|]*\|[^\|]*\s+([\d\.]+)/'
  image: node:22.15.0-alpine3.21
  cache:
    - !reference [.cache_app_pull]
    - !reference [.cache_coverage]
  rules:
    - !reference [.ui_rules]
  script:
    - npx nx run ui:test:production --coverage --watch=false --ci --skip-nx-cache

test:ui-lint:
  stage: test
  needs: ['install']
  image: node:22.15.0-alpine3.21
  cache:
    - !reference [.cache_app_pull]
  rules:
    - !reference [.ui_rules]
  script:
    - npx nx lint ui # --skip-nx-cache
  interruptible: true

build:ui:
  stage: build
  needs:
    - job: install
    - job: test:ui-lint
      optional: true
    - job: test:ui
      optional: true
  image: node:22.15.0-alpine3.21
  rules:
    - !reference [.chromatic_ui_rules]
  cache:
    - !reference [.cache_app_pull]
  script:
    - npx nx build ui --skip-nx-cache
  resource_group: zeliq-$ENVIRONMENT

deploy:chromatic-ui-develop:
  needs: ['build:ui']
  stage: deploy
  image: node:22.15.0-alpine3.21
  rules:
    - !reference [.chromatic_ui_develop_rules]
  cache:
    - !reference [.cache_app_pull]
  script:
    - apk add git
    - yarn chromatic:ui --auto-accept-changes
  resource_group: zeliq-$ENVIRONMENT
  interruptible: true
  variables:
    CHROMATIC_PROJECT_TOKEN: $UI_CHROMATIC_PROJECT_TOKEN

deploy:chromatic-ui-feature:
  needs: ['build:ui']
  stage: deploy
  image: node:22.15.0-alpine3.21
  rules:
    - !reference [.chromatic_ui_rules]
  cache:
    - !reference [.cache_app_pull]
  script:
    - apk add git
    - yarn chromatic:ui --only-changed
  resource_group: zeliq-$ENVIRONMENT
  interruptible: true
  variables:
    CHROMATIC_PROJECT_TOKEN: $UI_CHROMATIC_PROJECT_TOKEN

# Gitlab Page
##
pages:
  stage: deploy
  needs: ['install']
  image: node:22.15.0-alpine3.21
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" && $PACKAGE_MANAGER_SET =~ /(\bnpm|yarn|pnpm\b)/'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      #      changes: *ui-changes
      when: never
    - if: '$CI_COMMIT_REF_NAME == "develop"'
      when: always
  cache:
    - !reference [.cache_app_pull]
  script:
    - npx nx build-storybook:ui --skip-nx-cache
    - mv dist/storybook/frontend/ui-components/ $CI_PROJECT_DIR/public
  artifacts:
    paths:
      - public
  allow_failure: true
