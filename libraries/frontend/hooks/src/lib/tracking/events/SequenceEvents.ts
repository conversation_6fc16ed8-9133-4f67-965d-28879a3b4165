export enum SequenceEvents {
  SEQUENCE_OVERVIEW_CLICK_CREATE_NEW_SEQUENCE = 'SequenceOverview_click_CreateNewSequence',
  SEQUENCE_ADD_LEADS_DRAWER_CLICK_NEXT = 'SequenceAddLeadsDrawer_click_Next',
  SEQUENCE_ADD_LEADS_DRAWER_CLICK_LIST_SELECTOR = 'SequenceAddLeadsDrawer_click_ListSelector',
  SEQUENCE_ADD_LEADS_DRAWER_CHANGE_LIST_SELECTOR_VALUE = 'SequenceAddLeadsDrawer_change_ListSelectorValue',
  SEQUENCE_ADD_LEADS_DRAWER_CLICK_PREVIOUS = 'SequenceAddLeadsDrawer_click_Previous',
  SEQUENCE_ADD_LEADS_DRAWER_CLICK_SELECT_TAB = 'SequenceAddLeadsDrawer_click_SelectTab',
  SEQUENCE_ADD_LEADS_DRAWER_CLICK_ADD_LEADS = 'SequenceAddLeadsDrawer_click_AddLeads',
  SEQUENCE_HEADER_CLICK_EDIT_SEQUENCE_NAME = 'SequenceHeader_click_EditSequenceName',
  SEQUENCE_HEADER_CLICK_DELETE_SEQUENCE = 'SequenceHeader_click_DeleteSequence',
  SEQUENCE_DELETE_CONFIRM_CLICK_DELETE_SEQUENCE = 'SequenceDeleteConfirm_click_DeleteSequence',
  SEQUENCE_DELETE_CONFIRM_CLICK_CANCEL_DELETE_SEQUENCE = 'SequenceDeleteConfirm_click_CancelDeleteSequence',
  SAVE_SEQUENCE_MODAL_CLICK_SAVE_THE_SEQUENCE = 'SaveSequenceModal_click_SaveTheSequence',
  SAVE_SEQUENCE_MODAL_CLICK_CROSS_SAVE_THE_SEQUENCE = 'SaveSequenceModal_click_CrossSaveTheSequence',
  SAVE_SEQUENCE_MODAL_CLICK_QUIT_WITHOUT_SAVING = 'SaveSequenceModal_click_QuitWithoutSaving',
  SEQUENCE_HEADER_CLICK_NEXT_BUTTON = 'SequenceHeader_click_NextButton',
  SEQUENCE_HEADER_CLICK_TAB = 'SequenceHeader_click_Tab',
  SEQUENCE_ADD_STEP_DRAWER_CLICK_STEP_OPTION = 'SequenceAddStepDrawer_click_StepOption',
  SEQUENCE_ADD_STEP_PAGE_CLICK_ADD_STEP_BUTTON = 'SequenceAddStepPage_click_AddStepButton',
  SEQUENCE_ADD_STEP_PAGE_CLICK_DELETE_STEP = 'SequenceAddStepPage_click_DeleteStep',
  SEQUENCE_ADD_STEP_PAGE_CLICK_DUPLICATE_STEP = 'SequenceAddStepPage_click_DuplicateStep',
  SEQUENCE_SETTINGS_PAGE_CLICK_EDIT_SCHEDULE = 'SequenceSettingsPage_click_EditSchedule',
  SEQUENCE_EDIT_SCHEDULE_PAGE_CLICK_SAVE_SCHEDULE = 'SequenceEditSchedulePage_click_SaveSchedule',
  SEQUENCE_ENRICH_PAGE_CLICK_ENRICH_TAB_EMAIL_PHONE = 'SequenceEnrichPage_click_EnrichTabEmailPhone',
  REVIEW_ERRORS_PAGE_CLICK_REVIEW_ERRORS = 'ReviewErrorsPage_click_ReviewErrors',
  REVIEW_ERRORS_DRAWER_CLICK_REMOVE_ALL = 'ReviewErrorsDrawer_click_RemoveAll',
  REVIEW_ERRORS_DRAWER_CLICK_SAVE = 'ReviewErrorsDrawer_click_Save',
  SEQUENCE_HEADER_CLICK_LAUNCH = 'SequenceHeader_click_Launch',
  ADD_TO_SEQUENCE_DRAWER_CLICK_ADD_X_LEADS = 'AddToSequenceDrawer_click_AddXLeads',
  SEQUENCE_SELECT_LEADS_CLICK_BREAD_CRUMB = 'SequenceSelectLeads_click_BreadCrumb',
  SEQUENCE_V2_HEADER_CLICK_EDIT_SEQUENCE_NAME = 'SequenceV2Header_click_EditSequenceName',
  SEQUENCE_V2_HEADER_CLICK_STEPPER = 'SequenceV2Header_click_Stepper',
  SEQUENCE_V2_HEADER_CLICK_CROSS = 'SequenceV2Header_click_Cross',
  SEQUENCE_V2_STEP_BUILDER_CLICK_STEP_OPTION = 'SequenceV2StepBuilder_click_StepOption',
  SEQUENCE_V2_STEP_BUILDER_CLICK_STEPS_OPTIONS_MODAL_SELECT_STEPS_TYPE = 'SequenceV2StepBuilder_click_StepsOptionsModalSelectStepsType',
  SEQUENCE_STEP_BUILDER_CLICK_ADD_MULTIPLES_STEPS = 'SequenceStepBuilder_click_AddMultiplesSteps',
  SEQUENCE_V2_STEP_BUILDER_CLICK_DELETE_STEP = 'SequenceV2StepBuilder_click_DeleteStep',
  SEQUENCE_V2_STEP_BUILDER_CLICK_DUPLICATE_STEP = 'SequenceV2StepBuilder_click_DuplicateStep',
  SEQUENCE_V2_STEP_BUILDER_CLICK_RENAME = 'SequenceV2StepBuilder_click_Rename',
  DELAY_BETWEEN_STEPS_CLICK_ANY_DAYS = 'DelayBetweenSteps_click_AnyDays',
  SEQUENCE_V2_SELECT_LEADS_CLICK_ADD_LEADS = 'SequenceV2SelectLeads_click_AddLeads',
  SEQUENCE_V2_SELECT_LEADS_CLICK_MY_LEADS = 'SequenceV2SelectLeads_click_MyLeads',
  SEQUENCE_V2_SELECT_LEADS_CLICK_ALL_LEADS = 'SequenceV2SelectLeads_click_AllLeads',
  SEQUENCE_V2_SELECT_LEADS_CLICK_BREAD_CRUMB = 'SequenceV2SelectLeads_click_BreadCrumb',
  SAVE_SEQUENCE_V2_MODAL_CLICK_SAVE_THE_SEQUENCE = 'SaveSequenceV2Modal_click_SaveTheSequence',
  SAVE_SEQUENCE_V2_MODAL_CLICK_QUIT_WITHOUT_SAVING = 'SaveSequenceV2Modal_click_QuitWithoutSaving',
  SAVE_SEQUENCE_V2_MODAL_CLICK_CROSS_SAVE_THE_SEQUENCE = 'SaveSequenceV2Modal_click_CrossSaveTheSequence',
  SEQUENCE_V2_FOOTER_CLICK_FOOTER = 'SequenceV2Footer_click_Footer',
  SEQUENCE_ADD_LEADS_CLICK_EXCLUDE_LEADS_IN_OTHER_SEQUENCE = 'SequenceAddLeads_click_ExcludeLeadsInOtherSequence',
  SEQUENCE_SELECT_LEADS_FLOATING_BAR = 'SequenceSelectLeads_floating_bar',
  SEQUENCE_V2_STEP_BUILDER_BUTTON_CLICK_CALL_STEPS = 'SequenceV2-Step-Builder-Button_Click_CallSteps',
  SEQUENCE_V2_PREVIEW_DROPDOWN_CLICK_PHONE_SELECTOR = 'SequenceV2-Preview-Dropdown_Click_PhoneSelector',
  SEQUENCE_V2_PREVIEW_BUTTON_CLICK_ENRICH_PHONE = 'SequenceV2-Preview-Button_Click_EnrichPhone',
  SEQUENCE_V2_PREVIEW_BUTTON_CLICK_ADD_PHONE = 'SequenceV2-Preview-Button_Click_AddPhone',
  SEQUENCE_V2_PREVIEW_LIST_CLICK_LEAD_PHONE = 'SequenceV2-Preview-List_Click_LeadPhone',
  SEQUENCE_DONUT_DRAWER_BUTTON_CLICK_SKIP = 'SequencesDonutsDrawer-Button_Click_Skip',
  SEQUENCE_V2_PREVIEW_HELPER_CLICK_MISSING_VARIABLES = 'SequenceV2-Preview-Helper_Click_MissingVariables',
  SEQUENCE_V2_STEPS_HEADER_CLICK_DELETE = 'SequenceV2-StepsHeader_Click_Delete',
  SEQUENCE_V2_STEPS_HEADER_CLICK_DUPLICATE = 'SequenceV2-StepsHeader_Click_Duplicate',
  SEQUENCE_V2_STEPS_HEADER_CLICK_EDIT_STEP_NAME = 'SequenceV2-StepsHeader_Click_EditStepName',
  SEQUENCE_V2_PREVIEW_BUTTON_CLICK_LAUNCH_SEQUENCE = 'SequenceV2-Preview-Button_Click_LaunchSequence',
  SEQUENCE_V2_STEP_BUILDER_DROPDOWN_CLICK_STEPS_OVERVIEW = 'SequenceV2-Step-Builder-Dropdown_Click_StepsOverview',
  SEQUENCE_V2_STEP_BUILDER_STEPS_OVERVIEW_CLICK_STEP = 'SequenceV2-Step-Builder-StepsOverview_Click_Step',
  SEQUENCE_V2_STEP_BUILDER_DRAG_CLICK_DRAG_N_DROP = 'SequenceV2-Step-Builder-Drag_Click_drag&Drop',
  SEQUENCE_V2_STEP_BUILDER_CHEVRON_CLICK_DELAY_BETWEEN_STEPS = 'SequenceV2-Step-Builder-chevron_Click_DelayBetweenSteps',
  SEQUENCE_V2_STEP_BUILDER_POPOVER_CLICK_TIMING_SAVE = 'SequenceV2-Step-Builder-Popover_Click_Timing_Save',
  SEQUENCE_V2_STEP_BUILDER_BUTTON_CLICK_SAVE_EDIT = 'SequenceV2-Step-Builder-Button_Click_SaveEdit',
  SEQUENCE_V2_SEQUENCE_BUTTON_CLICK_DUPLICATE = 'Sequence-Button_Click_Duplicate',
  SEQUENCE_V2_STEP_BUILDER_DROPDOWN_CLICK_EMAIL_TYPES = 'SequenceV2-Step-Builder-Dropdown_Click_EmailTypes',

  SEQUENCE_SETTINGS_PANEL_BUTTON_CLICK_TIME_PICKER = 'SequenceSettingsPanel-Button_Click_TimePicker',

  SEQUENCE_SETTINGS_PANEL_BUTTON_CLICK_DATE_PICKER = 'SequenceSettingsPanel-Button_Click_DatePicker',
  SEQUENCE_V2_STEP_BUILDER_BUTTON_CLICK_START_SEQUENCE_DATE = 'SequenceV2-Step-Builder-Button_Click_StartSequenceDate',

  SEQUENCE_SETTINGS_PANEL_BUTTON_CLICK_ACTIVE_DAY = 'SequenceSettingsPanel-Button_Click_ActiveDay',
  SEQUENCE_SETTINGS_PANEL_BUTTON_CLICK_ACTIVE_HOUR = 'SequenceSettingsPanel-Button_Click_ActiveHour',
}
