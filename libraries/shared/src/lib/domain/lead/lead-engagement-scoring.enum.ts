export enum LeadEngagementScoringPriority {
  URGENT = 'URGENT',
  HIGH = 'HIGH',
  POOL = 'POOL',
  STANDBY = 'STANDBY',
}

export enum LeadEngagementScoringMetadataKey {
  ASSIGNATION_OVERDUE_DATE = 'assignationOverdueDate',
  HAS_SENT_EMAIL = 'hasSentEmail',
  SENT_EMAIL_DATE = 'sentEmailDate',
  MAILS_RECEIVED = 'mailsReceived',
  MAIL_RECEIVED_ID = 'id',
  MAIL_RECEIVED_DATE = 'date',
  NB_MISSED_CALLS = 'nbMissedCalls',
  MISSED_CALLS = 'missedCalls',
  MISSED_CALLS_ID = 'id',
  MISSED_CALLS_DATE = 'date',
  OPENED_EMAILS = 'openedEmails',
  NB_OPENED_EMAILS = 'nbOpenedEmails',
  OPENED_EMAIL_ID = 'mailId',
  NB_CLICKED_LINKS = 'nbClickedLinks',
  CLICKED_LINKS = 'clickedLinks',
  CLICKED_LINKS_ID = 'mailId',
  NEXT_TASK_DATE = 'nextTaskDate',
  NEXT_TASK_ID = 'nextTaskId',
  FOLLOW_UP_DATE = 'followUpDate',
  FOLLOW_UP_CALL_DATE = 'followUpCallDate',
  FOLLOW_UP_MAIL_DATE = 'followUpMailDate',
  IS_MANUAL_UPDATE = 'isManualUpdate',
}

export type LeadEngagementScoringMetadataKeyType = {
  [key in LeadEngagementScoringMetadataKey]?:
    | string
    | number
    | boolean
    | Date
    | []
}
