export enum SequenceContactStatus {
  RUNNING = 'running',
  SCHEDULE = 'schedule',
  DONE = 'done',
  CONTACT_ANSWERED = 'contact_answered',

  ERROR = 'error',
  ERROR_EMAIL_VARIABLE = 'error_email_variable',
  ERROR_MISSING_VARIABLE = 'error_missing_variable',
  ERROR_EMAIL_EMPTY = 'error_email_empty',
  ERROR_EMAIL_INVALID = 'error_email_invalid',
  ERROR_LINKEDIN_URL_EMPTY = 'error_linkedin_url_empty',
  ERROR_LINKEDIN_NO_CONNECTION = 'error_linkedin_no_connection',
  CONTACT_ARCHIVED = 'contact_archived',
  CONTACT_UNSUBSCRIBED = 'contact_unsubscribed',
  CONTACT_UNASSIGNED = 'contact_unassigned',
  CONTACT_BOUNCED = 'contact_bounced',
  SDR_EMAIL_NOT_SYNC = 'sdr_email_not_sync',
  SDR_LINKEDIN_NOT_SYNC = 'sdr_linkedin_not_sync',
  SDR_LINKEDIN_INVALID_COOKIE = 'sdr_linkedin_invalid_cookie',
}

export type SequenceCodeError =
  | 'error'
  | 'sdr_email_not_sync'
  | 'sdr_linkedin_invalid_cookie'
  | 'sdr_linkedin_not_sync'
  | 'contact_unsubscribed'

export type SequenceContactError =
  | 'contact_unassigned'
  | 'contact_bounced'
  | 'error_linkedin_no_connection'
  | 'error_missing_variable'
  | 'error_email_invalid'
  | 'error_email_empty'
  | 'error_linkedin_url_empty'
  | 'contact_archived'

export function getSequenceContactErrorStatus(): string[] {
  return [
    SequenceContactStatus.ERROR,
    SequenceContactStatus.ERROR_EMAIL_EMPTY,
    SequenceContactStatus.ERROR_MISSING_VARIABLE,
    SequenceContactStatus.ERROR_LINKEDIN_URL_EMPTY,
    SequenceContactStatus.CONTACT_ARCHIVED,
    SequenceContactStatus.CONTACT_UNSUBSCRIBED,
    SequenceContactStatus.CONTACT_UNASSIGNED,
    SequenceContactStatus.SDR_EMAIL_NOT_SYNC,
    SequenceContactStatus.SDR_LINKEDIN_NOT_SYNC,
    SequenceContactStatus.CONTACT_BOUNCED,
    SequenceContactStatus.SDR_LINKEDIN_INVALID_COOKIE,
    SequenceContactStatus.ERROR_LINKEDIN_NO_CONNECTION,
    SequenceContactStatus.ERROR_EMAIL_INVALID,
  ]
}
