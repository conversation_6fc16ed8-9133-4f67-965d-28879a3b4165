import { ExternalLeadsFilterIdEnum } from './search-filters.types'
import { BUSINESS_UNITS, COUNTRIES, INDUSTRIES } from './constants/index'

export const SearchNewFiltersValues: {
  [key: string]: Array<string>
} = {
  [ExternalLeadsFilterIdEnum.JOB_COMPANY_SIZE]: [
    '1-10',
    '11-50',
    '51-200',
    '201-500',
    '501-1000',
    '1001-5000',
    '5001-10000',
    '10001+',
  ],
  [ExternalLeadsFilterIdEnum.CONTACT_LOCATION_COUNTRY]: COUNTRIES,
  [ExternalLeadsFilterIdEnum.COMPANY_LOCATION_COUNTRY]: COUNTRIES,
  [ExternalLeadsFilterIdEnum.INDUSTRY]: INDUSTRIES,
  [ExternalLeadsFilterIdEnum.JOB_TITLE_LEVELS]: [
    'c-level',
    'vp',
    'head',
    'manager',
    'senior',
    'junior',
    'intern',
    'other',
  ],
  [ExternalLeadsFilterIdEnum.BUSINESS_UNIT]: BUSINESS_UNITS,
  [ExternalLeadsFilterIdEnum.COMPANY_BUSINESS_UNIT]: BUSINESS_UNITS,
  [ExternalLeadsFilterIdEnum.BUSINESS_UNIT_UNITS]: BUSINESS_UNITS,
  [ExternalLeadsFilterIdEnum.FUNDING_ROUND]: [
    'convertible note',
    'crowdfunding',
    'debt',
    'private equity',
    'seed',
    'series a',
    'series b',
    'series c',
    'series d',
    'series e',
    'series f',
    'unknown',
    'venture',
    'other',
  ],
  job_title_roles: [
    'customer_service',
    'design',
    'education',
    'engineering',
    'finance',
    'health',
    'human_resources',
    'legal',
    'marketing',
    'media',
    'operations',
    'public_relations',
    'real_estate',
    'sales',
    'trades',
  ],
  job_title_subroles: [
    'accounting',
    'accounts',
    'brand_marketing',
    'broadcasting',
    'business_development',
    'compensation',
    'content_marketing',
    'customer_success',
    'data',
    'dental',
    'devops',
    'doctor',
    'editorial',
    'education_administration',
    'electrical',
    'employee_development',
    'events',
    'fitness',
    'graphic_design',
    'information_technology',
    'instructor',
    'investment',
    'journalism',
    'judicial',
    'lawyer',
    'logistics',
    'mechanical',
    'media_relations',
    'network',
    'nursing',
    'office_management',
    'paralegal',
    'pipeline',
    'product',
    'product_design',
    'product_marketing',
    'professor',
    'project_engineering',
    'project_management',
    'property_management',
    'quality_assurance',
    'realtor',
    'recruiting',
    'researcher',
    'security',
    'software',
    'support',
    'systems',
    'tax',
    'teacher',
    'therapy',
    'video',
    'web',
    'web_design',
    'wellness',
    'writing',
  ],
}
