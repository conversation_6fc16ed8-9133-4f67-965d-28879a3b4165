import type { OrganizationCurrency } from '../subscription'
import type { User, UserRole } from '../user'
import type { OrganizationServiceSubscriptionPlan } from './subscription-plan.enum'

export interface Organization {
  id: string
  name: string
  website: string
  slug: string
  domain: string
  createdAt: Date
  updatedAt: Date
  createdBy: User
  members: OrganizationMember[]
  invitations: InvitationMember[]
  countryCode: string
  zipcode?: string
  province?: string
  currency?: OrganizationCurrency
  plan: OrganizationServiceSubscriptionPlan
  creditBalance: number
  creditConsumed: number
}

export interface OrganizationMember {
  id: string
  role: UserRole
  organization: Organization
  member: User
  createdBy: User
  createdAt: Date
  updatedAt: Date
  lastActivity?: string
}

export interface InvitationMember {
  id: string
  email: string
  role: UserRole
  organization: Organization
  createdBy: User
  createdAt: Date
  updatedAt: Date
}
