import type { SerializedError } from '@reduxjs/toolkit'
import type { FetchBaseQueryError } from '@reduxjs/toolkit/dist/query/fetchBaseQuery'

export const BASE_URL = 'https://www.linkedin.com'

export const DORIAN_LINKEDIN_PROFILE_URL =
  'https://www.linkedin.com/in/dorian-ciavarella/'

/* COOKIES */
export enum LinkedInCookieKeys {
  LI_AT = 'li_at', // LinkedIn access token
  LI_A = 'li_a', // Sales Nav access token
}

export type CookiesType = {
  [LinkedInCookieKeys.LI_AT]: string | null
  [LinkedInCookieKeys.LI_A]?: string | null
}

/* API */
export type AuthorizeLinkedinPayload = {
  organizationId: string
  redirectUrl: string
}

export type AuthorizeLinkedinResponse = {
  authorizationUrl: string
}

export type CaptainDataLinkedInAccountPatch = {
  cookies: CookiesType
}
export type CaptainDataLinkedInAccountPatchResponse =
  | CaptainDataLinkedInAccountPatch
  | { error: FetchBaseQueryError | SerializedError }

export type CaptainDataLinkedInAccountPatchPayload =
  CaptainDataLinkedInAccountPatch

export type DeleteLinkedInAuthorizePayload = {
  organizationId: string
}

export type GetCookiesLinkedInResponse = {
  cookies: CookiesType
  success?: boolean
  error?: any
}

export type LinkedInThread = {
  id: string
  theadUrl: string
  user: LinkedInUser
  contact: LinkedInContact
  messages: LinkedInMessage[]
}

export type LinkedInMessage = {
  id: string
  direction: LinkedInMessageDirection
  date: Date
  from: string
  to: string
  body: string
}

export type LinkedInUser = {
  linkedinAccountName: string
}

export type LinkedInContact = {
  id: string
  firstName: string
  lastName: string
}

export enum LinkedInMessageDirection {
  INBOUND = 'INBOUND',
  OUTBOUND = 'OUTBOUND',
}
