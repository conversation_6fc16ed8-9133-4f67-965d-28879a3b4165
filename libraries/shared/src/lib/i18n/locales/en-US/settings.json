{"{{integrationServiceName}} properties": "{{integrationServiceName}} properties", "{{integrationServiceName}} property": "{{integrationServiceName}} property", "{{integrationServiceName}} settings": "{{integrationServiceName}} settings", "{{integrationServiceName}} status": "{{integrationServiceName}} status", "{{serviceName}} authorization form": "{{serviceName}} authorization form", "{{serviceName}} connected": "{{serviceName}} connected", "1 year commitment period": "1 year commitment period", "14 days free trial. No credit card required.": "14 days free trial. No credit card required.", "79sec = 45 mails per hour": "79sec = 45 mails per hour", "Access your invoices. Update your billing and payment method": "Access your invoices. Update your billing and payment method.", "Account": "Account", "Account sender name": "Account sender name", "activities": "Activities", "Actual Plan": "Actual Plan", "Add custom property": "Add custom property", "Add exclusion rules": "Add exclusion rules", "Add more credits": "Add more credits", "Add more credits to your current plan": "Add more credits to your current plan", "After-Call Work": "After-Call Work", "Aircall": "Aircall", "aircall": "aircall", "All data": "All data", "Already connected to another CRM": "Already connected to another CRM", "An email account is already connected": "An email account is already connected", "An error occurred while authenticating with CRM provider. Please try again.": "An error occurred while authenticating with CRM provider. Please try again.", "An error occurred while desynchronizing": "An error occurred while desynchronizing", "An error occurred while fetching Hubspot contact lists": "An error occurred while fetching Hubspot contact lists", "An error occurred while fetching HubSpot contact lists": "An error occurred while fetching HubSpot contact lists", "An error occurred while saving global sync configuration": "An error occurred while saving global sync configuration", "An error occurred while synchronizing": "An error occurred while synchronizing", "An error occurred, please try again later": "An error occurred, please try again later", "Annually": "Annually", "Any new company created in Zeliq will be pushed to Hubspot, and the fields of this company will be updated in Hubspot if it is changed in Zeliq.": "Any new company created in ZELIQ will be pushed to Hubspot, and the fields of this company will be updated in Hubspot if it is changed in ZELIQ", "Any new company created in Zeliq will be pushed to HubSpot, and the fields of this company will be updated in HubSpot if it is changed in Zeliq.": "Any new company created in ZELIQ will be pushed to HubSpot, and the fields of this company will be updated in HubSpot if it is changed in ZELIQ", "Any new contact created in Zeliq will be pushed to Hubspot, and the fields of this contact will be updated in Hubspot if it is changed in Zeliq. If the company associated to the contact doesn’t exist in your Hubspot yet, company will also be created.": "Any new contact created in ZELIQ will be pushed to Hubspot, and the fields of this contact will be updated in Hubspot if it is changed in ZELIQ. If the company associated to the contact doesn’t exist in your Hubspot yet, company will also be created.", "Any new contact created in Zeliq will be pushed to HubSpot, and the fields of this contact will be updated in HubSpot if it is changed in Zeliq. If the company associated to the contact doesn’t exist in your HubSpot yet, company will also be created.": "Any new contact created in ZELIQ will be pushed to HubSpot, and the fields of this contact will be updated in HubSpot if it is changed in ZELIQ. If the company associated to the contact doesn’t exist in your HubSpot yet, company will also be created.", "API Keys": "API Keys", "Are you sure you want to disconnect {{serviceName}} from your organization ?": "Are you sure you want to disconnect {{serviceName}} from your organization ?", "Basic reporting": "Basic reporting", "Be a part of the co-creation program": "Be a part of the co-creation program", "Become a ZELIQ Co-Designer": "Become a ZELIQ Co-Designer", "Bidirectional": "Bidirectional", "Billed annually": "Billed annually", "Billed annually only": "Billed annually only", "Billed monthly": "Billed monthly", "Billed monthly or annually": "Billed monthly or annually", "Buy additional credits to boost your current plan and match your needs.": "Buy additional credits to boost your current plan and match your needs.", "Buying Intent (Soon)": "Buying Intent (Soon)", "By default Zeliq requires 3 criterions to create a contact : first name, last name and company name. If a contact doesn't have these 3 criterions it will be automatically excluded from the synchronisation.": "By default Zeliq requires 3 criterions to create a contact : first name, last name and company name. If a contact doesn't have these 3 criterions it will be automatically excluded from the synchronisation.", "call": "Call", "Call Answered": "Call Answered", "Call Hangup": "Call Hangup", "Call Missed": "Call Missed", "Call Ringing": "Call Ringing", "call-description": "push all phone calls made through <PERSON><PERSON><PERSON> to your HubSpot Account", "Cancel": "Cancel", "Cancel & close": "Cancel & close", "Change the language of the product": "Change the language of the product", "Changes saved successfully": "Changes saved successfully", "Check out our guides on our Help Center to learn more about automation and sequences in Zeliq.": "Check out our guides on our Help Center to learn more about automation and sequences in Zeliq.", "Check the Email Account's Health support article to": "Check the Email Account's Health support article to", "Check your location and time zone": "Check your location and time zone", "Choose an individual or team plan, and upgrade easily in 3 clicks.": "Choose an individual or team plan, and upgrade easily in 3 clicks.", "Choose the language used in the platform, including strategy generation and personality generation": "Choose the language used in the platform, including strategy generation and personality generation", "Choose your payment frequency": "Choose your payment frequency", "Coming soon!": "Coming soon!", "Company": "Company", "company": "company", "company-description": "Any new company created in Zeliq will be pushed to HubSpot, and the fields of this company will be updated in HubSpot if it is changed in Zeliq.", "Confirm this information": "Confirm this information", "Connect": "Connect", "Connect LinkedIn": "Connect LinkedIn", "Connect to Ringover": "Connect to Ringover", "Connect your CRM to Zeliq and start enriching your prospect database": "Connect your CRM to ZELIQ and start enriching your prospect database", "Connect your email account": "Connect your email account", "Connect Zeliq with linkedin.": "Connect ZELIQ with LinkedIn.", "Connected by": "Connected by", "Contact": "Contact", "contact": "contact", "Contact our Sales-rep": "Contact our Sales-rep", "Contact our Sales-reps": "Contact our Sales-reps", "Contact support": "Contact support", "contact-description": "Any new contact created in Zeliq will be pushed to HubSpot, and the fields of this contact will be updated in HubSpot if it is changed in Zeliq. If the company associated to the contact doesn’t exist in your HubSpot yet, company will also be created.", "Continue my free trial": "Continue my free trial", "Continue on free plan": "Continue on free plan", "Copy and paste each link in the matching Ringover's input_one": "Copy and paste the {{ count }} link in the matching Ring<PERSON>'s input", "Copy and paste each link in the matching Ringover's input_other": "Copy and paste the {{ count }} links in the matching Ring<PERSON>'s input", "Copy the API Key": "Copy the API Key", "Copy your signature from your email provider": "Copy your signature from your email provider", "CRM": "CRM", "CRM configurator cannot be loaded": "CRM configurator cannot be loaded", "CRM Enrichment (Soon)": "CRM Enrichment (Soon)", "CRM Settings": "CRM Settings", "CRM settings": "CRM Settings", "Current plan": "Current plan", "custom": "custom", "Custom fields (Soon)": "Custom fields (Soon)", "Custom name is required": "Custom name is required", "Daily Ramp-up": "Daily Ramp-up", "Data health center": "Data health center", "Dedicated Customer Success": "Dedicated Customer Success", "Define your mapping between Hubspot's and Zeliq's properties, and the type of synchronisation you want to have for each field. You'll also be able to add custom properties for properties that don't exist in Zeliq yet.": "Define your mapping between Hubspot's and ZELIQ's properties, and the type of synchronisation you want to have for each field. You'll also be able to add custom properties for properties that don't exist in ZELIQ yet.", "Define your mapping between HubSpot's and Zeliq's properties, and the type of synchronisation you want to have for each field. You'll also be able to add custom properties for properties that don't exist in Zeliq yet.": "Define your mapping between HubSpot's and ZELIQ's properties, and the type of synchronisation you want to have for each field. You'll also be able to add custom properties for properties that don't exist in ZELIQ yet.", "Define your push settings to indicate which entities can be created in your CRM if they don't exist yet.": "Define your push settings to indicate which entities can be created in your CRM if they don't exist yet.", "Delay between email sent": "Delay between email sent", "Dialer": "<PERSON><PERSON><PERSON>", "Disconnect": "Disconnect", "Disconnect {{serviceName}}": "Disconnect {{serviceName}}", "Disconnect your Gmail account from Zeliq": "Disconnect your {{emailProvider}} account from ZELIQ", "DNS settings": "DNS settings", "Downgrade plan": "Downgrade plan", "Downgrade to free plan": "Downgrade to free plan", "Download extension": "Download extension", "Download our chrome extension": "Download our chrome extension", "Edit for your default signature from Gmail": "Edit for your default signature from {{emailProvider}}", "Edit your account sender name directly within your Gmail's general settings": "Edit your account sender name directly within your {{emailProvider}}'s general settings", "Email": "Email", "email": "email", "Email enrichment": "Email enrichment", "Email signature": "Email signature", "email-description": "push all emails sent through Zeliq to your HubSpot Account", "Enable the Call Event webhook": "Enable the Call Event webhook", "Enabled": "Enabled", "Enabled by": "Enabled by", "Entities": "Entities", "entities": "Entities", "Error": "Error", "Error updating signature": "Error updating signature", "Error while updating the settings.": "Error while updating the settings.", "Error while updating the user.": "Error while updating the user.", "Everything in Free": "Everything in Free", "Everything in Starter": "Everything in Starter", "Exclusion rules": "Exclusion rules", "Failed to copy!": "Failed to copy!", "Feeling lost ?": "Feeling lost ?", "Feeling lost?": "Feeling lost?", "Field already mapped": "Field already mapped", "First name": "First name", "First name can't be empty": "First name can't be empty", "First name successfully saved": "First name successfully saved", "First select your type of synchronisation : you can synchronise your entire CRM, or only specific lists if you’ve already set them up in your Hubspot. You will be able to define exclusion rules during the second step of this process if you want to filter what you synchronise to Zeliq.": "First select your type of synchronisation : you can synchronise your entire CRM, or only specific lists if you’ve already set them up in your Hubspot. You will be able to define exclusion rules during the second step of this process if you want to filter what you synchronise to Zeliq.", "First select your type of synchronisation : you can synchronise your entire CRM, or only specific lists if you’ve already set them up in your HubSpot. You will be able to define exclusion rules during the second step of this process if you want to filter what you synchronise to Zeliq.": "First select your type of synchronisation : you can synchronise your entire CRM, or only specific lists if you’ve already set them up in your HubSpot. You will be able to define exclusion rules during the second step of this process if you want to filter what you synchronise to Zeliq.", "First select your type of synchronisation : you can synchronise your entire CRM, or only specific lists if you’ve already set them up in your Hubspot. You’ll then be able to define your push settings to indicate which entities can be created in your CRM, map your properties between Zeliq and Hubspot and set your exclusion rules if you want to filter what you synchronise to Zeliq.": "First select your type of synchronisation : you can synchronise your entire CRM, or only specific lists if you’ve already set them up in your Hubspot. You’ll then be able to define your push settings to indicate which entities can be created in your CRM, map your properties between Zeliq and Hubspot and set your exclusion rules if you want to filter what you synchronise to Zeliq.", "First select your type of synchronisation : you can synchronise your entire CRM, or only specific lists if you’ve already set them up in your HubSpot. You’ll then be able to define your push settings to indicate which entities can be created in your CRM, map your properties between Zeliq and HubSpot and set your exclusion rules if you want to filter what you synchronise to Zeliq.": "First select your type of synchronisation : you can synchronise your entire CRM, or only specific lists if you’ve already set them up in your HubSpot. You’ll then be able to define your push settings to indicate which entities can be created in your CRM, map your properties between Zeliq and HubSpot and set your exclusion rules if you want to filter what you synchronise to Zeliq.", "firstname": "firstname", "For now Zeliq doesn't allow the creation of custom status, but you'll be able to select multiple status from your Hubspot account and match it with a unique status in Zeliq. In this case, make sure to select your main status, which will be the value pushed to your Hubspot account when a synchronized lead is updated. Please make sure to map all of the status you use in Hubspot during this step": "For now Zeliq doesn't allow the creation of custom status, but you'll be able to select multiple status from your Hubspot account and match it with a unique status in Zeliq. In this case, make sure to select your main status, which will be the value pushed to your Hubspot account when a synchronized lead is updated. Please make sure to map all of the status you use in Hubspot during this step", "For now Zeliq doesn't allow the creation of custom status, but you'll be able to select multiple status from your HubSpot account and match it with a unique status in Zeliq. In this case, make sure to select your main status, which will be the value pushed to your HubSpot account when a synchronized lead is updated. Please make sure to map all of the status you use in HubSpot during this step": "For now Zeliq doesn't allow the creation of custom status, but you'll be able to select multiple status from your HubSpot account and match it with a unique status in Zeliq. In this case, make sure to select your main status, which will be the value pushed to your HubSpot account when a synchronized lead is updated. Please make sure to map all of the status you use in HubSpot during this step", "For now Zeliq doesn't allow the creation of custom status, but you'll be able to select multiple status from your Hubspot account and match it with a unique status in Zeliq. In this case, make sure to select your main status, which will be the value pushed to your Hubspot account when a synchronized lead is updated. Please make sure to map all of the status you use in Hubspot during this step.": "For now Zeliq doesn't allow the creation of custom status, but you'll be able to select multiple status from your Hubspot account and match it with a unique status in Zeliq. In this case, <1>make sure to select your main status</1>, which will be the value pushed to your Hubspot account when a synchronized lead is updated. <1>Please make sure to map all of the status you use in Hubspot during this step.</1>", "For now Zeliq doesn't allow the creation of custom status, but you'll be able to select multiple status from your HubSpot account and match it with a unique status in Zeliq. In this case, make sure to select your main status, which will be the value pushed to your HubSpot account when a synchronized lead is updated. Please make sure to map all of the status you use in HubSpot during this step.": "For now Zeliq doesn't allow the creation of custom status, but you'll be able to select multiple status from your HubSpot account and match it with a unique status in Zeliq. In this case, <1>make sure to select your main status</1>, which will be the value pushed to your HubSpot account when a synchronized lead is updated. <1>Please make sure to map all of the status you use in HubSpot during this step.</1>", "free": "free", "Free account limit reached": "Free account limit reached", "Free trial ended": "Free trial ended", "From": "From", "Gamification leaderboard (Soon)": "Gamification leaderboard (Soon)", "GDPR": "GDPR", "Generated on {{date}}": "Generated on {{date}}", "Get API key": "Get API key", "Global sync": "Global sync", "How does it work?": "How does it work?", "hubspot": "hubspot", "Hubspot field and synchronization rule are required": "Hubspot field and synchronization rule are required", "HubSpot field and synchronization rule are required": "HubSpot field and synchronization rule are required", "Hubspot field is required": "Hubspot field is required", "HubSpot field is required": "HubSpot field is required", "Hubspot field, Zeliq field and synchronization rule are required": "Hubspot field, ZELIQ field as well as synchronization rule are required", "HubSpot field, Zeliq field and synchronization rule are required": "HubSpot field, ZELIQ field as well as synchronization rule are required", "Hubspot to Zeliq": "Hubspot to ZELIQ", "HubSpot to Zeliq": "HubSpot to ZELIQ", "I want to stay on the starter plan": "I want to stay on the starter plan", "If you want to edit your sender name :": "If you want to edit your sender name :", "If you want to modify this signature :": "If you want to modify this signature :", "In sync": "In sync", "Includes": "Includes", "Individual plan": "Individual plan", "Insert your signature here": "Insert your signature here", "Integrations": "Integrations", "Invoice & billing": "Invoice & billing", "Invoices & Billing": "Invoices & Billing", "Job Change Alert / Job Posting (Soon)": "Job Change Alert / Job Posting (Soon)", "Join us": "Join us", "Keep going on starter plan": "Keep going on starter plan", "Language": "Language", "Last name": "Last name", "Last name can't be empty": "Last name can't be empty", "Last name successfully saved": "Last name successfully saved", "lastname": "lastname", "Launch sync": "Launch sync", "Leads autopilot generator (Soon)": "Leads autopilot generator (Soon)", "Leads personality analysis (1 Credit)": "Leads personality analysis (1 Credit)", "Leads portfolio management": "Leads portfolio management", "Leads pre-analysis": "Leads pre-analysis", "Leads warming & Sequences": "Leads warming & Sequences", "learn more": "learn more", "Legals": "Legals", "Link the email address you want to use for sending sequences.": "Link the email address you want to use for sending sequences.", "Link Webhooks": "Link webhooks", "linkedIn": "linkedIn", "LinkedIn Extension": "LinkedIn Extension", "Links to copy": "Links to copy", "list_one": "{{count}} list", "list_other": "{{count}} lists", "Localization": "Localization", "Location": "Location", "Looks like you did not set up any sender name in your gmail account": "Looks like you did not set up any sender name in your gmail account", "Manage all of your integrations": "Manage all of your integrations", "Manage members": "Manage members", "manage plan": "manage plan", "Manage your plan, billing and payment methods. You can also add more credits here.": "Manage your plan, billing and payment methods. You can also add more credits here.", "Mandatory fields should only mapped with Hubspot to Zeliq or bidirectional rule": "Mandatory fields should only mapped with Hubspot to ZELIQ or bidirectional rule", "Mandatory fields should only mapped with HubSpot to Zeliq or bidirectional rule": "Mandatory fields should only mapped with HubSpot to ZELIQ or bidirectional rule", "Mapping": "Mapping", "Mappings rules": "Mappings rules", "Maximum {{maximum}}": "Maximum {{maximum}}", "Maximum 400": "Maximum 400", "Minimum value is 1": "Minimum value is 1", "month": "month", "Monthly": "Monthly", "Multichannel outreach": "Multichannel outreach", "Name": "Name", "Navigate to the <link>webhooks page</link>": "Navigate to the <link>webhooks page</link>", "Navigate to the webhooks page": "Navigate to the <link>webhooks page</link>", "Need help ?": "Need help ?", "Need more credits?": "Need more credits?", "Next": "Next", "No commitment period": "No commitment period", "No email aliases found": "No email aliases found", "No sender name found": "No sender name found", "No signature found": "No signature found", "None": "None", "Not in sync": "Not in sync", "note": "Note", "note-description": "push all notes taken into Zeliq to your HubSpot account", "On": "On", "on": "on", "On demand": "On demand", "On Starter plan, you’ll access to all features: unlimited sequences, advanced search filters and many more...": "On Starter plan, you’ll access to all features: unlimited sequences, advanced search filters and many more...", "On-demand": "On-demand", "Only {{ days }} Days Left, until the end of the trial period": "Only {{ days }} Days Left, until the end of the trial period", "Only {{days}} Days Left, until the end of the trial period_one": "Only {{days}} Day Left, until the end of the trial period", "Only {{days}} Days Left, until the end of the trial period_other": "Only {{days}} Days Left, until the end of the trial period", "Only {{days}} days remaining on your Starter Plan trial_one": "Only {{days}} day remaining on your Starter Plan trial", "Only {{days}} days remaining on your Starter Plan trial_other": "Only {{days}} days remaining on your Starter Plan trial", "Only {{nbDays}} days left until the end of your free trial!": "Only {{nbDays}} days left until the end of your free trial!", "Only {{nbDays}} days left until the end of your free trial!_one": "Only {{nbDays}} day left until the end of your free trial!", "Only {{nbDays}} days left until the end of your free trial!_other": "Only {{nbDays}} days left until the end of your free trial!", "Only {{nbDays}} days remaining on your Starter Plan trial_one": "Only {{nbDays}} days remaining on your Starter Plan trial_one", "Only {{nbDays}} days remaining on your Starter Plan trial_other": "Only {{nbDays}} days remaining on your Starter Plan trial_other", "Open Outlook settings": "Open Outlook settings", "Opt-out link": "Opt-out link", "optional": "optional", "Organization info": "Organization info", "Organization name": "Organization name", "Paste the API in the input below": "Paste the API in the input below", "Permission Profiles": "Permission Profiles", "Phone": "Phone", "phone": "phone", "Phone number enrichment (1 Credit)": "Phone number enrichment (1 Credit)", "Phone number enrichment (Unlimited)": "Phone number enrichment (Unlimited)", "Plan": "Plan", "Plans": "Plans", "Platform language": "Platform language", "Please connect your CRM first": "Please connect your CRM first", "Please contact our support team for assistance. We apologize for any inconvenience this may have caused. Our support team is ready to help resolve the issue promptly.": "Please contact our support team for assistance. We apologize for any inconvenience this may have caused. Our support team is ready to help resolve the issue promptly.", "Please provide a Ringover API key": "Please provide a Ringover API key", "Please verify the information of our exclusion rules to validate the configuration.": "Please verify the information of our exclusion rules to validate the configuration.", "Previous": "Previous", "Pricing plan & settings": "Pricing plan & settings", "Privacy policy": "Privacy policy", "Profile": "Profile", "Profile info": "Profile info", "Profile successfully saved": "Profile successfully saved", "Push data": "Push data", "Push only fields": "Push only fields", "Push settings": "Push settings", "push_one": "{{count}} push", "push_other": "{{count}} push", "Ramp-Up value": "Ramp-Up value", "Reconnect your Gmail account to Zeliq": "Reconnect your {{emailProvider}} account to ZELIQ", "Refresh": "Refresh", "Refresh aliases": "Refresh aliases", "Refresh cookies": "Refresh cookies", "Remove": "Remove", "Remove {{domainName}}": "Remove {{domainName}}", "Remove account": "Remove account", "Remove multiples leads from sequence_one": "Remove {{ count }} lead from sequence", "Remove multiples leads from sequence_other": "Remove {{ count }} leads from sequence", "Remove your Linkedin": "Remove your LinkedIn", "Renew automatically every month": "Renew automatically every month", "Renew automatically every year": "Renew automatically every year", "ringover": "ringover", "Ringover was connected with success": "<PERSON><PERSON> was connected with success", "rule_one": "{{count}} rule", "rule_other": "{{count}} rules", "Safety settings": "Safety settings", "Save": "Save", "Save changes": "Save changes", "Search leads over 1.8B people": "Search leads over 1.8B people", "See more details": "See more details", "See the Documentation API for setup guidance.": "See the Documentation API for setup guidance.", "Select a country": "Select one country", "Select a field": "Select a field", "Select a time zone": "Select a time zone", "Select a value": "Select a value", "Select data": "Select data", "Select data to sync from {{integrationServiceName}} to Zeliq": "Select data to sync from {{integrationServiceName}} to ZELIQ", "Select plan": "Select plan", "Selective sync": "Selective sync", "Set a limit per day": "Set a limit per day", "Set up your synchronisation": "Set up your synchronisation", "Set up your synchronization": "Set up your synchronization", "Settings": "Settings", "settings": "settings", "Settings has been updated": "Settings has been updated", "Signature is required": "Signature is required", "Signature is too large": "Signature is too large", "Signature updated successfully": "Signature updated successfully", "Something went wrong": "Something went wrong", "Standard fields": "Standard fields", "starter": "starter", "Starting amount": "Starting amount", "Status": "Status", "Sync all data (default)": "Sync all data (default)", "Sync data from {{integrationServiceName}}": "Sync data from {{integrationServiceName}}", "Sync in progress": "Sync in progress", "Synchronisation type": "Synchronisation type", "Synchronization data": "Synchronization data", "Synchronization rule cannot be set to none for mandatory fields": "Synchronization rule cannot be set to none for mandatory fields", "Synchronization rule is required": "Synchronization rule is required", "Synchronization rules": "Synchronization rules", "Synchronization type": "Synchronization type", "Tasks Assignation": "Tasks Assignation", "Team plan": "Team plan", "Teams management": "Teams management", "Template builder": "Template builder", "Term of use": "Term of use", "The api key is copied to your clipboard": "The api key is copied to your clipboard", "The current plan of your organization": "The current plan of your organization", "The email used to log in, and receive information & notifications": "The email used to log in, and receive information & notifications", "The free period during which you used the starter plan is over.": "The free period during which you used the starter plan is over.", "The name of your organization": "The name of your organization", "The name others in your team will see you as": "The name others in your team will see you as", "The user is not currently logged into LinkedIn. Please log in to your LinkedIn account in order to synchronize your profile. This will ensure that your account details are up to date and synchronized properly with the desired service or application.": "The user is not currently logged into LinkedIn. Please log in to your LinkedIn account in order to synchronize your profile. This will ensure that your account details are up to date and synchronized properly with the desired service or application.", "There was an error while disconnecting Ringover. Please, try again": "There was an error while disconnecting <PERSON><PERSON>. Please, try again", "There was an issue while connecting with {{serviceName}}": "There was an issue while connecting with {{serviceName}}", "There was an issue while connecting with Ringover. Please, try again": "There was an issue while connecting with <PERSON><PERSON>. Please, try again", "There was an issue while creating the billing portal session": "There was an issue while creating the billing portal session", "There was an issue while creating the checkout session": "There was an issue while creating the checkout session", "These fields can only be mapped from Zeliq to Hubspot": "These fields can only be mapped from ZELIQ to Hubspot", "These fields can only be mapped from Zeliq to HubSpot": "These fields can only be mapped from ZELIQ to HubSpot", "This email signature will be automatically added at the end of all your outgoing messages. This signature refers to your main signature in your Gmail account.": "This email signature will be automatically added at the end of all your outgoing messages. This signature refers to your main signature in your {{emailProvider}} account.", "This fields is required": "This fields is required", "This information is required by LinkedIn for seamless integration.": "This information is required by LinkedIn for seamless integration.", "Time zone": "Time zone", "To connect your LinkedIn account, please download the Chrome extension provided. This extension enables seamless integration between your browser and LinkedIn, allowing for a smoother and more efficient user experience.": "To connect your LinkedIn account, please download the Chrome extension provided. This extension enables seamless integration between your browser and LinkedIn, allowing for a smoother and more efficient user experience.", "To find this API key, please follow the guidelines below": "To find this API key, please follow the guidelines below", "To integrate Ringover within Zeliq we need an API Key that is available in your Ringover account. Please follow the guidelines below.": "To integrate Ringover within Zeliq we need an API Key that is available in your Ringover account. Please follow the guidelines below.", "Total of email send per day": "Total of email send per day", "Turn off your synchronization": "Turn off your synchronization", "Turn on your synchronization": "Turn on your synchronization", "Unable to connect {{integrationServiceName}}": "Enable to connect {{integrationServiceName}}", "Unmapped property": "Unmapped property", "Unsubscribe via opt-out link": "Unsubscribe via opt-out link", "Upgrade my account": "Upgrade my account", "Upgrade now to get more features": "Upgrade now to get more features", "Upgrade now to not lose your features": "Upgrade now to not lose your features", "Upgrade now to retain access to all features": "Upgrade now to retain access to all features", "Upgrade now to Starter Plan and sweeten your Sales experience": "Upgrade now to Starter Plan and sweeten your Sales experience", "Upgrade plan": "Upgrade plan", "Use exclusion rules to filter the contacts or companies that you don't want to synchronize in Zeliq.": "Use exclusion rules to filter the contacts or companies that you don't want to synchronize in ZELIQ.", "Used to manage your outreach": "Used to manage your outreach", "Used to secure your account and contact you if necessary": "Used to secure your account and contact you if necessary", "User already integrated": "User already integrated", "Value should be under {{max}}": "Value should be under {{max}}", "Visit help center": "Visit help center", "Want more features?": "Want more features?", "Webhook copied": "Webhook copied", "Webhook copied: ": "Webhook copied", "When desynchronize a CRM account, Zeliq will stop syncing but will keep all data associated.": "When desynchronize a CRM account, ZELIQ will stop syncing but will keep all data associated.", "When removing a CRM account, Zeliq will stop syncing all data, leading to the deletion of all contacts and data associated.": "When removing a CRM account, ZELIQ will stop syncing all data, leading to the deletion of all contacts and data associated.", "When removing an email account, Zeliq will stop syncing your email and calendar account, leading to the deletion of all future emails and calendar events.": "When removing an email account, <PERSON><PERSON><PERSON> will stop syncing your email and calendar account, leading to the deletion of all future emails and calendar events.", "Workspace": "Workspace", "You are about to remove your linkedin account. Are you sure you want to proceed with the deletion?": "You are about to remove your LinkedIn account. Are you sure you want to proceed with the deletion?", "You are not connect to Linkedin": "You are not connect to LinkedIn", "You can edit your exclusion rules to filter what you synchronize from Hubspot to Zeliq. Adding exclusion rules won’t delete entities previously synchronized to Zeliq, but taking off exclusion rules will synchronize entities previously filtered.": "You can edit your exclusion rules to filter what you synchronize from Hubspot to ZELIQ. Adding exclusion rules won’t delete entities previously synchronized to ZELIQ, but taking off exclusion rules will synchronize entities previously filtered.", "You can edit your exclusion rules to filter what you synchronize from HubSpot to Zeliq. Adding exclusion rules won’t delete entities previously synchronized to Zeliq, but taking off exclusion rules will synchronize entities previously filtered.": "You can edit your exclusion rules to filter what you synchronize from HubSpot to ZELIQ. Adding exclusion rules won’t delete entities previously synchronized to ZELIQ, but taking off exclusion rules will synchronize entities previously filtered.", "You can edit your mapping at any time but changes done will not be retroactive, they will only apply to leads synchronized after you make that change.": "You can edit your mapping at any time but changes done will not be retroactive, they will only apply to leads synchronized after you make that change.", "You can edit your push settings at any time to define which entities can be created in your Hubspot. Entities previously created won’t be deleted if you turn off push settings.": "You can edit your push settings at any time to define which entities can be created in your Hubspot. Entities previously created won’t be deleted if you turn off push settings.", "You can edit your push settings at any time to define which entities can be created in your HubSpot. Entities previously created won’t be deleted if you turn off push settings.": "You can edit your push settings at any time to define which entities can be created in your HubSpot. Entities previously created won’t be deleted if you turn off push settings.", "You can keep your starter plan by simply adding a payment method, but you can also upgrade to a team plan, the custom plan. You can also continue to use Zeliq for free, but will no longer have access to sequences and outreach.": "You can keep your starter plan by simply adding a payment method, but you can also upgrade to a team plan, the custom plan. You can also continue to use Zeliq for free, but will no longer have access to sequences and outreach.", "You can only connect one CRM at a time": "You can only connect one CRM at a time", "You can synchronize your entire CRM with Zeliq, or only specific lists if you've already set them up in your Hubspot. Whatever the type of synchronisation you choose, you'll be able to add exclusion rules before launching your first synchronisation.": "You can synchronize your entire CRM with Zeliq, or only specific lists if you've already set them up in your Hubspot. Whatever the type of synchronisation you choose, you'll be able to add exclusion rules before launching your first synchronisation.", "You can synchronize your entire CRM with Zeliq, or only specific lists if you've already set them up in your HubSpot. Whatever the type of synchronisation you choose, you'll be able to add exclusion rules before launching your first synchronisation.": "You can synchronize your entire CRM with Zeliq, or only specific lists if you've already set them up in your HubSpot. Whatever the type of synchronisation you choose, you'll be able to add exclusion rules before launching your first synchronisation.", "You can use exclusion rules to exclude data that you don't want synchronized from your organization's {{integrationServiceName}} account to Zeliq": "You can use exclusion rules to exclude data that you don't want synchronized from your organization's {{integrationServiceName}} account to <PERSON><PERSON>q", "You can use exclusion rules to exclude data that you don't want synchronized to your organization's {{integrationServiceName}} account": "You can use exclusion rules to exclude data that you don't want synchronized to your organization's {{integrationServiceName}} account", "You can’t connect multiple VoIP providers at the same time": "You can’t connect multiple VoIP providers at the same time", "You should connect to Hubspot first": "You should connect to Hubspot first", "You should connect to HubSpot first": "You should connect to HubSpot first", "You’re about to remove multiple contacts from this sequence. Are you sure?_one": "You're about to remove {{ count }} lead from this sequence. Are you sure?", "You’re about to remove multiple contacts from this sequence. Are you sure?_other": "You're about to remove {{ count }} leads from this sequence. Are you sure?", "You’re about to remove this email account. You can’t undo this action. Are you sure you want to continue?": "You’re about to remove this email account. You can’t undo this action. Are you sure you want to continue?", "You’re about to remove this email domain. You can’t undo this action. Are you sure you want to continue?": "You’re about to remove this email domain. You can’t undo this action. Are you sure you want to continue?", "Your account sender will appear on all your outgoing e-mails. This account sender refers to the one in your Gmail account.": "Your account sender will appear on all your outgoing e-mails. This account sender refers to the one in your {{emailProvider}} account.", "Your Aircall company account is now linked to Zeliq !": "Your Aircall company account is now linked to ZELIQ !", "Your API Key": "Your API Key", "Your free trial ends in less than {{nbDays}} days, so think about upgrading now so you don't lose the chance to make sequences or outreach.": "Your free trial ends in less than {{nbDays}} days, so think about upgrading now so you don't lose the chance to make sequences or outreach.", "Your free trial has ended!": "Your free trial has ended!", "Zeliq exclusion rules": "ZELIQ exclusion rules", "Zeliq properties": "ZELIQ properties", "Zeliq property": "ZELIQ property", "Zeliq Score Engine (Soon)": "ZELIQ Score Engine (Soon)", "Zeliq status": "ZELIQ status", "Zeliq to Hubspot": "ZELIQ to Hubspot", "Zeliq to HubSpot": "ZELIQ to HubSpot"}