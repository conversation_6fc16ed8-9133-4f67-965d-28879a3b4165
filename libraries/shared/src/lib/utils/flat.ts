// Function to flatten an object
export function flattenObject(obj: Record<string, any>): Record<string, any> {
  const flattened: Record<string, any> = {}

  function flattenHelper(current: Record<string, any>, path: string[] = []) {
    for (const key in current) {
      if (typeof current[key] === 'object' && current[key] !== null) {
        flattenHelper(current[key], [...path, key])
      } else {
        flattened[path.concat(key).join('.')] = current[key]
      }
    }
  }

  flattenHelper(obj)

  return flattened
}

// Function to unflatten an object
export function unflattenObject(obj: Record<string, any>): Record<string, any> {
  const unflattened: Record<string, any> = {}

  for (const key in obj) {
    const keys = key.split('.')
    let current = unflattened

    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {}
      }
      current = current[keys[i]]
    }

    current[keys[keys.length - 1]] = obj[key]
  }

  return unflattened
}
