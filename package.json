{"name": "getheroes-dashboard", "version": "1.78.0", "engines": {"node": ">22"}, "license": "MIT", "scripts": {"start": "npx nx run-many --parallel --target=serve --projects=frontend,api,websocket", "lint": "npx nx run-many --parallel --target=lint --projects=frontend,api,ui,ui-business", "build": "npx nx run-many --parallel --target=build --projects=frontend,ui,web-extension,api", "test:packages": "npx nx run-many --parallel --target=test --projects=frontend,api,ui,ui-business", "typeorm:migration:generate": "npx nx generate-migration api", "typeorm:migration:run": "npx nx run-migration api", "typeorm:migration:revert": "npx nx revert-migration api", "typeorm:migration:create": "npx nx create-migration api", "storybook": "npx nx storybook frontend", "build-storybook:frontend": "NODE_OPTIONS=\"--openssl-legacy-provider --max_old_space_size=8192\" npx nx build-storybook frontend", "test": "npx nx test frontend", "test:extension": "npx nx test web-extension", "test:chrome-extension": "npx nx test chrome-extension", "test-storybook": "npx nx test-storybook frontend", "test-storybook:reporter": "yarn run storybook && yarn run test-storybook --json --outputFile scripts/storybook-reporter/snapshot-reporter.json ; yarn run storybook-reporter", "storybook-reporter": "cd ./scripts/storybook-reporter && node format-storybook-reporter.js", "prepare": "husky install", "stylelint": "npx stylelint \"packages/frontend/src/**/*.{scss, css, sass, less}\" --fix --cache", "i18n:scan": "npx i18next-scanner --config packages/frontend/i18next-scanner.config.js 'packages/frontend/src/**/*.{js,jsx,ts,tsx}'", "extension:dev": "npx nx dev web-extension", "extension:build": "npx nx build web-extension", "extension:build:dev": "npx nx build:dev web-extension", "chrome-extension:dev": "npx nx dev chrome-extension", "chrome-extension:build": "npx nx build chrome-extension", "chrome-extension:build:dev": "npx nx build:dev chrome-extension", "install-scripts": "npx nx install-scripts scripts", "create-release": "cd ./scripts/gitlab && yarn run create-release", "deploy": "cd ./scripts/gitlab && yarn run deploy", "storybook:ui": "npx nx storybook ui", "build-storybook:ui": "npx nx build-storybook ui", "build:ui": "npx nx build ui", "lint:ui": "npx nx lint ui", "test:ui": "npx nx test ui", "chromatic:frontend": "npx chromatic --build-script-name=\"build-storybook:frontend\" --external=\"packages/frontend/**\" --allow-console-errors", "chromatic:ui": "npx chromatic --build-script-name=\"build-storybook:ui\" --external=\"libraries/frontend/ui-components/**\"", "storybook:ui-business": "npx nx storybook ui-business", "build-storybook:ui-business": "npx nx build-storybook ui-business", "build:ui-business": "npx nx build ui-business", "lint:ui-business": "npx nx lint ui-business", "test:ui-business": "npx nx test ui-business", "chromatic:ui-business": "npx chromatic --build-script-name=\"build-storybook:ui-business\" --external=\"libraries/frontend/ui-business-components/**\"", "coverage": "nyc report --reporter=lcov --report-dir=./coverage", "ciruclar-dependencies": "madge --extensions js,jsx,ts,tsx --circular --ts-config=packages/frontend/tsconfig.json packages/frontend/src"}, "private": true, "dependencies": {"@automapper/classes": "^8.8.1", "@automapper/core": "^8.8.1", "@automapper/nestjs": "^8.8.1", "@automock/adapters.nestjs": "^2.1.0", "@automock/jest": "^2.1.0", "@aws-sdk/client-apigatewaymanagementapi": "^3.749.0", "@aws-sdk/client-cognito-identity-provider": "^3.749.0", "@aws-sdk/client-s3": "^3.749.0", "@aws-sdk/client-sns": "^3.749.0", "@aws-sdk/client-ssm": "^3.749.0", "@aws-sdk/s3-request-presigner": "^3.749.0", "@azure/msal-node": "^2.6.1", "@bull-board/api": "^6.9.6", "@bull-board/express": "^6.9.6", "@datadog/browser-logs": "^6.6.4", "@datadog/browser-rum": "^4.42.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/utilities": "^3.2.2", "@elastic/elasticsearch": "7.13", "@flatfile/api": "^1.15.3", "@flatfile/hooks": "^1.6.0", "@flatfile/react": "^7.13.5", "@floating-ui/react": "^0.27.7", "@headlessui/react": "^1.7.17", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.9.1", "@hubspot/api-client": "^10.2.0", "@langchain/community": "^0.2.33", "@langchain/openai": "^0.0.34", "@lexical/file": "0.14.5", "@lexical/headless": "0.14.5", "@lexical/react": "0.14.5", "@microsoft/fetch-event-source": "^2.0.1", "@microsoft/microsoft-graph-client": "^3.0.7", "@nestjs/bull": "^11.0.2", "@nestjs/common": "11.0.10", "@nestjs/config": "^4.0.0", "@nestjs/core": "11.0.10", "@nestjs/cqrs": "^11.0.2", "@nestjs/elasticsearch": "^11.0.0", "@nestjs/event-emitter": "^3.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "11.0.10", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "11.0.3", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "@prismatic-io/embedded": "^2.12.0", "@reduxjs/toolkit": "^1.9.1", "@segment/analytics-next": "^1.77.0", "@sendgrid/mail": "^7.7.0", "@slack/web-api": "^7.8.0", "@taskforcesh/bullmq-pro": "^7.28.0", "@taskforcesh/nestjs-bullmq-pro": "^3.0.0", "@types/react-vertical-timeline-component": "^3.3.6", "@types/twilio": "^3.19.3", "@types/webextension-polyfill": "^0.12.3", "add": "^2.0.6", "ag-grid-community": "33.2.3", "ag-grid-enterprise": "33.2.3", "ag-grid-react": "33.2.3", "aircall-everywhere": "^1.8.0", "api": "^5.0.8", "aws-amplify": "^6.13.0", "aws-crt": "^1.25.3", "bull": "^4.16.5", "canvas-confetti": "^1.9.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "clsx": "^1.2.1", "core-js": "3.40.0", "crypto-js": "^4.2.0", "csv-parser": "^3.2.0", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dd-trace": "^5.47.0", "deepl-node": "^1.16.0", "dompurify": "^3.2.4", "express-basic-auth": "^1.2.1", "googleapis": "^118.0.0", "helmet": "^6.0.1", "html-to-text": "^9.0.5", "i18n-iso-countries": "^7.13.0", "i18next": "^23.5.1", "i18next-browser-languagedetector": "^7.1.0", "i18next-http-backend": "^2.6.2", "iconoir-react": "^7.10.1", "ioredis": "^5.5.0", "isbot": "^5.1.26", "jsonpath": "^1.1.1", "jwks-rsa": "^3.1.0", "langchain": "^0.1", "langsmith": "^0.1.61", "lexical": "0.14.5", "libphonenumber-js": "^1.11.20", "lodash": "^4.17.21", "luxon": "^3.5.0", "modern-diacritics": "^2.3.1", "motion": "^12.4.3", "nestjs-cls": "^5.3.0", "nestjs-ddtrace": "^6.0.0", "nestjs-pino": "^4.3.0", "nodemailer": "^6.10.0", "object-hash": "^3.0.0", "openai": "^3.3.0", "papaparse": "^5.5.2", "parse-full-name": "^1.2.6", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "peopledatalabs": "^5.1.0", "pg": "^8.13.3", "pg-cursor": "^2.12.3", "pg-format": "^1.0.4", "pg-query-stream": "^4.7.3", "pino-http": "^8.5.0", "rc-slider": "^10.6.2", "react": "18.3.1", "react-accessible-accordion": "^5.0.0", "react-circle-flags": "^0.0.20", "react-datepicker": "^4.11.0", "react-dom": "18.3.1", "react-draggable": "^4.4.6", "react-error-boundary": "^4.0.10", "react-hook-form": "7.54.2", "react-i18next": "^13.2.2", "react-infinite-scroll-component": "^6.1.0", "react-multi-email": "^1.0.25", "react-paginate": "^8.3.0", "react-phone-number-input": "^3.4.11", "react-pin-field": "^3.1.5", "react-redux": "^8.0.5", "react-router-dom": "^6.26.2", "react-scroll-sync": "^0.11.2", "react-select": "^5.10.0", "react-simple-wysiwyg": "^3.2.0", "react-spinners": "^0.13.8", "react-toastify": "10.0.6", "react-use-gesture": "^9.1.3", "react-use-intercom": "^5.4.3", "react-use-websocket": "^4.13.0", "react-vertical-timeline-component": "^3.6.0", "react-virtuoso": "^4.12.5", "reflect-metadata": "^0.1.13", "ringover-sdk": "^1.1.3", "rxjs": "^7.8.1", "sanitize-html": "^2.14.0", "stripe": "^15.6.0", "tinyld": "^1.3.4", "tslib": "2.8.1", "twilio": "^5.4.4", "typeorm": "^0.3.11", "typeorm-naming-strategies": "^4.1.0", "use-deep-compare-effect": "^1.8.1", "usehooks-ts": "^2.9.1", "uuid": "^9.0.0", "webextension-polyfill": "^0.12.0", "whois": "^2.14.2", "yjs": "^13.6.23", "yup": "^1.6.1", "yup-phone-lite": "^2.0.1"}, "devDependencies": {"@babel/core": "^7.26.9", "@commitlint/cli": "^17.4.0", "@commitlint/config-conventional": "^17.4.0", "@cucumber/cucumber": "^10.9.0", "@faker-js/faker": "^7.6.0", "@jest/globals": "^29.7.0", "@laynezh/vite-plugin-lib-assets": "^0.6.1", "@microsoft/microsoft-graph-types": "^2.40.0", "@nestjs/schematics": "11.0.1", "@nestjs/testing": "11.0.10", "@nx/cypress": "20.4.4", "@nx/devkit": "20.4.4", "@nx/eslint": "20.4.4", "@nx/eslint-plugin": "20.4.4", "@nx/jest": "20.4.4", "@nx/js": "20.4.4", "@nx/nest": "20.4.4", "@nx/node": "20.4.4", "@nx/playwright": "20.4.4", "@nx/react": "20.4.4", "@nx/rspack": "20.4.4", "@nx/storybook": "20.4.4", "@nx/vite": "20.4.4", "@nx/web": "20.4.4", "@nx/webpack": "20.4.4", "@nx/workspace": "20.4.4", "@plasmohq/chrome-webstore-api": "^2.11.0", "@playwright/test": "^1.50.1", "@storybook/addon-actions": "8.5.6", "@storybook/addon-backgrounds": "8.5.6", "@storybook/addon-essentials": "8.5.6", "@storybook/addon-interactions": "8.5.6", "@storybook/addon-links": "8.5.6", "@storybook/core-common": "8.5.6", "@storybook/core-server": "8.5.6", "@storybook/react": "8.5.6", "@storybook/react-vite": "8.5.6", "@storybook/test": "8.5.6", "@storybook/test-runner": "^0.19.1", "@svgr/webpack": "^6.1.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "16.2.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.6.1", "@types/aws-lambda": "^8.10.147", "@types/canvas-confetti": "^1.9.0", "@types/chrome": "^0.0.306", "@types/cron": "^2.4.3", "@types/crypto-js": "^4.2.2", "@types/css-modules": "^1.0.5", "@types/dompurify": "3.2.0", "@types/draft-convert": "^2.1.8", "@types/draft-js": "^0.11.10", "@types/eslint__eslintrc": "^2.1.2", "@types/eslint__js": "^8.42.3", "@types/html-to-text": "^9.0.4", "@types/jest": "^29.5.14", "@types/jsonpath": "^0.2.0", "@types/katex": "^0.16.0", "@types/node": "^22.5.5", "@types/object-hash": "^3.0.6", "@types/papaparse": "^5.3.15", "@types/parse-full-name": "^1.2.5", "@types/postcss-custom-properties": "^12.0.0", "@types/react": "^18.3.3", "@types/react-datepicker": "^4.11.2", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "5.3.3", "@types/react-scroll-sync": "^0.9.0", "@types/react-table": "^7.7.20", "@types/sanitize-html": "^2.13.0", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "@vitejs/plugin-react": "4.3.4", "@vitejs/plugin-react-swc": "^3.7.2", "@vitest/coverage-v8": "2.0.5", "@vitest/ui": "2.0.5", "autoprefixer": "^10.4.20", "babel-jest": "29.7.0", "babel-loader": "^8.3.0", "commitizen": "^4.3.1", "concurrently": "^7.6.0", "cross-env": "^7.0.3", "cypress": "13.13.2", "cz-conventional-changelog": "^3.3.0", "esbuild": "^0.23.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-css-modules": "^2.12.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "5.1.0", "eslint-plugin-react-refresh": "^0.4.14", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-unused-imports": "^4.1.4", "expect": "29.7.0", "globals": "^15.14.0", "history": "^5.3.0", "husky": "^8.0.3", "i18next-scanner": "^4.6.0", "i18next-scanner-typescript": "^1.2.1", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-environment-node": "^29.7.0", "jest-junit": "^16.0.0", "jsdom": "22.1.0", "json2csv": "^6.0.0-alpha.2", "latest": "^0.2.0", "lint-staged": "^11.2.3", "magic-string": "^0.30.10", "msw": "^2.7.0", "msw-storybook-addon": "^2.0.4", "nx": "20.8.1", "pino-pretty": "^11.0.0", "postcss": "8.5.2", "postcss-custom-properties": "^14.0.4", "postcss-prefix-selector": "^2.1.0", "postcss-rem-to-pixel": "^4.1.2", "postcss-scope": "^1.7.4", "postcss-scss": "^4.0.9", "prettier": "^3.5.1", "prettier-eslint": "^16.3.0", "react-refresh": "~0.14.0", "rimraf": "^6.0.1", "sass": "1.55.0", "storybook": "8.5.6", "storybook-mock-date-decorator": "^2.0.6", "storybook-react-i18next": "^3.2.1", "stylelint": "^14.0.1", "stylelint-config-css-modules": "^2.2.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^6.0.0", "stylelint-config-standard": "^34.0.0", "tailwindcss": "3.4.3", "ts-jest": "29.2.5", "ts-loader": "^9.5.1", "ts-node": "10.9.2", "tsconfig-paths": "^4.2.0", "tslib": "2.8.1", "typeorm-fixtures-cli": "^3.0.2", "typescript": "5.7.3", "typescript-eslint": "^8.20.0", "url-loader": "^4.1.1", "vite": "6.0.15", "vite-plugin-chunk-split": "^0.5.0", "vite-plugin-dts": "^4.5.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-istanbul": "^5.0.0", "vite-plugin-static-copy": "^2.2.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "2.0.5", "vitest-dom": "^0.1.0"}, "volta": {"node": "22.15.0", "yarn": "4.5.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "msw": {"workerDirectory": ["packages/frontend/public"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"packages/chrome-extension/src/**/*.{js,jsx,ts,tsx,css,scss,md}": ["npx stylelint \"packages/chrome-extension/src/**/*.{scss, css, sass, less}\" --fix --cache", "npx nx lint chrome-extension", "prettier --write --ignore-unknown"], "packages/frontend/src/**/*.{js,jsx,ts,tsx,css,scss,md}": ["npx stylelint \"packages/frontend/src/**/*.{scss, css, sass, less}\" --fix --cache", "npx nx lint frontend", "prettier --write --ignore-unknown"], "libraries/frontend/{ui-components, ui-business-components}/src/**/*.{js,jsx,ts,tsx,css,scss,md}": ["npx nx run-many --parallel --target=lint --projects=ui,ui-business", "prettier --write --ignore-unknown"], "packages/api/src/**/*.{js,jsx,ts,tsx}": ["eslint --quiet --fix --config ./packages/api/.eslintrc.js --ignore-path ./.eslintignore --cache --cache-location ./packages/api/.eslintcache", "prettier --write --ignore-unknown"]}, "packageManager": "yarn@4.5.0", "workspaces": ["packages/websocket", "packages/prismatic"], "nx": {}}